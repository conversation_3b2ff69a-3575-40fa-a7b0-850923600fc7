<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python字典完整教程 - 30页互动PPT</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow: hidden;
        }

        .presentation-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100%;
            display: none;
            padding: 60px;
            position: absolute;
            top: 0;
            left: 0;
        }

        .slide.active {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .slide-number {
            position: absolute;
            top: 30px;
            right: 30px;
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            backdrop-filter: blur(10px);
        }

        .slide-content {
            max-width: 1000px;
            width: 100%;
            text-align: center;
            max-height: 80vh;
            overflow-y: auto;
            padding-right: 20px;
        }

        .slide-content::-webkit-scrollbar {
            width: 8px;
        }

        .slide-content::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
        }

        .slide-content::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
        }

        .emoji {
            font-size: 4rem;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        h1 {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: slideInLeft 1s ease-out;
        }

        h2 {
            font-size: 2.5rem;
            font-weight: 600;
            color: white;
            margin-bottom: 25px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        h3 {
            font-size: 1.8rem;
            font-weight: 500;
            color: #f0f0f0;
            margin-bottom: 20px;
            text-align: left;
        }

        p, li {
            font-size: 1.3rem;
            line-height: 1.8;
            color: #f5f5f5;
            margin-bottom: 15px;
            text-align: left;
        }

        ul {
            margin-left: 30px;
            margin-bottom: 25px;
            text-align: left;
        }

        .code-container {
            background: #1a1a1a;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            border-left: 4px solid #00d4aa;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            overflow-x: auto;
            text-align: left;
        }

        .code {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 1.1rem;
            line-height: 1.6;
            color: #f8f8f2;
            text-align: left;
            white-space: pre;
            overflow-x: auto;
        }

        .keyword { color: #ff79c6; }
        .string { color: #f1fa8c; }
        .number { color: #bd93f9; }
        .comment { color: #6272a4; }
        .function { color: #50fa7b; }
        .variable { color: #8be9fd; }

        .highlight-box {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #ffd700;
            backdrop-filter: blur(10px);
            text-align: left;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            font-size: 16px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 4px;
            background: #00d4aa;
            transition: width 0.3s ease;
            z-index: 1001;
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .animate-in {
            animation: fadeInUp 0.8s ease-out;
        }

        .prompt-template {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            border: 2px solid rgba(255,255,255,0.3);
            text-align: left;
        }

        .ai-tip {
            background: rgba(0, 212, 170, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #00d4aa;
            text-align: left;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .summary-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
            text-align: left;
        }

        .summary-item h4 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }

        .summary-item p {
            margin: 0;
            font-size: 14px;
            line-height: 1.4;
        }

        .next-steps {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
            text-align: left;
        }

        .thank-you-content {
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .achievement {
            background: rgba(76, 175, 80, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: left;
        }

        .achievement ul {
            list-style: none;
            padding: 0;
        }

        .achievement li {
            padding: 8px 0;
            font-size: 16px;
        }

        .motivation, .contact {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .final-message {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .final-message h2 {
            margin: 0;
            font-size: 24px;
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <div class="progress-bar" id="progressBar"></div>
        
        <!-- 第1页：标题页 -->
        <div class="slide active">
            <div class="slide-number">1/30</div>
            <div class="slide-content">
                <div class="emoji">📚</div>
                <h1>Python字典完整教程</h1>
                <h2>从基础到高级应用</h2>
                <div class="highlight-box">
                    <h3>🎯 学习目标</h3>
                    <ul>
                        <li>掌握Python字典的基本概念和操作</li>
                        <li>学会字典的高级应用和排序技巧</li>
                        <li>了解如何使用AI辅助编程工具学习</li>
                        <li>获得实用的提示词模板</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第2页：什么是字典 -->
        <div class="slide">
            <div class="slide-number">2/30</div>
            <div class="slide-content">
                <div class="emoji">🔤</div>
                <h1>什么是字典？</h1>
                <p>字典是Python中的一种数据结构，用于存储键值对（key-value pairs）。</p>
                
                <div class="highlight-box">
                    <h3>🔑 字典的特点</h3>
                    <ul>
                        <li>无序性：字典中的元素没有固定顺序</li>
                        <li>可变性：可以添加、删除、修改元素</li>
                        <li>键唯一性：每个键在字典中只能出现一次</li>
                        <li>键不可变：键必须是不可变类型（字符串、数字、元组）</li>
                    </ul>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 字典的基本结构</span>
<span class="variable">student</span> = {
    <span class="string">"姓名"</span>: <span class="string">"张三"</span>,
    <span class="string">"年龄"</span>: <span class="number">20</span>,
    <span class="string">"专业"</span>: <span class="string">"计算机科学"</span>
}</div>
                </div>
            </div>
        </div>

        <!-- 第3页：创建字典 -->
        <div class="slide">
            <div class="slide-number">3/30</div>
            <div class="slide-content">
                <div class="emoji">🏗️</div>
                <h1>创建字典的方法</h1>
                
                <div class="code-container">
                    <div class="code"><span class="comment"># 方法1：使用花括号</span>
<span class="variable">dict1</span> = {<span class="string">"name"</span>: <span class="string">"Alice"</span>, <span class="string">"age"</span>: <span class="number">25</span>}

<span class="comment"># 方法2：使用dict()函数</span>
<span class="variable">dict2</span> = <span class="function">dict</span>(<span class="variable">name</span>=<span class="string">"Bob"</span>, <span class="variable">age</span>=<span class="number">30</span>)

<span class="comment"># 方法3：从列表创建</span>
<span class="variable">pairs</span> = [(<span class="string">"x"</span>, <span class="number">1</span>), (<span class="string">"y"</span>, <span class="number">2</span>)]
<span class="variable">dict3</span> = <span class="function">dict</span>(<span class="variable">pairs</span>)

<span class="comment"># 方法4：字典推导式</span>
<span class="variable">dict4</span> = {<span class="variable">i</span>: <span class="variable">i</span>**<span class="number">2</span> <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">5</span>)}

<span class="comment"># 创建空字典</span>
<span class="variable">empty_dict</span> = {}
<span class="variable">empty_dict2</span> = <span class="function">dict</span>()</div>
                </div>
            </div>
        </div>

        <!-- 第4页：访问字典元素 -->
        <div class="slide">
            <div class="slide-number">4/30</div>
            <div class="slide-content">
                <div class="emoji">🔍</div>
                <h1>访问字典元素</h1>
                
                <div class="code-container">
                    <div class="code"><span class="variable">student</span> = {
    <span class="string">"姓名"</span>: <span class="string">"李明"</span>,
    <span class="string">"年龄"</span>: <span class="number">22</span>,
    <span class="string">"成绩"</span>: <span class="number">85</span>
}

<span class="comment"># 使用方括号访问</span>
<span class="function">print</span>(<span class="variable">student</span>[<span class="string">"姓名"</span>])  <span class="comment"># 李明</span>

<span class="comment"># 使用get()方法（推荐）</span>
<span class="function">print</span>(<span class="variable">student</span>.<span class="function">get</span>(<span class="string">"年龄"</span>))  <span class="comment"># 22</span>
<span class="function">print</span>(<span class="variable">student</span>.<span class="function">get</span>(<span class="string">"电话"</span>, <span class="string">"未提供"</span>))  <span class="comment"># 未提供</span>

<span class="comment"># 检查键是否存在</span>
<span class="keyword">if</span> <span class="string">"成绩"</span> <span class="keyword">in</span> <span class="variable">student</span>:
    <span class="function">print</span>(<span class="string">"成绩："</span>, <span class="variable">student</span>[<span class="string">"成绩"</span>])</div>
                </div>
            </div>
        </div>

        <!-- 第5页：修改字典 -->
        <div class="slide">
            <div class="slide-number">5/30</div>
            <div class="slide-content">
                <div class="emoji">✏️</div>
                <h1>修改字典</h1>
                
                <div class="code-container">
                    <div class="code"><span class="variable">student</span> = {<span class="string">"姓名"</span>: <span class="string">"王芳"</span>, <span class="string">"年龄"</span>: <span class="number">21</span>}

<span class="comment"># 添加新键值对</span>
<span class="variable">student</span>[<span class="string">"专业"</span>] = <span class="string">"数学"</span>
<span class="function">print</span>(<span class="variable">student</span>)  <span class="comment"># {'姓名': '王芳', '年龄': 21, '专业': '数学'}</span>

<span class="comment"># 修改现有值</span>
<span class="variable">student</span>[<span class="string">"年龄"</span>] = <span class="number">22</span>

<span class="comment"># 使用update()方法批量更新</span>
<span class="variable">student</span>.<span class="function">update</span>({<span class="string">"成绩"</span>: <span class="number">90</span>, <span class="string">"班级"</span>: <span class="string">"A班"</span>})

<span class="comment"># 使用setdefault()添加（如果键不存在）</span>
<span class="variable">student</span>.<span class="function">setdefault</span>(<span class="string">"爱好"</span>, <span class="string">"阅读"</span>)
<span class="function">print</span>(<span class="variable">student</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第6页：删除字典元素 -->
        <div class="slide">
            <div class="slide-number">6/30</div>
            <div class="slide-content">
                <div class="emoji">🗑️</div>
                <h1>删除字典元素</h1>
                
                <div class="code-container">
                    <div class="code"><span class="variable">data</span> = {
    <span class="string">"a"</span>: <span class="number">1</span>, <span class="string">"b"</span>: <span class="number">2</span>, <span class="string">"c"</span>: <span class="number">3</span>, <span class="string">"d"</span>: <span class="number">4</span>
}

<span class="comment"># 使用del删除指定键</span>
<span class="keyword">del</span> <span class="variable">data</span>[<span class="string">"a"</span>]
<span class="function">print</span>(<span class="variable">data</span>)  <span class="comment"># {'b': 2, 'c': 3, 'd': 4}</span>

<span class="comment"># 使用pop()删除并返回值</span>
<span class="variable">value</span> = <span class="variable">data</span>.<span class="function">pop</span>(<span class="string">"b"</span>)
<span class="function">print</span>(<span class="variable">value</span>)  <span class="comment"># 2</span>
<span class="function">print</span>(<span class="variable">data</span>)   <span class="comment"># {'c': 3, 'd': 4}</span>

<span class="comment"># 使用popitem()删除最后一个键值对</span>
<span class="variable">item</span> = <span class="variable">data</span>.<span class="function">popitem</span>()
<span class="function">print</span>(<span class="variable">item</span>)  <span class="comment"># ('d', 4)</span>

<span class="comment"># 使用clear()清空字典</span>
<span class="variable">data</span>.<span class="function">clear</span>()
<span class="function">print</span>(<span class="variable">data</span>)  <span class="comment"># {}</span></div>
                </div>
            </div>
        </div>

        <!-- 第7页：常用字典方法 -->
        <div class="slide">
            <div class="slide-number">7/30</div>
            <div class="slide-content">
                <div class="emoji">🛠️</div>
                <h1>常用字典方法</h1>
                
                <div class="code-container">
                    <div class="code"><span class="variable">scores</span> = {<span class="string">"Alice"</span>: <span class="number">85</span>, <span class="string">"Bob"</span>: <span class="number">92</span>, <span class="string">"Charlie"</span>: <span class="number">78</span>}

<span class="comment"># keys() - 获取所有键</span>
<span class="function">print</span>(<span class="function">list</span>(<span class="variable">scores</span>.<span class="function">keys</span>()))    <span class="comment"># ['Alice', 'Bob', 'Charlie']</span>

<span class="comment"># values() - 获取所有值</span>
<span class="function">print</span>(<span class="function">list</span>(<span class="variable">scores</span>.<span class="function">values</span>()))  <span class="comment"># [85, 92, 78]</span>

<span class="comment"># items() - 获取所有键值对</span>
<span class="function">print</span>(<span class="function">list</span>(<span class="variable">scores</span>.<span class="function">items</span>()))   <span class="comment"># [('Alice', 85), ('Bob', 92), ('Charlie', 78)]</span>

<span class="comment"># copy() - 创建浅拷贝</span>
<span class="variable">scores_copy</span> = <span class="variable">scores</span>.<span class="function">copy</span>()

<span class="comment"># setdefault() - 获取值或设置默认值</span>
<span class="variable">david_score</span> = <span class="variable">scores</span>.<span class="function">setdefault</span>(<span class="string">"David"</span>, <span class="number">80</span>)
<span class="function">print</span>(<span class="variable">scores</span>)  <span class="comment"># 包含David: 80</span></div>
                </div>
            </div>
        </div>

        <!-- 第8页：字典遍历 -->
        <div class="slide">
            <div class="slide-number">8/30</div>
            <div class="slide-content">
                <div class="emoji">🔄</div>
                <h1>字典遍历</h1>
                
                <div class="code-container">
                    <div class="code"><span class="variable">student_grades</span> = {
    <span class="string">"张三"</span>: <span class="number">85</span>,
    <span class="string">"李四"</span>: <span class="number">92</span>,
    <span class="string">"王五"</span>: <span class="number">78</span>
}

<span class="comment"># 遍历键</span>
<span class="keyword">for</span> <span class="variable">name</span> <span class="keyword">in</span> <span class="variable">student_grades</span>:
    <span class="function">print</span>(<span class="string">f"学生：{<span class="variable">name</span>}"</span>)

<span class="comment"># 遍历值</span>
<span class="keyword">for</span> <span class="variable">grade</span> <span class="keyword">in</span> <span class="variable">student_grades</span>.<span class="function">values</span>():
    <span class="function">print</span>(<span class="string">f"成绩：{<span class="variable">grade</span>}"</span>)

<span class="comment"># 遍历键值对</span>
<span class="keyword">for</span> <span class="variable">name</span>, <span class="variable">grade</span> <span class="keyword">in</span> <span class="variable">student_grades</span>.<span class="function">items</span>():
    <span class="function">print</span>(<span class="string">f"{<span class="variable">name</span>}的成绩是{<span class="variable">grade</span>}分"</span>)

<span class="comment"># 带索引的遍历</span>
<span class="keyword">for</span> <span class="variable">i</span>, (<span class="variable">name</span>, <span class="variable">grade</span>) <span class="keyword">in</span> <span class="function">enumerate</span>(<span class="variable">student_grades</span>.<span class="function">items</span>()):
    <span class="function">print</span>(<span class="string">f"{<span class="variable">i</span>+<span class="number">1</span>}. {<span class="variable">name</span>}: {<span class="variable">grade</span>}"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第9页：字典推导式 -->
        <div class="slide">
            <div class="slide-number">9/30</div>
            <div class="slide-content">
                <div class="emoji">⚡</div>
                <h1>字典推导式</h1>
                <p>字典推导式是创建字典的简洁方式，类似于列表推导式。</p>
                
                <div class="code-container">
                    <div class="code"><span class="comment"># 基本语法：{key_expr: value_expr for item in iterable}</span>

<span class="comment"># 创建平方字典</span>
<span class="variable">squares</span> = {<span class="variable">x</span>: <span class="variable">x</span>**<span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1</span>, <span class="number">6</span>)}
<span class="function">print</span>(<span class="variable">squares</span>)  <span class="comment"># {1: 1, 2: 4, 3: 9, 4: 16, 5: 25}</span>

<span class="comment"># 从列表创建字典</span>
<span class="variable">words</span> = [<span class="string">"apple"</span>, <span class="string">"banana"</span>, <span class="string">"cherry"</span>]
<span class="variable">word_lengths</span> = {<span class="variable">word</span>: <span class="function">len</span>(<span class="variable">word</span>) <span class="keyword">for</span> <span class="variable">word</span> <span class="keyword">in</span> <span class="variable">words</span>}
<span class="function">print</span>(<span class="variable">word_lengths</span>)  <span class="comment"># {'apple': 5, 'banana': 6, 'cherry': 6}</span>

<span class="comment"># 带条件的字典推导式</span>
<span class="variable">even_squares</span> = {<span class="variable">x</span>: <span class="variable">x</span>**<span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">10</span>) <span class="keyword">if</span> <span class="variable">x</span> % <span class="number">2</span> == <span class="number">0</span>}
<span class="function">print</span>(<span class="variable">even_squares</span>)  <span class="comment"># {0: 0, 2: 4, 4: 16, 6: 36, 8: 64}</span>

<span class="comment"># 转换现有字典</span>
<span class="variable">temps_c</span> = {<span class="string">"北京"</span>: <span class="number">25</span>, <span class="string">"上海"</span>: <span class="number">28</span>, <span class="string">"广州"</span>: <span class="number">32</span>}
<span class="variable">temps_f</span> = {<span class="variable">city</span>: <span class="variable">temp</span> * <span class="number">9</span>/<span class="number">5</span> + <span class="number">32</span> <span class="keyword">for</span> <span class="variable">city</span>, <span class="variable">temp</span> <span class="keyword">in</span> <span class="variable">temps_c</span>.<span class="function">items</span>()}
<span class="function">print</span>(<span class="variable">temps_f</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第10页：嵌套字典 -->
        <div class="slide">
            <div class="slide-number">10/30</div>
            <div class="slide-content">
                <div class="emoji">🏢</div>
                <h1>嵌套字典</h1>
                <p>字典可以包含其他字典作为值，形成复杂的数据结构。</p>
                
                <div class="code-container">
                    <div class="code"><span class="comment"># 学生信息系统</span>
<span class="variable">students</span> = {
    <span class="string">"001"</span>: {
        <span class="string">"姓名"</span>: <span class="string">"张三"</span>,
        <span class="string">"年龄"</span>: <span class="number">20</span>,
        <span class="string">"成绩"</span>: {
            <span class="string">"数学"</span>: <span class="number">95</span>,
            <span class="string">"英语"</span>: <span class="number">87</span>,
            <span class="string">"物理"</span>: <span class="number">92</span>
        }
    },
    <span class="string">"002"</span>: {
        <span class="string">"姓名"</span>: <span class="string">"李四"</span>,
        <span class="string">"年龄"</span>: <span class="number">21</span>,
        <span class="string">"成绩"</span>: {
            <span class="string">"数学"</span>: <span class="number">87</span>,
            <span class="string">"英语"</span>: <span class="number">94</span>,
            <span class="string">"物理"</span>: <span class="number">89</span>
        }
    }
}

<span class="comment"># 访问嵌套数据</span>
<span class="function">print</span>(<span class="variable">students</span>[<span class="string">"001"</span>][<span class="string">"姓名"</span>])  <span class="comment"># 张三</span>
<span class="function">print</span>(<span class="variable">students</span>[<span class="string">"001"</span>][<span class="string">"成绩"</span>][<span class="string">"数学"</span>])  <span class="comment"># 95</span>

<span class="comment"># 遍历嵌套字典</span>
<span class="keyword">for</span> <span class="variable">student_id</span>, <span class="variable">info</span> <span class="keyword">in</span> <span class="variable">students</span>.<span class="function">items</span>():
    <span class="function">print</span>(<span class="string">f"学号：{<span class="variable">student_id</span>}，姓名：{<span class="variable">info</span>[<span class="string">'姓名'</span>]}"</span>)
    <span class="keyword">for</span> <span class="variable">subject</span>, <span class="variable">score</span> <span class="keyword">in</span> <span class="variable">info</span>[<span class="string">"成绩"</span>].<span class="function">items</span>():
        <span class="function">print</span>(<span class="string">f"  {<span class="variable">subject</span>}: {<span class="variable">score</span>}分"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第11页：字典排序基础 -->
        <div class="slide">
            <div class="slide-number">11/30</div>
            <div class="slide-content">
                <div class="emoji">📊</div>
                <h1>字典排序基础</h1>
                <p>虽然字典本身是无序的，但我们可以对其进行排序操作。</p>
                
                <div class="code-container">
                    <div class="code"><span class="variable">scores</span> = {
    <span class="string">"Alice"</span>: <span class="number">85</span>,
    <span class="string">"Bob"</span>: <span class="number">92</span>,
    <span class="string">"Charlie"</span>: <span class="number">78</span>,
    <span class="string">"David"</span>: <span class="number">96</span>
}

<span class="comment"># 按键排序</span>
<span class="variable">sorted_by_key</span> = <span class="function">dict</span>(<span class="function">sorted</span>(<span class="variable">scores</span>.<span class="function">items</span>()))
<span class="function">print</span>(<span class="variable">sorted_by_key</span>)
<span class="comment"># {'Alice': 85, 'Bob': 92, 'Charlie': 78, 'David': 96}</span>

<span class="comment"># 按值排序（升序）</span>
<span class="variable">sorted_by_value_asc</span> = <span class="function">dict</span>(<span class="function">sorted</span>(<span class="variable">scores</span>.<span class="function">items</span>(), <span class="variable">key</span>=<span class="keyword">lambda</span> <span class="variable">x</span>: <span class="variable">x</span>[<span class="number">1</span>]))
<span class="function">print</span>(<span class="variable">sorted_by_value_asc</span>)
<span class="comment"># {'Charlie': 78, 'Alice': 85, 'Bob': 92, 'David': 96}</span>

<span class="comment"># 按值排序（降序）</span>
<span class="variable">sorted_by_value_desc</span> = <span class="function">dict</span>(<span class="function">sorted</span>(<span class="variable">scores</span>.<span class="function">items</span>(), <span class="variable">key</span>=<span class="keyword">lambda</span> <span class="variable">x</span>: <span class="variable">x</span>[<span class="number">1</span>], <span class="variable">reverse</span>=<span class="keyword">True</span>))
<span class="function">print</span>(<span class="variable">sorted_by_value_desc</span>)
<span class="comment"># {'David': 96, 'Bob': 92, 'Alice': 85, 'Charlie': 78}</span></div>
                </div>
            </div>
        </div>

        <!-- 第12页：高级排序技巧 -->
        <div class="slide">
            <div class="slide-number">12/30</div>
            <div class="slide-content">
                <div class="emoji">🎯</div>
                <h1>高级排序技巧</h1>
                
                <div class="code-container">
                    <div class="code"><span class="comment"># 复杂数据结构排序</span>
<span class="variable">employees</span> = {
    <span class="string">"E001"</span>: {<span class="string">"name"</span>: <span class="string">"张三"</span>, <span class="string">"salary"</span>: <span class="number">8000</span>, <span class="string">"dept"</span>: <span class="string">"IT"</span>},
    <span class="string">"E002"</span>: {<span class="string">"name"</span>: <span class="string">"李四"</span>, <span class="string">"salary"</span>: <span class="number">12000</span>, <span class="string">"dept"</span>: <span class="string">"Sales"</span>},
    <span class="string">"E003"</span>: {<span class="string">"name"</span>: <span class="string">"王五"</span>, <span class="string">"salary"</span>: <span class="number">9500</span>, <span class="string">"dept"</span>: <span class="string">"IT"</span>}
}

<span class="comment"># 按薪资排序</span>
<span class="variable">sorted_by_salary</span> = <span class="function">dict</span>(<span class="function">sorted</span>(
    <span class="variable">employees</span>.<span class="function">items</span>(), 
    <span class="variable">key</span>=<span class="keyword">lambda</span> <span class="variable">x</span>: <span class="variable">x</span>[<span class="number">1</span>][<span class="string">"salary"</span>], 
    <span class="variable">reverse</span>=<span class="keyword">True</span>
))

<span class="comment"># 多条件排序：先按部门，再按薪资</span>
<span class="variable">sorted_multi</span> = <span class="function">dict</span>(<span class="function">sorted</span>(
    <span class="variable">employees</span>.<span class="function">items</span>(),
    <span class="variable">key</span>=<span class="keyword">lambda</span> <span class="variable">x</span>: (<span class="variable">x</span>[<span class="number">1</span>][<span class="string">"dept"</span>], -<span class="variable">x</span>[<span class="number">1</span>][<span class="string">"salary"</span>])
))

<span class="keyword">for</span> <span class="variable">emp_id</span>, <span class="variable">info</span> <span class="keyword">in</span> <span class="variable">sorted_multi</span>.<span class="function">items</span>():
    <span class="function">print</span>(<span class="string">f"{<span class="variable">info</span>[<span class="string">'name'</span>]}: {<span class="variable">info</span>[<span class="string">'dept'</span>]} - {<span class="variable">info</span>[<span class="string">'salary'</span>]}"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第13页：字典合并 -->
        <div class="slide">
            <div class="slide-number">13/30</div>
            <div class="slide-content">
                <div class="emoji">🔗</div>
                <h1>字典合并</h1>
                
                <div class="code-container">
                    <div class="code"><span class="variable">dict1</span> = {<span class="string">"a"</span>: <span class="number">1</span>, <span class="string">"b"</span>: <span class="number">2</span>}
<span class="variable">dict2</span> = {<span class="string">"c"</span>: <span class="number">3</span>, <span class="string">"d"</span>: <span class="number">4</span>}
<span class="variable">dict3</span> = {<span class="string">"b"</span>: <span class="number">5</span>, <span class="string">"e"</span>: <span class="number">6</span>}

<span class="comment"># 方法1：使用update()（修改原字典）</span>
<span class="variable">result1</span> = <span class="variable">dict1</span>.<span class="function">copy</span>()
<span class="variable">result1</span>.<span class="function">update</span>(<span class="variable">dict2</span>)
<span class="function">print</span>(<span class="variable">result1</span>)  <span class="comment"># {'a': 1, 'b': 2, 'c': 3, 'd': 4}</span>

<span class="comment"># 方法2：使用**操作符（Python 3.5+）</span>
<span class="variable">result2</span> = {**<span class="variable">dict1</span>, **<span class="variable">dict2</span>, **<span class="variable">dict3</span>}
<span class="function">print</span>(<span class="variable">result2</span>)  <span class="comment"># {'a': 1, 'b': 5, 'c': 3, 'd': 4, 'e': 6}</span>

<span class="comment"># 方法3：使用|操作符（Python 3.9+）</span>
<span class="variable">result3</span> = <span class="variable">dict1</span> | <span class="variable">dict2</span> | <span class="variable">dict3</span>
<span class="function">print</span>(<span class="variable">result3</span>)

<span class="comment"># 方法4：使用dict()和items()</span>
<span class="variable">result4</span> = <span class="function">dict</span>(<span class="function">list</span>(<span class="variable">dict1</span>.<span class="function">items</span>()) + <span class="function">list</span>(<span class="variable">dict2</span>.<span class="function">items</span>()))
<span class="function">print</span>(<span class="variable">result4</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第14页：字典过滤 -->
        <div class="slide">
            <div class="slide-number">14/30</div>
            <div class="slide-content">
                <div class="emoji">🔍</div>
                <h1>字典过滤</h1>
                
                <div class="code-container">
                    <div class="code"><span class="variable">products</span> = {
    <span class="string">"苹果"</span>: <span class="number">5.5</span>,
    <span class="string">"香蕉"</span>: <span class="number">3.2</span>,
    <span class="string">"橙子"</span>: <span class="number">4.8</span>,
    <span class="string">"葡萄"</span>: <span class="number">8.9</span>,
    <span class="string">"草莓"</span>: <span class="number">12.5</span>
}

<span class="comment"># 过滤价格大于5的商品</span>
<span class="variable">expensive</span> = {<span class="variable">k</span>: <span class="variable">v</span> <span class="keyword">for</span> <span class="variable">k</span>, <span class="variable">v</span> <span class="keyword">in</span> <span class="variable">products</span>.<span class="function">items</span>() <span class="keyword">if</span> <span class="variable">v</span> > <span class="number">5</span>}
<span class="function">print</span>(<span class="variable">expensive</span>)  <span class="comment"># {'苹果': 5.5, '葡萄': 8.9, '草莓': 12.5}</span>

<span class="comment"># 使用filter()函数</span>
<span class="variable">cheap_items</span> = <span class="function">dict</span>(<span class="function">filter</span>(<span class="keyword">lambda</span> <span class="variable">item</span>: <span class="variable">item</span>[<span class="number">1</span>] <= <span class="number">5</span>, <span class="variable">products</span>.<span class="function">items</span>()))
<span class="function">print</span>(<span class="variable">cheap_items</span>)  <span class="comment"># {'香蕉': 3.2, '橙子': 4.8}</span>

<span class="comment"># 按键过滤（包含特定字符）</span>
<span class="variable">fruits_with_apple</span> = {<span class="variable">k</span>: <span class="variable">v</span> <span class="keyword">for</span> <span class="variable">k</span>, <span class="variable">v</span> <span class="keyword">in</span> <span class="variable">products</span>.<span class="function">items</span>() <span class="keyword">if</span> <span class="string">"苹"</span> <span class="keyword">in</span> <span class="variable">k</span>}
<span class="function">print</span>(<span class="variable">fruits_with_apple</span>)  <span class="comment"># {'苹果': 5.5}</span>

<span class="comment"># 复杂条件过滤</span>
<span class="variable">mid_range</span> = {<span class="variable">k</span>: <span class="variable">v</span> <span class="keyword">for</span> <span class="variable">k</span>, <span class="variable">v</span> <span class="keyword">in</span> <span class="variable">products</span>.<span class="function">items</span>() <span class="keyword">if</span> <span class="number">4</span> <= <span class="variable">v</span> <= <span class="number">8</span>}
<span class="function">print</span>(<span class="variable">mid_range</span>)  <span class="comment"># {'苹果': 5.5, '橙子': 4.8}</span></div>
                </div>
            </div>
        </div>

        <!-- 第15页：Counter应用 -->
        <div class="slide">
            <div class="slide-number">15/30</div>
            <div class="slide-content">
                <div class="emoji">🔢</div>
                <h1>Counter应用</h1>
                <p>Counter是字典的子类，专门用于计数。</p>
                
                <div class="code-container">
                    <div class="code"><span class="keyword">from</span> <span class="variable">collections</span> <span class="keyword">import</span> <span class="variable">Counter</span>

<span class="comment"># 统计字符出现次数</span>
<span class="variable">text</span> = <span class="string">"hello world"</span>
<span class="variable">char_count</span> = <span class="function">Counter</span>(<span class="variable">text</span>)
<span class="function">print</span>(<span class="variable">char_count</span>)  <span class="comment"># Counter({'l': 3, 'o': 2, 'h': 1, 'e': 1, ' ': 1, 'w': 1, 'r': 1, 'd': 1})</span>

<span class="comment"># 统计列表元素</span>
<span class="variable">fruits</span> = [<span class="string">"苹果"</span>, <span class="string">"香蕉"</span>, <span class="string">"苹果"</span>, <span class="string">"橙子"</span>, <span class="string">"香蕉"</span>, <span class="string">"苹果"</span>]
<span class="variable">fruit_count</span> = <span class="function">Counter</span>(<span class="variable">fruits</span>)
<span class="function">print</span>(<span class="variable">fruit_count</span>)  <span class="comment"># Counter({'苹果': 3, '香蕉': 2, '橙子': 1})</span>

<span class="comment"># 最常见的元素</span>
<span class="function">print</span>(<span class="variable">fruit_count</span>.<span class="function">most_common</span>(<span class="number">2</span>))  <span class="comment"># [('苹果', 3), ('香蕉', 2)]</span>

<span class="comment"># Counter运算</span>
<span class="variable">counter1</span> = <span class="function">Counter</span>([<span class="string">"a"</span>, <span class="string">"b"</span>, <span class="string">"c"</span>, <span class="string">"a"</span>])
<span class="variable">counter2</span> = <span class="function">Counter</span>([<span class="string">"a"</span>, <span class="string">"b"</span>, <span class="string">"b"</span>, <span class="string">"d"</span>])

<span class="function">print</span>(<span class="variable">counter1</span> + <span class="variable">counter2</span>)  <span class="comment"># 相加</span>
<span class="function">print</span>(<span class="variable">counter1</span> - <span class="variable">counter2</span>)  <span class="comment"># 相减</span>
<span class="function">print</span>(<span class="variable">counter1</span> & <span class="variable">counter2</span>)  <span class="comment"># 交集</span>
<span class="function">print</span>(<span class="variable">counter1</span> | <span class="variable">counter2</span>)  <span class="comment"># 并集</span></div>
                </div>
            </div>
        </div>

        <!-- 第16页：性能优化 -->
        <div class="slide">
            <div class="slide-number">16/30</div>
            <div class="slide-content">
                <div class="emoji">⚡</div>
                <h1>字典性能优化</h1>
                
                <div class="code-container">
                    <div class="code"><span class="keyword">import</span> <span class="variable">time</span>

<span class="comment"># 1. 使用get()而不是检查键存在</span>
<span class="comment"># 慢的方式</span>
<span class="keyword">if</span> <span class="string">"key"</span> <span class="keyword">in</span> <span class="variable">my_dict</span>:
    <span class="variable">value</span> = <span class="variable">my_dict</span>[<span class="string">"key"</span>]
<span class="keyword">else</span>:
    <span class="variable">value</span> = <span class="string">"default"</span>

<span class="comment"># 快的方式</span>
<span class="variable">value</span> = <span class="variable">my_dict</span>.<span class="function">get</span>(<span class="string">"key"</span>, <span class="string">"default"</span>)

<span class="comment"># 2. 使用setdefault()避免重复检查</span>
<span class="comment"># 慢的方式</span>
<span class="keyword">if</span> <span class="string">"key"</span> <span class="keyword">not</span> <span class="keyword">in</span> <span class="variable">my_dict</span>:
    <span class="variable">my_dict</span>[<span class="string">"key"</span>] = []
<span class="variable">my_dict</span>[<span class="string">"key"</span>].<span class="function">append</span>(<span class="string">"value"</span>)

<span class="comment"># 快的方式</span>
<span class="variable">my_dict</span>.<span class="function">setdefault</span>(<span class="string">"key"</span>, []).<span class="function">append</span>(<span class="string">"value"</span>)

<span class="comment"># 3. 使用字典推导式而不是循环</span>
<span class="comment"># 慢的方式</span>
<span class="variable">result</span> = {}
<span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1000</span>):
    <span class="variable">result</span>[<span class="variable">i</span>] = <span class="variable">i</span> ** <span class="number">2</span>

<span class="comment"># 快的方式</span>
<span class="variable">result</span> = {<span class="variable">i</span>: <span class="variable">i</span> ** <span class="number">2</span> <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1000</span>)}</div>
                </div>
            </div>
        </div>

        <!-- 第17页：实际应用案例1 -->
         <div class="slide">
             <div class="slide-number">17/30</div>
             <div class="slide-content">
                 <div class="emoji">📦</div>
                 <h1>实际应用：商品库存管理</h1>
                 
                 <div class="code-container">
                     <div class="code"><span class="keyword">class</span> <span class="function">InventoryManager</span>:
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="variable">self</span>):
        <span class="variable">self</span>.<span class="variable">inventory</span> = {}
    
    <span class="keyword">def</span> <span class="function">add_product</span>(<span class="variable">self</span>, <span class="variable">product_id</span>, <span class="variable">name</span>, <span class="variable">quantity</span>, <span class="variable">price</span>):
        <span class="variable">self</span>.<span class="variable">inventory</span>[<span class="variable">product_id</span>] = {
            <span class="string">"name"</span>: <span class="variable">name</span>,
            <span class="string">"quantity"</span>: <span class="variable">quantity</span>,
            <span class="string">"price"</span>: <span class="variable">price</span>
        }
    
    <span class="keyword">def</span> <span class="function">update_quantity</span>(<span class="variable">self</span>, <span class="variable">product_id</span>, <span class="variable">change</span>):
        <span class="keyword">if</span> <span class="variable">product_id</span> <span class="keyword">in</span> <span class="variable">self</span>.<span class="variable">inventory</span>:
            <span class="variable">self</span>.<span class="variable">inventory</span>[<span class="variable">product_id</span>][<span class="string">"quantity"</span>] += <span class="variable">change</span>
    
    <span class="keyword">def</span> <span class="function">get_low_stock</span>(<span class="variable">self</span>, <span class="variable">threshold</span>=<span class="number">10</span>):
        <span class="keyword">return</span> {
            <span class="variable">pid</span>: <span class="variable">info</span> <span class="keyword">for</span> <span class="variable">pid</span>, <span class="variable">info</span> <span class="keyword">in</span> <span class="variable">self</span>.<span class="variable">inventory</span>.<span class="function">items</span>()
            <span class="keyword">if</span> <span class="variable">info</span>[<span class="string">"quantity"</span>] < <span class="variable">threshold</span>
        }

<span class="comment"># 使用示例</span>
<span class="variable">manager</span> = <span class="function">InventoryManager</span>()
<span class="variable">manager</span>.<span class="function">add_product</span>(<span class="string">"P001"</span>, <span class="string">"笔记本"</span>, <span class="number">5</span>, <span class="number">5999</span>)
<span class="variable">manager</span>.<span class="function">add_product</span>(<span class="string">"P002"</span>, <span class="string">"手机"</span>, <span class="number">15</span>, <span class="number">3999</span>)

<span class="function">print</span>(<span class="string">f"低库存商品: {<span class="variable">manager</span>.<span class="function">get_low_stock</span>()}"</span>)</div>
                 </div>
             </div>
         </div>

         <!-- 第18页：实际应用案例2 -->
         <div class="slide">
             <div class="slide-number">18/30</div>
             <div class="slide-content">
                 <div class="emoji">📊</div>
                 <h1>实际应用：数据分析</h1>
                 
                 <div class="code-container">
                     <div class="code"><span class="comment"># 销售数据分析</span>
<span class="variable">sales_data</span> = [
    {<span class="string">"product"</span>: <span class="string">"笔记本"</span>, <span class="string">"region"</span>: <span class="string">"北京"</span>, <span class="string">"amount"</span>: <span class="number">12000</span>},
    {<span class="string">"product"</span>: <span class="string">"手机"</span>, <span class="string">"region"</span>: <span class="string">"上海"</span>, <span class="string">"amount"</span>: <span class="number">8000</span>},
    {<span class="string">"product"</span>: <span class="string">"笔记本"</span>, <span class="string">"region"</span>: <span class="string">"上海"</span>, <span class="string">"amount"</span>: <span class="number">15000</span>},
    {<span class="string">"product"</span>: <span class="string">"平板"</span>, <span class="string">"region"</span>: <span class="string">"北京"</span>, <span class="string">"amount"</span>: <span class="number">6000</span>}
]

<span class="comment"># 按产品统计销售额</span>
<span class="variable">product_sales</span> = {}
<span class="keyword">for</span> <span class="variable">sale</span> <span class="keyword">in</span> <span class="variable">sales_data</span>:
    <span class="variable">product</span> = <span class="variable">sale</span>[<span class="string">"product"</span>]
    <span class="variable">amount</span> = <span class="variable">sale</span>[<span class="string">"amount"</span>]
    <span class="variable">product_sales</span>[<span class="variable">product</span>] = <span class="variable">product_sales</span>.<span class="function">get</span>(<span class="variable">product</span>, <span class="number">0</span>) + <span class="variable">amount</span>

<span class="function">print</span>(<span class="string">"按产品销售额排序:"</span>)
<span class="keyword">for</span> <span class="variable">product</span>, <span class="variable">total</span> <span class="keyword">in</span> <span class="function">sorted</span>(<span class="variable">product_sales</span>.<span class="function">items</span>(), <span class="variable">key</span>=<span class="keyword">lambda</span> <span class="variable">x</span>: <span class="variable">x</span>[<span class="number">1</span>], <span class="variable">reverse</span>=<span class="keyword">True</span>):
    <span class="function">print</span>(<span class="string">f"{<span class="variable">product</span>}: {<span class="variable">total</span>}元"</span>)</div>
                 </div>
             </div>
         </div>

         <!-- 第19页：缓存系统 -->
         <div class="slide">
             <div class="slide-number">19/30</div>
             <div class="slide-content">
                 <div class="emoji">💾</div>
                 <h1>实际应用：缓存系统</h1>
                 
                 <div class="code-container">
                     <div class="code"><span class="keyword">class</span> <span class="function">SimpleCache</span>:
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="variable">self</span>, <span class="variable">max_size</span>=<span class="number">100</span>):
        <span class="variable">self</span>.<span class="variable">cache</span> = {}
        <span class="variable">self</span>.<span class="variable">max_size</span> = <span class="variable">max_size</span>
        <span class="variable">self</span>.<span class="variable">access_order</span> = []
    
    <span class="keyword">def</span> <span class="function">get</span>(<span class="variable">self</span>, <span class="variable">key</span>):
        <span class="keyword">if</span> <span class="variable">key</span> <span class="keyword">in</span> <span class="variable">self</span>.<span class="variable">cache</span>:
            <span class="variable">self</span>.<span class="variable">access_order</span>.<span class="function">remove</span>(<span class="variable">key</span>)
            <span class="variable">self</span>.<span class="variable">access_order</span>.<span class="function">append</span>(<span class="variable">key</span>)
            <span class="keyword">return</span> <span class="variable">self</span>.<span class="variable">cache</span>[<span class="variable">key</span>]
        <span class="keyword">return</span> <span class="keyword">None</span>
    
    <span class="keyword">def</span> <span class="function">set</span>(<span class="variable">self</span>, <span class="variable">key</span>, <span class="variable">value</span>):
        <span class="keyword">if</span> <span class="function">len</span>(<span class="variable">self</span>.<span class="variable">cache</span>) >= <span class="variable">self</span>.<span class="variable">max_size</span>:
            <span class="variable">oldest_key</span> = <span class="variable">self</span>.<span class="variable">access_order</span>.<span class="function">pop</span>(<span class="number">0</span>)
            <span class="keyword">del</span> <span class="variable">self</span>.<span class="variable">cache</span>[<span class="variable">oldest_key</span>]
        
        <span class="variable">self</span>.<span class="variable">cache</span>[<span class="variable">key</span>] = <span class="variable">value</span>
        <span class="variable">self</span>.<span class="variable">access_order</span>.<span class="function">append</span>(<span class="variable">key</span>)

<span class="comment"># 使用示例</span>
<span class="variable">cache</span> = <span class="function">SimpleCache</span>(<span class="variable">max_size</span>=<span class="number">3</span>)
<span class="variable">cache</span>.<span class="function">set</span>(<span class="string">"user_1"</span>, {<span class="string">"name"</span>: <span class="string">"张三"</span>})</div>
                 </div>
             </div>
         </div>

         <!-- 第20页：AI辅助编程介绍 -->
         <div class="slide">
             <div class="slide-number">20/30</div>
             <div class="slide-content">
                 <div class="emoji">🤖</div>
                 <h1>AI辅助编程学习字典</h1>
                 <p>利用AI工具可以大大提升学习Python字典的效率和深度。</p>
                 
                 <h3>🎯 AI学习的优势</h3>
                 <ul>
                     <li>即时解答疑问，无需等待</li>
                     <li>个性化学习路径定制</li>
                     <li>代码实例生成和解释</li>
                     <li>错误调试和优化建议</li>
                     <li>最佳实践推荐</li>
                 </ul>

                 <div class="ai-tip">
                     <strong>💡 学习建议：</strong>将AI作为学习伙伴，而不是答案提供者。通过提问和讨论来加深理解。
                 </div>

                 <h3>🛠️ 推荐AI工具</h3>
                 <ul>
                     <li>ChatGPT - 代码解释和生成</li>
                     <li>GitHub Copilot - 代码补全</li>
                     <li>Claude - 深度技术讨论</li>
                     <li>Cursor - AI编程IDE</li>
                 </ul>
             </div>
         </div>

         <!-- 第21页：基础学习提示词 -->
         <div class="slide">
             <div class="slide-number">21/30</div>
             <div class="slide-content">
                 <div class="emoji">📝</div>
                 <h1>基础学习提示词模板</h1>
                 
                 <div class="prompt-template">
                     <h3>🔍 概念理解模板</h3>
                     <div class="code-container">
                         <div class="code">请详细解释Python字典的[具体概念]，包括：
1. 基本定义和特点
2. 与其他数据结构的区别
3. 使用场景和最佳实践
4. 提供3个由浅入深的代码示例
5. 常见错误和避免方法

示例：请详细解释Python字典的键值对概念...</div>
                     </div>
                 </div>

                 <div class="prompt-template">
                     <h3>💻 代码生成模板</h3>
                     <div class="code-container">
                         <div class="code">请帮我编写一个Python字典程序，要求：
功能：[具体功能描述]
输入：[输入格式说明]
输出：[期望输出格式]
约束：[性能或其他要求]

请提供：
1. 完整可运行的代码
2. 详细的注释说明
3. 测试用例
4. 可能的优化建议</div>
                     </div>
                 </div>
             </div>
         </div>

         <!-- 第22页：调试和优化提示词 -->
         <div class="slide">
             <div class="slide-number">22/30</div>
             <div class="slide-content">
                 <div class="emoji">🔧</div>
                 <h1>调试和优化提示词</h1>
                 
                 <div class="prompt-template">
                     <h3>🐛 调试模板</h3>
                     <div class="code-container">
                         <div class="code">我的Python字典代码遇到了问题：

[粘贴完整代码]

错误信息：[具体错误信息]
期望结果：[描述期望的行为]
实际结果：[描述实际发生的情况]

请帮我：
1. 分析错误原因
2. 提供修复方案
3. 解释为什么会出现这个问题
4. 给出预防类似错误的建议</div>
                     </div>
                 </div>

                 <div class="prompt-template">
                     <h3>⚡ 性能优化模板</h3>
                     <div class="code-container">
                         <div class="code">请帮我优化这段字典操作代码的性能：

[粘贴代码]

当前性能问题：[描述性能瓶颈]
数据规模：[说明数据量大小]
性能要求：[具体的性能目标]

请提供：
1. 性能分析
2. 优化后的代码
3. 性能对比说明
4. 进一步优化的建议</div>
                     </div>
                 </div>
             </div>
         </div>

         <!-- 第23页：进阶学习提示词 -->
         <div class="slide">
             <div class="slide-number">23/30</div>
             <div class="slide-content">
                 <div class="emoji">🚀</div>
                 <h1>进阶学习提示词模板</h1>
                 
                 <div class="prompt-template">
                     <h3>🎓 深度学习模板</h3>
                     <div class="code-container">
                         <div class="code">我想深入学习Python字典的[高级主题]：

学习目标：[具体想要达到的水平]
当前水平：[描述现有知识基础]
应用场景：[实际使用场景]

请为我设计一个学习计划，包括：
1. 知识点梳理和学习顺序
2. 每个阶段的练习项目
3. 进阶阅读材料推荐
4. 实战项目建议
5. 学习成果检验方法</div>
                     </div>
                 </div>

                 <div class="prompt-template">
                     <h3>🔬 源码分析模板</h3>
                     <div class="code-container">
                         <div class="code">请帮我分析Python字典的底层实现：

关注点：[具体想了解的方面，如哈希表实现、内存管理等]

请解释：
1. 底层数据结构设计
2. 关键算法实现原理
3. 时间复杂度分析
4. 内存使用特点
5. 与其他语言实现的对比</div>
                     </div>
                 </div>
             </div>
         </div>

         <!-- 第24页：实战项目提示词 -->
         <div class="slide">
             <div class="slide-number">24/30</div>
             <div class="slide-content">
                 <div class="emoji">🎯</div>
                 <h1>实战项目提示词模板</h1>
                 
                 <div class="prompt-template">
                     <h3>🏗️ 项目构建模板</h3>
                     <div class="code-container">
                         <div class="code">我想用Python字典构建一个[项目类型]项目：

项目描述：[详细描述项目功能和目标]
技术要求：[使用的技术栈和框架]
数据来源：[数据的来源和格式]
用户群体：[目标用户和使用场景]

请帮我：
1. 设计整体架构
2. 规划数据结构
3. 提供核心代码框架
4. 建议测试策略
5. 给出部署建议</div>
                     </div>
                 </div>

                 <div class="prompt-template">
                     <h3>📊 数据处理项目模板</h3>
                     <div class="code-container">
                         <div class="code">我需要处理以下数据，请用字典设计解决方案：

数据描述：[数据的结构和特点]
处理需求：[具体的数据处理要求]
性能要求：[处理速度和内存限制]
输出格式：[期望的结果格式]

请提供：
1. 数据建模方案
2. 处理算法设计
3. 完整实现代码
4. 性能优化建议
5. 错误处理机制</div>
                     </div>
                 </div>
             </div>
         </div>

         <!-- 第25页：学习路径规划 -->
         <div class="slide">
             <div class="slide-number">25/30</div>
             <div class="slide-content">
                 <div class="emoji">🗺️</div>
                 <h1>AI辅助学习路径</h1>
                 
                 <h3>📚 初级阶段（1-2周）</h3>
                 <ul>
                     <li>掌握字典基本操作和语法</li>
                     <li>理解键值对概念和访问方法</li>
                     <li>练习字典的增删改查操作</li>
                     <li>学会使用字典方法和属性</li>
                 </ul>

                 <h3>🚀 中级阶段（2-3周）</h3>
                 <ul>
                     <li>掌握字典推导式和高级操作</li>
                     <li>学习字典排序和过滤技巧</li>
                     <li>理解嵌套字典和复杂数据结构</li>
                     <li>练习实际项目中的应用</li>
                 </ul>

                 <h3>⭐ 高级阶段（3-4周）</h3>
                 <ul>
                     <li>深入理解字典的底层实现</li>
                     <li>掌握性能优化和最佳实践</li>
                     <li>学习与其他数据结构的配合使用</li>
                     <li>完成复杂的实战项目</li>
                 </ul>

                 <div class="ai-tip">
                     <strong>💡 学习建议：</strong>每个阶段都要结合AI工具进行实践，通过提问和讨论加深理解。
                 </div>
             </div>
         </div>

         <!-- 第26页：常见错误和解决方案 -->
         <div class="slide">
             <div class="slide-number">26/30</div>
             <div class="slide-content">
                 <div class="emoji">⚠️</div>
                 <h1>常见错误和AI解决方案</h1>
                 
                 <div class="code-container">
                     <div class="code"><span class="comment"># 错误1：KeyError - 访问不存在的键</span>
<span class="comment"># 错误代码：</span>
<span class="variable">data</span> = {<span class="string">"name"</span>: <span class="string">"张三"</span>}
<span class="function">print</span>(<span class="variable">data</span>[<span class="string">"age"</span>])  <span class="comment"># KeyError!</span>

<span class="comment"># 正确做法：</span>
<span class="function">print</span>(<span class="variable">data</span>.<span class="function">get</span>(<span class="string">"age"</span>, <span class="string">"未知"</span>))  <span class="comment"># 安全访问</span>

<span class="comment"># 错误2：使用可变对象作为键</span>
<span class="comment"># 错误代码：</span>
<span class="variable">bad_dict</span> = {[<span class="number">1</span>, <span class="number">2</span>]: <span class="string">"value"</span>}  <span class="comment"># TypeError!</span>

<span class="comment"># 正确做法：</span>
<span class="variable">good_dict</span> = {(<span class="number">1</span>, <span class="number">2</span>): <span class="string">"value"</span>}  <span class="comment"># 使用元组</span>

<span class="comment"># 错误3：在遍历时修改字典</span>
<span class="comment"># 错误代码：</span>
<span class="keyword">for</span> <span class="variable">key</span> <span class="keyword">in</span> <span class="variable">data</span>:
    <span class="keyword">if</span> <span class="variable">key</span> == <span class="string">"name"</span>:
        <span class="keyword">del</span> <span class="variable">data</span>[<span class="variable">key</span>]  <span class="comment"># RuntimeError!</span>

<span class="comment"># 正确做法：</span>
<span class="variable">keys_to_delete</span> = [<span class="variable">k</span> <span class="keyword">for</span> <span class="variable">k</span> <span class="keyword">in</span> <span class="variable">data</span> <span class="keyword">if</span> <span class="variable">k</span> == <span class="string">"name"</span>]
<span class="keyword">for</span> <span class="variable">key</span> <span class="keyword">in</span> <span class="variable">keys_to_delete</span>:
    <span class="keyword">del</span> <span class="variable">data</span>[<span class="variable">key</span>]</div>
                 </div>

                 <div class="prompt-template">
                     <h3>🤖 AI调试提示词</h3>
                     <div class="code-container">
                         <div class="code">我的代码出现了[具体错误]，请帮我分析原因并提供解决方案。
同时请告诉我如何避免类似错误，以及相关的最佳实践。</div>
                     </div>
                 </div>
             </div>
         </div>

         <!-- 第27页：最佳实践总结 -->
         <div class="slide">
             <div class="slide-number">27/30</div>
             <div class="slide-content">
                 <div class="emoji">✨</div>
                 <h1>字典使用最佳实践</h1>
                 
                 <h3>🎯 性能优化原则</h3>
                 <ul>
                     <li>优先使用字典的内置方法（get、setdefault等）</li>
                     <li>避免在循环中频繁创建字典</li>
                     <li>合理使用字典推导式提高效率</li>
                     <li>选择合适的键类型（字符串、数字、元组）</li>
                 </ul>

                 <h3>🛡️ 安全编程建议</h3>
                 <ul>
                     <li>始终检查键是否存在再访问</li>
                     <li>使用try-except处理KeyError异常</li>
                     <li>避免使用可变对象作为字典键</li>
                     <li>在多线程环境中注意字典的线程安全</li>
                 </ul>

                 <h3>📝 代码可读性</h3>
                 <ul>
                     <li>使用有意义的键名</li>
                     <li>保持字典结构的一致性</li>
                     <li>适当添加注释说明复杂的数据结构</li>
                     <li>合理使用嵌套，避免过度复杂化</li>
                 </ul>

                 <div class="ai-tip">
                     <strong>💡 记住：</strong>好的代码不仅要能运行，还要易于理解和维护。
                 </div>
             </div>
         </div>

         <!-- 第28页：进阶学习资源 -->
         <div class="slide">
             <div class="slide-number">28/30</div>
             <div class="slide-content">
                 <div class="emoji">📚</div>
                 <h1>进阶学习资源推荐</h1>
                 
                 <h3>🤖 AI学习工具</h3>
                 <ul>
                     <li><strong>ChatGPT/Claude：</strong>代码解释、调试、优化建议</li>
                     <li><strong>GitHub Copilot：</strong>智能代码补全和生成</li>
                     <li><strong>Cursor：</strong>AI驱动的代码编辑器</li>
                     <li><strong>Replit：</strong>在线编程环境，支持AI助手</li>
                 </ul>

                 <h3>📖 学习资源</h3>
                 <ul>
                     <li><strong>官方文档：</strong>Python.org 字典文档</li>
                     <li><strong>在线教程：</strong>Real Python、GeeksforGeeks</li>
                     <li><strong>视频课程：</strong>YouTube、B站编程教程</li>
                     <li><strong>练习平台：</strong>LeetCode、HackerRank</li>
                 </ul>

                 <h3>🎯 实践项目建议</h3>
                 <ul>
                     <li>构建个人通讯录管理系统</li>
                     <li>开发简单的数据分析工具</li>
                     <li>创建配置文件解析器</li>
                     <li>实现缓存系统原型</li>
                 </ul>

                 <div class="prompt-template">
                     <h3>🔍 资源查找提示词</h3>
                     <div class="code-container">
                         <div class="code">请推荐一些关于Python字典[具体主题]的学习资源，
包括教程、文档、实践项目和相关工具。
我的当前水平是[初级/中级/高级]。</div>
                     </div>
                 </div>
             </div>
         </div>

         <!-- 第29页：课程总结 -->
         <div class="slide">
             <div class="slide-number">29/30</div>
             <div class="slide-content">
                 <div class="emoji">🎓</div>
                 <h1>课程总结</h1>
                 
                 <h3>📋 我们学习了什么</h3>
                 <div class="summary-grid">
                     <div class="summary-item">
                         <h4>🔤 基础概念</h4>
                         <p>字典定义、创建、访问和基本操作</p>
                     </div>
                     <div class="summary-item">
                         <h4>🛠️ 核心方法</h4>
                         <p>增删改查、遍历、推导式等核心技能</p>
                     </div>
                     <div class="summary-item">
                         <h4>📊 排序技巧</h4>
                         <p>按键、按值、多条件排序的各种方法</p>
                     </div>
                     <div class="summary-item">
                         <h4>🚀 高级应用</h4>
                         <p>嵌套字典、性能优化、实际项目应用</p>
                     </div>
                     <div class="summary-item">
                         <h4>🤖 AI辅助</h4>
                         <p>如何使用AI工具加速学习和开发</p>
                     </div>
                     <div class="summary-item">
                         <h4>💡 最佳实践</h4>
                         <p>代码规范、性能优化、错误处理</p>
                     </div>
                 </div>

                 <div class="next-steps">
                     <h3>🎯 下一步学习建议</h3>
                     <ul>
                         <li>深入学习其他Python数据结构（集合、元组）</li>
                         <li>探索数据库操作和ORM框架</li>
                         <li>学习算法和数据结构进阶知识</li>
                         <li>参与开源项目，实践所学技能</li>
                     </ul>
                 </div>
             </div>
         </div>

         <!-- 第30页：感谢页面 -->
         <div class="slide">
             <div class="slide-number">30/30</div>
             <div class="slide-content">
                 <div class="emoji">🙏</div>
                 <h1>感谢学习</h1>
                 
                 <div class="thank-you-content">
                     <h2>🎉 恭喜完成Python字典学习！</h2>
                     
                     <div class="achievement">
                         <h3>🏆 你已经掌握了：</h3>
                         <ul>
                             <li>✅ 字典的基础操作和高级技巧</li>
                             <li>✅ 各种排序和数据处理方法</li>
                             <li>✅ 实际项目中的应用技能</li>
                             <li>✅ AI辅助编程的学习方法</li>
                         </ul>
                     </div>

                     <div class="motivation">
                         <h3>💪 继续前进的动力</h3>
                         <p>编程是一个持续学习的过程，每一行代码都是向目标迈进的一步。</p>
                         <p>记住：<strong>实践是最好的老师，AI是最好的助手！</strong></p>
                     </div>

                     <div class="contact">
                         <h3>🤝 保持联系</h3>
                         <p>继续使用AI工具探索更多编程知识</p>
                         <p>在实践中不断提升，在分享中共同成长</p>
                     </div>

                     <div class="final-message">
                         <h2>🚀 愿你的编程之路越走越精彩！</h2>
                     </div>
                 </div>
             </div>
         </div>

         <div class="navigation">
             <button class="nav-btn" onclick="previousSlide()">⬅️ 上一页</button>
             <button class="nav-btn" onclick="nextSlide()">下一页 ➡️</button>
         </div>
     </div>

     <script>
         let currentSlide = 0;
         const slides = document.querySelectorAll('.slide');
         const totalSlides = slides.length;

         function showSlide(n) {
             slides[currentSlide].classList.remove('active');
             currentSlide = (n + totalSlides) % totalSlides;
             slides[currentSlide].classList.add('active');
             
             // 更新进度条
             const progress = ((currentSlide + 1) / totalSlides) * 100;
             document.getElementById('progressBar').style.width = progress + '%';
             
             // 添加动画效果
             slides[currentSlide].querySelector('.slide-content').classList.add('animate-in');
             setTimeout(() => {
                 slides[currentSlide].querySelector('.slide-content').classList.remove('animate-in');
             }, 800);
         }

         function nextSlide() {
             showSlide(currentSlide + 1);
         }

         function previousSlide() {
             showSlide(currentSlide - 1);
         }

         // 键盘导航
         document.addEventListener('keydown', function(e) {
             if (e.key === 'ArrowRight' || e.key === ' ') {
                 nextSlide();
             } else if (e.key === 'ArrowLeft') {
                 previousSlide();
             }
         });

         // 初始化
         showSlide(0);

         // 代码高亮动画
         function highlightCode() {
             const codeElements = document.querySelectorAll('.code');
             codeElements.forEach(code => {
                 const lines = code.innerHTML.split('\n');
                 code.innerHTML = '';
                 
                 lines.forEach((line, index) => {
                     setTimeout(() => {
                         const lineElement = document.createElement('div');
                         lineElement.innerHTML = line;
                         lineElement.style.opacity = '0';
                         lineElement.style.transform = 'translateX(-20px)';
                         code.appendChild(lineElement);
                         
                         setTimeout(() => {
                             lineElement.style.transition = 'all 0.3s ease';
                             lineElement.style.opacity = '1';
                             lineElement.style.transform = 'translateX(0)';
                         }, 50);
                     }, index * 100);
                 });
             });
         }

         // 页面加载完成后执行代码高亮
         document.addEventListener('DOMContentLoaded', function() {
             setTimeout(highlightCode, 1000);
         });

         // 滚动支持
         document.addEventListener('wheel', function(e) {
             const activeSlide = document.querySelector('.slide.active');
             const slideContent = activeSlide.querySelector('.slide-content');
             
             if (e.deltaY > 0) {
                 // 向下滚动
                 if (slideContent.scrollTop + slideContent.clientHeight >= slideContent.scrollHeight - 10) {
                     nextSlide();
                 } else {
                     slideContent.scrollTop += 50;
                 }
             } else {
                 // 向上滚动
                 if (slideContent.scrollTop <= 10) {
                     previousSlide();
                 } else {
                     slideContent.scrollTop -= 50;
                 }
             }
         });
     </script>
 </body>
 </html>