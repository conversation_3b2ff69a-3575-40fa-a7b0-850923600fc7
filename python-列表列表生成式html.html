<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python列表与列表生成式教学PPT</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
            height: 100vh;
        }

        .ppt-container {
            position: relative;
            width: 100%;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slide {
            position: absolute;
            width: 90%;
            max-width: 1200px;
            height: 85vh;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            display: none;
            opacity: 0;
            transform: translateX(100px);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .slide:first-child {
            display: block;
            opacity: 1;
            transform: translateX(0);
        }

        .slide.active {
            display: block !important;
            opacity: 1 !important;
            transform: translateX(0) !important;
        }

        .slide-content {
            padding: 40px;
            height: 100%;
            overflow-y: auto;
            text-align: left;
        }

        .slide-content::-webkit-scrollbar {
            width: 8px;
        }

        .slide-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .slide-content::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        .slide-content::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        h1 {
            font-size: 3em;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            animation: titleSlideIn 0.6s ease-out;
        }

        h2 {
            font-size: 2.2em;
            color: #34495e;
            margin-bottom: 25px;
            text-align: left;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            animation: fadeInUp 0.4s ease-out 0.1s both;
        }

        h3 {
            font-size: 1.6em;
            color: #2980b9;
            margin: 20px 0 15px 0;
            text-align: left;
            animation: fadeInUp 0.4s ease-out 0.15s both;
        }

        p, li {
            font-size: 1.2em;
            line-height: 1.8;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: left;
            animation: fadeInUp 0.4s ease-out 0.2s both;
        }

        ul {
            padding-left: 30px;
            margin-bottom: 20px;
        }

        li {
            margin-bottom: 10px;
        }

        .emoji {
            font-size: 1.5em;
            margin-right: 10px;
            animation: bounce 2s infinite;
        }

        .code-block {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 1.1em;
            line-height: 1.6;
            text-align: left;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow-x: auto;
            animation: codeSlideIn 0.3s ease-out 0.1s both;
        }

        .code-block::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 30px;
            background: #323233;
            border-radius: 12px 12px 0 0;
        }

        .code-block::after {
            content: '● ● ●';
            position: absolute;
            top: 8px;
            left: 15px;
            color: #ff6b6b;
            font-size: 12px;
        }

        .code-line {
            display: block;
            margin-top: 35px;
            animation: typewriter 0.05s ease-out forwards;
            opacity: 0;
        }

        .keyword { color: #569cd6; }
        .string { color: #ce9178; }
        .number { color: #b5cea8; }
        .comment { color: #6a9955; }
        .function { color: #dcdcaa; }
        .variable { color: #9cdcfe; }

        .highlight-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: left;
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
            animation: highlightPulse 2s ease-in-out infinite alternate;
        }

        .tips-box {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: left;
            border-left: 5px solid #0066cc;
            animation: fadeInLeft 0.5s ease-out 0.25s both;
        }

        .prompt-template {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            border-left: 4px solid #4299e1;
            font-family: 'Consolas', monospace;
            animation: fadeInUp 0.4s ease-out 0.3s both;
        }

        .ai-tool {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: left;
            animation: fadeInUp 0.4s ease-out 0.2s both;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            color: #2c3e50;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .page-indicator {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: rgba(52, 73, 94, 0.9);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 1001;
        }

        .slide-number {
            position: absolute;
            top: 20px;
            right: 30px;
            background: rgba(52, 73, 94, 0.8);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
        }

        @keyframes titleSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes codeSlideIn {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        @keyframes highlightPulse {
            from {
                box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
            }
            to {
                box-shadow: 0 12px 35px rgba(240, 147, 251, 0.6);
            }
        }

        @keyframes typewriter {
            to {
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="ppt-container">
        <!-- 第1页：封面 -->
        <div class="slide">
            <div class="slide-number">1/30</div>
            <div class="slide-content">
                <h1><span class="emoji">🐍</span>Python列表与列表生成式</h1>
                <div style="text-align: center; margin-top: 50px;">
                    <h2 style="border: none; text-align: center;">掌握Python最强大的数据结构</h2>
                    <p style="font-size: 1.4em; text-align: center; margin-top: 30px;">
                        <span class="emoji">📚</span>从基础到高级应用<br>
                        <span class="emoji">⚡</span>列表生成式的威力<br>
                        <span class="emoji">🤖</span>AI辅助学习技巧
                    </p>
                </div>
            </div>
        </div>

        <!-- 第2页：课程大纲 -->
        <div class="slide">
            <div class="slide-number">2/30</div>
            <div class="slide-content">
                <h2><span class="emoji">📋</span>课程大纲</h2>
                
                <div class="highlight-box">
                    <h3>🎯 学习目标</h3>
                    <ul>
                        <li>掌握Python列表的全部操作方法</li>
                        <li>熟练使用列表生成式简化代码</li>
                        <li>学会用AI工具辅助Python学习</li>
                        <li>培养Pythonic编程思维</li>
                    </ul>
                </div>

                <h3>📖 课程内容</h3>
                <ul>
                    <li><strong>第一部分</strong>：Python列表基础（10页）</li>
                    <li><strong>第二部分</strong>：列表生成式深入（10页）</li>
                    <li><strong>第三部分</strong>：AI辅助编程（6页）</li>
                    <li><strong>第四部分</strong>：实践与总结（2页）</li>
                </ul>

                <div class="tips-box">
                    <p><strong>💡 学习建议：</strong></p>
                    <ul>
                        <li>每页都有实用代码示例，建议动手练习</li>
                        <li>关注代码的性能优化技巧</li>
                        <li>尝试用AI工具来解答疑问</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第3页：列表基础概念 -->
        <div class="slide">
            <div class="slide-number">3/30</div>
            <div class="slide-content">
                <h2><span class="emoji">📦</span>列表基础概念和创建</h2>
                
                <h3>什么是列表？</h3>
                <p>列表是Python中最常用的数据结构，用于存储多个有序的元素。</p>

                <h3>创建列表的多种方式</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 1. 直接创建</span></span>
                    <span class="code-line"><span class="variable">numbers</span> = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>]</span>
                    <span class="code-line"><span class="variable">fruits</span> = [<span class="string">"苹果"</span>, <span class="string">"香蕉"</span>, <span class="string">"橙子"</span>]</span>
                    <span class="code-line"><span class="variable">mixed</span> = [<span class="number">1</span>, <span class="string">"hello"</span>, <span class="number">3.14</span>, <span class="keyword">True</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 2. 使用list()函数</span></span>
                    <span class="code-line"><span class="variable">empty_list</span> = <span class="function">list</span>()</span>
                    <span class="code-line"><span class="variable">string_list</span> = <span class="function">list</span>(<span class="string">"hello"</span>)  <span class="comment"># ['h', 'e', 'l', 'l', 'o']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 3. 使用range()创建</span></span>
                    <span class="code-line"><span class="variable">range_list</span> = <span class="function">list</span>(<span class="function">range</span>(<span class="number">1</span>, <span class="number">11</span>))  <span class="comment"># [1, 2, 3, ..., 10]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 4. 重复创建</span></span>
                    <span class="code-line"><span class="variable">zeros</span> = [<span class="number">0</span>] * <span class="number">5</span>           <span class="comment"># [0, 0, 0, 0, 0]</span></span>
                </div>

                <div class="highlight-box">
                    <p><strong>🔑 核心特性：</strong></p>
                    <ul>
                        <li><strong>可变性</strong>：创建后可以修改内容</li>
                        <li><strong>有序性</strong>：元素有固定的顺序</li>
                        <li><strong>异构性</strong>：可以存储不同类型的数据</li>
                        <li><strong>索引性</strong>：支持索引和切片操作</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第4页：列表索引和切片 -->
        <div class="slide">
            <div class="slide-number">4/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🔢</span>列表索引和切片操作</h2>
                
                <h3>索引访问</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">languages</span> = [<span class="string">"Python"</span>, <span class="string">"Java"</span>, <span class="string">"C++"</span>, <span class="string">"JavaScript"</span>, <span class="string">"Go"</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 正向索引（从0开始）</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">languages</span>[<span class="number">0</span>])   <span class="comment"># Python</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">languages</span>[<span class="number">1</span>])   <span class="comment"># Java</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 反向索引（从-1开始）</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">languages</span>[-<span class="number">1</span>])  <span class="comment"># Go</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">languages</span>[-<span class="number">2</span>])  <span class="comment"># JavaScript</span></span>
                </div>

                <h3>切片操作</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 基础切片语法：list[start:end:step]</span></span>
                    <span class="code-line"><span class="variable">numbers</span> = [<span class="number">0</span>, <span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>, <span class="number">7</span>, <span class="number">8</span>, <span class="number">9</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">numbers</span>[<span class="number">2</span>:<span class="number">5</span>])     <span class="comment"># [2, 3, 4]</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">numbers</span>[:<span class="number">3</span>])      <span class="comment"># [0, 1, 2] 从开头开始</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">numbers</span>[<span class="number">7</span>:])      <span class="comment"># [7, 8, 9] 到结尾</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">numbers</span>[:])       <span class="comment"># [0, 1, 2, ..., 9] 完整拷贝</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 步长切片</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">numbers</span>[::2])     <span class="comment"># [0, 2, 4, 6, 8] 每隔一个</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">numbers</span>[::-1])    <span class="comment"># [9, 8, 7, ..., 0] 反转</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">numbers</span>[<span class="number">1</span>::<span class="number">2</span>])   <span class="comment"># [1, 3, 5, 7, 9] 从索引1开始每隔一个</span></span>
                </div>

                <div class="tips-box">
                    <p><strong>💡 切片技巧：</strong></p>
                    <ul>
                        <li>切片不会改变原列表，返回新列表</li>
                        <li>索引超出范围会报错，但切片不会</li>
                        <li>负数步长可以实现反向切片</li>
                        <li><code>list[:]</code> 是创建列表副本的快捷方式</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第5页：列表添加和插入 -->
        <div class="slide">
            <div class="slide-number">5/30</div>
            <div class="slide-content">
                <h2><span class="emoji">➕</span>列表添加和插入元素</h2>
                
                <h3>append() - 在末尾添加单个元素</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">shopping_list</span> = [<span class="string">"牛奶"</span>, <span class="string">"面包"</span>]</span>
                    <span class="code-line"><span class="variable">shopping_list</span>.<span class="function">append</span>(<span class="string">"鸡蛋"</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">shopping_list</span>)  <span class="comment"># ['牛奶', '面包', '鸡蛋']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 注意：append只能添加一个元素</span></span>
                    <span class="code-line"><span class="variable">shopping_list</span>.<span class="function">append</span>([<span class="string">"苹果"</span>, <span class="string">"香蕉"</span>])  <span class="comment"># 整个列表作为一个元素</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">shopping_list</span>)  <span class="comment"># ['牛奶', '面包', '鸡蛋', ['苹果', '香蕉']]</span></span>
                </div>

                <h3>extend() - 添加多个元素</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">numbers</span> = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>]</span>
                    <span class="code-line"><span class="variable">numbers</span>.<span class="function">extend</span>([<span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>])</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">numbers</span>)  <span class="comment"># [1, 2, 3, 4, 5, 6]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># extend可以接受任何可迭代对象</span></span>
                    <span class="code-line"><span class="variable">numbers</span>.<span class="function">extend</span>(<span class="string">"abc"</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">numbers</span>)  <span class="comment"># [1, 2, 3, 4, 5, 6, 'a', 'b', 'c']</span></span>
                </div>

                <h3>insert() - 在指定位置插入</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">colors</span> = [<span class="string">"红"</span>, <span class="string">"绿"</span>, <span class="string">"蓝"</span>]</span>
                    <span class="code-line"><span class="variable">colors</span>.<span class="function">insert</span>(<span class="number">1</span>, <span class="string">"黄"</span>)  <span class="comment"># 在索引1处插入</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">colors</span>)  <span class="comment"># ['红', '黄', '绿', '蓝']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 在开头插入</span></span>
                    <span class="code-line"><span class="variable">colors</span>.<span class="function">insert</span>(<span class="number">0</span>, <span class="string">"白"</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">colors</span>)  <span class="comment"># ['白', '红', '黄', '绿', '蓝']</span></span>
                </div>

                <h3>+ 运算符 - 连接列表</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">list1</span> = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>]</span>
                    <span class="code-line"><span class="variable">list2</span> = [<span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>]</span>
                    <span class="code-line"><span class="variable">combined</span> = <span class="variable">list1</span> + <span class="variable">list2</span>  <span class="comment"># 创建新列表</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">combined</span>)  <span class="comment"># [1, 2, 3, 4, 5, 6]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># += 运算符（相当于extend）</span></span>
                    <span class="code-line"><span class="variable">list1</span> += <span class="variable">list2</span>  <span class="comment"># 修改原列表</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">list1</span>)  <span class="comment"># [1, 2, 3, 4, 5, 6]</span></span>
                </div>

                <div class="highlight-box">
                    <p><strong>⚡ 性能对比：</strong></p>
                    <ul>
                        <li><code>append()</code>：O(1) - 最快</li>
                        <li><code>extend()</code>：O(k) - k为添加元素数量</li>
                        <li><code>insert()</code>：O(n) - 需要移动后续元素</li>
                        <li><code>+</code>：O(n) - 创建新列表</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第6页：列表删除和修改 -->
        <div class="slide">
            <div class="slide-number">6/30</div>
            <div class="slide-content">
                <h2><span class="emoji">❌</span>列表删除和修改元素</h2>
                
                <h3>remove() - 按值删除</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">fruits</span> = [<span class="string">"苹果"</span>, <span class="string">"香蕉"</span>, <span class="string">"橙子"</span>, <span class="string">"香蕉"</span>]</span>
                    <span class="code-line"><span class="variable">fruits</span>.<span class="function">remove</span>(<span class="string">"香蕉"</span>)  <span class="comment"># 只删除第一个匹配的</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">fruits</span>)  <span class="comment"># ['苹果', '橙子', '香蕉']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 如果元素不存在会报错，安全删除：</span></span>
                    <span class="code-line"><span class="keyword">if</span> <span class="string">"葡萄"</span> <span class="keyword">in</span> <span class="variable">fruits</span>:</span>
                    <span class="code-line">    <span class="variable">fruits</span>.<span class="function">remove</span>(<span class="string">"葡萄"</span>)</span>
                </div>

                <h3>pop() - 按索引删除并返回</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">numbers</span> = [<span class="number">10</span>, <span class="number">20</span>, <span class="number">30</span>, <span class="number">40</span>, <span class="number">50</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 删除最后一个元素</span></span>
                    <span class="code-line"><span class="variable">last</span> = <span class="variable">numbers</span>.<span class="function">pop</span>()</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">last</span>)     <span class="comment"># 50</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">numbers</span>)  <span class="comment"># [10, 20, 30, 40]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 删除指定索引的元素</span></span>
                    <span class="code-line"><span class="variable">second</span> = <span class="variable">numbers</span>.<span class="function">pop</span>(<span class="number">1</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">second</span>)   <span class="comment"># 20</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">numbers</span>)  <span class="comment"># [10, 30, 40]</span></span>
                </div>

                <h3>del语句 - 删除元素或切片</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">data</span> = [<span class="string">"a"</span>, <span class="string">"b"</span>, <span class="string">"c"</span>, <span class="string">"d"</span>, <span class="string">"e"</span>, <span class="string">"f"</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 删除单个元素</span></span>
                    <span class="code-line"><span class="keyword">del</span> <span class="variable">data</span>[<span class="number">2</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">data</span>)  <span class="comment"># ['a', 'b', 'd', 'e', 'f']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 删除切片</span></span>
                    <span class="code-line"><span class="keyword">del</span> <span class="variable">data</span>[<span class="number">1</span>:<span class="number">3</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">data</span>)  <span class="comment"># ['a', 'e', 'f']</span></span>
                </div>

                <h3>clear() - 清空列表</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">items</span> = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>]</span>
                    <span class="code-line"><span class="variable">items</span>.<span class="function">clear</span>()</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">items</span>)  <span class="comment"># []</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 等价于：del items[:]</span></span>
                </div>

                <h3>修改元素</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">scores</span> = [<span class="number">85</span>, <span class="number">92</span>, <span class="number">78</span>, <span class="number">96</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 修改单个元素</span></span>
                    <span class="code-line"><span class="variable">scores</span>[<span class="number">2</span>] = <span class="number">88</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">scores</span>)  <span class="comment"># [85, 92, 88, 96]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 修改切片</span></span>
                    <span class="code-line"><span class="variable">scores</span>[<span class="number">1</span>:<span class="number">3</span>] = [<span class="number">90</span>, <span class="number">85</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">scores</span>)  <span class="comment"># [85, 90, 85, 96]</span></span>
                </div>

                <div class="tips-box">
                    <p><strong>🎯 选择合适的删除方法：</strong></p>
                    <ul>
                        <li><strong>知道值</strong>：用 <code>remove()</code></li>
                        <li><strong>知道索引</strong>：用 <code>pop()</code> 或 <code>del</code></li>
                        <li><strong>需要返回值</strong>：用 <code>pop()</code></li>
                        <li><strong>删除多个</strong>：用 <code>del</code> 切片</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第7页：列表查找和统计 -->
        <div class="slide">
            <div class="slide-number">7/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🔍</span>列表查找和统计方法</h2>
                
                <h3>in 和 not in - 成员检查</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">programming_languages</span> = [<span class="string">"Python"</span>, <span class="string">"Java"</span>, <span class="string">"C++"</span>, <span class="string">"JavaScript"</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">"Python"</span> <span class="keyword">in</span> <span class="variable">programming_languages</span>)     <span class="comment"># True</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">"Go"</span> <span class="keyword">in</span> <span class="variable">programming_languages</span>)         <span class="comment"># False</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">"Ruby"</span> <span class="keyword">not in</span> <span class="variable">programming_languages</span>)  <span class="comment"># True</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 实际应用</span></span>
                    <span class="code-line"><span class="keyword">if</span> <span class="string">"Python"</span> <span class="keyword">in</span> <span class="variable">programming_languages</span>:</span>
                    <span class="code-line">    <span class="function">print</span>(<span class="string">"Python是一门很棒的语言！"</span>)</span>
                </div>

                <h3>index() - 查找元素索引</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">colors</span> = [<span class="string">"红"</span>, <span class="string">"绿"</span>, <span class="string">"蓝"</span>, <span class="string">"黄"</span>, <span class="string">"绿"</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 找到第一个匹配元素的索引</span></span>
                    <span class="code-line"><span class="variable">green_index</span> = <span class="variable">colors</span>.<span class="function">index</span>(<span class="string">"绿"</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">green_index</span>)  <span class="comment"># 1</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 在指定范围内查找</span></span>
                    <span class="code-line"><span class="variable">second_green</span> = <span class="variable">colors</span>.<span class="function">index</span>(<span class="string">"绿"</span>, <span class="number">2</span>)  <span class="comment"># 从索引2开始查找</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">second_green</span>)  <span class="comment"># 4</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 安全查找（避免ValueError）</span></span>
                    <span class="code-line"><span class="keyword">try</span>:</span>
                    <span class="code-line">    <span class="variable">index</span> = <span class="variable">colors</span>.<span class="function">index</span>(<span class="string">"紫"</span>)</span>
                    <span class="code-line"><span class="keyword">except</span> <span class="variable">ValueError</span>:</span>
                    <span class="code-line">    <span class="function">print</span>(<span class="string">"未找到紫色"</span>)</span>
                </div>

                <h3>count() - 统计元素出现次数</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">grades</span> = [<span class="string">"A"</span>, <span class="string">"B"</span>, <span class="string">"A"</span>, <span class="string">"C"</span>, <span class="string">"B"</span>, <span class="string">"A"</span>, <span class="string">"A"</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="variable">a_count</span> = <span class="variable">grades</span>.<span class="function">count</span>(<span class="string">"A"</span>)</span>
                    <span class="code-line"><span class="variable">b_count</span> = <span class="variable">grades</span>.<span class="function">count</span>(<span class="string">"B"</span>)</span>
                    <span class="code-line"><span class="variable">c_count</span> = <span class="variable">grades</span>.<span class="function">count</span>(<span class="string">"C"</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">f"A级: {a_count}个, B级: {b_count}个, C级: {c_count}个"</span>)</span>
                    <span class="code-line"><span class="comment"># A级: 4个, B级: 2个, C级: 1个</span></span>
                </div>

                <h3>len() - 获取列表长度</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">shopping_cart</span> = [<span class="string">"笔记本"</span>, <span class="string">"鼠标"</span>, <span class="string">"键盘"</span>]</span>
                    <span class="code-line"><span class="variable">item_count</span> = <span class="function">len</span>(<span class="variable">shopping_cart</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">f"购物车中有 {item_count} 件商品"</span>)  <span class="comment"># 购物车中有 3 件商品</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 检查列表是否为空</span></span>
                    <span class="code-line"><span class="keyword">if</span> <span class="function">len</span>(<span class="variable">shopping_cart</span>) > <span class="number">0</span>:</span>
                    <span class="code-line">    <span class="function">print</span>(<span class="string">"购物车不为空"</span>)</span>
                    <span class="code-line"><span class="comment"># 更Pythonic的写法：</span></span>
                    <span class="code-line"><span class="keyword">if</span> <span class="variable">shopping_cart</span>:  <span class="comment"># 空列表为False</span></span>
                    <span class="code-line">    <span class="function">print</span>(<span class="string">"购物车不为空"</span>)</span>
                </div>

                <h3>实用的查找函数</h3>
                <div class="code-block">
                    <span class="code-line"><span class="keyword">def</span> <span class="function">find_all_indices</span>(<span class="variable">lst</span>, <span class="variable">value</span>):</span>
                    <span class="code-line">    <span class="string">"""查找所有匹配元素的索引"""</span></span>
                    <span class="code-line">    <span class="keyword">return</span> [<span class="variable">i</span> <span class="keyword">for</span> <span class="variable">i</span>, <span class="variable">x</span> <span class="keyword">in</span> <span class="function">enumerate</span>(<span class="variable">lst</span>) <span class="keyword">if</span> <span class="variable">x</span> == <span class="variable">value</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="variable">numbers</span> = [<span class="number">1</span>, <span class="number">3</span>, <span class="number">5</span>, <span class="number">3</span>, <span class="number">7</span>, <span class="number">3</span>, <span class="number">9</span>]</span>
                    <span class="code-line"><span class="variable">indices</span> = <span class="function">find_all_indices</span>(<span class="variable">numbers</span>, <span class="number">3</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">indices</span>)  <span class="comment"># [1, 3, 5]</span></span>
                </div>

                <div class="highlight-box">
                    <p><strong>🚀 性能提示：</strong></p>
                    <ul>
                        <li><code>in</code> 操作：O(n) - 需要遍历列表</li>
                        <li><code>index()</code>：O(n) - 最坏情况遍历整个列表</li>
                        <li><code>count()</code>：O(n) - 总是遍历整个列表</li>
                        <li>频繁查找建议使用集合(set)或字典(dict)</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第8页：列表排序和反转 -->
        <div class="slide">
            <div class="slide-number">8/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🔄</span>列表排序和反转操作</h2>
                
                <h3>sort() - 原地排序</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 升序排序</span></span>
                    <span class="code-line"><span class="variable">numbers</span> = [<span class="number">64</span>, <span class="number">34</span>, <span class="number">25</span>, <span class="number">12</span>, <span class="number">22</span>, <span class="number">11</span>, <span class="number">90</span>]</span>
                    <span class="code-line"><span class="variable">numbers</span>.<span class="function">sort</span>()</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">numbers</span>)  <span class="comment"># [11, 12, 22, 25, 34, 64, 90]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 降序排序</span></span>
                    <span class="code-line"><span class="variable">scores</span> = [<span class="number">85</span>, <span class="number">92</span>, <span class="number">78</span>, <span class="number">96</span>, <span class="number">83</span>]</span>
                    <span class="code-line"><span class="variable">scores</span>.<span class="function">sort</span>(<span class="variable">reverse</span>=<span class="keyword">True</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">scores</span>)  <span class="comment"># [96, 92, 85, 83, 78]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 字符串排序</span></span>
                    <span class="code-line"><span class="variable">names</span> = [<span class="string">"张三"</span>, <span class="string">"李四"</span>, <span class="string">"王五"</span>, <span class="string">"赵六"</span>]</span>
                    <span class="code-line"><span class="variable">names</span>.<span class="function">sort</span>()</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">names</span>)  <span class="comment"># ['张三', '李四', '王五', '赵六']</span></span>
                </div>

                <h3>sorted() - 返回新的排序列表</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">original</span> = [<span class="number">3</span>, <span class="number">1</span>, <span class="number">4</span>, <span class="number">1</span>, <span class="number">5</span>, <span class="number">9</span>]</span>
                    <span class="code-line"><span class="variable">sorted_list</span> = <span class="function">sorted</span>(<span class="variable">original</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">f"原列表: {original}"</span>)     <span class="comment"># [3, 1, 4, 1, 5, 9]</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">f"排序后: {sorted_list}"</span>)  <span class="comment"># [1, 1, 3, 4, 5, 9]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 降序</span></span>
                    <span class="code-line"><span class="variable">desc_list</span> = <span class="function">sorted</span>(<span class="variable">original</span>, <span class="variable">reverse</span>=<span class="keyword">True</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">desc_list</span>)  <span class="comment"># [9, 5, 4, 3, 1, 1]</span></span>
                </div>

                <h3>自定义排序规则</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 按字符串长度排序</span></span>
                    <span class="code-line"><span class="variable">words</span> = [<span class="string">"python"</span>, <span class="string">"java"</span>, <span class="string">"c"</span>, <span class="string">"javascript"</span>, <span class="string">"go"</span>]</span>
                    <span class="code-line"><span class="variable">words</span>.<span class="function">sort</span>(<span class="variable">key</span>=<span class="function">len</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">words</span>)  <span class="comment"># ['c', 'go', 'java', 'python', 'javascript']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 按绝对值排序</span></span>
                    <span class="code-line"><span class="variable">numbers</span> = [-<span class="number">5</span>, <span class="number">2</span>, -<span class="number">8</span>, <span class="number">1</span>, <span class="number">7</span>, -<span class="number">3</span>]</span>
                    <span class="code-line"><span class="variable">numbers</span>.<span class="function">sort</span>(<span class="variable">key</span>=<span class="function">abs</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">numbers</span>)  <span class="comment"># [1, 2, -3, -5, 7, -8]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 复杂对象排序</span></span>
                    <span class="code-line"><span class="variable">students</span> = [</span>
                    <span class="code-line">    {<span class="string">"name"</span>: <span class="string">"张三"</span>, <span class="string">"age"</span>: <span class="number">20</span>, <span class="string">"score"</span>: <span class="number">85</span>},</span>
                    <span class="code-line">    {<span class="string">"name"</span>: <span class="string">"李四"</span>, <span class="string">"age"</span>: <span class="number">19</span>, <span class="string">"score"</span>: <span class="number">92</span>},</span>
                    <span class="code-line">    {<span class="string">"name"</span>: <span class="string">"王五"</span>, <span class="string">"age"</span>: <span class="number">21</span>, <span class="string">"score"</span>: <span class="number">78</span>}</span>
                    <span class="code-line">]</span>
                    <span class="code-line"><span class="comment"># 按分数降序排序</span></span>
                    <span class="code-line"><span class="variable">students</span>.<span class="function">sort</span>(<span class="variable">key</span>=<span class="keyword">lambda</span> <span class="variable">x</span>: <span class="variable">x</span>[<span class="string">"score"</span>], <span class="variable">reverse</span>=<span class="keyword">True</span>)</span>
                </div>

                <h3>reverse() - 反转列表</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">sequence</span> = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>]</span>
                    <span class="code-line"><span class="variable">sequence</span>.<span class="function">reverse</span>()</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">sequence</span>)  <span class="comment"># [5, 4, 3, 2, 1]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 使用切片反转（不修改原列表）</span></span>
                    <span class="code-line"><span class="variable">original</span> = [<span class="string">"a"</span>, <span class="string">"b"</span>, <span class="string">"c"</span>, <span class="string">"d"</span>]</span>
                    <span class="code-line"><span class="variable">reversed_copy</span> = <span class="variable">original</span>[::-<span class="number">1</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">f"原列表: {original}"</span>)        <span class="comment"># ['a', 'b', 'c', 'd']</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">f"反转副本: {reversed_copy}"</span>) <span class="comment"># ['d', 'c', 'b', 'a']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 使用reversed()函数</span></span>
                    <span class="code-line"><span class="variable">reversed_list</span> = <span class="function">list</span>(<span class="function">reversed</span>(<span class="variable">original</span>))</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">reversed_list</span>)  <span class="comment"># ['d', 'c', 'b', 'a']</span></span>
                </div>

                <div class="tips-box">
                    <p><strong>📊 排序方法选择：</strong></p>
                    <ul>
                        <li><strong>修改原列表</strong>：用 <code>sort()</code> 和 <code>reverse()</code></li>
                        <li><strong>保留原列表</strong>：用 <code>sorted()</code> 和切片 <code>[::-1]</code></li>
                        <li><strong>复杂排序</strong>：使用 <code>key</code> 参数</li>
                        <li><strong>多条件排序</strong>：使用 <code>operator.itemgetter()</code></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第9页：嵌套列表和多维数据 -->
        <div class="slide">
            <div class="slide-number">9/30</div>
            <div class="slide-content">
                <h2><span class="emoji">📚</span>嵌套列表和多维数据</h2>
                
                <h3>创建二维列表</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 手动创建二维列表</span></span>
                    <span class="code-line"><span class="variable">matrix</span> = [</span>
                    <span class="code-line">    [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>],</span>
                    <span class="code-line">    [<span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>],</span>
                    <span class="code-line">    [<span class="number">7</span>, <span class="number">8</span>, <span class="number">9</span>]</span>
                    <span class="code-line">]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 使用列表生成式创建</span></span>
                    <span class="code-line"><span class="variable">zeros_matrix</span> = [[<span class="number">0</span> <span class="keyword">for</span> <span class="variable">_</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">3</span>)] <span class="keyword">for</span> <span class="variable">_</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">3</span>)]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">zeros_matrix</span>)  <span class="comment"># [[0, 0, 0], [0, 0, 0], [0, 0, 0]]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># ⚠️ 错误的创建方式（浅拷贝问题）</span></span>
                    <span class="code-line"><span class="variable">wrong_matrix</span> = [[<span class="number">0</span>] * <span class="number">3</span>] * <span class="number">3</span>  <span class="comment"># 所有行都是同一个对象！</span></span>
                </div>

                <h3>访问和修改二维列表</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">grades</span> = [</span>
                    <span class="code-line">    [<span class="number">85</span>, <span class="number">92</span>, <span class="number">78</span>],  <span class="comment"># 张三的成绩</span></span>
                    <span class="code-line">    [<span class="number">91</span>, <span class="number">87</span>, <span class="number">94</span>],  <span class="comment"># 李四的成绩</span></span>
                    <span class="code-line">    [<span class="number">76</span>, <span class="number">89</span>, <span class="number">82</span>]   <span class="comment"># 王五的成绩</span></span>
                    <span class="code-line">]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 访问元素</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">grades</span>[<span class="number">0</span>][<span class="number">1</span>])     <span class="comment"># 92 (张三第二科成绩)</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">grades</span>[<span class="number">1</span>])        <span class="comment"># [91, 87, 94] (李四所有成绩)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 修改元素</span></span>
                    <span class="code-line"><span class="variable">grades</span>[<span class="number">2</span>][<span class="number">0</span>] = <span class="number">80</span>  <span class="comment"># 修改王五第一科成绩</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 添加新学生</span></span>
                    <span class="code-line"><span class="variable">grades</span>.<span class="function">append</span>([<span class="number">88</span>, <span class="number">95</span>, <span class="number">91</span>])  <span class="comment"># 赵六的成绩</span></span>
                </div>

                <h3>遍历二维列表</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">classroom</span> = [</span>
                    <span class="code-line">    [<span class="string">"张三"</span>, <span class="string">"李四"</span>, <span class="string">"王五"</span>],</span>
                    <span class="code-line">    [<span class="string">"赵六"</span>, <span class="string">"钱七"</span>, <span class="string">"孙八"</span>],</span>
                    <span class="code-line">    [<span class="string">"周九"</span>, <span class="string">"吴十"</span>, <span class="string">"郑十一"</span>]</span>
                    <span class="code-line">]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 方法1：使用索引</span></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="function">len</span>(<span class="variable">classroom</span>)):</span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">j</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="function">len</span>(<span class="variable">classroom</span>[<span class="variable">i</span>])):</span>
                    <span class="code-line">        <span class="function">print</span>(<span class="string">f"第{i+1}排第{j+1}座: {classroom[i][j]}"</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 方法2：直接遍历（推荐）</span></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">row_index</span>, <span class="variable">row</span> <span class="keyword">in</span> <span class="function">enumerate</span>(<span class="variable">classroom</span>):</span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">col_index</span>, <span class="variable">student</span> <span class="keyword">in</span> <span class="function">enumerate</span>(<span class="variable">row</span>):</span>
                    <span class="code-line">        <span class="function">print</span>(<span class="string">f"({row_index}, {col_index}): {student}"</span>)</span>
                </div>

                <h3>实用的二维列表操作</h3>
                <div class="code-block">
                    <span class="code-line"><span class="keyword">def</span> <span class="function">print_matrix</span>(<span class="variable">matrix</span>):</span>
                    <span class="code-line">    <span class="string">"""美观地打印矩阵"""</span></span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">row</span> <span class="keyword">in</span> <span class="variable">matrix</span>:</span>
                    <span class="code-line">        <span class="function">print</span>(<span class="string">" "</span>.<span class="function">join</span>(<span class="function">f"{item:4}"</span> <span class="keyword">for</span> <span class="variable">item</span> <span class="keyword">in</span> <span class="variable">row</span>))</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="keyword">def</span> <span class="function">transpose</span>(<span class="variable">matrix</span>):</span>
                    <span class="code-line">    <span class="string">"""矩阵转置"""</span></span>
                    <span class="code-line">    <span class="keyword">return</span> [[<span class="variable">matrix</span>[<span class="variable">j</span>][<span class="variable">i</span>] <span class="keyword">for</span> <span class="variable">j</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="function">len</span>(<span class="variable">matrix</span>))]</span>
                    <span class="code-line">            <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="function">len</span>(<span class="variable">matrix</span>[<span class="number">0</span>]))]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="keyword">def</span> <span class="function">flatten</span>(<span class="variable">nested_list</span>):</span>
                    <span class="code-line">    <span class="string">"""扁平化嵌套列表"""</span></span>
                    <span class="code-line">    <span class="variable">result</span> = []</span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">item</span> <span class="keyword">in</span> <span class="variable">nested_list</span>:</span>
                    <span class="code-line">        <span class="keyword">if</span> <span class="function">isinstance</span>(<span class="variable">item</span>, <span class="variable">list</span>):</span>
                    <span class="code-line">            <span class="variable">result</span>.<span class="function">extend</span>(<span class="function">flatten</span>(<span class="variable">item</span>))</span>
                    <span class="code-line">        <span class="keyword">else</span>:</span>
                    <span class="code-line">            <span class="variable">result</span>.<span class="function">append</span>(<span class="variable">item</span>)</span>
                    <span class="code-line">    <span class="keyword">return</span> <span class="variable">result</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 示例使用</span></span>
                    <span class="code-line"><span class="variable">nested</span> = [[<span class="number">1</span>, <span class="number">2</span>], [<span class="number">3</span>, [<span class="number">4</span>, <span class="number">5</span>]], <span class="number">6</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">flatten</span>(<span class="variable">nested</span>))  <span class="comment"># [1, 2, 3, 4, 5, 6]</span></span>
                </div>

                <div class="highlight-box">
                    <p><strong>🎯 多维数据最佳实践：</strong></p>
                    <ul>
                        <li>避免使用 <code>[[0] * n] * m</code> 创建二维列表</li>
                        <li>使用 <code>enumerate()</code> 同时获取索引和值</li>
                        <li>对于大型矩阵运算，考虑使用 NumPy</li>
                        <li>深层嵌套时注意性能和可读性</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第10页：列表常用内置函数 -->
        <div class="slide">
            <div class="slide-number">10/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🧮</span>列表常用内置函数</h2>
                
                <h3>数值运算函数</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">scores</span> = [<span class="number">85</span>, <span class="number">92</span>, <span class="number">78</span>, <span class="number">96</span>, <span class="number">83</span>, <span class="number">90</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 基本统计函数</span></span>
                    <span class="code-line"><span class="variable">total</span> = <span class="function">sum</span>(<span class="variable">scores</span>)          <span class="comment"># 524 (总和)</span></span>
                    <span class="code-line"><span class="variable">maximum</span> = <span class="function">max</span>(<span class="variable">scores</span>)       <span class="comment"># 96 (最大值)</span></span>
                    <span class="code-line"><span class="variable">minimum</span> = <span class="function">min</span>(<span class="variable">scores</span>)       <span class="comment"># 78 (最小值)</span></span>
                    <span class="code-line"><span class="variable">count</span> = <span class="function">len</span>(<span class="variable">scores</span>)         <span class="comment"># 6 (元素个数)</span></span>
                    <span class="code-line"><span class="variable">average</span> = <span class="variable">total</span> / <span class="variable">count</span>   <span class="comment"># 87.33 (平均值)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">f"总分: {total}, 平均分: {average:.2f}"</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">f"最高分: {maximum}, 最低分: {minimum}"</span>)</span>
                </div>

                <h3>all() 和 any() - 逻辑判断</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># all() - 所有元素都为True才返回True</span></span>
                    <span class="code-line"><span class="variable">all_passed</span> = [<span class="number">85</span>, <span class="number">92</span>, <span class="number">78</span>, <span class="number">96</span>]</span>
                    <span class="code-line"><span class="variable">some_failed</span> = [<span class="number">85</span>, <span class="number">45</span>, <span class="number">78</span>, <span class="number">96</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">all</span>(<span class="variable">score</span> >= <span class="number">60</span> <span class="keyword">for</span> <span class="variable">score</span> <span class="keyword">in</span> <span class="variable">all_passed</span>))   <span class="comment"># True</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">all</span>(<span class="variable">score</span> >= <span class="number">60</span> <span class="keyword">for</span> <span class="variable">score</span> <span class="keyword">in</span> <span class="variable">some_failed</span>))  <span class="comment"># False</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># any() - 任何一个元素为True就返回True</span></span>
                    <span class="code-line"><span class="variable">grades</span> = [<span class="number">45</span>, <span class="number">52</span>, <span class="number">38</span>, <span class="number">96</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">any</span>(<span class="variable">grade</span> >= <span class="number">90</span> <span class="keyword">for</span> <span class="variable">grade</span> <span class="keyword">in</span> <span class="variable">grades</span>))  <span class="comment"># True (有96分)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 实际应用</span></span>
                    <span class="code-line"><span class="variable">permissions</span> = [<span class="keyword">True</span>, <span class="keyword">False</span>, <span class="keyword">True</span>, <span class="keyword">True</span>]</span>
                    <span class="code-line"><span class="keyword">if</span> <span class="function">all</span>(<span class="variable">permissions</span>):</span>
                    <span class="code-line">    <span class="function">print</span>(<span class="string">"所有权限都开启"</span>)</span>
                    <span class="code-line"><span class="keyword">elif</span> <span class="function">any</span>(<span class="variable">permissions</span>):</span>
                    <span class="code-line">    <span class="function">print</span>(<span class="string">"部分权限开启"</span>)</span>
                </div>

                <h3>enumerate() - 同时获取索引和值</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">fruits</span> = [<span class="string">"苹果"</span>, <span class="string">"香蕉"</span>, <span class="string">"橙子"</span>, <span class="string">"葡萄"</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 基础用法</span></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">index</span>, <span class="variable">fruit</span> <span class="keyword">in</span> <span class="function">enumerate</span>(<span class="variable">fruits</span>):</span>
                    <span class="code-line">    <span class="function">print</span>(<span class="string">f"{index + 1}. {fruit}"</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 指定起始值</span></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">index</span>, <span class="variable">fruit</span> <span class="keyword">in</span> <span class="function">enumerate</span>(<span class="variable">fruits</span>, <span class="variable">start</span>=<span class="number">1</span>):</span>
                    <span class="code-line">    <span class="function">print</span>(<span class="string">f"{index}. {fruit}"</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 转换为字典</span></span>
                    <span class="code-line"><span class="variable">fruit_dict</span> = <span class="function">dict</span>(<span class="function">enumerate</span>(<span class="variable">fruits</span>))</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">fruit_dict</span>)  <span class="comment"># {0: '苹果', 1: '香蕉', 2: '橙子', 3: '葡萄'}</span></span>
                </div>

                <h3>zip() - 并行遍历多个列表</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">names</span> = [<span class="string">"张三"</span>, <span class="string">"李四"</span>, <span class="string">"王五"</span>]</span>
                    <span class="code-line"><span class="variable">ages</span> = [<span class="number">25</span>, <span class="number">30</span>, <span class="number">35</span>]</span>
                    <span class="code-line"><span class="variable">cities</span> = [<span class="string">"北京"</span>, <span class="string">"上海"</span>, <span class="string">"广州"</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 并行遍历</span></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">name</span>, <span class="variable">age</span>, <span class="variable">city</span> <span class="keyword">in</span> <span class="function">zip</span>(<span class="variable">names</span>, <span class="variable">ages</span>, <span class="variable">cities</span>):</span>
                    <span class="code-line">    <span class="function">print</span>(<span class="string">f"{name}，{age}岁，来自{city}"</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 创建字典</span></span>
                    <span class="code-line"><span class="variable">person_info</span> = <span class="function">dict</span>(<span class="function">zip</span>(<span class="variable">names</span>, <span class="variable">ages</span>))</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">person_info</span>)  <span class="comment"># {'张三': 25, '李四': 30, '王五': 35}</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 转置操作</span></span>
                    <span class="code-line"><span class="variable">matrix</span> = [[<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>], [<span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>]]</span>
                    <span class="code-line"><span class="variable">transposed</span> = <span class="function">list</span>(<span class="function">zip</span>(*<span class="variable">matrix</span>))</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">transposed</span>)  <span class="comment"># [(1, 4), (2, 5), (3, 6)]</span></span>
                </div>

                <h3>filter() 和 map() - 函数式编程</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">numbers</span> = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>, <span class="number">7</span>, <span class="number">8</span>, <span class="number">9</span>, <span class="number">10</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># filter() - 过滤元素</span></span>
                    <span class="code-line"><span class="variable">even_numbers</span> = <span class="function">list</span>(<span class="function">filter</span>(<span class="keyword">lambda</span> <span class="variable">x</span>: <span class="variable">x</span> % <span class="number">2</span> == <span class="number">0</span>, <span class="variable">numbers</span>))</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">even_numbers</span>)  <span class="comment"># [2, 4, 6, 8, 10]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># map() - 变换元素</span></span>
                    <span class="code-line"><span class="variable">squares</span> = <span class="function">list</span>(<span class="function">map</span>(<span class="keyword">lambda</span> <span class="variable">x</span>: <span class="variable">x</span> ** <span class="number">2</span>, <span class="variable">numbers</span>))</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">squares</span>)  <span class="comment"># [1, 4, 9, 16, 25, ...]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 结合使用</span></span>
                    <span class="code-line"><span class="variable">even_squares</span> = <span class="function">list</span>(<span class="function">map</span>(<span class="keyword">lambda</span> <span class="variable">x</span>: <span class="variable">x</span> ** <span class="number">2</span>, </span>
                    <span class="code-line">                       <span class="function">filter</span>(<span class="keyword">lambda</span> <span class="variable">x</span>: <span class="variable">x</span> % <span class="number">2</span> == <span class="number">0</span>, <span class="variable">numbers</span>)))</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">even_squares</span>)  <span class="comment"># [4, 16, 36, 64, 100]</span></span>
                </div>

                <div class="tips-box">
                    <p><strong>⚡ 性能提示：</strong></p>
                    <ul>
                        <li><code>sum(), max(), min()</code> 时间复杂度为 O(n)</li>
                        <li><code>enumerate()</code> 比手动维护索引更高效</li>
                        <li><code>zip()</code> 以最短列表为准停止</li>
                        <li>对于大数据，考虑使用生成器版本</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第11页：列表拷贝 -->
        <div class="slide">
            <div class="slide-number">11/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🔗</span>列表拷贝：浅拷贝vs深拷贝</h2>
                
                <h3>浅拷贝（Shallow Copy）</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">original</span> = [<span class="number">1</span>, <span class="number">2</span>, [<span class="number">3</span>, <span class="number">4</span>], <span class="number">5</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 方法1：使用切片</span></span>
                    <span class="code-line"><span class="variable">copy1</span> = <span class="variable">original</span>[:]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 方法2：使用list()构造函数</span></span>
                    <span class="code-line"><span class="variable">copy2</span> = <span class="function">list</span>(<span class="variable">original</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 方法3：使用copy模块</span></span>
                    <span class="code-line"><span class="keyword">import</span> <span class="variable">copy</span></span>
                    <span class="code-line"><span class="variable">copy3</span> = <span class="variable">copy</span>.<span class="function">copy</span>(<span class="variable">original</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 浅拷贝的问题</span></span>
                    <span class="code-line"><span class="variable">copy1</span>[<span class="number">2</span>][<span class="number">0</span>] = <span class="number">99</span>  <span class="comment"># 修改嵌套列表</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">original</span>)  <span class="comment"># [1, 2, [99, 4], 5] 原列表也被影响！</span></span>
                </div>

                <h3>深拷贝（Deep Copy）</h3>
                <div class="code-block">
                    <span class="code-line"><span class="keyword">import</span> <span class="variable">copy</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="variable">original</span> = [<span class="number">1</span>, <span class="number">2</span>, [<span class="number">3</span>, <span class="number">4</span>], <span class="number">5</span>]</span>
                    <span class="code-line"><span class="variable">deep_copy</span> = <span class="variable">copy</span>.<span class="function">deepcopy</span>(<span class="variable">original</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 修改深拷贝不会影响原列表</span></span>
                    <span class="code-line"><span class="variable">deep_copy</span>[<span class="number">2</span>][<span class="number">0</span>] = <span class="number">99</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">original</span>)   <span class="comment"># [1, 2, [3, 4], 5] 原列表不受影响</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">deep_copy</span>)  <span class="comment"># [1, 2, [99, 4], 5]</span></span>
                </div>

                <h3>引用 vs 拷贝</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">list_a</span> = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 引用（不是拷贝）</span></span>
                    <span class="code-line"><span class="variable">list_b</span> = <span class="variable">list_a</span>  <span class="comment"># 同一个对象</span></span>
                    <span class="code-line"><span class="variable">list_b</span>.<span class="function">append</span>(<span class="number">4</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">list_a</span>)  <span class="comment"># [1, 2, 3, 4] 原列表被修改</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 浅拷贝</span></span>
                    <span class="code-line"><span class="variable">list_c</span> = <span class="variable">list_a</span>[:]  <span class="comment"># 新对象</span></span>
                    <span class="code-line"><span class="variable">list_c</span>.<span class="function">append</span>(<span class="number">5</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">list_a</span>)  <span class="comment"># [1, 2, 3, 4] 不受影响</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">list_c</span>)  <span class="comment"># [1, 2, 3, 4, 5]</span></span>
                </div>

                <div class="highlight-box">
                    <p><strong>🎯 选择合适的拷贝方式：</strong></p>
                    <ul>
                        <li><strong>简单列表</strong>：使用切片 <code>[:]</code> 或 <code>list()</code></li>
                        <li><strong>嵌套列表</strong>：使用 <code>copy.deepcopy()</code></li>
                        <li><strong>性能考虑</strong>：浅拷贝更快，深拷贝更安全</li>
                        <li><strong>避免引用</strong>：除非确实需要共享数据</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第12页：列表最佳实践 -->
        <div class="slide">
            <div class="slide-number">12/30</div>
            <div class="slide-content">
                <h2><span class="emoji">💡</span>列表最佳实践和性能优化</h2>
                
                <h3>列表创建优化</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># ❌ 低效的方式</span></span>
                    <span class="code-line"><span class="variable">slow_list</span> = []</span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1000</span>):</span>
                    <span class="code-line">    <span class="variable">slow_list</span>.<span class="function">append</span>(<span class="variable">i</span> ** <span class="number">2</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># ✅ 高效的方式</span></span>
                    <span class="code-line"><span class="variable">fast_list</span> = [<span class="variable">i</span> ** <span class="number">2</span> <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1000</span>)]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># ✅ 更高效（如果数据量很大）</span></span>
                    <span class="code-line"><span class="variable">generator</span> = (<span class="variable">i</span> ** <span class="number">2</span> <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1000</span>))</span>
                </div>

                <h3>成员检查优化</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">large_list</span> = <span class="function">list</span>(<span class="function">range</span>(<span class="number">10000</span>))</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># ❌ 列表查找：O(n)</span></span>
                    <span class="code-line"><span class="keyword">if</span> <span class="number">5000</span> <span class="keyword">in</span> <span class="variable">large_list</span>:</span>
                    <span class="code-line">    <span class="function">print</span>(<span class="string">"找到了"</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># ✅ 集合查找：O(1)</span></span>
                    <span class="code-line"><span class="variable">large_set</span> = <span class="function">set</span>(<span class="variable">large_list</span>)</span>
                    <span class="code-line"><span class="keyword">if</span> <span class="number">5000</span> <span class="keyword">in</span> <span class="variable">large_set</span>:</span>
                    <span class="code-line">    <span class="function">print</span>(<span class="string">"找到了"</span>)</span>
                </div>

                <h3>列表拼接优化</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># ❌ 字符串拼接低效</span></span>
                    <span class="code-line"><span class="variable">words</span> = [<span class="string">"Python"</span>, <span class="string">"is"</span>, <span class="string">"awesome"</span>]</span>
                    <span class="code-line"><span class="variable">sentence</span> = <span class="string">""</span></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">word</span> <span class="keyword">in</span> <span class="variable">words</span>:</span>
                    <span class="code-line">    <span class="variable">sentence</span> += <span class="variable">word</span> + <span class="string">" "</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># ✅ 使用join()高效</span></span>
                    <span class="code-line"><span class="variable">sentence</span> = <span class="string">" "</span>.<span class="function">join</span>(<span class="variable">words</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># ✅ 列表拼接也要小心</span></span>
                    <span class="code-line"><span class="variable">result</span> = []</span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">lst</span> <span class="keyword">in</span> [<span class="variable">list1</span>, <span class="variable">list2</span>, <span class="variable">list3</span>]:</span>
                    <span class="code-line">    <span class="variable">result</span>.<span class="function">extend</span>(<span class="variable">lst</span>)  <span class="comment"># 比 result += lst 更清晰</span></span>
                </div>

                <h3>内存使用优化</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># ❌ 创建不必要的中间列表</span></span>
                    <span class="code-line"><span class="variable">numbers</span> = <span class="function">list</span>(<span class="function">range</span>(<span class="number">1000000</span>))</span>
                    <span class="code-line"><span class="variable">squares</span> = [<span class="variable">x</span> ** <span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">numbers</span>]</span>
                    <span class="code-line"><span class="variable">even_squares</span> = [<span class="variable">x</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">squares</span> <span class="keyword">if</span> <span class="variable">x</span> % <span class="number">2</span> == <span class="number">0</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># ✅ 一步到位</span></span>
                    <span class="code-line"><span class="variable">even_squares</span> = [<span class="variable">x</span> ** <span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1000000</span>) <span class="keyword">if</span> (<span class="variable">x</span> ** <span class="number">2</span>) % <span class="number">2</span> == <span class="number">0</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># ✅ 使用生成器节省内存</span></span>
                    <span class="code-line"><span class="variable">even_squares_gen</span> = (<span class="variable">x</span> ** <span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1000000</span>) <span class="keyword">if</span> (<span class="variable">x</span> ** <span class="number">2</span>) % <span class="number">2</span> == <span class="number">0</span>)</span>
                </div>

                <div class="tips-box">
                    <p><strong>🚀 性能原则：</strong></p>
                    <ul>
                        <li><strong>优先使用列表生成式</strong>而不是循环</li>
                        <li><strong>频繁查找用集合</strong>，有序数据用列表</li>
                        <li><strong>大数据处理考虑生成器</strong>节省内存</li>
                        <li><strong>避免不必要的拷贝</strong>和中间变量</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第13页：列表生成式基础 -->
        <div class="slide">
            <div class="slide-number">13/30</div>
            <div class="slide-content">
                <h2><span class="emoji">⚡</span>列表生成式基础语法</h2>
                
                <h3>什么是列表生成式？</h3>
                <p>列表生成式（List Comprehension）是Python中创建列表的简洁语法，能够用一行代码替代多行循环。</p>

                <h3>基础语法结构</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 基本语法：[表达式 for 变量 in 可迭代对象]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 传统方式：创建平方数列表</span></span>
                    <span class="code-line"><span class="variable">squares_old</span> = []</span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">10</span>):</span>
                    <span class="code-line">    <span class="variable">squares_old</span>.<span class="function">append</span>(<span class="variable">x</span> ** <span class="number">2</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 列表生成式：一行搞定</span></span>
                    <span class="code-line"><span class="variable">squares_new</span> = [<span class="variable">x</span> ** <span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">10</span>)]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">squares_new</span>)  <span class="comment"># [0, 1, 4, 9, 16, 25, 36, 49, 64, 81]</span></span>
                </div>

                <h3>常见的列表生成式模式</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 1. 数值变换</span></span>
                    <span class="code-line"><span class="variable">celsius</span> = [<span class="number">0</span>, <span class="number">20</span>, <span class="number">25</span>, <span class="number">30</span>, <span class="number">100</span>]</span>
                    <span class="code-line"><span class="variable">fahrenheit</span> = [<span class="variable">c</span> * <span class="number">9</span>/<span class="number">5</span> + <span class="number">32</span> <span class="keyword">for</span> <span class="variable">c</span> <span class="keyword">in</span> <span class="variable">celsius</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">fahrenheit</span>)  <span class="comment"># [32.0, 68.0, 77.0, 86.0, 212.0]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 2. 字符串处理</span></span>
                    <span class="code-line"><span class="variable">words</span> = [<span class="string">"hello"</span>, <span class="string">"world"</span>, <span class="string">"python"</span>]</span>
                    <span class="code-line"><span class="variable">upper_words</span> = [<span class="variable">word</span>.<span class="function">upper</span>() <span class="keyword">for</span> <span class="variable">word</span> <span class="keyword">in</span> <span class="variable">words</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">upper_words</span>)  <span class="comment"># ['HELLO', 'WORLD', 'PYTHON']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 3. 函数调用</span></span>
                    <span class="code-line"><span class="variable">numbers</span> = [-<span class="number">5</span>, -<span class="number">2</span>, <span class="number">0</span>, <span class="number">3</span>, <span class="number">8</span>]</span>
                    <span class="code-line"><span class="variable">absolute_values</span> = [<span class="function">abs</span>(<span class="variable">x</span>) <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">numbers</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">absolute_values</span>)  <span class="comment"># [5, 2, 0, 3, 8]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 4. 复杂表达式</span></span>
                    <span class="code-line"><span class="variable">coordinates</span> = [(<span class="variable">x</span>, <span class="variable">x</span>**<span class="number">2</span>) <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">5</span>)]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">coordinates</span>)  <span class="comment"># [(0, 0), (1, 1), (2, 4), (3, 9), (4, 16)]</span></span>
                </div>

                <h3>可迭代对象的多样性</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 字符串</span></span>
                    <span class="code-line"><span class="variable">chars</span> = [<span class="variable">char</span>.<span class="function">upper</span>() <span class="keyword">for</span> <span class="variable">char</span> <span class="keyword">in</span> <span class="string">"hello"</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">chars</span>)  <span class="comment"># ['H', 'E', 'L', 'L', 'O']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 元组</span></span>
                    <span class="code-line"><span class="variable">tuple_data</span> = (<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>)</span>
                    <span class="code-line"><span class="variable">doubled</span> = [<span class="variable">x</span> * <span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">tuple_data</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">doubled</span>)  <span class="comment"># [2, 4, 6, 8, 10]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 其他列表</span></span>
                    <span class="code-line"><span class="variable">matrix</span> = [[<span class="number">1</span>, <span class="number">2</span>], [<span class="number">3</span>, <span class="number">4</span>], [<span class="number">5</span>, <span class="number">6</span>]]</span>
                    <span class="code-line"><span class="variable">flattened</span> = [<span class="variable">item</span> <span class="keyword">for</span> <span class="variable">row</span> <span class="keyword">in</span> <span class="variable">matrix</span> <span class="keyword">for</span> <span class="variable">item</span> <span class="keyword">in</span> <span class="variable">row</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">flattened</span>)  <span class="comment"># [1, 2, 3, 4, 5, 6]</span></span>
                </div>

                <div class="highlight-box">
                    <p><strong>✨ 列表生成式的优势：</strong></p>
                    <ul>
                        <li><strong>简洁性</strong>：一行代码替代多行循环</li>
                        <li><strong>可读性</strong>：表达式直观易懂</li>
                        <li><strong>性能</strong>：通常比传统循环更快</li>
                        <li><strong>Pythonic</strong>：符合Python代码风格</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第14页：基础列表生成式实例 -->
        <div class="slide">
            <div class="slide-number">14/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🎯</span>基础列表生成式实例</h2>
                
                <h3>数学运算实例</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 生成前10个偶数</span></span>
                    <span class="code-line"><span class="variable">evens</span> = [<span class="variable">x</span> * <span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">10</span>)]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">evens</span>)  <span class="comment"># [0, 2, 4, 6, 8, 10, 12, 14, 16, 18]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 计算阶乘序列</span></span>
                    <span class="code-line"><span class="keyword">import</span> <span class="variable">math</span></span>
                    <span class="code-line"><span class="variable">factorials</span> = [<span class="variable">math</span>.<span class="function">factorial</span>(<span class="variable">n</span>) <span class="keyword">for</span> <span class="variable">n</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">6</span>)]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">factorials</span>)  <span class="comment"># [1, 1, 2, 6, 24, 120]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 生成平方根列表</span></span>
                    <span class="code-line"><span class="variable">square_roots</span> = [<span class="variable">n</span> ** <span class="number">0.5</span> <span class="keyword">for</span> <span class="variable">n</span> <span class="keyword">in</span> [<span class="number">1</span>, <span class="number">4</span>, <span class="number">9</span>, <span class="number">16</span>, <span class="number">25</span>]]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">square_roots</span>)  <span class="comment"># [1.0, 2.0, 3.0, 4.0, 5.0]</span></span>
                </div>

                <h3>字符串处理实例</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 提取姓名首字母</span></span>
                    <span class="code-line"><span class="variable">names</span> = [<span class="string">"张三"</span>, <span class="string">"李四"</span>, <span class="string">"王五"</span>, <span class="string">"赵六"</span>]</span>
                    <span class="code-line"><span class="variable">initials</span> = [<span class="variable">name</span>[<span class="number">0</span>] <span class="keyword">for</span> <span class="variable">name</span> <span class="keyword">in</span> <span class="variable">names</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">initials</span>)  <span class="comment"># ['张', '李', '王', '赵']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 计算字符串长度</span></span>
                    <span class="code-line"><span class="variable">words</span> = [<span class="string">"Python"</span>, <span class="string">"Java"</span>, <span class="string">"C++"</span>, <span class="string">"JavaScript"</span>]</span>
                    <span class="code-line"><span class="variable">word_lengths</span> = [<span class="function">len</span>(<span class="variable">word</span>) <span class="keyword">for</span> <span class="variable">word</span> <span class="keyword">in</span> <span class="variable">words</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">word_lengths</span>)  <span class="comment"># [6, 4, 3, 10]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 反转字符串</span></span>
                    <span class="code-line"><span class="variable">sentences</span> = [<span class="string">"hello world"</span>, <span class="string">"python programming"</span>]</span>
                    <span class="code-line"><span class="variable">reversed_sentences</span> = [<span class="variable">sentence</span>[::-<span class="number">1</span>] <span class="keyword">for</span> <span class="variable">sentence</span> <span class="keyword">in</span> <span class="variable">sentences</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">reversed_sentences</span>)  <span class="comment"># ['dlrow olleh', 'gnimmargorp nohtyp']</span></span>
                </div>

                <h3>数据转换实例</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 字符串转整数</span></span>
                    <span class="code-line"><span class="variable">str_numbers</span> = [<span class="string">"1"</span>, <span class="string">"2"</span>, <span class="string">"3"</span>, <span class="string">"4"</span>, <span class="string">"5"</span>]</span>
                    <span class="code-line"><span class="variable">int_numbers</span> = [<span class="function">int</span>(<span class="variable">s</span>) <span class="keyword">for</span> <span class="variable">s</span> <span class="keyword">in</span> <span class="variable">str_numbers</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">int_numbers</span>)  <span class="comment"># [1, 2, 3, 4, 5]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 提取字典值</span></span>
                    <span class="code-line"><span class="variable">students</span> = [</span>
                    <span class="code-line">    {<span class="string">"name"</span>: <span class="string">"张三"</span>, <span class="string">"score"</span>: <span class="number">85</span>},</span>
                    <span class="code-line">    {<span class="string">"name"</span>: <span class="string">"李四"</span>, <span class="string">"score"</span>: <span class="number">92</span>},</span>
                    <span class="code-line">    {<span class="string">"name"</span>: <span class="string">"王五"</span>, <span class="string">"score"</span>: <span class="number">78</span>}</span>
                    <span class="code-line">]</span>
                    <span class="code-line"><span class="variable">scores</span> = [<span class="variable">student</span>[<span class="string">"score"</span>] <span class="keyword">for</span> <span class="variable">student</span> <span class="keyword">in</span> <span class="variable">students</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">scores</span>)  <span class="comment"># [85, 92, 78]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 创建坐标对</span></span>
                    <span class="code-line"><span class="variable">coordinates</span> = [(<span class="variable">x</span>, <span class="variable">y</span>) <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> [<span class="number">1</span>, <span class="number">2</span>] <span class="keyword">for</span> <span class="variable">y</span> <span class="keyword">in</span> [<span class="number">3</span>, <span class="number">4</span>]]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">coordinates</span>)  <span class="comment"># [(1, 3), (1, 4), (2, 3), (2, 4)]</span></span>
                </div>

                <h3>实用工具函数</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 生成ASCII码表</span></span>
                    <span class="code-line"><span class="variable">ascii_table</span> = [(<span class="function">chr</span>(<span class="variable">i</span>), <span class="variable">i</span>) <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">65</span>, <span class="number">75</span>)]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">ascii_table</span>)  <span class="comment"># [('A', 65), ('B', 66), ..., ('J', 74)]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 创建格式化字符串</span></span>
                    <span class="code-line"><span class="variable">items</span> = [<span class="string">"苹果"</span>, <span class="string">"香蕉"</span>, <span class="string">"橙子"</span>]</span>
                    <span class="code-line"><span class="variable">prices</span> = [<span class="number">3.5</span>, <span class="number">2.0</span>, <span class="number">4.2</span>]</span>
                    <span class="code-line"><span class="variable">price_list</span> = [<span class="string">f"{item}: ¥{price:.2f}"</span> <span class="keyword">for</span> <span class="variable">item</span>, <span class="variable">price</span> <span class="keyword">in</span> <span class="function">zip</span>(<span class="variable">items</span>, <span class="variable">prices</span>)]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">price_list</span>)  <span class="comment"># ['苹果: ¥3.50', '香蕉: ¥2.00', '橙子: ¥4.20']</span></span>
                </div>

                <div class="tips-box">
                    <p><strong>💡 实战技巧：</strong></p>
                    <ul>
                        <li><strong>简单变换</strong>：优先选择列表生成式</li>
                        <li><strong>可读性</strong>：表达式不要过于复杂</li>
                        <li><strong>性能</strong>：列表生成式通常比循环快20-30%</li>
                        <li><strong>调试</strong>：复杂逻辑先写普通循环再优化</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第15页：条件过滤的列表生成式 -->
        <div class="slide">
            <div class="slide-number">15/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🚦</span>条件过滤的列表生成式</h2>
                
                <h3>基础过滤语法</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 语法：[表达式 for 变量 in 可迭代对象 if 条件]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 传统方式：过滤偶数</span></span>
                    <span class="code-line"><span class="variable">evens_old</span> = []</span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">20</span>):</span>
                    <span class="code-line">    <span class="keyword">if</span> <span class="variable">x</span> % <span class="number">2</span> == <span class="number">0</span>:</span>
                    <span class="code-line">        <span class="variable">evens_old</span>.<span class="function">append</span>(<span class="variable">x</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 列表生成式：一行搞定</span></span>
                    <span class="code-line"><span class="variable">evens_new</span> = [<span class="variable">x</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">20</span>) <span class="keyword">if</span> <span class="variable">x</span> % <span class="number">2</span> == <span class="number">0</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">evens_new</span>)  <span class="comment"># [0, 2, 4, 6, 8, 10, 12, 14, 16, 18]</span></span>
                </div>

                <h3>数值过滤实例</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 过滤正数</span></span>
                    <span class="code-line"><span class="variable">numbers</span> = [-<span class="number">5</span>, -<span class="number">2</span>, <span class="number">0</span>, <span class="number">3</span>, <span class="number">8</span>, -<span class="number">1</span>, <span class="number">7</span>]</span>
                    <span class="code-line"><span class="variable">positive_numbers</span> = [<span class="variable">x</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">numbers</span> <span class="keyword">if</span> <span class="variable">x</span> > <span class="number">0</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">positive_numbers</span>)  <span class="comment"># [3, 8, 7]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 过滤质数</span></span>
                    <span class="code-line"><span class="keyword">def</span> <span class="function">is_prime</span>(<span class="variable">n</span>):</span>
                    <span class="code-line">    <span class="keyword">if</span> <span class="variable">n</span> < <span class="number">2</span>:</span>
                    <span class="code-line">        <span class="keyword">return</span> <span class="keyword">False</span></span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">2</span>, <span class="function">int</span>(<span class="variable">n</span> ** <span class="number">0.5</span>) + <span class="number">1</span>):</span>
                    <span class="code-line">        <span class="keyword">if</span> <span class="variable">n</span> % <span class="variable">i</span> == <span class="number">0</span>:</span>
                    <span class="code-line">            <span class="keyword">return</span> <span class="keyword">False</span></span>
                    <span class="code-line">    <span class="keyword">return</span> <span class="keyword">True</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="variable">primes</span> = [<span class="variable">x</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">2</span>, <span class="number">20</span>) <span class="keyword">if</span> <span class="function">is_prime</span>(<span class="variable">x</span>)]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">primes</span>)  <span class="comment"># [2, 3, 5, 7, 11, 13, 17, 19]</span></span>
                </div>

                <h3>字符串过滤实例</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 过滤长度超过5的单词</span></span>
                    <span class="code-line"><span class="variable">words</span> = [<span class="string">"Python"</span>, <span class="string">"is"</span>, <span class="string">"awesome"</span>, <span class="string">"and"</span>, <span class="string">"powerful"</span>]</span>
                    <span class="code-line"><span class="variable">long_words</span> = [<span class="variable">word</span> <span class="keyword">for</span> <span class="variable">word</span> <span class="keyword">in</span> <span class="variable">words</span> <span class="keyword">if</span> <span class="function">len</span>(<span class="variable">word</span>) > <span class="number">5</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">long_words</span>)  <span class="comment"># ['Python', 'awesome', 'powerful']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 过滤包含特定字符的字符串</span></span>
                    <span class="code-line"><span class="variable">names</span> = [<span class="string">"张三"</span>, <span class="string">"李四"</span>, <span class="string">"王小明"</span>, <span class="string">"赵六"</span>, <span class="string">"小红"</span>]</span>
                    <span class="code-line"><span class="variable">names_with_xiao</span> = [<span class="variable">name</span> <span class="keyword">for</span> <span class="variable">name</span> <span class="keyword">in</span> <span class="variable">names</span> <span class="keyword">if</span> <span class="string">"小"</span> <span class="keyword">in</span> <span class="variable">name</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">names_with_xiao</span>)  <span class="comment"># ['王小明', '小红']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 过滤文件扩展名</span></span>
                    <span class="code-line"><span class="variable">files</span> = [<span class="string">"data.txt"</span>, <span class="string">"script.py"</span>, <span class="string">"image.jpg"</span>, <span class="string">"doc.pdf"</span>, <span class="string">"code.py"</span>]</span>
                    <span class="code-line"><span class="variable">python_files</span> = [<span class="variable">file</span> <span class="keyword">for</span> <span class="variable">file</span> <span class="keyword">in</span> <span class="variable">files</span> <span class="keyword">if</span> <span class="variable">file</span>.<span class="function">endswith</span>(<span class="string">".py"</span>)]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">python_files</span>)  <span class="comment"># ['script.py', 'code.py']</span></span>
                </div>

                <h3>复杂条件过滤</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 多个条件：and</span></span>
                    <span class="code-line"><span class="variable">scores</span> = [<span class="number">45</span>, <span class="number">67</span>, <span class="number">89</span>, <span class="number">92</span>, <span class="number">56</span>, <span class="number">78</span>, <span class="number">95</span>]</span>
                    <span class="code-line"><span class="variable">good_scores</span> = [<span class="variable">score</span> <span class="keyword">for</span> <span class="variable">score</span> <span class="keyword">in</span> <span class="variable">scores</span> <span class="keyword">if</span> <span class="variable">score</span> >= <span class="number">60</span> <span class="keyword">and</span> <span class="variable">score</span> <= <span class="number">90</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">good_scores</span>)  <span class="comment"># [67, 89, 78]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 多个条件：or</span></span>
                    <span class="code-line"><span class="variable">extreme_scores</span> = [<span class="variable">score</span> <span class="keyword">for</span> <span class="variable">score</span> <span class="keyword">in</span> <span class="variable">scores</span> <span class="keyword">if</span> <span class="variable">score</span> < <span class="number">50</span> <span class="keyword">or</span> <span class="variable">score</span> > <span class="number">90</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">extreme_scores</span>)  <span class="comment"># [45, 92, 95]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 结合函数判断</span></span>
                    <span class="code-line"><span class="variable">students</span> = [</span>
                    <span class="code-line">        {<span class="string">"name"</span>: <span class="string">"张三"</span>, <span class="string">"age"</span>: <span class="number">20</span>, <span class="string">"score"</span>: <span class="number">85</span>},</span>
                    <span class="code-line">        {<span class="string">"name"</span>: <span class="string">"李四"</span>, <span class="string">"age"</span>: <span class="number">19</span>, <span class="string">"score"</span>: <span class="number">92</span>},</span>
                    <span class="code-line">        {<span class="string">"name"</span>: <span class="string">"王五"</span>, <span class="string">"age"</span>: <span class="number">21</span>, <span class="string">"score"</span>: <span class="number">78</span>}</span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"><span class="variable">excellent_students</span> = [</span>
                    <span class="code-line">        <span class="variable">s</span>[<span class="string">"name"</span>] <span class="keyword">for</span> <span class="variable">s</span> <span class="keyword">in</span> <span class="variable">students</span> </span>
                    <span class="code-line">        <span class="keyword">if</span> <span class="variable">s</span>[<span class="string">"score"</span>] >= <span class="number">85</span> <span class="keyword">and</span> <span class="variable">s</span>[<span class="string">"age"</span>] <= <span class="number">20</span></span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">excellent_students</span>)  <span class="comment"># ['张三', '李四']</span></span>
                </div>

                <h3>条件表达式结合</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 三元运算符 + 过滤</span></span>
                    <span class="code-line"><span class="variable">numbers</span> = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>, <span class="number">7</span>, <span class="number">8</span>, <span class="number">9</span>, <span class="number">10</span>]</span>
                    <span class="code-line"><span class="variable">labeled_evens</span> = [</span>
                    <span class="code-line">        <span class="string">f"偶数:{x}"</span> <span class="keyword">if</span> <span class="variable">x</span> % <span class="number">2</span> == <span class="number">0</span> <span class="keyword">else</span> <span class="string">f"奇数:{x}"</span> </span>
                    <span class="code-line">        <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">numbers</span> <span class="keyword">if</span> <span class="variable">x</span> <= <span class="number">5</span></span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">labeled_evens</span>)  <span class="comment"># ['奇数:1', '偶数:2', '奇数:3', '偶数:4', '奇数:5']</span></span>
                </div>

                <div class="highlight-box">
                    <p><strong>🎯 过滤条件设计原则：</strong></p>
                    <ul>
                        <li><strong>清晰性</strong>：条件表达式要直观易懂</li>
                        <li><strong>性能</strong>：先过滤再变换通常更高效</li>
                        <li><strong>复杂度</strong>：过于复杂的条件考虑用函数</li>
                        <li><strong>可读性</strong>：必要时分解为多步操作</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第16页：嵌套循环的列表生成式 -->
        <div class="slide">
            <div class="slide-number">16/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🔄</span>嵌套循环的列表生成式</h2>
                
                <h3>基础嵌套循环语法</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 语法：[表达式 for 外层变量 in 外层迭代 for 内层变量 in 内层迭代]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 传统嵌套循环</span></span>
                    <span class="code-line"><span class="variable">result_old</span> = []</span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>]:</span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">y</span> <span class="keyword">in</span> [<span class="string">"a"</span>, <span class="string">"b"</span>]:</span>
                    <span class="code-line">        <span class="variable">result_old</span>.<span class="function">append</span>((<span class="variable">x</span>, <span class="variable">y</span>))</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 列表生成式：嵌套循环</span></span>
                    <span class="code-line"><span class="variable">result_new</span> = [(<span class="variable">x</span>, <span class="variable">y</span>) <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>] <span class="keyword">for</span> <span class="variable">y</span> <span class="keyword">in</span> [<span class="string">"a"</span>, <span class="string">"b"</span>]]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">result_new</span>)  <span class="comment"># [(1, 'a'), (1, 'b'), (2, 'a'), (2, 'b'), (3, 'a'), (3, 'b')]</span></span>
                </div>

                <h3>创建坐标网格</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 生成2D坐标点</span></span>
                    <span class="code-line"><span class="variable">coordinates</span> = [(<span class="variable">x</span>, <span class="variable">y</span>) <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">3</span>) <span class="keyword">for</span> <span class="variable">y</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">3</span>)]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">coordinates</span>)</span>
                    <span class="code-line"><span class="comment"># [(0, 0), (0, 1), (0, 2), (1, 0), (1, 1), (1, 2), (2, 0), (2, 1), (2, 2)]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 生成棋盘坐标</span></span>
                    <span class="code-line"><span class="variable">chess_squares</span> = [<span class="string">f"{col}{row}"</span> <span class="keyword">for</span> <span class="variable">col</span> <span class="keyword">in</span> <span class="string">"abcdefgh"</span> <span class="keyword">for</span> <span class="variable">row</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1</span>, <span class="number">9</span>)]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">chess_squares</span>[:<span class="number">8</span>])  <span class="comment"># ['a1', 'a2', 'a3', 'a4', 'a5', 'a6', 'a7', 'a8']</span></span>
                </div>

                <h3>扁平化嵌套列表</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 二维列表扁平化</span></span>
                    <span class="code-line"><span class="variable">matrix</span> = [[<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>], [<span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>], [<span class="number">7</span>, <span class="number">8</span>, <span class="number">9</span>]]</span>
                    <span class="code-line"><span class="variable">flattened</span> = [<span class="variable">item</span> <span class="keyword">for</span> <span class="variable">row</span> <span class="keyword">in</span> <span class="variable">matrix</span> <span class="keyword">for</span> <span class="variable">item</span> <span class="keyword">in</span> <span class="variable">row</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">flattened</span>)  <span class="comment"># [1, 2, 3, 4, 5, 6, 7, 8, 9]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 多层嵌套扁平化</span></span>
                    <span class="code-line"><span class="variable">nested_lists</span> = [[[<span class="number">1</span>, <span class="number">2</span>], [<span class="number">3</span>, <span class="number">4</span>]], [[<span class="number">5</span>, <span class="number">6</span>], [<span class="number">7</span>, <span class="number">8</span>]]]</span>
                    <span class="code-line"><span class="variable">deep_flattened</span> = [<span class="variable">item</span> <span class="keyword">for</span> <span class="variable">level1</span> <span class="keyword">in</span> <span class="variable">nested_lists</span> 
                                     <span class="keyword">for</span> <span class="variable">level2</span> <span class="keyword">in</span> <span class="variable">level1</span> 
                                     <span class="keyword">for</span> <span class="variable">item</span> <span class="keyword">in</span> <span class="variable">level2</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">deep_flattened</span>)  <span class="comment"># [1, 2, 3, 4, 5, 6, 7, 8]</span></span>
                </div>

                <h3>结合条件的嵌套循环</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 过滤对角线元素</span></span>
                    <span class="code-line"><span class="variable">non_diagonal</span> = [(<span class="variable">i</span>, <span class="variable">j</span>) <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">3</span>) <span class="keyword">for</span> <span class="variable">j</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">3</span>) <span class="keyword">if</span> <span class="variable">i</span> != <span class="variable">j</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">non_diagonal</span>)  <span class="comment"># [(0, 1), (0, 2), (1, 0), (1, 2), (2, 0), (2, 1)]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 生成乘法表</span></span>
                    <span class="code-line"><span class="variable">multiplication_table</span> = [</span>
                    <span class="code-line">        <span class="string">f"{i} × {j} = {i*j}"</span> <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1</span>, <span class="number">4</span>) 
                                        <span class="keyword">for</span> <span class="variable">j</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1</span>, <span class="number">4</span>) 
                                        <span class="keyword">if</span> <span class="variable">i</span> <= <span class="variable">j</span>
                    ]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">multiplication_table</span>)</span>
                    <span class="code-line"><span class="comment"># ['1 × 1 = 1', '1 × 2 = 2', '1 × 3 = 3', '2 × 2 = 4', '2 × 3 = 6', '3 × 3 = 9']</span></span>
                </div>

                <h3>处理字符串组合</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 生成所有可能的名字组合</span></span>
                    <span class="code-line"><span class="variable">first_names</span> = [<span class="string">"张"</span>, <span class="string">"李"</span>, <span class="string">"王"</span>]</span>
                    <span class="code-line"><span class="variable">last_names</span> = [<span class="string">"伟"</span>, <span class="string">"芳"</span>]</span>
                    <span class="code-line"><span class="variable">full_names</span> = [<span class="variable">first</span> + <span class="variable">last</span> <span class="keyword">for</span> <span class="variable">first</span> <span class="keyword">in</span> <span class="variable">first_names</span> <span class="keyword">for</span> <span class="variable">last</span> <span class="keyword">in</span> <span class="variable">last_names</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">full_names</span>)  <span class="comment"># ['张伟', '张芳', '李伟', '李芳', '王伟', '王芳']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 生成密码组合（简化版）</span></span>
                    <span class="code-line"><span class="variable">letters</span> = <span class="string">"abc"</span></span>
                    <span class="code-line"><span class="variable">numbers</span> = <span class="string">"123"</span></span>
                    <span class="code-line"><span class="variable">passwords</span> = [<span class="variable">letter</span> + <span class="variable">num</span> <span class="keyword">for</span> <span class="variable">letter</span> <span class="keyword">in</span> <span class="variable">letters</span> <span class="keyword">for</span> <span class="variable">num</span> <span class="keyword">in</span> <span class="variable">numbers</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">passwords</span>)  <span class="comment"># ['a1', 'a2', 'a3', 'b1', 'b2', 'b3', 'c1', 'c2', 'c3']</span></span>
                </div>

                <div class="tips-box">
                    <p><strong>⚠️ 注意事项：</strong></p>
                    <ul>
                        <li><strong>顺序</strong>：外层循环在前，内层循环在后</li>
                        <li><strong>可读性</strong>：超过2层嵌套考虑分解</li>
                        <li><strong>性能</strong>：注意避免笛卡尔积爆炸</li>
                        <li><strong>调试</strong>：复杂嵌套先用普通循环验证</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第17页：复杂表达式的列表生成式 -->
        <div class="slide">
            <div class="slide-number">17/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🎨</span>复杂表达式的列表生成式</h2>
                
                <h3>条件表达式（三元运算符）</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 语法：[表达式1 if 条件 else 表达式2 for 变量 in 迭代器]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 基础条件表达式</span></span>
                    <span class="code-line"><span class="variable">numbers</span> = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>]</span>
                    <span class="code-line"><span class="variable">parity</span> = [<span class="string">"偶数"</span> <span class="keyword">if</span> <span class="variable">x</span> % <span class="number">2</span> == <span class="number">0</span> <span class="keyword">else</span> <span class="string">"奇数"</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">numbers</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">parity</span>)  <span class="comment"># ['奇数', '偶数', '奇数', '偶数', '奇数', '偶数']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 处理除零错误</span></span>
                    <span class="code-line"><span class="variable">denominators</span> = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">0</span>, <span class="number">4</span>, <span class="number">5</span>]</span>
                    <span class="code-line"><span class="variable">safe_division</span> = [<span class="number">10</span> / <span class="variable">x</span> <span class="keyword">if</span> <span class="variable">x</span> != <span class="number">0</span> <span class="keyword">else</span> <span class="keyword">float</span>(<span class="string">"inf"</span>) <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">denominators</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">safe_division</span>)  <span class="comment"># [10.0, 5.0, inf, 2.5, 2.0]</span></span>
                </div>

                <h3>函数调用和方法链</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 复杂的字符串处理</span></span>
                    <span class="code-line"><span class="variable">raw_data</span> = [<span class="string">"  Python  "</span>, <span class="string">"JAVA"</span>, <span class="string">"  c++  "</span>, <span class="string">"JavaScript"</span>]</span>
                    <span class="code-line"><span class="variable">cleaned_data</span> = [<span class="variable">item</span>.<span class="function">strip</span>().<span class="function">lower</span>().<span class="function">title</span>() <span class="keyword">for</span> <span class="variable">item</span> <span class="keyword">in</span> <span class="variable">raw_data</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">cleaned_data</span>)  <span class="comment"># ['Python', 'Java', 'C++', 'Javascript']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 使用lambda函数</span></span>
                    <span class="code-line"><span class="variable">transform</span> = <span class="keyword">lambda</span> <span class="variable">x</span>: <span class="variable">x</span> ** <span class="number">2</span> <span class="keyword">if</span> <span class="variable">x</span> > <span class="number">0</span> <span class="keyword">else</span> <span class="variable">x</span> ** <span class="number">3</span></span>
                    <span class="code-line"><span class="variable">numbers</span> = [-<span class="number">2</span>, -<span class="number">1</span>, <span class="number">0</span>, <span class="number">1</span>, <span class="number">2</span>]</span>
                    <span class="code-line"><span class="variable">transformed</span> = [<span class="function">transform</span>(<span class="variable">x</span>) <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">numbers</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">transformed</span>)  <span class="comment"># [-8, -1, 0, 1, 4]</span></span>
                </div>

                <h3>处理复杂数据结构</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 处理字典列表</span></span>
                    <span class="code-line"><span class="variable">employees</span> = [</span>
                    <span class="code-line">        {<span class="string">"name"</span>: <span class="string">"张三"</span>, <span class="string">"department"</span>: <span class="string">"技术部"</span>, <span class="string">"salary"</span>: <span class="number">15000</span>},</span>
                    <span class="code-line">        {<span class="string">"name"</span>: <span class="string">"李四"</span>, <span class="string">"department"</span>: <span class="string">"销售部"</span>, <span class="string">"salary"</span>: <span class="number">12000</span>},</span>
                    <span class="code-line">        {<span class="string">"name"</span>: <span class="string">"王五"</span>, <span class="string">"department"</span>: <span class="string">"技术部"</span>, <span class="string">"salary"</span>: <span class="number">18000</span>}</span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 创建格式化的员工信息</span></span>
                    <span class="code-line"><span class="variable">employee_info</span> = [</span>
                    <span class="code-line">        <span class="string">f"{emp['name']}({emp['department']}) - "
                        f"{'高薪' if emp['salary'] > 15000 else '普通'}"</span></span>
                    <span class="code-line">        <span class="keyword">for</span> <span class="variable">emp</span> <span class="keyword">in</span> <span class="variable">employees</span></span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">employee_info</span>)</span>
                    <span class="code-line"><span class="comment"># ['张三(技术部) - 普通', '李四(销售部) - 普通', '王五(技术部) - 高薪']</span></span>
                </div>

                <h3>数学和科学计算</h3>
                <div class="code-block">
                    <span class="code-line"><span class="keyword">import</span> <span class="variable">math</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 计算三角函数值</span></span>
                    <span class="code-line"><span class="variable">angles</span> = [<span class="number">0</span>, <span class="number">30</span>, <span class="number">45</span>, <span class="number">60</span>, <span class="number">90</span>]</span>
                    <span class="code-line"><span class="variable">sin_values</span> = [</span>
                    <span class="code-line">        <span class="function">round</span>(<span class="variable">math</span>.<span class="function">sin</span>(<span class="variable">math</span>.<span class="function">radians</span>(<span class="variable">angle</span>)), <span class="number">3</span>) </span>
                    <span class="code-line">        <span class="keyword">for</span> <span class="variable">angle</span> <span class="keyword">in</span> <span class="variable">angles</span></span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">sin_values</span>)  <span class="comment"># [0.0, 0.5, 0.707, 0.866, 1.0]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 生成斐波那契数列（递推式）</span></span>
                    <span class="code-line"><span class="keyword">def</span> <span class="function">fibonacci_list</span>(<span class="variable">n</span>):</span>
                    <span class="code-line">    <span class="variable">fib</span> = [<span class="number">0</span>, <span class="number">1</span>]</span>
                    <span class="code-line">    <span class="keyword">return</span> <span class="variable">fib</span> + [<span class="variable">fib</span>[<span class="variable">i-1</span>] + <span class="variable">fib</span>[<span class="variable">i-2</span>] <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">2</span>, <span class="variable">n</span>)]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">fibonacci_list</span>(<span class="number">10</span>))  <span class="comment"># [0, 1, 1, 2, 3, 5, 8, 13, 21, 34]</span></span>
                </div>

                <h3>错误处理和异常安全</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 安全的类型转换</span></span>
                    <span class="code-line"><span class="variable">mixed_data</span> = [<span class="string">"123"</span>, <span class="string">"abc"</span>, <span class="string">"45.6"</span>, <span class="string">"xyz"</span>, <span class="string">"789"</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="keyword">def</span> <span class="function">safe_int</span>(<span class="variable">value</span>):</span>
                    <span class="code-line">    <span class="keyword">try</span>:</span>
                    <span class="code-line">        <span class="keyword">return</span> <span class="function">int</span>(<span class="function">float</span>(<span class="variable">value</span>))</span>
                    <span class="code-line">    <span class="keyword">except</span> <span class="variable">ValueError</span>:</span>
                    <span class="code-line">        <span class="keyword">return</span> <span class="keyword">None</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="variable">converted_numbers</span> = [</span>
                    <span class="code-line">        <span class="function">safe_int</span>(<span class="variable">item</span>) <span class="keyword">for</span> <span class="variable">item</span> <span class="keyword">in</span> <span class="variable">mixed_data</span></span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">converted_numbers</span>)  <span class="comment"># [123, None, 45, None, 789]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 过滤None值</span></span>
                    <span class="code-line"><span class="variable">valid_numbers</span> = [<span class="variable">x</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">converted_numbers</span> <span class="keyword">if</span> <span class="variable">x</span> <span class="keyword">is not</span> <span class="keyword">None</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">valid_numbers</span>)  <span class="comment"># [123, 45, 789]</span></span>
                </div>

                <div class="highlight-box">
                    <p><strong>🎯 复杂表达式设计原则：</strong></p>
                    <ul>
                        <li><strong>可读性优先</strong>：复杂逻辑考虑提取为函数</li>
                        <li><strong>异常安全</strong>：处理可能的类型错误和异常</li>
                        <li><strong>性能考虑</strong>：避免在表达式中重复昂贵计算</li>
                        <li><strong>测试友好</strong>：复杂表达式应该易于单元测试</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第18页：列表生成式处理字符串 -->
        <div class="slide">
            <div class="slide-number">18/30</div>
            <div class="slide-content">
                <h2><span class="emoji">📊</span>列表生成式处理字符串</h2>
                
                <h3>字符串分析和统计</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">text</span> = <span class="string">"Python Programming is Fun and Powerful"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 统计每个单词的长度</span></span>
                    <span class="code-line"><span class="variable">word_lengths</span> = [<span class="function">len</span>(<span class="variable">word</span>) <span class="keyword">for</span> <span class="variable">word</span> <span class="keyword">in</span> <span class="variable">text</span>.<span class="function">split</span>()]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">word_lengths</span>)  <span class="comment"># [6, 11, 2, 3, 3, 8]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 提取所有大写字母</span></span>
                    <span class="code-line"><span class="variable">uppercase_chars</span> = [<span class="variable">char</span> <span class="keyword">for</span> <span class="variable">char</span> <span class="keyword">in</span> <span class="variable">text</span> <span class="keyword">if</span> <span class="variable">char</span>.<span class="function">isupper</span>()]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">uppercase_chars</span>)  <span class="comment"># ['P', 'P', 'F', 'P']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 统计每个字符的ASCII值</span></span>
                    <span class="code-line"><span class="variable">word</span> = <span class="string">"Hello"</span></span>
                    <span class="code-line"><span class="variable">ascii_values</span> = [(<span class="variable">char</span>, <span class="function">ord</span>(<span class="variable">char</span>)) <span class="keyword">for</span> <span class="variable">char</span> <span class="keyword">in</span> <span class="variable">word</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">ascii_values</span>)  <span class="comment"># [('H', 72), ('e', 101), ('l', 108), ('l', 108), ('o', 111)]</span></span>
                </div>

                <h3>字符串清理和格式化</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 清理用户输入数据</span></span>
                    <span class="code-line"><span class="variable">user_inputs</span> = [<span class="string">"  张三  "</span>, <span class="string">"李四"</span>, <span class="string">" 王五 "</span>, <span class="string">"  "</span>, <span class="string">"赵六"</span>]</span>
                    <span class="code-line"><span class="variable">cleaned_names</span> = [</span>
                    <span class="code-line">        <span class="variable">name</span>.<span class="function">strip</span>() <span class="keyword">for</span> <span class="variable">name</span> <span class="keyword">in</span> <span class="variable">user_inputs</span> </span>
                    <span class="code-line">        <span class="keyword">if</span> <span class="variable">name</span>.<span class="function">strip</span>()  <span class="comment"># 过滤空字符串</span></span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">cleaned_names</span>)  <span class="comment"># ['张三', '李四', '王五', '赵六']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># Email地址格式化</span></span>
                    <span class="code-line"><span class="variable">emails</span> = [<span class="string">"<EMAIL>"</span>, <span class="string">"<EMAIL>"</span>, <span class="string">"<EMAIL>"</span>]</span>
                    <span class="code-line"><span class="variable">normalized_emails</span> = [<span class="variable">email</span>.<span class="function">lower</span>() <span class="keyword">for</span> <span class="variable">email</span> <span class="keyword">in</span> <span class="variable">emails</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">normalized_emails</span>)</span>
                    <span class="code-line"><span class="comment"># ['<EMAIL>', '<EMAIL>', '<EMAIL>']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 电话号码格式化</span></span>
                    <span class="code-line"><span class="variable">phone_numbers</span> = [<span class="string">"138-1234-5678"</span>, <span class="string">"139 8765 4321"</span>, <span class="string">"15012345678"</span>]</span>
                    <span class="code-line"><span class="variable">formatted_phones</span> = [</span>
                    <span class="code-line">        <span class="string">"-"</span>.<span class="function">join</span>([<span class="variable">phone</span>.<span class="function">replace</span>(<span class="string">"-"</span>, <span class="string">""</span>).<span class="function">replace</span>(<span class="string">" "</span>, <span class="string">""</span>)[<span class="variable">i</span>:<span class="variable">i</span>+<span class="number">4</span>] 
                                 <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> [<span class="number">0</span>, <span class="number">3</span>, <span class="number">7</span>]]) </span>
                        <span class="keyword">for</span> <span class="variable">phone</span> <span class="keyword">in</span> <span class="variable">phone_numbers</span></span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">formatted_phones</span>)  <span class="comment"># ['138-1234-5678', '139-8765-4321', '150-1234-5678']</span></span>
                </div>

                <h3>文本解析和提取</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 从HTML标签中提取文本（简化版）</span></span>
                    <span class="code-line"><span class="variable">html_tags</span> = [<span class="string">"<h1>标题</h1>"</span>, <span class="string">"<p>段落内容</p>"</span>, <span class="string">"<a>链接</a>"</span>]</span>
                    <span class="code-line"><span class="variable">extracted_text</span> = [</span>
                    <span class="code-line">        <span class="variable">tag</span>[<span class="variable">tag</span>.<span class="function">find</span>(<span class="string">">"</span>)+<span class="number">1</span>:<span class="variable">tag</span>.<span class="function">rfind</span>(<span class="string">"<"</span>)] </span>
                        <span class="keyword">for</span> <span class="variable">tag</span> <span class="keyword">in</span> <span class="variable">html_tags</span></span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">extracted_text</span>)  <span class="comment"># ['标题', '段落内容', '链接']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 提取文件扩展名</span></span>
                    <span class="code-line"><span class="variable">filenames</span> = [<span class="string">"document.pdf"</span>, <span class="string">"image.jpg"</span>, <span class="string">"script.py"</span>, <span class="string">"readme"</span>]</span>
                    <span class="code-line"><span class="variable">extensions</span> = [</span>
                    <span class="code-line">        <span class="variable">filename</span>.<span class="function">split</span>(<span class="string">"."</span>)[-<span class="number">1</span>] <span class="keyword">if</span> <span class="string">"."</span> <span class="keyword">in</span> <span class="variable">filename</span> <span class="keyword">else</span> <span class="string">"无扩展名"</span></span>
                        <span class="keyword">for</span> <span class="variable">filename</span> <span class="keyword">in</span> <span class="variable">filenames</span></span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">extensions</span>)  <span class="comment"># ['pdf', 'jpg', 'py', '无扩展名']</span></span>
                </div>

                <h3>文本变换和编码</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 简单的凯撒密码</span></span>
                    <span class="code-line"><span class="keyword">def</span> <span class="function">caesar_cipher</span>(<span class="variable">text</span>, <span class="variable">shift</span>):</span>
                    <span class="code-line">    <span class="keyword">return</span> <span class="string">""</span>.<span class="function">join</span>([</span>
                    <span class="code-line">        <span class="function">chr</span>((<span class="function">ord</span>(<span class="variable">char</span>) - <span class="function">ord</span>(<span class="string">'a'</span>) + <span class="variable">shift</span>) % <span class="number">26</span> + <span class="function">ord</span>(<span class="string">'a'</span>)) </span>
                    <span class="code-line">        <span class="keyword">if</span> <span class="variable">char</span>.<span class="function">islower</span>() <span class="keyword">else</span> <span class="variable">char</span></span>
                        <span class="keyword">for</span> <span class="variable">char</span> <span class="keyword">in</span> <span class="variable">text</span></span>
                    <span class="code-line">    ])</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="variable">message</span> = <span class="string">"hello world"</span></span>
                    <span class="code-line"><span class="variable">encrypted</span> = <span class="function">caesar_cipher</span>(<span class="variable">message</span>, <span class="number">3</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">encrypted</span>)  <span class="comment"># "khoor zruog"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 反转每个单词</span></span>
                    <span class="code-line"><span class="variable">sentence</span> = <span class="string">"Python is awesome"</span></span>
                    <span class="code-line"><span class="variable">reversed_words</span> = [<span class="variable">word</span>[::-<span class="number">1</span>] <span class="keyword">for</span> <span class="variable">word</span> <span class="keyword">in</span> <span class="variable">sentence</span>.<span class="function">split</span>()]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">" "</span>.<span class="function">join</span>(<span class="variable">reversed_words</span>))  <span class="comment"># "nohtyP si emosewa"</span></span>
                </div>

                <h3>正则表达式结合</h3>
                <div class="code-block">
                    <span class="code-line"><span class="keyword">import</span> <span class="variable">re</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 提取所有数字</span></span>
                    <span class="code-line"><span class="variable">text</span> = <span class="string">"订单号：12345，金额：299.99元，日期：2024-01-15"</span></span>
                    <span class="code-line"><span class="variable">numbers</span> = [<span class="function">float</span>(<span class="variable">match</span>) <span class="keyword">for</span> <span class="variable">match</span> <span class="keyword">in</span> <span class="variable">re</span>.<span class="function">findall</span>(<span class="string">r'\d+\.?\d*'</span>, <span class="variable">text</span>)]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">numbers</span>)  <span class="comment"># [12345.0, 299.99, 2024.0, 1.0, 15.0]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 验证邮箱格式</span></span>
                    <span class="code-line"><span class="variable">emails</span> = [<span class="string">"<EMAIL>"</span>, <span class="string">"invalid-email"</span>, <span class="string">"<EMAIL>"</span>]</span>
                    <span class="code-line"><span class="variable">valid_emails</span> = [</span>
                    <span class="code-line">        <span class="variable">email</span> <span class="keyword">for</span> <span class="variable">email</span> <span class="keyword">in</span> <span class="variable">emails</span> </span>
                    <span class="code-line">        <span class="keyword">if</span> <span class="variable">re</span>.<span class="function">match</span>(<span class="string">r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'</span>, <span class="variable">email</span>)</span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">valid_emails</span>)  <span class="comment"># ['<EMAIL>', '<EMAIL>']</span></span>
                </div>

                <div class="tips-box">
                    <p><strong>📝 字符串处理技巧：</strong></p>
                    <ul>
                        <li><strong>链式调用</strong>：合理使用字符串方法链</li>
                        <li><strong>正则表达式</strong>：复杂模式匹配的利器</li>
                        <li><strong>异常处理</strong>：注意字符串操作的边界情况</li>
                        <li><strong>性能优化</strong>：大量文本处理考虑使用生成器</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第19页：列表生成式处理字典 -->
        <div class="slide">
            <div class="slide-number">19/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🗂️</span>列表生成式处理字典</h2>
                
                <h3>从字典中提取数据</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">students</span> = [</span>
                    <span class="code-line">        {<span class="string">"name"</span>: <span class="string">"张三"</span>, <span class="string">"age"</span>: <span class="number">20</span>, <span class="string">"score"</span>: <span class="number">85</span>, <span class="string">"city"</span>: <span class="string">"北京"</span>},</span>
                    <span class="code-line">        {<span class="string">"name"</span>: <span class="string">"李四"</span>, <span class="string">"age"</span>: <span class="number">19</span>, <span class="string">"score"</span>: <span class="number">92</span>, <span class="string">"city"</span>: <span class="string">"上海"</span>},</span>
                    <span class="code-line">        {<span class="string">"name"</span>: <span class="string">"王五"</span>, <span class="string">"age"</span>: <span class="number">21</span>, <span class="string">"score"</span>: <span class="number">78</span>, <span class="string">"city"</span>: <span class="string">"广州"</span>}</span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 提取所有姓名</span></span>
                    <span class="code-line"><span class="variable">names</span> = [<span class="variable">student</span>[<span class="string">"name"</span>] <span class="keyword">for</span> <span class="variable">student</span> <span class="keyword">in</span> <span class="variable">students</span>]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">names</span>)  <span class="comment"># ['张三', '李四', '王五']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 提取高分学生的姓名</span></span>
                    <span class="code-line"><span class="variable">high_scorers</span> = [</span>
                    <span class="code-line">        <span class="variable">student</span>[<span class="string">"name"</span>] <span class="keyword">for</span> <span class="variable">student</span> <span class="keyword">in</span> <span class="variable">students</span> </span>
                    <span class="code-line">        <span class="keyword">if</span> <span class="variable">student</span>[<span class="string">"score"</span>] >= <span class="number">85</span></span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">high_scorers</span>)  <span class="comment"># ['张三', '李四']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 计算平均分</span></span>
                    <span class="code-line"><span class="variable">scores</span> = [<span class="variable">student</span>[<span class="string">"score"</span>] <span class="keyword">for</span> <span class="variable">student</span> <span class="keyword">in</span> <span class="variable">students</span>]</span>
                    <span class="code-line"><span class="variable">average_score</span> = <span class="function">sum</span>(<span class="variable">scores</span>) / <span class="function">len</span>(<span class="variable">scores</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">f"平均分: {average_score:.1f}"</span>)  <span class="comment"># 平均分: 85.0</span></span>
                </div>

                <h3>创建新的字典结构</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 创建姓名到分数的映射</span></span>
                    <span class="code-line"><span class="variable">name_score_pairs</span> = [</span>
                    <span class="code-line">        (<span class="variable">student</span>[<span class="string">"name"</span>], <span class="variable">student</span>[<span class="string">"score"</span>]) </span>
                    <span class="code-line">        <span class="keyword">for</span> <span class="variable">student</span> <span class="keyword">in</span> <span class="variable">students</span></span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"><span class="variable">score_dict</span> = <span class="function">dict</span>(<span class="variable">name_score_pairs</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">score_dict</span>)  <span class="comment"># {'张三': 85, '李四': 92, '王五': 78}</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 创建格式化的学生信息</span></span>
                    <span class="code-line"><span class="variable">student_info</span> = [</span>
                    <span class="code-line">        {</span>
                    <span class="code-line">            <span class="string">"id"</span>: <span class="variable">i</span> + <span class="number">1</span>,</span>
                    <span class="code-line">            <span class="string">"name"</span>: <span class="variable">student</span>[<span class="string">"name"</span>],</span>
                    <span class="code-line">            <span class="string">"grade"</span>: <span class="string">"优秀"</span> <span class="keyword">if</span> <span class="variable">student</span>[<span class="string">"score"</span>] >= <span class="number">90</span> <span class="keyword">else</span> <span class="string">"良好"</span> <span class="keyword">if</span> <span class="variable">student</span>[<span class="string">"score"</span>] >= <span class="number">80</span> <span class="keyword">else</span> <span class="string">"及格"</span>,</span>
                    <span class="code-line">            <span class="string">"location"</span>: <span class="variable">student</span>[<span class="string">"city"</span>]</span>
                    <span class="code-line">        }</span>
                    <span class="code-line">        <span class="keyword">for</span> <span class="variable">i</span>, <span class="variable">student</span> <span class="keyword">in</span> <span class="function">enumerate</span>(<span class="variable">students</span>)</span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">student_info</span>[<span class="number">0</span>])  <span class="comment"># {'id': 1, 'name': '张三', 'grade': '良好', 'location': '北京'}</span></span>
                </div>

                <h3>处理嵌套字典</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">company_data</span> = {</span>
                    <span class="code-line">        <span class="string">"departments"</span>: {</span>
                    <span class="code-line">            <span class="string">"技术部"</span>: {<span class="string">"employees"</span>: [<span class="string">"张三"</span>, <span class="string">"李四"</span>], <span class="string">"budget"</span>: <span class="number">500000</span>},</span>
                    <span class="code-line">            <span class="string">"销售部"</span>: {<span class="string">"employees"</span>: [<span class="string">"王五"</span>, <span class="string">"赵六"</span>, <span class="string">"钱七"</span>], <span class="string">"budget"</span>: <span class="number">300000</span>},</span>
                    <span class="code-line">            <span class="string">"人事部"</span>: {<span class="string">"employees"</span>: [<span class="string">"孙八"</span>], <span class="string">"budget"</span>: <span class="number">200000</span>}</span>
                    <span class="code-line">        }</span>
                    <span class="code-line">    }</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 提取所有员工</span></span>
                    <span class="code-line"><span class="variable">all_employees</span> = [</span>
                    <span class="code-line">        <span class="variable">employee</span> <span class="keyword">for</span> <span class="variable">dept_info</span> <span class="keyword">in</span> <span class="variable">company_data</span>[<span class="string">"departments"</span>].<span class="function">values</span>()</span>
                    <span class="code-line">        <span class="keyword">for</span> <span class="variable">employee</span> <span class="keyword">in</span> <span class="variable">dept_info</span>[<span class="string">"employees"</span>]</span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">all_employees</span>)  <span class="comment"># ['张三', '李四', '王五', '赵六', '钱七', '孙八']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 计算每个部门的人均预算</span></span>
                    <span class="code-line"><span class="variable">dept_budgets</span> = [</span>
                    <span class="code-line">        {</span>
                    <span class="code-line">            <span class="string">"department"</span>: <span class="variable">dept_name</span>,</span>
                    <span class="code-line">            <span class="string">"per_employee_budget"</span>: <span class="variable">dept_info</span>[<span class="string">"budget"</span>] / <span class="function">len</span>(<span class="variable">dept_info</span>[<span class="string">"employees"</span>])</span>
                    <span class="code-line">        }</span>
                    <span class="code-line">        <span class="keyword">for</span> <span class="variable">dept_name</span>, <span class="variable">dept_info</span> <span class="keyword">in</span> <span class="variable">company_data</span>[<span class="string">"departments"</span>].<span class="function">items</span>()</span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">dept_budgets</span>[<span class="number">0</span>])  <span class="comment"># {'department': '技术部', 'per_employee_budget': 250000.0}</span></span>
                </div>

                <h3>数据聚合和分组</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">sales_data</span> = [</span>
                    <span class="code-line">        {<span class="string">"product"</span>: <span class="string">"手机"</span>, <span class="string">"category"</span>: <span class="string">"电子产品"</span>, <span class="string">"sales"</span>: <span class="number">15000</span>, <span class="string">"region"</span>: <span class="string">"华北"</span>},</span>
                    <span class="code-line">        {<span class="string">"product"</span>: <span class="string">"笔记本"</span>, <span class="string">"category"</span>: <span class="string">"电子产品"</span>, <span class="string">"sales"</span>: <span class="number">12000</span>, <span class="string">"region"</span>: <span class="string">"华南"</span>},</span>
                    <span class="code-line">        {<span class="string">"product"</span>: <span class="string">"衣服"</span>, <span class="string">"category"</span>: <span class="string">"服装"</span>, <span class="string">"sales"</span>: <span class="number">8000</span>, <span class="string">"region"</span>: <span class="string">"华北"</span>},</span>
                    <span class="code-line">        {<span class="string">"product"</span>: <span class="string">"鞋子"</span>, <span class="string">"category"</span>: <span class="string">"服装"</span>, <span class="string">"sales"</span>: <span class="number">6000</span>, <span class="string">"region"</span>: <span class="string">"华南"</span>}</span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 按类别统计销售额</span></span>
                    <span class="code-line"><span class="variable">categories</span> = <span class="function">list</span>(<span class="function">set</span>([<span class="variable">item</span>[<span class="string">"category"</span>] <span class="keyword">for</span> <span class="variable">item</span> <span class="keyword">in</span> <span class="variable">sales_data</span>]))</span>
                    <span class="code-line"><span class="variable">category_sales</span> = [</span>
                    <span class="code-line">        {</span>
                    <span class="code-line">            <span class="string">"category"</span>: <span class="variable">category</span>,</span>
                    <span class="code-line">            <span class="string">"total_sales"</span>: <span class="function">sum</span>([</span>
                    <span class="code-line">                <span class="variable">item</span>[<span class="string">"sales"</span>] <span class="keyword">for</span> <span class="variable">item</span> <span class="keyword">in</span> <span class="variable">sales_data</span> </span>
                    <span class="code-line">                <span class="keyword">if</span> <span class="variable">item</span>[<span class="string">"category"</span>] == <span class="variable">category</span></span>
                    <span class="code-line">            ])</span>
                    <span class="code-line">        }</span>
                    <span class="code-line">        <span class="keyword">for</span> <span class="variable">category</span> <span class="keyword">in</span> <span class="variable">categories</span></span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">category_sales</span>)</span>
                    <span class="code-line"><span class="comment"># [{'category': '电子产品', 'total_sales': 27000}, {'category': '服装', 'total_sales': 14000}]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 找出每个地区的畅销产品</span></span>
                    <span class="code-line"><span class="variable">regions</span> = <span class="function">list</span>(<span class="function">set</span>([<span class="variable">item</span>[<span class="string">"region"</span>] <span class="keyword">for</span> <span class="variable">item</span> <span class="keyword">in</span> <span class="variable">sales_data</span>]))</span>
                    <span class="code-line"><span class="variable">top_products_by_region</span> = [</span>
                    <span class="code-line">        {</span>
                    <span class="code-line">            <span class="string">"region"</span>: <span class="variable">region</span>,</span>
                    <span class="code-line">            <span class="string">"top_product"</span>: <span class="function">max</span>([</span>
                    <span class="code-line">                <span class="variable">item</span> <span class="keyword">for</span> <span class="variable">item</span> <span class="keyword">in</span> <span class="variable">sales_data</span> </span>
                    <span class="code-line">                <span class="keyword">if</span> <span class="variable">item</span>[<span class="string">"region"</span>] == <span class="variable">region</span></span>
                    <span class="code-line">            ], <span class="variable">key</span>=<span class="keyword">lambda</span> <span class="variable">x</span>: <span class="variable">x</span>[<span class="string">"sales"</span>])[<span class="string">"product"</span>]</span>
                    <span class="code-line">        }</span>
                    <span class="code-line">        <span class="keyword">for</span> <span class="variable">region</span> <span class="keyword">in</span> <span class="variable">regions</span></span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">top_products_by_region</span>)</span>
                </div>

                <div class="highlight-box">
                    <p><strong>📊 字典处理要点：</strong></p>
                    <ul>
                        <li><strong>键的存在性</strong>：使用 <code>get()</code> 方法或检查键是否存在</li>
                        <li><strong>数据一致性</strong>：确保所有字典具有相同的键结构</li>
                        <li><strong>性能优化</strong>：大量数据聚合考虑使用pandas</li>
                        <li><strong>异常处理</strong>：防范键不存在或类型错误</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第20页：列表生成式vs传统循环对比 -->
        <div class="slide">
            <div class="slide-number">20/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🌟</span>列表生成式vs传统循环对比</h2>
                
                <h3>代码简洁性对比</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 任务：生成1-10的平方数</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 传统循环方式（8行）</span></span>
                    <span class="code-line"><span class="variable">squares_traditional</span> = []</span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1</span>, <span class="number">11</span>):</span>
                    <span class="code-line">    <span class="variable">square</span> = <span class="variable">i</span> ** <span class="number">2</span></span>
                    <span class="code-line">    <span class="variable">squares_traditional</span>.<span class="function">append</span>(<span class="variable">square</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 列表生成式（1行）</span></span>
                    <span class="code-line"><span class="variable">squares_comprehension</span> = [<span class="variable">i</span> ** <span class="number">2</span> <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1</span>, <span class="number">11</span>)]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">squares_comprehension</span>)  <span class="comment"># [1, 4, 9, 16, 25, 36, 49, 64, 81, 100]</span></span>
                </div>

                <h3>性能对比测试</h3>
                <div class="code-block">
                    <span class="code-line"><span class="keyword">import</span> <span class="variable">time</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="keyword">def</span> <span class="function">time_comparison</span>(<span class="variable">n</span>=<span class="number">100000</span>):</span>
                    <span class="code-line">    <span class="comment"># 测试传统循环</span></span>
                    <span class="code-line">    <span class="variable">start_time</span> = <span class="variable">time</span>.<span class="function">time</span>()</span>
                    <span class="code-line">    <span class="variable">result1</span> = []</span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="variable">n</span>):</span>
                    <span class="code-line">        <span class="variable">result1</span>.<span class="function">append</span>(<span class="variable">i</span> * <span class="number">2</span>)</span>
                    <span class="code-line">    <span class="variable">loop_time</span> = <span class="variable">time</span>.<span class="function">time</span>() - <span class="variable">start_time</span></span>
                    <span class="code-line"></span>
                    <span class="code-line">    <span class="comment"># 测试列表生成式</span></span>
                    <span class="code-line">    <span class="variable">start_time</span> = <span class="variable">time</span>.<span class="function">time</span>()</span>
                    <span class="code-line">    <span class="variable">result2</span> = [<span class="variable">i</span> * <span class="number">2</span> <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="variable">n</span>)]</span>
                    <span class="code-line">    <span class="variable">comp_time</span> = <span class="variable">time</span>.<span class="function">time</span>() - <span class="variable">start_time</span></span>
                    <span class="code-line"></span>
                    <span class="code-line">    <span class="function">print</span>(<span class="string">f"传统循环: {loop_time:.4f}秒"</span>)</span>
                    <span class="code-line">    <span class="function">print</span>(<span class="string">f"列表生成式: {comp_time:.4f}秒"</span>)</span>
                    <span class="code-line">    <span class="function">print</span>(<span class="string">f"性能提升: {loop_time/comp_time:.2f}倍"</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># time_comparison()  # 执行测试</span></span>
                </div>

                <h3>可读性对比</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 任务：提取及格学生的姓名</span></span>
                    <span class="code-line"><span class="variable">students</span> = [</span>
                    <span class="code-line">        {<span class="string">"name"</span>: <span class="string">"张三"</span>, <span class="string">"score"</span>: <span class="number">85</span>},</span>
                    <span class="code-line">        {<span class="string">"name"</span>: <span class="string">"李四"</span>, <span class="string">"score"</span>: <span class="number">55</span>},</span>
                    <span class="code-line">        {<span class="string">"name"</span>: <span class="string">"王五"</span>, <span class="string">"score"</span>: <span class="number">78</span>}</span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 传统循环方式</span></span>
                    <span class="code-line"><span class="variable">passed_students_loop</span> = []</span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">student</span> <span class="keyword">in</span> <span class="variable">students</span>:</span>
                    <span class="code-line">    <span class="keyword">if</span> <span class="variable">student</span>[<span class="string">"score"</span>] >= <span class="number">60</span>:</span>
                    <span class="code-line">        <span class="variable">passed_students_loop</span>.<span class="function">append</span>(<span class="variable">student</span>[<span class="string">"name"</span>])</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 列表生成式（更直观）</span></span>
                    <span class="code-line"><span class="variable">passed_students_comp</span> = [</span>
                    <span class="code-line">        <span class="variable">student</span>[<span class="string">"name"</span>] <span class="keyword">for</span> <span class="variable">student</span> <span class="keyword">in</span> <span class="variable">students</span> </span>
                    <span class="code-line">        <span class="keyword">if</span> <span class="variable">student</span>[<span class="string">"score"</span>] >= <span class="number">60</span></span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">passed_students_comp</span>)  <span class="comment"># ['张三', '王五']</span></span>
                </div>

                <h3>复杂场景对比</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 任务：创建二维坐标网格，过滤对角线</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 传统循环方式</span></span>
                    <span class="code-line"><span class="variable">coordinates_loop</span> = []</span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">3</span>):</span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">y</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">3</span>):</span>
                    <span class="code-line">        <span class="keyword">if</span> <span class="variable">x</span> != <span class="variable">y</span>:</span>
                    <span class="code-line">            <span class="variable">coordinate</span> = (<span class="variable">x</span>, <span class="variable">y</span>)</span>
                    <span class="code-line">            <span class="variable">coordinates_loop</span>.<span class="function">append</span>(<span class="variable">coordinate</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 列表生成式</span></span>
                    <span class="code-line"><span class="variable">coordinates_comp</span> = [</span>
                    <span class="code-line">        (<span class="variable">x</span>, <span class="variable">y</span>) <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">3</span>) </span>
                    <span class="code-line">        <span class="keyword">for</span> <span class="variable">y</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">3</span>) </span>
                    <span class="code-line">        <span class="keyword">if</span> <span class="variable">x</span> != <span class="variable">y</span></span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">coordinates_comp</span>)  <span class="comment"># [(0, 1), (0, 2), (1, 0), (1, 2), (2, 0), (2, 1)]</span></span>
                </div>

                <h3>选择指南</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># ✅ 适合列表生成式的场景</span></span>
                    <span class="code-line"><span class="comment"># 1. 简单的数据变换</span></span>
                    <span class="code-line"><span class="variable">doubled</span> = [<span class="variable">x</span> * <span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>]]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 2. 简单的过滤操作</span></span>
                    <span class="code-line"><span class="variable">evens</span> = [<span class="variable">x</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">10</span>) <span class="keyword">if</span> <span class="variable">x</span> % <span class="number">2</span> == <span class="number">0</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 3. 数据提取</span></span>
                    <span class="code-line"><span class="variable">names</span> = [<span class="variable">person</span>[<span class="string">"name"</span>] <span class="keyword">for</span> <span class="variable">person</span> <span class="keyword">in</span> <span class="variable">people</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># ❌ 不适合列表生成式的场景</span></span>
                    <span class="code-line"><span class="comment"># 1. 复杂的业务逻辑</span></span>
                    <span class="code-line"><span class="variable">results</span> = []</span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">item</span> <span class="keyword">in</span> <span class="variable">data</span>:</span>
                    <span class="code-line">    <span class="keyword">try</span>:</span>
                    <span class="code-line">        <span class="variable">processed</span> = <span class="function">complex_processing</span>(<span class="variable">item</span>)</span>
                    <span class="code-line">        <span class="keyword">if</span> <span class="function">validate</span>(<span class="variable">processed</span>):</span>
                    <span class="code-line">            <span class="variable">results</span>.<span class="function">append</span>(<span class="variable">processed</span>)</span>
                    <span class="code-line">        <span class="keyword">else</span>:</span>
                    <span class="code-line">            <span class="function">log_error</span>(<span class="variable">item</span>)</span>
                    <span class="code-line">    <span class="keyword">except</span> <span class="variable">Exception</span> <span class="keyword">as</span> <span class="variable">e</span>:</span>
                    <span class="code-line">        <span class="function">handle_error</span>(<span class="variable">e</span>, <span class="variable">item</span>)</span>
                </div>

                <div class="tips-box">
                    <p><strong>🎯 最佳实践建议：</strong></p>
                    <ul>
                        <li><strong>简洁优先</strong>：简单变换和过滤首选列表生成式</li>
                        <li><strong>可读性第一</strong>：超过2行的逻辑考虑传统循环</li>
                        <li><strong>性能考虑</strong>：列表生成式通常更快，但差距不大</li>
                        <li><strong>团队约定</strong>：统一代码风格，保持一致性</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第21页：性能优势和内存效率 -->
        <div class="slide">
            <div class="slide-number">21/30</div>
            <div class="slide-content">
                <h2><span class="emoji">⚡</span>性能优势和内存效率</h2>
                
                <h3>内存使用对比</h3>
                <div class="code-block">
                    <span class="code-line"><span class="keyword">import</span> <span class="variable">sys</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 传统循环</span></span>
                    <span class="code-line"><span class="variable">traditional_list</span> = []</span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1000</span>):</span>
                    <span class="code-line">    <span class="variable">traditional_list</span>.<span class="function">append</span>(<span class="variable">i</span> ** <span class="number">2</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 列表生成式</span></span>
                    <span class="code-line"><span class="variable">comprehension_list</span> = [<span class="variable">i</span> ** <span class="number">2</span> <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1000</span>)]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 生成器表达式（最节省内存）</span></span>
                    <span class="code-line"><span class="variable">generator_expr</span> = (<span class="variable">i</span> ** <span class="number">2</span> <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1000</span>))</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">f"传统列表内存: {sys.getsizeof(traditional_list)} 字节"</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">f"生成式列表内存: {sys.getsizeof(comprehension_list)} 字节"</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">f"生成器内存: {sys.getsizeof(generator_expr)} 字节"</span>)</span>
                </div>

                <h3>CPU效率优势</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 列表生成式在C层面优化，减少Python函数调用开销</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 示例：处理大量数据的效率对比</span></span>
                    <span class="code-line"><span class="keyword">import</span> <span class="variable">time</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="keyword">def</span> <span class="function">benchmark_performance</span>():</span>
                    <span class="code-line">    <span class="variable">data</span> = <span class="function">list</span>(<span class="function">range</span>(<span class="number">100000</span>))</span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="comment"># 方法1：传统循环 + append</span></span>
                    <span class="code-line">    <span class="variable">start</span> = <span class="variable">time</span>.<span class="function">time</span>()</span>
                    <span class="code-line">    <span class="variable">result1</span> = []</span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">data</span>:</span>
                    <span class="code-line">        <span class="keyword">if</span> <span class="variable">x</span> % <span class="number">2</span> == <span class="number">0</span>:</span>
                    <span class="code-line">            <span class="variable">result1</span>.<span class="function">append</span>(<span class="variable">x</span> * <span class="number">2</span>)</span>
                    <span class="code-line">    <span class="variable">time1</span> = <span class="variable">time</span>.<span class="function">time</span>() - <span class="variable">start</span></span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="comment"># 方法2：列表生成式</span></span>
                    <span class="code-line">    <span class="variable">start</span> = <span class="variable">time</span>.<span class="function">time</span>()</span>
                    <span class="code-line">    <span class="variable">result2</span> = [<span class="variable">x</span> * <span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">data</span> <span class="keyword">if</span> <span class="variable">x</span> % <span class="number">2</span> == <span class="number">0</span>]</span>
                    <span class="code-line">    <span class="variable">time2</span> = <span class="variable">time</span>.<span class="function">time</span>() - <span class="variable">start</span></span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="function">print</span>(<span class="string">f"传统方法: {time1:.4f}s"</span>)</span>
                    <span class="code-line">    <span class="function">print</span>(<span class="string">f"列表生成式: {time2:.4f}s"</span>)</span>
                    <span class="code-line">    <span class="function">print</span>(<span class="string">f"性能提升: {time1/time2:.2f}x"</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># benchmark_performance()</span></span>
                </div>

                <h3>适合大数据处理的技巧</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 1. 分批处理大数据</span></span>
                    <span class="code-line"><span class="keyword">def</span> <span class="function">process_large_data</span>(<span class="variable">data</span>, <span class="variable">batch_size</span>=<span class="number">1000</span>):</span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">0</span>, <span class="function">len</span>(<span class="variable">data</span>), <span class="variable">batch_size</span>):</span>
                    <span class="code-line">        <span class="variable">batch</span> = <span class="variable">data</span>[<span class="variable">i</span>:<span class="variable">i</span>+<span class="variable">batch_size</span>]</span>
                    <span class="code-line">        <span class="variable">processed_batch</span> = [<span class="function">expensive_operation</span>(<span class="variable">x</span>) <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">batch</span>]</span>
                    <span class="code-line">        <span class="keyword">yield</span> <span class="variable">processed_batch</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 2. 使用生成器表达式节省内存</span></span>
                    <span class="code-line"><span class="variable">large_numbers</span> = <span class="function">range</span>(<span class="number">1000000</span>)</span>
                    <span class="code-line"><span class="variable">squares_gen</span> = (<span class="variable">x</span> ** <span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">large_numbers</span> <span class="keyword">if</span> <span class="variable">x</span> % <span class="number">1000</span> == <span class="number">0</span>)</span>
                    <span class="code-line"><span class="comment"># 按需计算，不占用大量内存</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 3. 链式处理避免中间列表</span></span>
                    <span class="code-line"><span class="variable">result</span> = [<span class="variable">x</span> ** <span class="number">0.5</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> [<span class="variable">y</span> ** <span class="number">2</span> <span class="keyword">for</span> <span class="variable">y</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">100</span>) <span class="keyword">if</span> <span class="variable">y</span> % <span class="number">2</span> == <span class="number">0</span>]]</span>
                    <span class="code-line"><span class="comment"># 更好的方式：</span></span>
                    <span class="code-line"><span class="variable">better_result</span> = [<span class="function">abs</span>(<span class="variable">y</span>) <span class="keyword">for</span> <span class="variable">y</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">100</span>) <span class="keyword">if</span> <span class="variable">y</span> % <span class="number">2</span> == <span class="number">0</span>]</span>
                </div>

                <div class="highlight-box">
                    <p><strong>🚀 性能优化要点：</strong></p>
                    <ul>
                        <li><strong>内存效率</strong>：大数据处理优先考虑生成器</li>
                        <li><strong>CPU效率</strong>：简单操作列表生成式更快</li>
                        <li><strong>可读性平衡</strong>：不要为了性能牺牲可读性</li>
                        <li><strong>实际测试</strong>：具体场景具体分析，实测为准</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第22页：高级应用和实际案例 -->
        <div class="slide">
            <div class="slide-number">22/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🎪</span>高级应用和实际案例</h2>
                
                <h3>数据科学中的应用</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 数据清洗和预处理</span></span>
                    <span class="code-line"><span class="variable">raw_data</span> = [</span>
                    <span class="code-line">        {"name": "张三", "age": "25", "salary": "15000", "department": "技术部"},</span>
                    <span class="code-line">        {"name": "李四", "age": "invalid", "salary": "12000", "department": "销售部"},</span>
                    <span class="code-line">        {"name": "", "age": "30", "salary": "18000", "department": "技术部"},</span>
                    <span class="code-line">        {"name": "王五", "age": "28", "salary": "abc", "department": "人事部"}</span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 数据清洗流水线</span></span>
                    <span class="code-line"><span class="variable">cleaned_data</span> = [</span>
                    <span class="code-line">        {</span>
                    <span class="code-line">            "name": <span class="variable">record</span>["name"].<span class="function">strip</span>(),</span>
                    <span class="code-line">            "age": <span class="function">int</span>(<span class="variable">record</span>["age"]) <span class="keyword">if</span> <span class="variable">record</span>["age"].<span class="function">isdigit</span>() <span class="keyword">else</span> <span class="keyword">None</span>,</span>
                    <span class="code-line">            "salary": <span class="function">int</span>(<span class="variable">record</span>["salary"]) <span class="keyword">if</span> <span class="variable">record</span>["salary"].<span class="function">isdigit</span>() <span class="keyword">else</span> <span class="keyword">None</span>,</span>
                    <span class="code-line">            "department": <span class="variable">record</span>["department"]</span>
                    <span class="code-line">        }</span>
                    <span class="code-line">        <span class="keyword">for</span> <span class="variable">record</span> <span class="keyword">in</span> <span class="variable">raw_data</span></span>
                    <span class="code-line">        <span class="keyword">if</span> <span class="variable">record</span>["name"].<span class="function">strip</span>()  <span class="comment"># 过滤空名字</span></span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 进一步过滤有效数据</span></span>
                    <span class="code-line"><span class="variable">valid_data</span> = [</span>
                    <span class="code-line">        <span class="variable">record</span> <span class="keyword">for</span> <span class="variable">record</span> <span class="keyword">in</span> <span class="variable">cleaned_data</span></span>
                    <span class="code-line">        <span class="keyword">if</span> <span class="variable">record</span>["age"] <span class="keyword">is not</span> <span class="keyword">None</span> <span class="keyword">and</span> <span class="variable">record</span>["salary"] <span class="keyword">is not</span> <span class="keyword">None</span></span>
                    <span class="code-line">    ]</span>
                </div>

                <h3>Web开发中的应用</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># API响应数据处理</span></span>
                    <span class="code-line"><span class="variable">api_response</span> = {</span>
                    <span class="code-line">        "users": [</span>
                    <span class="code-line">            {"id": 1, "name": "张三", "active": True, "posts": [1, 2, 3]},</span>
                    <span class="code-line">            {"id": 2, "name": "李四", "active": False, "posts": []},</span>
                    <span class="code-line">            {"id": 3, "name": "王五", "active": True, "posts": [4, 5]}</span>
                    <span class="code-line">        ]</span>
                    <span class="code-line">    }</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 提取活跃用户的精简信息</span></span>
                    <span class="code-line"><span class="variable">active_users_summary</span> = [</span>
                    <span class="code-line">        {</span>
                    <span class="code-line">            "user_id": <span class="variable">user</span>["id"],</span>
                    <span class="code-line">            "name": <span class="variable">user</span>["name"],</span>
                    <span class="code-line">            "post_count": <span class="function">len</span>(<span class="variable">user</span>["posts"]),</span>
                    <span class="code-line">            "is_prolific": <span class="function">len</span>(<span class="variable">user</span>["posts"]) > <span class="number">2</span></span>
                    <span class="code-line">        }</span>
                    <span class="code-line">        <span class="keyword">for</span> <span class="variable">user</span> <span class="keyword">in</span> <span class="variable">api_response</span>["users"]</span>
                    <span class="code-line">        <span class="keyword">if</span> <span class="variable">user</span>["active"]</span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># HTML模板数据生成</span></span>
                    <span class="code-line"><span class="variable">menu_items</span> = [</span>
                    <span class="code-line">        {"name": "首页", "url": "/", "icon": "home"},</span>
                    <span class="code-line">        {"name": "产品", "url": "/products", "icon": "box"},</span>
                    <span class="code-line">        {"name": "关于", "url": "/about", "icon": "info"}</span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="variable">html_menu</span> = <span class="string">'\n'</span>.<span class="function">join</span>([</span>
                    <span class="code-line">        <span class="string">f'<li><a href="{item["url"]}"><i class="{item["icon"]}"></i>{item["name"]}</a></li>'</span></span>
                    <span class="code-line">        <span class="keyword">for</span> <span class="variable">item</span> <span class="keyword">in</span> <span class="variable">menu_items</span></span>
                    <span class="code-line">    ])</span>
                </div>

                <h3>算法和数据结构应用</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 矩阵操作</span></span>
                    <span class="code-line"><span class="keyword">def</span> <span class="function">matrix_multiply</span>(<span class="variable">A</span>, <span class="variable">B</span>):</span>
                    <span class="code-line">    <span class="string">"""使用列表生成式实现矩阵乘法"""</span></span>
                    <span class="code-line">    <span class="keyword">return</span> [</span>
                    <span class="code-line">        [<span class="function">sum</span>(<span class="variable">A</span>[<span class="variable">i</span>][<span class="variable">k</span>] * <span class="variable">B</span>[<span class="variable">k</span>][<span class="variable">j</span>] <span class="keyword">for</span> <span class="variable">k</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="function">len</span>(<span class="variable">B</span>)))</span>
                    <span class="code-line">         <span class="keyword">for</span> <span class="variable">j</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="function">len</span>(<span class="variable">B</span>[<span class="number">0</span>]))]</span>
                    <span class="code-line">        <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="function">len</span>(<span class="variable">A</span>))</span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 图算法：邻接表生成</span></span>
                    <span class="code-line"><span class="variable">edges</span> = [(<span class="number">0</span>, <span class="number">1</span>), (<span class="number">0</span>, <span class="number">2</span>), (<span class="number">1</span>, <span class="number">2</span>), (<span class="number">2</span>, <span class="number">3</span>)]</span>
                    <span class="code-line"><span class="variable">vertices</span> = <span class="function">set</span>([<span class="variable">v</span> <span class="keyword">for</span> <span class="variable">edge</span> <span class="keyword">in</span> <span class="variable">edges</span> <span class="keyword">for</span> <span class="variable">v</span> <span class="keyword">in</span> <span class="variable">edge</span>])</span>
                    <span class="code-line"><span class="variable">adjacency_list</span> = {</span>
                    <span class="code-line">        <span class="variable">v</span>: [<span class="variable">neighbor</span> <span class="keyword">for</span> <span class="variable">u</span>, <span class="variable">neighbor</span> <span class="keyword">in</span> <span class="variable">edges</span> <span class="keyword">if</span> <span class="variable">u</span> == <span class="variable">v</span>]</span>
                    <span class="code-line">        <span class="keyword">for</span> <span class="variable">v</span> <span class="keyword">in</span> <span class="variable">vertices</span></span>
                    <span class="code-line">    }</span>
                </div>

                <h3>文件和系统操作</h3>
                <div class="code-block">
                    <span class="code-line"><span class="keyword">import</span> <span class="variable">os</span></span>
                    <span class="code-line"><span class="keyword">from</span> <span class="variable">pathlib</span> <span class="keyword">import</span> <span class="variable">Path</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 文件批处理</span></span>
                    <span class="code-line"><span class="variable">project_root</span> = <span class="variable">Path</span>(<span class="string">"/path/to/project"</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 查找所有Python文件并统计行数</span></span>
                    <span class="code-line"><span class="variable">python_files_info</span> = [</span>
                    <span class="code-line">        {</span>
                    <span class="code-line">            "file": <span class="variable">file_path</span>.<span class="variable">name</span>,</span>
                    <span class="code-line">            "path": <span class="function">str</span>(<span class="variable">file_path</span>),</span>
                    <span class="code-line">            "lines": <span class="function">len</span>(<span class="variable">file_path</span>.<span class="function">read_text</span>().<span class="function">splitlines</span>())</span>
                    <span class="code-line">        }</span>
                    <span class="code-line">        <span class="keyword">for</span> <span class="variable">file_path</span> <span class="keyword">in</span> <span class="variable">project_root</span>.<span class="function">rglob</span>(<span class="string">"*.py"</span>)</span>
                    <span class="code-line">        <span class="keyword">if</span> <span class="variable">file_path</span>.<span class="function">is_file</span>()</span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 日志文件分析</span></span>
                    <span class="code-line"><span class="variable">log_lines</span> = [</span>
                    <span class="code-line">        <span class="string">"2024-01-15 10:30:25 ERROR Connection failed"</span>,</span>
                    <span class="code-line">        <span class="string">"2024-01-15 10:30:26 INFO Retrying connection"</span>,</span>
                    <span class="code-line">        <span class="string">"2024-01-15 10:30:27 ERROR Connection failed again"</span></span>
                    <span class="code-line">    ]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="variable">error_entries</span> = [</span>
                    <span class="code-line">        {</span>
                    <span class="code-line">            "timestamp": <span class="variable">line</span>.<span class="function">split</span>()[:<span class="number">2</span>],</span>
                    <span class="code-line">            "level": <span class="variable">line</span>.<span class="function">split</span>()[<span class="number">2</span>],</span>
                    <span class="code-line">            "message": <span class="string">" "</span>.<span class="function">join</span>(<span class="variable">line</span>.<span class="function">split</span>()[<span class="number">3</span>:])</span>
                    <span class="code-line">        }</span>
                    <span class="code-line">        <span class="keyword">for</span> <span class="variable">line</span> <span class="keyword">in</span> <span class="variable">log_lines</span></span>
                    <span class="code-line">        <span class="keyword">if</span> <span class="string">"ERROR"</span> <span class="keyword">in</span> <span class="variable">line</span></span>
                    <span class="code-line">    ]</span>
                </div>

                <div class="tips-box">
                    <p><strong>💼 实际应用建议：</strong></p>
                    <ul>
                        <li><strong>数据处理</strong>：ETL流水线中的数据变换</li>
                        <li><strong>配置管理</strong>：动态生成配置文件和模板</li>
                        <li><strong>测试数据</strong>：快速生成测试用例和Mock数据</li>
                        <li><strong>报表生成</strong>：从原始数据提取统计信息</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第23页：AI编程助手简介 -->
        <div class="slide">
            <div class="slide-number">23/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🤖</span>AI编程助手简介</h2>
                
                <h3>主流AI编程工具</h3>
                <div class="ai-tool">
                    <h4><span class="emoji">💬</span> ChatGPT / Claude</h4>
                    <ul>
                        <li><strong>优势</strong>：对话式交互，解释详细，适合学习</li>
                        <li><strong>适用场景</strong>：代码解释、算法设计、调试帮助</li>
                        <li><strong>使用技巧</strong>：提供完整上下文，分步骤提问</li>
                    </ul>
                </div>

                <div class="ai-tool">
                    <h4><span class="emoji">⚡</span> GitHub Copilot</h4>
                    <ul>
                        <li><strong>优势</strong>：IDE集成，实时代码补全</li>
                        <li><strong>适用场景</strong>：快速编码，重复性代码生成</li>
                        <li><strong>使用技巧</strong>：写好注释，让AI理解意图</li>
                    </ul>
                </div>

                <div class="ai-tool">
                    <h4><span class="emoji">🔍</span> Cursor / CodeGPT</h4>
                    <ul>
                        <li><strong>优势</strong>：专门为编程设计的IDE</li>
                        <li><strong>适用场景</strong>：整个项目的代码重构和优化</li>
                        <li><strong>使用技巧</strong>：配合代码库上下文使用</li>
                    </ul>
                </div>

                <h3>AI辅助学习Python列表的优势</h3>
                <div class="highlight-box">
                    <p><strong>🎯 学习加速器：</strong></p>
                    <ul>
                        <li><strong>即时答疑</strong>：随时解答语法和概念问题</li>
                        <li><strong>代码审查</strong>：帮助发现代码中的问题和改进点</li>
                        <li><strong>实例生成</strong>：根据需求快速生成练习代码</li>
                        <li><strong>最佳实践</strong>：推荐Pythonic的编码风格</li>
                    </ul>
                </div>

                <h3>AI工具的局限性</h3>
                <div class="tips-box">
                    <p><strong>⚠️ 注意事项：</strong></p>
                    <ul>
                        <li><strong>不是万能的</strong>：复杂业务逻辑仍需人工设计</li>
                        <li><strong>可能有错误</strong>：生成的代码需要验证和测试</li>
                        <li><strong>缺乏上下文</strong>：可能不了解项目的具体需求</li>
                        <li><strong>过度依赖</strong>：要保持独立思考和学习能力</li>
                    </ul>
                </div>

                <h3>选择合适的AI工具</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 学习阶段：使用ChatGPT/Claude</span></span>
                    <span class="code-line"><span class="comment"># 提问示例："请解释Python列表生成式的工作原理"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 开发阶段：使用GitHub Copilot</span></span>
                    <span class="code-line"><span class="comment"># 在IDE中写注释，让AI生成代码</span></span>
                    <span class="code-line"><span class="comment"># 示例：输入注释后按Tab键</span></span>
                    <span class="code-line"><span class="comment"># 创建一个函数，从学生列表中提取高分学生的姓名</span></span>
                    <span class="code-line"><span class="keyword">def</span> <span class="function">get_high_scorers</span>(<span class="variable">students</span>, <span class="variable">threshold</span>=<span class="number">85</span>):</span>
                    <span class="code-line">    <span class="keyword">return</span> [<span class="variable">s</span>[<span class="string">"name"</span>] <span class="keyword">for</span> <span class="variable">s</span> <span class="keyword">in</span> <span class="variable">students</span> <span class="keyword">if</span> <span class="variable">s</span>[<span class="string">"score"</span>] >= <span class="variable">threshold</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 调试阶段：结合AI和传统调试工具</span></span>
                    <span class="code-line"><span class="comment"># 向AI描述错误现象，获取调试建议</span></span>
                </div>
            </div>
        </div>

        <!-- 第24页：与AI对话的技巧 -->
        <div class="slide">
            <div class="slide-number">24/30</div>
            <div class="slide-content">
                <h2><span class="emoji">💬</span>与AI对话的技巧</h2>
                
                <h3>提问的艺术</h3>
                <div class="highlight-box">
                    <h4><span class="emoji">✅</span> 好的提问方式</h4>
                    <ul>
                        <li><strong>具体明确</strong>："如何用列表生成式过滤字典列表中年龄大于25的人？"</li>
                        <li><strong>提供背景</strong>："我是Python初学者，想要理解列表生成式的语法"</li>
                        <li><strong>明确需求</strong>："请给出代码示例并解释每个部分的作用"</li>
                        <li><strong>逐步深入</strong>："先教我基础语法，然后展示高级用法"</li>
                    </ul>
                </div>

                <div class="tips-box">
                    <h4><span class="emoji">❌</span> 避免的提问方式</h4>
                    <ul>
                        <li><strong>过于模糊</strong>："怎么用Python处理数据？"</li>
                        <li><strong>一次太多</strong>："教我所有Python列表操作和性能优化"</li>
                        <li><strong>缺乏上下文</strong>："这个代码为什么不工作？"（没提供代码）</li>
                        <li><strong>过度依赖</strong>："帮我写整个项目的代码"</li>
                    </ul>
                </div>

                <h3>有效的对话模式</h3>
                <div class="prompt-template">
                    <h4>📝 学习新概念模式</h4>
                    <p><strong>你：</strong> "我想学习Python列表生成式，请从最基础的语法开始解释"</p>
                    <p><strong>AI：</strong> [解释基础语法和简单示例]</p>
                    <p><strong>你：</strong> "我理解了基础语法，能否给我一个实际项目中的使用例子？"</p>
                    <p><strong>AI：</strong> [提供实际应用场景]</p>
                    <p><strong>你：</strong> "如果我要处理大量数据，有什么性能注意事项？"</p>
                </div>

                <div class="prompt-template">
                    <h4>🐛 调试问题模式</h4>
                    <p><strong>你：</strong> "我有以下代码：[贴出代码]，报错信息是：[贴出错误]，请帮我分析问题"</p>
                    <p><strong>AI：</strong> [分析错误原因]</p>
                    <p><strong>你：</strong> "除了修复错误，有没有更好的写法？"</p>
                    <p><strong>AI：</strong> [提供优化建议]</p>
                </div>

                <h3>提高回答质量的技巧</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 技巧1：提供完整的代码上下文</span></span>
                    <span class="code-line"><span class="comment"># ❌ 不好的提问</span></span>
                    <span class="code-line"><span class="comment"># "这行代码有问题：[x for x in data if x > 5]"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># ✅ 好的提问</span></span>
                    <span class="code-line"><span class="comment"># "我想从学生分数列表中筛选高分，以下代码报错："</span></span>
                    <span class="code-line"><span class="variable">students_scores</span> = [<span class="string">"85"</span>, <span class="string">"92"</span>, <span class="string">"abc"</span>, <span class="string">"78"</span>]</span>
                    <span class="code-line"><span class="variable">high_scores</span> = [<span class="variable">x</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">students_scores</span> <span class="keyword">if</span> <span class="variable">x</span> > <span class="number">85</span>]</span>
                    <span class="code-line"><span class="comment"># 错误：TypeError: '>' not supported between instances of 'str' and 'int'</span></span>
                </div>

                <div class="code-block">
                    <span class="code-line"><span class="comment"># 技巧2：指定期望的回答格式</span></span>
                    <span class="code-line"><span class="comment"># "请用以下格式回答：</span></span>
                    <span class="code-line"><span class="comment"># 1. 问题分析</span></span>
                    <span class="code-line"><span class="comment"># 2. 解决方案代码</span></span>
                    <span class="code-line"><span class="comment"># 3. 代码解释</span></span>
                    <span class="code-line"><span class="comment"># 4. 最佳实践建议"</span></span>
                </div>

                <h3>建立学习循环</h3>
                <div class="ai-tool">
                    <h4>🔄 持续改进模式</h4>
                    <ol>
                        <li><strong>学习</strong>：向AI学习新概念和技巧</li>
                        <li><strong>实践</strong>：自己编写代码应用所学知识</li>
                        <li><strong>反思</strong>：向AI询问代码改进建议</li>
                        <li><strong>优化</strong>：根据建议重构和优化代码</li>
                        <li><strong>拓展</strong>：探索相关的高级主题</li>
                    </ol>
                </div>

                <div class="highlight-box">
                    <p><strong>🎯 对话效果最大化：</strong></p>
                    <ul>
                        <li><strong>保持好奇心</strong>：多问"为什么"和"如何"</li>
                        <li><strong>要求解释</strong>：不只要代码，还要理解原理</li>
                        <li><strong>寻求替代方案</strong>：询问不同的实现方式</li>
                        <li><strong>关注最佳实践</strong>：学习专业的编码规范</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第25页：编写有效提示词 -->
        <div class="slide">
            <div class="slide-number">25/30</div>
            <div class="slide-content">
                <h2><span class="emoji">📝</span>编写有效提示词</h2>
                
                <h3>提示词模板结构</h3>
                <div class="prompt-template">
                    <h4>🏗️ 基础提示词模板</h4>
                    <p><strong>角色设定</strong> + <strong>任务描述</strong> + <strong>具体要求</strong> + <strong>输出格式</strong></p>
                    <br>
                    <p><strong>示例：</strong></p>
                    <p>"你是一位经验丰富的Python老师。请教我如何使用列表生成式处理学生数据。要求包含代码示例、详细解释和最佳实践建议。请用代码块格式展示示例。"</p>
                </div>

                <h3>针对列表操作的专用提示词</h3>
                <div class="prompt-template">
                    <h4>📊 数据处理提示词</h4>
                    <p>"我有一个包含学生信息的字典列表：[{name: '张三', age: 20, scores: [85, 90, 78]}...]。请帮我：</p>
                    <ol>
                        <li>用列表生成式提取所有学生姓名</li>
                        <li>筛选平均分超过80的学生</li>
                        <li>创建姓名到平均分的映射字典</li>
                        <li>解释每个步骤的原理和性能考虑</li>
                    </ol>
                    <p>请提供完整可运行的代码示例。"</p>
                </div>

                <div class="prompt-template">
                    <h4>🐛 调试帮助提示词</h4>
                    <p>"我正在学习Python列表生成式，以下代码运行出错：</p>
                    <p><code>[代码块]</code></p>
                    <p>错误信息：<code>[错误信息]</code></p>
                    <p>请帮我：</p>
                    <ol>
                        <li>分析错误原因</li>
                        <li>提供修正后的代码</li>
                        <li>解释正确的语法规则</li>
                        <li>给出避免类似错误的建议</li>
                    </ol>"
                </div>

                <h3>进阶提示词技巧</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 技巧1：分步骤引导</span></span>
                    <span class="code-line"><span class="comment"># "请按以下步骤教我列表生成式：</span></span>
                    <span class="code-line"><span class="comment"># Step 1: 解释基本语法结构</span></span>
                    <span class="code-line"><span class="comment"># Step 2: 展示简单的数值变换例子</span></span>
                    <span class="code-line"><span class="comment"># Step 3: 添加条件过滤</span></span>
                    <span class="code-line"><span class="comment"># Step 4: 处理嵌套循环</span></span>
                    <span class="code-line"><span class="comment"># Step 5: 实际项目应用场景"</span></span>
                </div>

                <div class="code-block">
                    <span class="code-line"><span class="comment"># 技巧2：指定学习风格</span></span>
                    <span class="code-line"><span class="comment"># "我是视觉学习者，请用图表或类比的方式解释列表生成式"</span></span>
                    <span class="code-line"><span class="comment"># "我喜欢通过实际例子学习，请提供3-5个不同场景的应用"</span></span>
                    <span class="code-line"><span class="comment"># "我需要深入理解原理，请解释Python内部如何执行列表生成式"</span></span>
                </div>

                <h3>特定场景的提示词模板</h3>
                <div class="prompt-template">
                    <h4>🔍 性能优化提示词</h4>
                    <p>"我需要处理一个包含100万条记录的数据集。请分析以下列表操作代码的性能，并提供优化建议：</p>
                    <p><code>[代码]</code></p>
                    <p>请包含：</p>
                    <ul>
                        <li>时间复杂度分析</li>
                        <li>内存使用评估</li>
                        <li>优化后的代码版本</li>
                        <li>何时使用生成器替代列表</li>
                    </ul>"
                </div>

                <div class="prompt-template">
                    <h4>🎨 代码审查提示词</h4>
                    <p>"请以高级Python开发者的视角审查我的列表处理代码：</p>
                    <p><code>[代码]</code></p>
                    <p>关注点：</p>
                    <ul>
                        <li>代码可读性和Pythonic风格</li>
                        <li>错误处理和边界情况</li>
                        <li>性能优化机会</li>
                        <li>是否符合PEP 8规范</li>
                        <li>建议的重构方向</li>
                    </ul>"
                </div>

                <h3>提示词优化技巧</h3>
                <div class="highlight-box">
                    <p><strong>✨ 提升回答质量的要素：</strong></p>
                    <ul>
                        <li><strong>上下文丰富</strong>：提供足够的背景信息</li>
                        <li><strong>目标明确</strong>：清楚说明想要达到的效果</li>
                        <li><strong>约束具体</strong>：明确限制条件和要求</li>
                        <li><strong>格式规范</strong>：指定期望的输出格式</li>
                        <li><strong>示例引导</strong>：提供期望回答的模板</li>
                    </ul>
                </div>

                <div class="tips-box">
                    <p><strong>⚠️ 常见提示词误区：</strong></p>
                    <ul>
                        <li><strong>过于宽泛</strong>：避免"教我Python"这样的提问</li>
                        <li><strong>假设过多</strong>：不要假设AI了解你的项目背景</li>
                        <li><strong>一次太多</strong>：避免在一个提示词中包含太多不相关问题</li>
                        <li><strong>缺乏反馈</strong>：没有告诉AI回答是否满足需求</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第26页：AI帮助调试和优化 -->
        <div class="slide">
            <div class="slide-number">26/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🛠️</span>AI帮助调试和优化</h2>
                
                <h3>常见错误诊断</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 错误1：语法错误</span></span>
                    <span class="code-line"><span class="comment"># ❌ 问题代码</span></span>
                    <span class="code-line"><span class="variable">result</span> = [<span class="variable">x</span> * <span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">10</span>) <span class="keyword">if</span> <span class="variable">x</span> % <span class="number">2</span> = <span class="number">0</span>]  <span class="comment"># 错误：使用=而不是==</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># AI提示词：</span></span>
                    <span class="code-line"><span class="comment"># "以下代码报语法错误，请帮我找出问题：[代码]"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># ✅ 修正后</span></span>
                    <span class="code-line"><span class="variable">result</span> = [<span class="variable">x</span> * <span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">10</span>) <span class="keyword">if</span> <span class="variable">x</span> % <span class="number">2</span> == <span class="number">0</span>]</span>
                </div>

                <div class="code-block">
                    <span class="code-line"><span class="comment"># 错误2：类型错误</span></span>
                    <span class="code-line"><span class="comment"># ❌ 问题代码</span></span>
                    <span class="code-line"><span class="variable">numbers</span> = [<span class="string">"1"</span>, <span class="string">"2"</span>, <span class="string">"3"</span>, <span class="string">"abc"</span>]</span>
                    <span class="code-line"><span class="variable">squared</span> = [<span class="function">int</span>(<span class="variable">x</span>) ** <span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">numbers</span>]  <span class="comment"># ValueError: invalid literal</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># AI提示词：</span></span>
                    <span class="code-line"><span class="comment"># "我的列表包含非数字字符串，如何安全地转换和过滤？"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># ✅ 修正后：添加异常处理</span></span>
                    <span class="code-line"><span class="variable">squared</span> = [<span class="function">int</span>(<span class="variable">x</span>) ** <span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">numbers</span> <span class="keyword">if</span> <span class="variable">x</span>.<span class="function">isdigit</span>()]</span>
                </div>

                <h3>性能优化指导</h3>
                <div class="prompt-template">
                    <h4>🚀 性能分析提示词模板</h4>
                    <p>"请分析以下代码的性能瓶颈并提供优化建议：</p>
                    <p><code>[代码块]</code></p>
                    <p>数据规模：[描述数据大小]</p>
                    <p>性能要求：[描述性能目标]</p>
                    <p>请包含：</p>
                    <ol>
                        <li>当前代码的时间复杂度分析</li>
                        <li>内存使用情况评估</li>
                        <li>优化后的代码版本</li>
                        <li>性能提升预期</li>
                    </ol>"
                </div>

                <div class="code-block">
                    <span class="code-line"><span class="comment"># AI优化示例</span></span>
                    <span class="code-line"><span class="comment"># 原始代码（性能较差）</span></span>
                    <span class="code-line"><span class="variable">data</span> = <span class="function">range</span>(<span class="number">1000000</span>)</span>
                    <span class="code-line"><span class="variable">result</span> = []</span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">data</span>:</span>
                    <span class="code-line">    <span class="keyword">if</span> <span class="variable">x</span> % <span class="number">2</span> == <span class="number">0</span>:</span>
                    <span class="code-line">        <span class="variable">result</span>.<span class="function">append</span>(<span class="variable">x</span> ** <span class="number">2</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># AI建议的优化版本</span></span>
                    <span class="code-line"><span class="variable">result</span> = [<span class="variable">x</span> ** <span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">0</span>, <span class="number">1000000</span>, <span class="number">2</span>)]  <span class="comment"># 直接生成偶数</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 进一步优化：使用生成器</span></span>
                    <span class="code-line"><span class="variable">result_gen</span> = (<span class="variable">x</span> ** <span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">0</span>, <span class="number">1000000</span>, <span class="number">2</span>))</span>
                </div>

                <h3>代码重构建议</h3>
                <div class="ai-tool">
                    <h4>🔧 重构对话流程</h4>
                    <ol>
                        <li><strong>提交原始代码</strong>：完整展示当前实现</li>
                        <li><strong>说明痛点</strong>：描述遇到的具体问题</li>
                        <li><strong>明确目标</strong>：说明期望的改进效果</li>
                        <li><strong>获取建议</strong>：AI提供重构方案</li>
                        <li><strong>逐步实施</strong>：分步骤应用建议</li>
                        <li><strong>验证效果</strong>：测试重构后的代码</li>
                    </ol>
                </div>

                <div class="code-block">
                    <span class="code-line"><span class="comment"># 重构示例：从复杂循环到列表生成式</span></span>
                    <span class="code-line"><span class="comment"># ❌ 原始复杂代码</span></span>
                    <span class="code-line"><span class="variable">students</span> = [/* 学生数据 */]</span>
                    <span class="code-line"><span class="variable">result</span> = []</span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">student</span> <span class="keyword">in</span> <span class="variable">students</span>:</span>
                    <span class="code-line">    <span class="keyword">if</span> <span class="variable">student</span>.<span class="function">get</span>(<span class="string">"active"</span>, <span class="keyword">False</span>):</span>
                    <span class="code-line">        <span class="variable">total_score</span> = <span class="function">sum</span>(<span class="variable">student</span>.<span class="function">get</span>(<span class="string">"scores"</span>, []))</span>
                    <span class="code-line">        <span class="keyword">if</span> <span class="variable">total_score</span> > <span class="number">240</span>:</span>
                    <span class="code-line">            <span class="variable">avg_score</span> = <span class="variable">total_score</span> / <span class="function">len</span>(<span class="variable">student</span>[<span class="string">"scores"</span>])</span>
                    <span class="code-line">            <span class="variable">result</span>.<span class="function">append</span>({</span>
                    <span class="code-line">                <span class="string">"name"</span>: <span class="variable">student</span>[<span class="string">"name"</span>],</span>
                    <span class="code-line">                <span class="string">"average"</span>: <span class="variable">avg_score</span></span>
                    <span class="code-line">            })</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># ✅ AI建议的重构版本</span></span>
                    <span class="code-line"><span class="variable">result</span> = [</span>
                    <span class="code-line">        {</span>
                    <span class="code-line">            <span class="string">"name"</span>: <span class="variable">s</span>[<span class="string">"name"</span>],</span>
                    <span class="code-line">            <span class="string">"average"</span>: <span class="function">sum</span>(<span class="variable">s</span>[<span class="string">"scores"</span>]) / <span class="function">len</span>(<span class="variable">s</span>[<span class="string">"scores"</span>])</span>
                    <span class="code-line">        }</span>
                    <span class="code-line">        <span class="keyword">for</span> <span class="variable">s</span> <span class="keyword">in</span> <span class="variable">students</span></span>
                    <span class="code-line">        <span class="keyword">if</span> <span class="variable">s</span>.<span class="function">get</span>(<span class="string">"active"</span>, <span class="keyword">False</span>) <span class="keyword">and</span> <span class="function">sum</span>(<span class="variable">s</span>.<span class="function">get</span>(<span class="string">"scores"</span>, [])) > <span class="number">240</span></span>
                    <span class="code-line">    ]</span>
                </div>

                <h3>最佳实践验证</h3>
                <div class="prompt-template">
                    <h4>✅ 代码审查提示词</h4>
                    <p>"请以Python专家的身份审查我的列表处理代码：</p>
                    <p><code>[代码]</code></p>
                    <p>请检查：</p>
                    <ul>
                        <li>是否符合PEP 8编码规范</li>
                        <li>是否遵循Pythonic风格</li>
                        <li>错误处理是否完善</li>
                        <li>性能是否可以优化</li>
                        <li>代码可读性和维护性</li>
                    </ul>
                    <p>请提供具体的改进建议和修改后的代码。"</p>
                </div>

                <div class="highlight-box">
                    <p><strong>🎯 AI调试优化流程：</strong></p>
                    <ul>
                        <li><strong>问题定位</strong>：清楚描述遇到的具体问题</li>
                        <li><strong>代码分析</strong>：让AI分析代码结构和逻辑</li>
                        <li><strong>方案对比</strong>：获取多种解决方案的对比</li>
                        <li><strong>逐步验证</strong>：分步实施AI的建议</li>
                        <li><strong>效果评估</strong>：测试优化后的性能和正确性</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第27页：AI辅助学习Python -->
        <div class="slide">
            <div class="slide-number">27/30</div>
            <div class="slide-content">
                <h2><span class="emoji">📚</span>AI辅助学习Python</h2>
                
                <h3>制定学习计划</h3>
                <div class="prompt-template">
                    <h4>🎯 学习路径规划提示词</h4>
                    <p>"我是Python初学者，想要掌握列表和数据处理。请为我制定一个4周的学习计划，包括：</p>
                    <ul>
                        <li>每周的学习重点和目标</li>
                        <li>具体的练习项目建议</li>
                        <li>知识点的难度递进安排</li>
                        <li>每个阶段的检验标准</li>
                    </ul>
                    <p>我每天可以投入2小时学习时间。"</p>
                </div>

                <div class="ai-tool">
                    <h4>📅 AI生成的学习计划示例</h4>
                    <p><strong>第1周：基础列表操作</strong></p>
                    <ul>
                        <li>Day 1-2: 列表创建、索引、切片</li>
                        <li>Day 3-4: 列表方法(append, extend, remove等)</li>
                        <li>Day 5-7: 列表遍历和基础操作练习</li>
                        <li>项目：实现一个简单的任务清单管理器</li>
                    </ul>
                    <p><strong>第2周：列表生成式入门</strong></p>
                    <ul>
                        <li>Day 1-3: 基础语法和简单变换</li>
                        <li>Day 4-5: 条件过滤和嵌套结构</li>
                        <li>Day 6-7: 实战练习和错误调试</li>
                        <li>项目：数据清洗和格式转换工具</li>
                    </ul>
                </div>

                <h3>知识点深度学习</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># AI辅助概念理解</span></span>
                    <span class="code-line"><span class="comment"># 提示词："请用类比的方式解释列表生成式的工作原理"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># AI回答示例：</span></span>
                    <span class="code-line"><span class="comment"># "列表生成式就像一个自动化工厂流水线：</span></span>
                    <span class="code-line"><span class="comment"># 1. 原材料输入：for x in data（数据源）</span></span>
                    <span class="code-line"><span class="comment"># 2. 质检过滤：if condition（条件筛选）</span></span>
                    <span class="code-line"><span class="comment"># 3. 加工处理：expression（数据变换）</span></span>
                    <span class="code-line"><span class="comment"># 4. 产品输出：[最终结果列表]"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 实际代码对应：</span></span>
                    <span class="code-line"><span class="variable">processed_data</span> = [<span class="variable">transform</span>(<span class="variable">x</span>) <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">raw_data</span> <span class="keyword">if</span> <span class="function">is_valid</span>(<span class="variable">x</span>)]</span>
                    <span class="code-line"><span class="comment">#                   ↑加工处理    ↑原材料    ↑质检过滤</span></span>
                </div>

                <h3>互动式练习设计</h3>
                <div class="prompt-template">
                    <h4>🎮 练习生成提示词</h4>
                    <p>"请为我设计5个递进难度的列表生成式练习题：</p>
                    <ol>
                        <li>初级：基础数据变换</li>
                        <li>中级：条件过滤和字符串处理</li>
                        <li>高级：嵌套数据结构处理</li>
                        <li>专家级：性能优化和复杂逻辑</li>
                        <li>项目级：实际应用场景</li>
                    </ol>
                    <p>每题包含：题目描述、测试数据、期望输出、提示信息。"</p>
                </div>

                <div class="highlight-box">
                    <h4>💡 AI生成的练习示例</h4>
                    <p><strong>练习1（初级）：</strong>将华氏温度列表转换为摄氏温度</p>
                    <p><strong>数据：</strong>[32, 68, 86, 104, 212]</p>
                    <p><strong>公式：</strong>C = (F - 32) * 5/9</p>
                    <p><strong>期望：</strong>[0.0, 20.0, 30.0, 40.0, 100.0]</p>
                </div>

                <h3>错误学习机制</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># AI辅助错误分析</span></span>
                    <span class="code-line"><span class="comment"># 学生提交的错误代码：</span></span>
                    <span class="code-line"><span class="variable">ages</span> = [<span class="string">"20"</span>, <span class="string">"25"</span>, <span class="string">"30"</span>]</span>
                    <span class="code-line"><span class="variable">adult_ages</span> = [<span class="variable">age</span> <span class="keyword">for</span> <span class="variable">age</span> <span class="keyword">in</span> <span class="variable">ages</span> <span class="keyword">if</span> <span class="variable">age</span> >= <span class="number">18</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># AI分析：</span></span>
                    <span class="code-line"><span class="comment"># "这里有类型错误：字符串'20'无法与数字18比较"</span></span>
                    <span class="code-line"><span class="comment"># "解决方案：在比较前将字符串转换为整数"</span></span>
                    <span class="code-line"><span class="comment"># "学习要点：注意数据类型的一致性"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 修正版本：</span></span>
                    <span class="code-line"><span class="variable">adult_ages</span> = [<span class="function">int</span>(<span class="variable">age</span>) <span class="keyword">for</span> <span class="variable">age</span> <span class="keyword">in</span> <span class="variable">ages</span> <span class="keyword">if</span> <span class="function">int</span>(<span class="variable">age</span>) >= <span class="number">18</span>]</span>
                </div>

                <h3>进阶学习引导</h3>
                <div class="ai-tool">
                    <h4>🚀 知识拓展建议</h4>
                    <p>当掌握基础列表操作后，AI可以引导学习：</p>
                    <ul>
                        <li><strong>相关概念</strong>：生成器表达式、迭代器模式</li>
                        <li><strong>高级应用</strong>：函数式编程、数据管道</li>
                        <li><strong>性能优化</strong>：算法复杂度、内存管理</li>
                        <li><strong>实际项目</strong>：数据科学、Web开发应用</li>
                        <li><strong>最佳实践</strong>：代码规范、团队协作</li>
                    </ul>
                </div>

                <div class="prompt-template">
                    <h4>🎓 学习评估提示词</h4>
                    <p>"我已经学习列表生成式两周了，请帮我评估学习效果：</p>
                    <p>我能够：</p>
                    <ul>
                        <li>编写基础的列表生成式</li>
                        <li>使用条件过滤</li>
                        <li>处理简单的嵌套结构</li>
                    </ul>
                    <p>请设计一个综合测试，并根据我的表现推荐下一步学习方向。"</p>
                </div>

                <div class="tips-box">
                    <p><strong>📖 高效AI学习策略：</strong></p>
                    <ul>
                        <li><strong>主动提问</strong>：不懂就问，不要积累疑问</li>
                        <li><strong>实践验证</strong>：将AI的建议转化为实际代码</li>
                        <li><strong>举一反三</strong>：要求AI提供相似但不同的例子</li>
                        <li><strong>定期回顾</strong>：让AI帮助总结和复习知识点</li>
                        <li><strong>建立体系</strong>：构建完整的知识框架</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第28页：AI使用注意事项 -->
        <div class="slide">
            <div class="slide-number">28/30</div>
            <div class="slide-content">
                <h2><span class="emoji">⚠️</span>AI使用注意事项</h2>
                
                <h3>AI的能力边界</h3>
                <div class="tips-box">
                    <h4>❌ AI无法做到的事情</h4>
                    <ul>
                        <li><strong>理解业务上下文</strong>：不了解你的具体项目需求</li>
                        <li><strong>实时信息更新</strong>：无法获取最新的库版本信息</li>
                        <li><strong>执行和测试代码</strong>：无法运行代码验证正确性</li>
                        <li><strong>访问你的环境</strong>：看不到你的文件和配置</li>
                        <li><strong>长期记忆</strong>：每次对话都是独立的</li>
                    </ul>
                </div>

                <div class="highlight-box">
                    <h4>✅ AI擅长的方面</h4>
                    <ul>
                        <li><strong>语法和概念解释</strong>：清晰解释Python语法规则</li>
                        <li><strong>代码示例生成</strong>：快速提供可参考的代码</li>
                        <li><strong>错误分析</strong>：分析常见错误的原因</li>
                        <li><strong>最佳实践指导</strong>：提供编程规范建议</li>
                        <li><strong>学习路径规划</strong>：设计系统性学习计划</li>
                    </ul>
                </div>

                <h3>避免过度依赖</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># ❌ 错误的学习方式</span></span>
                    <span class="code-line"><span class="comment"># 1. 完全依赖AI写代码，不理解原理</span></span>
                    <span class="code-line"><span class="comment"># 2. 不验证AI给出的代码是否正确</span></span>
                    <span class="code-line"><span class="comment"># 3. 遇到问题立即求助AI，不先尝试自己解决</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># ✅ 正确的学习方式</span></span>
                    <span class="code-line"><span class="comment"># 1. 用AI学习概念，自己动手实践</span></span>
                    <span class="code-line"><span class="comment"># 2. 验证和测试AI生成的代码</span></span>
                    <span class="code-line"><span class="comment"># 3. 先独立思考，再寻求AI辅助</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 实践建议：AI给出代码后，试着：</span></span>
                    <span class="code-line"><span class="comment"># - 逐行理解代码逻辑</span></span>
                    <span class="code-line"><span class="comment"># - 修改部分代码验证理解</span></span>
                    <span class="code-line"><span class="comment"># - 尝试用不同方法实现同样功能</span></span>
                </div>

                <h3>验证AI回答的方法</h3>
                <div class="prompt-template">
                    <h4>🔍 验证策略</h4>
                    <ol>
                        <li><strong>运行测试</strong>：实际执行AI提供的代码</li>
                        <li><strong>交叉验证</strong>：咨询多个AI工具或查阅官方文档</li>
                        <li><strong>边界测试</strong>：用边界情况测试代码健壮性</li>
                        <li><strong>性能验证</strong>：对于性能相关建议，实际测量效果</li>
                        <li><strong>同行评议</strong>：与其他开发者讨论AI的建议</li>
                    </ol>
                </div>

                <div class="code-block">
                    <span class="code-line"><span class="comment"># 验证示例：AI建议的性能优化</span></span>
                    <span class="code-line"><span class="keyword">import</span> <span class="variable">time</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># AI建议的方法A</span></span>
                    <span class="code-line"><span class="variable">start</span> = <span class="variable">time</span>.<span class="function">time</span>()</span>
                    <span class="code-line"><span class="variable">result_a</span> = [<span class="variable">x</span> ** <span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">100000</span>) <span class="keyword">if</span> <span class="variable">x</span> % <span class="number">2</span> == <span class="number">0</span>]</span>
                    <span class="code-line"><span class="variable">time_a</span> = <span class="variable">time</span>.<span class="function">time</span>() - <span class="variable">start</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># AI建议的方法B</span></span>
                    <span class="code-line"><span class="variable">start</span> = <span class="variable">time</span>.<span class="function">time</span>()</span>
                    <span class="code-line"><span class="variable">result_b</span> = [<span class="variable">x</span> ** <span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">0</span>, <span class="number">100000</span>, <span class="number">2</span>)]</span>
                    <span class="code-line"><span class="variable">time_b</span> = <span class="variable">time</span>.<span class="function">time</span>() - <span class="variable">start</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 验证结果是否相同</span></span>
                    <span class="code-line"><span class="function">assert</span> <span class="variable">result_a</span> == <span class="variable">result_b</span>, <span class="string">"结果不一致！"</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">f"方法A: {time_a:.4f}s, 方法B: {time_b:.4f}s"</span>)</span>
                </div>

                <h3>处理AI错误的策略</h3>
                <div class="ai-tool">
                    <h4>🛠️ 错误处理流程</h4>
                    <ol>
                        <li><strong>识别错误</strong>：通过测试发现AI代码的问题</li>
                        <li><strong>分析原因</strong>：理解为什么会出现这个错误</li>
                        <li><strong>反馈AI</strong>：向AI描述发现的问题</li>
                        <li><strong>获取修正</strong>：要求AI提供修正版本</li>
                        <li><strong>学习改进</strong>：从错误中学习，避免类似问题</li>
                    </ol>
                </div>

                <div class="prompt-template">
                    <h4>🔧 错误反馈提示词模板</h4>
                    <p>"你之前给我的代码有问题：</p>
                    <p><code>[AI提供的代码]</code></p>
                    <p>错误现象：[具体的错误描述]</p>
                    <p>测试用例：[导致错误的输入数据]</p>
                    <p>请分析错误原因并提供正确的解决方案。"</p>
                </div>

                <h3>建立批判性思维</h3>
                <div class="highlight-box">
                    <p><strong>🤔 质疑AI回答的关键点：</strong></p>
                    <ul>
                        <li><strong>逻辑合理性</strong>：解决方案是否符合问题需求</li>
                        <li><strong>代码正确性</strong>：语法和逻辑是否正确</li>
                        <li><strong>性能声明</strong>：性能优化声明是否有依据</li>
                        <li><strong>最佳实践</strong>：是否符合当前的编程规范</li>
                        <li><strong>适用场景</strong>：是否适合你的具体情况</li>
                    </ul>
                </div>

                <div class="tips-box">
                    <p><strong>💡 健康的AI使用原则：</strong></p>
                    <ul>
                        <li><strong>AI是工具，不是替代</strong>：用AI辅助学习，不是替代思考</li>
                        <li><strong>保持好奇心</strong>：深入理解AI给出的每个建议</li>
                        <li><strong>培养判断力</strong>：学会识别AI的准确性和适用性</li>
                        <li><strong>持续学习</strong>：用AI加速学习，而不是停止学习</li>
                        <li><strong>实践验证</strong>：理论与实践相结合</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第29页：提示词模板汇总 -->
        <div class="slide">
            <div class="slide-number">29/30</div>
            <div class="slide-content">
                <h2><span class="emoji">📋</span>提示词模板汇总</h2>
                
                <h3>学习类提示词模板</h3>
                <div class="prompt-template">
                    <h4>🎓 概念学习模板</h4>
                    <p><strong>基础模板：</strong></p>
                    <p>"请详细解释[概念名称]，包括：定义、语法、使用场景、代码示例、最佳实践。我是[初学者/中级/高级]水平。"</p>
                    <br>
                    <p><strong>具体示例：</strong></p>
                    <p>"请详细解释Python列表生成式，包括：定义、语法、使用场景、代码示例、最佳实践。我是Python初学者。"</p>
                </div>

                <div class="prompt-template">
                    <h4>📚 对比学习模板</h4>
                    <p><strong>基础模板：</strong></p>
                    <p>"请对比[概念A]和[概念B]的区别，包括：语法差异、性能对比、适用场景、代码示例。用表格形式总结。"</p>
                    <br>
                    <p><strong>具体示例：</strong></p>
                    <p>"请对比列表生成式和传统for循环的区别，包括：语法差异、性能对比、适用场景、代码示例。用表格形式总结。"</p>
                </div>

                <h3>问题解决类提示词模板</h3>
                <div class="prompt-template">
                    <h4>🐛 调试协助模板</h4>
                    <p><strong>基础模板：</strong></p>
                    <p>"我的代码出现了问题：</p>
                    <p>代码：<code>[粘贴代码]</code></p>
                    <p>错误信息：<code>[错误消息]</code></p>
                    <p>期望结果：[描述期望的输出]</p>
                    <p>请帮我：1)分析错误原因 2)提供修正代码 3)解释解决思路 4)给出预防建议"</p>
                </div>

                <div class="prompt-template">
                    <h4>⚡ 性能优化模板</h4>
                    <p><strong>基础模板：</strong></p>
                    <p>"请分析以下代码的性能并提供优化建议：</p>
                    <p>代码：<code>[粘贴代码]</code></p>
                    <p>数据规模：[描述数据量]</p>
                    <p>性能要求：[描述性能目标]</p>
                    <p>请提供：1)性能分析 2)优化方案 3)优化后代码 4)预期提升"</p>
                </div>

                <h3>实践类提示词模板</h3>
                <div class="prompt-template">
                    <h4>🎯 练习生成模板</h4>
                    <p><strong>基础模板：</strong></p>
                    <p>"请为[知识点]设计[数量]个练习题，难度从[初级/中级/高级]开始递进。每题包含：题目描述、测试数据、期望输出、解题提示。"</p>
                    <br>
                    <p><strong>具体示例：</strong></p>
                    <p>"请为Python列表生成式设计5个练习题，难度从初级开始递进。每题包含：题目描述、测试数据、期望输出、解题提示。"</p>
                </div>

                <div class="prompt-template">
                    <h4>🔧 代码审查模板</h4>
                    <p><strong>基础模板：</strong></p>
                    <p>"请以专业开发者的角度审查我的代码：</p>
                    <p><code>[粘贴代码]</code></p>
                    <p>重点关注：1)代码规范 2)性能效率 3)可读性 4)错误处理 5)最佳实践</p>
                    <p>请提供具体的改进建议和修改后的代码。"</p>
                </div>

                <h3>项目类提示词模板</h3>
                <div class="prompt-template">
                    <h4>🏗️ 方案设计模板</h4>
                    <p><strong>基础模板：</strong></p>
                    <p>"我需要实现[功能描述]，要求：</p>
                    <ul>
                        <li>输入：[描述输入数据]</li>
                        <li>输出：[描述期望输出]</li>
                        <li>约束：[性能、内存等约束]</li>
                    </ul>
                    <p>请提供：1)解决思路 2)代码实现 3)时间复杂度 4)测试用例"</p>
                </div>

                <div class="prompt-template">
                    <h4>📖 学习路径模板</h4>
                    <p><strong>基础模板：</strong></p>
                    <p>"我想学习[技术领域]，当前水平是[描述现有基础]，目标是[学习目标]，每天可投入[时间]。</p>
                    <p>请制定详细学习计划，包括：1)学习路径 2)时间安排 3)实践项目 4)检验标准"</p>
                </div>

                <h3>专用场景模板</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 数据处理场景</span></span>
                    <span class="code-line"><span class="comment"># "我有以下格式的数据：[数据格式]</span></span>
                    <span class="code-line"><span class="comment"># 需要转换成：[目标格式]</span></span>
                    <span class="code-line"><span class="comment"># 请用列表生成式实现，并解释每个步骤。"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># Web开发场景</span></span>
                    <span class="code-line"><span class="comment"># "我需要处理API返回的JSON数据：[JSON结构]</span></span>
                    <span class="code-line"><span class="comment"># 提取出[具体字段]用于前端显示。</span></span>
                    <span class="code-line"><span class="comment"># 请提供高效的数据处理方案。"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 算法优化场景</span></span>
                    <span class="code-line"><span class="comment"># "以下算法的时间复杂度是O(n²)：[代码]</span></span>
                    <span class="code-line"><span class="comment"># 请帮我优化到O(n log n)或更好，</span></span>
                    <span class="code-line"><span class="comment"># 并保持功能不变。"</span></span>
                </div>

                <div class="highlight-box">
                    <p><strong>🎯 高效提示词要素：</strong></p>
                    <ul>
                        <li><strong>明确角色</strong>：告诉AI扮演什么角色</li>
                        <li><strong>具体任务</strong>：清楚描述要完成的任务</li>
                        <li><strong>详细上下文</strong>：提供充分的背景信息</li>
                        <li><strong>明确要求</strong>：说明期望的输出格式</li>
                        <li><strong>限制条件</strong>：明确约束和限制</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第30页：总结和下一步学习建议 -->
        <div class="slide">
            <div class="slide-number">30/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🎯</span>总结和下一步学习建议</h2>
                
                <h3>知识点回顾</h3>
                <div class="highlight-box">
                    <h4>📚 我们学到了什么</h4>
                    <p><strong>Python列表基础（10个核心概念）：</strong></p>
                    <ul>
                        <li>列表创建和基本操作</li>
                        <li>索引、切片、增删改查</li>
                        <li>列表方法和内置函数</li>
                        <li>嵌套列表和多维数据处理</li>
                        <li>性能优化和最佳实践</li>
                    </ul>
                    
                    <p><strong>列表生成式精通（10个高级技巧）：</strong></p>
                    <ul>
                        <li>基础语法到复杂表达式</li>
                        <li>条件过滤和嵌套循环</li>
                        <li>字符串和字典数据处理</li>
                        <li>与传统循环的对比分析</li>
                        <li>实际项目应用案例</li>
                    </ul>
                    
                    <p><strong>AI辅助编程（6个实用技能）：</strong></p>
                    <ul>
                        <li>选择合适的AI编程工具</li>
                        <li>编写高效的提示词</li>
                        <li>AI调试和优化技巧</li>
                        <li>避免过度依赖的策略</li>
                    </ul>
                </div>

                <h3>技能评估检查表</h3>
                <div class="ai-tool">
                    <h4>✅ 自我评估清单</h4>
                    <p><strong>基础技能（必须掌握）：</strong></p>
                    <ul>
                        <li>□ 能够熟练创建和操作Python列表</li>
                        <li>□ 理解列表索引、切片的工作原理</li>
                        <li>□ 会使用列表的主要方法（append, extend, remove等）</li>
                        <li>□ 能编写基础的列表生成式</li>
                        <li>□ 知道何时使用列表生成式vs传统循环</li>
                    </ul>
                    
                    <p><strong>进阶技能（建议掌握）：</strong></p>
                    <ul>
                        <li>□ 能处理嵌套列表和复杂数据结构</li>
                        <li>□ 熟练使用条件过滤和嵌套循环</li>
                        <li>□ 理解列表操作的性能特点</li>
                        <li>□ 能用AI工具辅助学习和调试</li>
                        <li>□ 会编写Pythonic风格的代码</li>
                    </ul>
                </div>

                <h3>学习路径建议</h3>
                <div class="tips-box">
                    <h4>🛤️ 后续学习方向</h4>
                    <p><strong>短期目标（1-2个月）：</strong></p>
                    <ul>
                        <li><strong>深化实践</strong>：完成10-15个列表处理的实际项目</li>
                        <li><strong>性能优化</strong>：学习算法复杂度分析</li>
                        <li><strong>数据结构</strong>：掌握字典、集合、元组的高级用法</li>
                        <li><strong>函数式编程</strong>：学习map、filter、reduce等函数</li>
                    </ul>
                    
                    <p><strong>中期目标（3-6个月）：</strong></p>
                    <ul>
                        <li><strong>高级Python</strong>：生成器、装饰器、上下文管理器</li>
                        <li><strong>数据科学</strong>：pandas、numpy的数据处理</li>
                        <li><strong>Web开发</strong>：Django/Flask中的数据处理</li>
                        <li><strong>算法设计</strong>：常见算法的Python实现</li>
                    </ul>
                </div>

                <h3>实践项目建议</h3>
                <div class="prompt-template">
                    <h4>💼 推荐项目</h4>
                    <p><strong>初级项目：</strong></p>
                    <ol>
                        <li><strong>任务管理器</strong>：用列表管理待办事项</li>
                        <li><strong>成绩统计系统</strong>：学生成绩的增删改查</li>
                        <li><strong>购物清单</strong>：商品管理和价格计算</li>
                    </ol>
                    
                    <p><strong>中级项目：</strong></p>
                    <ol>
                        <li><strong>数据清洗工具</strong>：CSV文件的批量处理</li>
                        <li><strong>日志分析器</strong>：Web服务器日志统计</li>
                        <li><strong>文本分析器</strong>：词频统计和情感分析</li>
                    </ol>
                    
                    <p><strong>高级项目：</strong></p>
                    <ol>
                        <li><strong>推荐系统</strong>：基于用户行为的商品推荐</li>
                        <li><strong>数据可视化</strong>：动态图表生成</li>
                        <li><strong>API数据处理</strong>：第三方API数据聚合</li>
                    </ol>
                </div>

                <h3>持续学习策略</h3>
                <div class="ai-tool">
                    <h4>🎓 终身学习计划</h4>
                    <ul>
                        <li><strong>每日练习</strong>：坚持每天写30分钟代码</li>
                        <li><strong>周期回顾</strong>：每周总结学到的新知识</li>
                        <li><strong>项目驱动</strong>：通过实际项目应用所学</li>
                        <li><strong>社区参与</strong>：加入Python学习社区交流</li>
                        <li><strong>AI伴学</strong>：保持与AI工具的有效互动</li>
                        <li><strong>知识分享</strong>：教授他人巩固自己的理解</li>
                    </ul>
                </div>

                <h3>最后的话</h3>
                <div class="highlight-box">
                    <p><strong>🌟 学习Python的金科玉律：</strong></p>
                    <blockquote style="font-style: italic; font-size: 1.1em; color: #2c3e50; border-left: 3px solid #3498db; padding-left: 15px;">
                        "编程是一门手艺，需要在实践中不断打磨。<br>
                        列表生成式不仅仅是一种语法，更是一种思维方式。<br>
                        AI是你的学习伙伴，但真正的技能来自于你的思考和实践。<br><br>
                        Remember: Code is poetry, and every bug is a lesson.<br>
                        Keep coding, keep learning, keep growing! 🚀"
                    </blockquote>
                </div>

                <div style="text-align: center; margin-top: 40px;">
                    <h4><span class="emoji">🎉</span> 恭喜完成学习！<span class="emoji">🎉</span></h4>
                    <p style="color: #7f8c8d;">现在，开始你的Python数据处理之旅吧！</p>
                </div>
            </div>
        </div>
        
    </div>

    <!-- 导航控件 -->
    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">上一页</button>
        <button class="nav-btn" onclick="nextSlide()">下一页</button>
    </div>

    <!-- 页面指示器 -->
    <div class="page-indicator">
        <span id="current-page">1</span> / 30
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        function showSlide(index) {
            // 隐藏所有幻灯片
            slides.forEach(slide => {
                slide.classList.remove('active');
            });

            // 显示当前幻灯片
            if (index >= 0 && index < totalSlides) {
                slides[index].classList.add('active');
                currentSlide = index;
                document.getElementById('current-page').textContent = index + 1;
            }
        }

        function nextSlide() {
            if (currentSlide < totalSlides - 1) {
                showSlide(currentSlide + 1);
            }
        }

        function previousSlide() {
            if (currentSlide > 0) {
                showSlide(currentSlide - 1);
            }
        }

        // 键盘导航
        document.addEventListener('keydown', function(event) {
            if (event.key === 'ArrowRight' || event.key === ' ') {
                nextSlide();
            } else if (event.key === 'ArrowLeft') {
                previousSlide();
            }
        });

        // 代码动画效果
        function animateCodeLines() {
            const codeLines = document.querySelectorAll('.code-line');
            codeLines.forEach((line, index) => {
                line.style.animationDelay = `${0.1 + index * 0.02}s`;
            });
        }

        // 页面加载完成后执行动画
        document.addEventListener('DOMContentLoaded', function() {
            animateCodeLines();
        });
    </script>
</body>
</html>