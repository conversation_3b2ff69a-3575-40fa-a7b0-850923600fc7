<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python 字符串完整教程 📚</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow-x: hidden;
        }

        .presentation {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100vh;
            display: none;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            padding: 60px;
            position: relative;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            overflow-y: auto;
            scroll-behavior: smooth;
        }

        .slide.active {
            display: flex;
            animation: slideIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .slide-header {
            width: 100%;
            margin-bottom: 40px;
            text-align: left;
        }

        .slide-title {
            font-size: 3.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: left;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .slide-subtitle {
            font-size: 1.5rem;
            color: #7f8c8d;
            text-align: left;
            animation: fadeInUp 1s ease-out 0.5s both;
        }

        .content {
            width: 100%;
            text-align: left;
            flex: 1;
        }

        .content h2 {
            font-size: 2.5rem;
            color: #34495e;
            margin: 30px 0 20px 0;
            text-align: left;
            animation: fadeInLeft 0.8s ease-out;
        }

        .content h3 {
            font-size: 2rem;
            color: #2980b9;
            margin: 25px 0 15px 0;
            text-align: left;
            animation: fadeInLeft 0.8s ease-out 0.2s both;
        }

        .content p, .content li {
            font-size: 1.3rem;
            line-height: 1.8;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: left;
            animation: fadeInUp 0.8s ease-out 0.4s both;
        }

        .content ul {
            margin-left: 30px;
            margin-bottom: 20px;
        }

        .emoji {
            font-size: 2rem;
            margin-right: 15px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .code-block {
            background: #1e1e1e;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 1.1rem;
            line-height: 1.6;
            color: #d4d4d4;
            text-align: left;
            overflow-x: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: slideInUp 0.8s ease-out 0.6s both;
        }

        .code-block .keyword {
            color: #569cd6;
        }

        .code-block .string {
            color: #ce9178;
        }

        .code-block .comment {
            color: #6a9955;
            font-style: italic;
        }

        .code-block .function {
            color: #dcdcaa;
        }

        .code-block .number {
            color: #b5cea8;
        }

        .highlight-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            text-align: left;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
            }
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50px;
            padding: 15px 25px;
            font-size: 1rem;
            font-weight: 600;
            color: #2c3e50;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            color: #2c3e50;
            backdrop-filter: blur(10px);
            z-index: 1000;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .ai-prompt {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            text-align: left;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from {
                box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
            }
            to {
                box-shadow: 0 0 30px rgba(255, 107, 107, 0.8);
            }
        }

        .tip-box {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            text-align: left;
            animation: slideInRight 0.8s ease-out;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    </style>
</head>
<body>
    <div class="presentation">
        <div class="slide-counter">
            <span id="current-slide">1</span> / <span id="total-slides">30</span>
        </div>

        <!-- 第1页：标题页 -->
        <div class="slide active">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">🐍</span>Python 字符串完整教程</h1>
                <p class="slide-subtitle">从基础到高级，掌握字符串的每一个细节</p>
            </div>
            <div class="content">
                <div class="highlight-box">
                    <h3><span class="emoji">🎯</span>学习目标</h3>
                    <ul>
                        <li>掌握Python字符串的基本概念和创建方法</li>
                        <li>熟练使用字符串的各种操作和方法</li>
                        <li>理解字符串格式化的多种方式</li>
                        <li>学会使用AI工具辅助学习和编程</li>
                    </ul>
                </div>
                <div class="tip-box">
                    <p><span class="emoji">💡</span><strong>提示：</strong>本教程支持竖向滚动，每页内容都可以上下滚动查看完整信息。</p>
                </div>
            </div>
        </div>

        <!-- 第2页：字符串基础概念 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">📝</span>字符串基础概念</h1>
            </div>
            <div class="content">
                <h2>什么是字符串？</h2>
                <p>字符串是由字符组成的序列，在Python中用引号包围。字符串是不可变的数据类型。</p>
                
                <h3><span class="emoji">🔤</span>字符串的特点</h3>
                <ul>
                    <li>不可变性：一旦创建就不能修改</li>
                    <li>有序性：字符有固定的顺序</li>
                    <li>可索引：可以通过索引访问单个字符</li>
                    <li>可切片：可以提取子字符串</li>
                </ul>

                <div class="code-block">
<span class="comment"># 字符串的基本创建方式</span>
<span class="keyword">str1</span> = <span class="string">"Hello World"</span>  <span class="comment"># 双引号</span>
<span class="keyword">str2</span> = <span class="string">'Python'</span>      <span class="comment"># 单引号</span>
<span class="keyword">str3</span> = <span class="string">"""多行
字符串"""</span>              <span class="comment"># 三引号</span>

<span class="function">print</span>(<span class="keyword">str1</span>)  <span class="comment"># 输出: Hello World</span>
<span class="function">print</span>(<span class="keyword">str2</span>)  <span class="comment"># 输出: Python</span>
<span class="function">print</span>(<span class="keyword">str3</span>)  <span class="comment"># 输出: 多行\n字符串</span>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI 提示词模板</h3>
                    <p><strong>学习字符串基础：</strong></p>
                    <p>"请解释Python中字符串的基本概念，包括创建方式、特点，并提供简单的代码示例。重点说明字符串的不可变性。"</p>
                </div>
            </div>
        </div>

        <!-- 第3页：字符串创建方法 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">🏗️</span>字符串创建方法</h1>
            </div>
            <div class="content">
                <h2>多种创建方式</h2>
                
                <h3><span class="emoji">1️⃣</span>直接赋值</h3>
                <div class="code-block">
<span class="comment"># 最常用的创建方式</span>
<span class="keyword">name</span> = <span class="string">"张三"</span>
<span class="keyword">city</span> = <span class="string">'北京'</span>
<span class="keyword">description</span> = <span class="string">"""这是一个
多行字符串
的例子"""</span>
                </div>

                <h3><span class="emoji">2️⃣</span>使用str()函数</h3>
                <div class="code-block">
<span class="comment"># 将其他类型转换为字符串</span>
<span class="keyword">age</span> = <span class="number">25</span>
<span class="keyword">age_str</span> = <span class="function">str</span>(<span class="keyword">age</span>)        <span class="comment"># "25"</span>
<span class="keyword">pi_str</span> = <span class="function">str</span>(<span class="number">3.14159</span>)    <span class="comment"># "3.14159"</span>
<span class="keyword">bool_str</span> = <span class="function">str</span>(<span class="keyword">True</span>)      <span class="comment"># "True"</span>
                </div>

                <h3><span class="emoji">3️⃣</span>原始字符串</h3>
                <div class="code-block">
<span class="comment"># 使用r前缀，不转义特殊字符</span>
<span class="keyword">path</span> = <span class="string">r"C:\Users\<USER>\file.txt"</span>
<span class="keyword">regex</span> = <span class="string">r"\d+\.\d+"</span>  <span class="comment"># 正则表达式</span>
                </div>

                <h3><span class="emoji">4️⃣</span>格式化字符串</h3>
                <div class="code-block">
<span class="comment"># f-string (Python 3.6+)</span>
<span class="keyword">name</span> = <span class="string">"李四"</span>
<span class="keyword">age</span> = <span class="number">30</span>
<span class="keyword">message</span> = <span class="string">f"我叫{name}，今年{age}岁"</span>
<span class="function">print</span>(<span class="keyword">message</span>)  <span class="comment"># 输出: 我叫李四，今年30岁</span>
                </div>

                <div class="tip-box">
                    <p><span class="emoji">💡</span><strong>最佳实践：</strong>优先使用f-string进行字符串格式化，它更简洁、高效且易读。</p>
                </div>
            </div>
        </div>

        <!-- 第4页：字符串索引和切片 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">🔍</span>字符串索引和切片</h1>
            </div>
            <div class="content">
                <h2>访问字符串中的字符</h2>
                
                <h3><span class="emoji">📍</span>索引访问</h3>
                <div class="code-block">
<span class="keyword">text</span> = <span class="string">"Python编程"</span>

<span class="comment"># 正向索引（从0开始）</span>
<span class="function">print</span>(<span class="keyword">text</span>[<span class="number">0</span>])   <span class="comment"># 输出: P</span>
<span class="function">print</span>(<span class="keyword">text</span>[<span class="number">1</span>])   <span class="comment"># 输出: y</span>
<span class="function">print</span>(<span class="keyword">text</span>[<span class="number">6</span>])   <span class="comment"># 输出: 编</span>

<span class="comment"># 负向索引（从-1开始）</span>
<span class="function">print</span>(<span class="keyword">text</span>[<span class="number">-1</span>])  <span class="comment"># 输出: 程</span>
<span class="function">print</span>(<span class="keyword">text</span>[<span class="number">-2</span>])  <span class="comment"># 输出: 编</span>
                </div>

                <h3><span class="emoji">✂️</span>切片操作</h3>
                <div class="code-block">
<span class="keyword">text</span> = <span class="string">"Hello Python World"</span>

<span class="comment"># 基本切片 [start:end]</span>
<span class="function">print</span>(<span class="keyword">text</span>[<span class="number">0</span>:<span class="number">5</span>])    <span class="comment"># 输出: Hello</span>
<span class="function">print</span>(<span class="keyword">text</span>[<span class="number">6</span>:<span class="number">12</span>])   <span class="comment"># 输出: Python</span>
<span class="function">print</span>(<span class="keyword">text</span>[<span class="number">13</span>:])     <span class="comment"># 输出: World</span>

<span class="comment"># 带步长的切片 [start:end:step]</span>
<span class="function">print</span>(<span class="keyword">text</span>[<span class="number">0</span>:<span class="number">10</span>:<span class="number">2</span>])  <span class="comment"># 输出: HloPt</span>
<span class="function">print</span>(<span class="keyword">text</span>[::<span class="number">-1</span>])     <span class="comment"># 输出: dlroW nohtyP olleH (反转)</span>
                </div>

                <h3><span class="emoji">🎯</span>实用切片技巧</h3>
                <div class="code-block">
<span class="keyword">url</span> = <span class="string">"https://www.python.org"</span>

<span class="comment"># 获取协议</span>
<span class="keyword">protocol</span> = <span class="keyword">url</span>[:<span class="keyword">url</span>.<span class="function">find</span>(<span class="string">"://"</span>)]  <span class="comment"># https</span>

<span class="comment"># 获取域名</span>
<span class="keyword">domain</span> = <span class="keyword">url</span>[<span class="keyword">url</span>.<span class="function">find</span>(<span class="string">"://"</span>) + <span class="number">3</span>:]    <span class="comment"># www.python.org</span>

<span class="comment"># 获取文件扩展名</span>
<span class="keyword">filename</span> = <span class="string">"document.pdf"</span>
<span class="keyword">extension</span> = <span class="keyword">filename</span>[<span class="keyword">filename</span>.<span class="function">rfind</span>(<span class="string">".") + <span class="number">1</span>:]  <span class="comment"># pdf</span>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI 提示词模板</h3>
                    <p>"请详细解释Python字符串的索引和切片操作，包括正负索引、切片语法[start:end:step]，并提供实际应用场景的代码示例。"</p>
                </div>
            </div>
        </div>

        <!-- 第5页：字符串基本操作 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">⚡</span>字符串基本操作</h1>
            </div>
            <div class="content">
                <h2>常用字符串操作</h2>
                
                <h3><span class="emoji">➕</span>字符串连接</h3>
                <div class="code-block">
<span class="comment"># 使用 + 操作符</span>
<span class="keyword">first_name</span> = <span class="string">"张"</span>
<span class="keyword">last_name</span> = <span class="string">"三"</span>
<span class="keyword">full_name</span> = <span class="keyword">first_name</span> + <span class="keyword">last_name</span>  <span class="comment"># "张三"</span>

<span class="comment"># 使用 += 操作符</span>
<span class="keyword">message</span> = <span class="string">"Hello"</span>
<span class="keyword">message</span> += <span class="string">" World"</span>  <span class="comment"># "Hello World"</span>

<span class="comment"># 使用 join() 方法（推荐）</span>
<span class="keyword">words</span> = [<span class="string">"Python"</span>, <span class="string">"是"</span>, <span class="string">"最好的"</span>, <span class="string">"语言"</span>]
<span class="keyword">sentence</span> = <span class="string">""</span>.<span class="function">join</span>(<span class="keyword">words</span>)  <span class="comment"># "Python是最好的语言"</span>
                </div>

                <h3><span class="emoji">✖️</span>字符串重复</h3>
                <div class="code-block">
<span class="comment"># 使用 * 操作符</span>
<span class="keyword">star</span> = <span class="string">"*"</span> * <span class="number">10</span>        <span class="comment"># "**********"</span>
<span class="keyword">separator</span> = <span class="string">"-"</span> * <span class="number">20</span>  <span class="comment"># "--------------------"</span>

<span class="comment"># 创建格式化分隔线</span>
<span class="keyword">title</span> = <span class="string">"重要通知"</span>
<span class="keyword">border</span> = <span class="string">"="</span> * <span class="function">len</span>(<span class="keyword">title</span>)
<span class="function">print</span>(<span class="keyword">border</span>)
<span class="function">print</span>(<span class="keyword">title</span>)
<span class="function">print</span>(<span class="keyword">border</span>)
                </div>

                <h3><span class="emoji">🔍</span>成员检测</h3>
                <div class="code-block">
<span class="keyword">text</span> = <span class="string">"Python编程很有趣"</span>

<span class="comment"># 使用 in 和 not in</span>
<span class="function">print</span>(<span class="string">"Python"</span> <span class="keyword">in</span> <span class="keyword">text</span>)      <span class="comment"># True</span>
<span class="function">print</span>(<span class="string">"Java"</span> <span class="keyword">in</span> <span class="keyword">text</span>)        <span class="comment"># False</span>
<span class="function">print</span>(<span class="string">"困难"</span> <span class="keyword">not in</span> <span class="keyword">text</span>)    <span class="comment"># True</span>

<span class="comment"># 实际应用：邮箱验证</span>
<span class="keyword">email</span> = <span class="string">"<EMAIL>"</span>
<span class="keyword">if</span> <span class="string">"@"</span> <span class="keyword">in</span> <span class="keyword">email</span> <span class="keyword">and</span> <span class="string">"."</span> <span class="keyword">in</span> <span class="keyword">email</span>:
    <span class="function">print</span>(<span class="string">"邮箱格式可能正确"</span>)
                </div>

                <h3><span class="emoji">📏</span>长度和比较</h3>
                <div class="code-block">
<span class="comment"># 获取字符串长度</span>
<span class="keyword">text</span> = <span class="string">"Hello 世界"</span>
<span class="function">print</span>(<span class="function">len</span>(<span class="keyword">text</span>))  <span class="comment"># 输出: 8</span>

<span class="comment"># 字符串比较</span>
<span class="function">print</span>(<span class="string">"apple"</span> < <span class="string">"banana"</span>)   <span class="comment"># True (字典序)</span>
<span class="function">print</span>(<span class="string">"Python"</span> == <span class="string">"python"</span>) <span class="comment"># False (区分大小写)</span>
                </div>

                <div class="tip-box">
                    <p><span class="emoji">⚠️</span><strong>注意：</strong>字符串连接时，使用join()方法比使用+操作符更高效，特别是连接大量字符串时。</p>
                </div>
            </div>
        </div>

        <!-- 第6页：字符串方法 - 大小写转换 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">🔄</span>大小写转换方法</h1>
            </div>
            <div class="content">
                <h2>字符串大小写操作</h2>
                
                <h3><span class="emoji">📝</span>基本转换方法</h3>
                <div class="code-block">
<span class="keyword">text</span> = <span class="string">"Hello Python World"</span>

<span class="comment"># 转换为小写</span>
<span class="function">print</span>(<span class="keyword">text</span>.<span class="function">lower</span>())      <span class="comment"># hello python world</span>

<span class="comment"># 转换为大写</span>
<span class="function">print</span>(<span class="keyword">text</span>.<span class="function">upper</span>())      <span class="comment"># HELLO PYTHON WORLD</span>

<span class="comment"># 首字母大写</span>
<span class="function">print</span>(<span class="keyword">text</span>.<span class="function">capitalize</span>()) <span class="comment"># Hello python world</span>

<span class="comment"># 每个单词首字母大写</span>
<span class="function">print</span>(<span class="keyword">text</span>.<span class="function">title</span>())      <span class="comment"># Hello Python World</span>
                </div>

                <h3><span class="emoji">🔀</span>大小写切换</h3>
                <div class="code-block">
<span class="keyword">mixed_text</span> = <span class="string">"PyThOn ProGramming"</span>

<span class="comment"># 大小写互换</span>
<span class="function">print</span>(<span class="keyword">mixed_text</span>.<span class="function">swapcase</span>())  <span class="comment"># pYtHoN pROgRAMMING</span>

<span class="comment"># 检查大小写状态</span>
<span class="keyword">text1</span> = <span class="string">"HELLO"</span>
<span class="keyword">text2</span> = <span class="string">"hello"</span>
<span class="keyword">text3</span> = <span class="string">"Hello World"</span>

<span class="function">print</span>(<span class="keyword">text1</span>.<span class="function">isupper</span>())    <span class="comment"># True</span>
<span class="function">print</span>(<span class="keyword">text2</span>.<span class="function">islower</span>())    <span class="comment"># True</span>
<span class="function">print</span>(<span class="keyword">text3</span>.<span class="function">istitle</span>())    <span class="comment"># True</span>
                </div>

                <h3><span class="emoji">🌍</span>实际应用场景</h3>
                <div class="code-block">
<span class="comment"># 用户输入标准化</span>
<span class="keyword">user_input</span> = <span class="string">"  ZHANG San  "</span>
<span class="keyword">normalized_name</span> = <span class="keyword">user_input</span>.<span class="function">strip</span>().<span class="function">title</span>()
<span class="function">print</span>(<span class="keyword">normalized_name</span>)  <span class="comment"># Zhang San</span>

<span class="comment"># 邮箱地址标准化</span>
<span class="keyword">email</span> = <span class="string">"<EMAIL>"</span>
<span class="keyword">clean_email</span> = <span class="keyword">email</span>.<span class="function">lower</span>()
<span class="function">print</span>(<span class="keyword">clean_email</span>)  <span class="comment"># <EMAIL></span>

<span class="comment"># 文件名处理</span>
<span class="keyword">filename</span> = <span class="string">"My Important Document.TXT"</span>
<span class="keyword">clean_filename</span> = <span class="keyword">filename</span>.<span class="function">lower</span>().<span class="function">replace</span>(<span class="string">" "</span>, <span class="string">"_"</span>)
<span class="function">print</span>(<span class="keyword">clean_filename</span>)  <span class="comment"># my_important_document.txt</span>
                </div>

                <h3><span class="emoji">🎯</span>高级应用</h3>
                <div class="code-block">
<span class="comment"># 创建常量名</span>
<span class="keyword">def</span> <span class="function">to_constant_case</span>(<span class="keyword">text</span>):
    <span class="keyword">return</span> <span class="keyword">text</span>.<span class="function">upper</span>().<span class="function">replace</span>(<span class="string">" "</span>, <span class="string">"_"</span>)

<span class="keyword">config_name</span> = <span class="function">to_constant_case</span>(<span class="string">"database connection timeout"</span>)
<span class="function">print</span>(<span class="keyword">config_name</span>)  <span class="comment"># DATABASE_CONNECTION_TIMEOUT</span>

<span class="comment"># 驼峰命名转换</span>
<span class="keyword">def</span> <span class="function">to_camel_case</span>(<span class="keyword">text</span>):
    <span class="keyword">words</span> = <span class="keyword">text</span>.<span class="function">split</span>(<span class="string">" "</span>)
    <span class="keyword">return</span> <span class="keyword">words</span>[<span class="number">0</span>].<span class="function">lower</span>() + <span class="string">""</span>.<span class="function">join</span>(<span class="keyword">word</span>.<span class="function">capitalize</span>() <span class="keyword">for</span> <span class="keyword">word</span> <span class="keyword">in</span> <span class="keyword">words</span>[<span class="number">1</span>:])

<span class="keyword">variable_name</span> = <span class="function">to_camel_case</span>(<span class="string">"user profile data"</span>)
<span class="function">print</span>(<span class="keyword">variable_name</span>)  <span class="comment"># userProfileData</span>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI 提示词模板</h3>
                    <p>"请介绍Python字符串的大小写转换方法，包括lower()、upper()、capitalize()、title()、swapcase()等，并提供实际开发中的应用场景和最佳实践。"</p>
                </div>
            </div>
        </div>

        <!-- 第7页：字符串查找和替换 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">🔍</span>字符串查找和替换</h1>
            </div>
            <div class="content">
                <h2>查找字符串内容</h2>
                
                <h3><span class="emoji">🎯</span>基本查找方法</h3>
                <div class="code-block">
<span class="keyword">text</span> = <span class="string">"Python是一门强大的编程语言，Python很受欢迎"</span>

<span class="comment"># find() - 返回第一次出现的索引，未找到返回-1</span>
<span class="function">print</span>(<span class="keyword">text</span>.<span class="function">find</span>(<span class="string">"Python"</span>))     <span class="comment"># 0</span>
<span class="function">print</span>(<span class="keyword">text</span>.<span class="function">find</span>(<span class="string">"Java"</span>))       <span class="comment"># -1</span>

<span class="comment"># rfind() - 从右边开始查找</span>
<span class="function">print</span>(<span class="keyword">text</span>.<span class="function">rfind</span>(<span class="string">"Python"</span>))    <span class="comment"># 15</span>

<span class="comment"># index() - 类似find()，但未找到会抛出异常</span>
<span class="keyword">try</span>:
    <span class="function">print</span>(<span class="keyword">text</span>.<span class="function">index</span>(<span class="string">"编程"</span>))      <span class="comment"># 8</span>
<span class="keyword">except</span> <span class="keyword">ValueError</span>:
    <span class="function">print</span>(<span class="string">"未找到"</span>)
                </div>

                <h3><span class="emoji">🔄</span>字符串替换</h3>
                <div class="code-block">
<span class="keyword">text</span> = <span class="string">"我喜欢Java，Java很好用"</span>

<span class="comment"># replace() - 替换所有匹配项</span>
<span class="keyword">new_text</span> = <span class="keyword">text</span>.<span class="function">replace</span>(<span class="string">"Java"</span>, <span class="string">"Python"</span>)
<span class="function">print</span>(<span class="keyword">new_text</span>)  <span class="comment"># 我喜欢Python，Python很好用</span>

<span class="comment"># 限制替换次数</span>
<span class="keyword">limited_replace</span> = <span class="keyword">text</span>.<span class="function">replace</span>(<span class="string">"Java"</span>, <span class="string">"Python"</span>, <span class="number">1</span>)
<span class="function">print</span>(<span class="keyword">limited_replace</span>)  <span class="comment"># 我喜欢Python，Java很好用</span>
                </div>

                <h3><span class="emoji">📊</span>计数方法</h3>
                <div class="code-block">
<span class="keyword">text</span> = <span class="string">"banana"</span>

<span class="comment"># count() - 统计子字符串出现次数</span>
<span class="function">print</span>(<span class="keyword">text</span>.<span class="function">count</span>(<span class="string">"a"</span>))      <span class="comment"># 3</span>
<span class="function">print</span>(<span class="keyword">text</span>.<span class="function">count</span>(<span class="string">"an"</span>))     <span class="comment"># 2</span>
<span class="function">print</span>(<span class="keyword">text</span>.<span class="function">count</span>(<span class="string">"ana"</span>))    <span class="comment"># 2</span>
                </div>

                <div class="tip-box">
                    <p><span class="emoji">💡</span><strong>提示：</strong>使用find()进行条件判断时，记住它返回-1表示未找到，而不是False。</p>
                </div>
            </div>
        </div>

        <!-- 第8页：字符串分割和连接 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">✂️</span>字符串分割和连接</h1>
            </div>
            <div class="content">
                <h2>字符串的分割操作</h2>
                
                <h3><span class="emoji">🔪</span>split() 方法</h3>
                <div class="code-block">
<span class="keyword">text</span> = <span class="string">"苹果,香蕉,橙子,葡萄"</span>

<span class="comment"># 按逗号分割</span>
<span class="keyword">fruits</span> = <span class="keyword">text</span>.<span class="function">split</span>(<span class="string">","</span>)
<span class="function">print</span>(<span class="keyword">fruits</span>)  <span class="comment"># ['苹果', '香蕉', '橙子', '葡萄']</span>

<span class="comment"># 按空格分割（默认）</span>
<span class="keyword">sentence</span> = <span class="string">"Python 是 最好的 编程语言"</span>
<span class="keyword">words</span> = <span class="keyword">sentence</span>.<span class="function">split</span>()
<span class="function">print</span>(<span class="keyword">words</span>)  <span class="comment"># ['Python', '是', '最好的', '编程语言']</span>

<span class="comment"># 限制分割次数</span>
<span class="keyword">data</span> = <span class="string">"name:age:city:country"</span>
<span class="keyword">parts</span> = <span class="keyword">data</span>.<span class="function">split</span>(<span class="string">":"</span>, <span class="number">2</span>)
<span class="function">print</span>(<span class="keyword">parts</span>)  <span class="comment"># ['name', 'age', 'city:country']</span>
                </div>

                <h3><span class="emoji">📝</span>其他分割方法</h3>
                <div class="code-block">
<span class="comment"># rsplit() - 从右边开始分割</span>
<span class="keyword">path</span> = <span class="string">"/home/<USER>/documents/file.txt"</span>
<span class="keyword">parts</span> = <span class="keyword">path</span>.<span class="function">rsplit</span>(<span class="string">"/"</span>, <span class="number">1</span>)
<span class="function">print</span>(<span class="keyword">parts</span>)  <span class="comment"># ['/home/<USER>/documents', 'file.txt']</span>

<span class="comment"># splitlines() - 按行分割</span>
<span class="keyword">multiline</span> = <span class="string">"""第一行
第二行
第三行"""</span>
<span class="keyword">lines</span> = <span class="keyword">multiline</span>.<span class="function">splitlines</span>()
<span class="function">print</span>(<span class="keyword">lines</span>)  <span class="comment"># ['第一行', '第二行', '第三行']</span>

<span class="comment"># partition() - 分割成三部分</span>
<span class="keyword">email</span> = <span class="string">"<EMAIL>"</span>
<span class="keyword">parts</span> = <span class="keyword">email</span>.<span class="function">partition</span>(<span class="string">"@"</span>)
<span class="function">print</span>(<span class="keyword">parts</span>)  <span class="comment"># ('user', '@', 'example.com')</span>
                </div>

                <h3><span class="emoji">🔗</span>字符串连接</h3>
                <div class="code-block">
<span class="keyword">words</span> = [<span class="string">"Python"</span>, <span class="string">"编程"</span>, <span class="string">"很"</span>, <span class="string">"有趣"</span>]

<span class="comment"># join() - 用指定分隔符连接</span>
<span class="keyword">sentence1</span> = <span class="string">" "</span>.<span class="function">join</span>(<span class="keyword">words</span>)
<span class="function">print</span>(<span class="keyword">sentence1</span>)  <span class="comment"># Python 编程 很 有趣</span>

<span class="keyword">sentence2</span> = <span class="string">"-"</span>.<span class="function">join</span>(<span class="keyword">words</span>)
<span class="function">print</span>(<span class="keyword">sentence2</span>)  <span class="comment"># Python-编程-很-有趣</span>

<span class="comment"># 连接数字列表（需要转换为字符串）</span>
<span class="keyword">numbers</span> = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>]
<span class="keyword">number_str</span> = <span class="string">","</span>.<span class="function">join</span>(<span class="function">map</span>(<span class="function">str</span>, <span class="keyword">numbers</span>))
<span class="function">print</span>(<span class="keyword">number_str</span>)  <span class="comment"># 1,2,3,4,5</span>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI 提示词模板</h3>
                    <p>"请详细解释Python字符串的分割和连接操作，包括split()、rsplit()、splitlines()、partition()和join()方法，并提供处理CSV数据、文件路径等实际场景的代码示例。"</p>
                </div>
            </div>
        </div>

        <!-- 第9页：字符串去除空白 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">🧹</span>字符串去除空白</h1>
            </div>
            <div class="content">
                <h2>处理字符串中的空白字符</h2>
                
                <h3><span class="emoji">✨</span>基本去除方法</h3>
                <div class="code-block">
<span class="keyword">text</span> = <span class="string">"   Hello Python World   "</span>

<span class="comment"># strip() - 去除两端空白</span>
<span class="function">print</span>(<span class="string">f"'{text.strip()}'"</span>)     <span class="comment"># 'Hello Python World'</span>

<span class="comment"># lstrip() - 去除左端空白</span>
<span class="function">print</span>(<span class="string">f"'{text.lstrip()}'"</span>)    <span class="comment"># 'Hello Python World   '</span>

<span class="comment"># rstrip() - 去除右端空白</span>
<span class="function">print</span>(<span class="string">f"'{text.rstrip()}'"</span>)    <span class="comment"># '   Hello Python World'</span>
                </div>

                <h3><span class="emoji">🎯</span>去除指定字符</h3>
                <div class="code-block">
<span class="keyword">text</span> = <span class="string">"...Hello World..."</span>

<span class="comment"># 去除指定字符</span>
<span class="function">print</span>(<span class="keyword">text</span>.<span class="function">strip</span>(<span class="string">"."</span>))      <span class="comment"># Hello World</span>

<span class="keyword">url</span> = <span class="string">"https://www.example.com/"</span>
<span class="comment"># 去除末尾的斜杠</span>
<span class="function">print</span>(<span class="keyword">url</span>.<span class="function">rstrip</span>(<span class="string">"/"</span>))      <span class="comment"># https://www.example.com</span>

<span class="keyword">phone</span> = <span class="string">"(*************"</span>
<span class="comment"># 去除括号和连字符</span>
<span class="keyword">clean_phone</span> = <span class="keyword">phone</span>.<span class="function">strip</span>(<span class="string">"()-"</span>)
<span class="function">print</span>(<span class="keyword">clean_phone</span>)  <span class="comment"># 123) 456-789</span>
                </div>

                <h3><span class="emoji">🔧</span>实际应用场景</h3>
                <div class="code-block">
<span class="comment"># 处理用户输入</span>
<span class="keyword">def</span> <span class="function">clean_user_input</span>(<span class="keyword">user_input</span>):
    <span class="keyword">return</span> <span class="keyword">user_input</span>.<span class="function">strip</span>().<span class="function">lower</span>()

<span class="keyword">username</span> = <span class="function">clean_user_input</span>(<span class="string">"  ADMIN  "</span>)
<span class="function">print</span>(<span class="keyword">username</span>)  <span class="comment"># admin</span>

<span class="comment"># 处理CSV数据</span>
<span class="keyword">csv_line</span> = <span class="string">"张三, 25 , 北京 ,工程师 "</span>
<span class="keyword">data</span> = [<span class="keyword">item</span>.<span class="function">strip</span>() <span class="keyword">for</span> <span class="keyword">item</span> <span class="keyword">in</span> <span class="keyword">csv_line</span>.<span class="function">split</span>(<span class="string">","</span>)]
<span class="function">print</span>(<span class="keyword">data</span>)  <span class="comment"># ['张三', '25', '北京', '工程师']</span>

<span class="comment"># 清理文件路径</span>
<span class="keyword">def</span> <span class="function">normalize_path</span>(<span class="keyword">path</span>):
    <span class="keyword">return</span> <span class="keyword">path</span>.<span class="function">strip</span>().<span class="function">rstrip</span>(<span class="string">"/\\"</span>)

<span class="keyword">clean_path</span> = <span class="function">normalize_path</span>(<span class="string">"  /home/<USER>/  "</span>)
<span class="function">print</span>(<span class="keyword">clean_path</span>)  <span class="comment"># /home/<USER>/span>
                </div>

                <h3><span class="emoji">⚡</span>高级技巧</h3>
                <div class="code-block">
<span class="comment"># 去除所有空白字符（包括中间的）</span>
<span class="keyword">text</span> = <span class="string">"  H e l l o   W o r l d  "</span>
<span class="keyword">no_spaces</span> = <span class="string">""</span>.<span class="function">join</span>(<span class="keyword">text</span>.<span class="function">split</span>())
<span class="function">print</span>(<span class="keyword">no_spaces</span>)  <span class="comment"># HelloWorld</span>

<span class="comment"># 标准化空白字符</span>
<span class="keyword">messy_text</span> = <span class="string">"Python\t\tis\n\nvery\r\ngood"</span>
<span class="keyword">clean_text</span> = <span class="string">" "</span>.<span class="function">join</span>(<span class="keyword">messy_text</span>.<span class="function">split</span>())
<span class="function">print</span>(<span class="keyword">clean_text</span>)  <span class="comment"># Python is very good</span>
                </div>

                <div class="tip-box">
                    <p><span class="emoji">⚠️</span><strong>注意：</strong>strip()方法只能去除字符串两端的字符，不能去除中间的字符。要去除中间的字符，需要使用replace()或正则表达式。</p>
                </div>
            </div>
        </div>

        <!-- 第10页：字符串判断方法 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">🔍</span>字符串判断方法</h1>
            </div>
            <div class="content">
                <h2>字符串内容检测</h2>
                
                <h3><span class="emoji">🔤</span>字符类型判断</h3>
                <div class="code-block">
<span class="comment"># 数字判断</span>
<span class="function">print</span>(<span class="string">"123"</span>.<span class="function">isdigit</span>())      <span class="comment"># True</span>
<span class="function">print</span>(<span class="string">"12.3"</span>.<span class="function">isdigit</span>())     <span class="comment"># False</span>
<span class="function">print</span>(<span class="string">"123"</span>.<span class="function">isnumeric</span>())    <span class="comment"># True</span>
<span class="function">print</span>(<span class="string">"Ⅴ"</span>.<span class="function">isnumeric</span>())      <span class="comment"># True (罗马数字)</span>

<span class="comment"># 字母判断</span>
<span class="function">print</span>(<span class="string">"abc"</span>.<span class="function">isalpha</span>())      <span class="comment"># True</span>
<span class="function">print</span>(<span class="string">"abc123"</span>.<span class="function">isalpha</span>())   <span class="comment"># False</span>
<span class="function">print</span>(<span class="string">"你好"</span>.<span class="function">isalpha</span>())      <span class="comment"># True</span>

<span class="comment"># 字母数字判断</span>
<span class="function">print</span>(<span class="string">"abc123"</span>.<span class="function">isalnum</span>())   <span class="comment"># True</span>
<span class="function">print</span>(<span class="string">"abc-123"</span>.<span class="function">isalnum</span>())  <span class="comment"># False</span>
                </div>

                <h3><span class="emoji">📏</span>格式判断</h3>
                <div class="code-block">
<span class="comment"># 空白字符判断</span>
<span class="function">print</span>(<span class="string">"   "</span>.<span class="function">isspace</span>())      <span class="comment"># True</span>
<span class="function">print</span>(<span class="string">"\t\n"</span>.<span class="function">isspace</span>())     <span class="comment"># True</span>
<span class="function">print</span>(<span class="string">""</span>.<span class="function">isspace</span>())        <span class="comment"># False</span>

<span class="comment"># 标识符判断</span>
<span class="function">print</span>(<span class="string">"variable_name"</span>.<span class="function">isidentifier</span>())  <span class="comment"># True</span>
<span class="function">print</span>(<span class="string">"123abc"</span>.<span class="function">isidentifier</span>())       <span class="comment"># False</span>
<span class="function">print</span>(<span class="string">"_private"</span>.<span class="function">isidentifier</span>())     <span class="comment"># True</span>

<span class="comment"># 可打印字符判断</span>
<span class="function">print</span>(<span class="string">"Hello"</span>.<span class="function">isprintable</span>())    <span class="comment"># True</span>
<span class="function">print</span>(<span class="string">"Hello\n"</span>.<span class="function">isprintable</span>())  <span class="comment"># False</span>
                </div>

                <h3><span class="emoji">🎯</span>开头结尾判断</h3>
                <div class="code-block">
<span class="keyword">filename</span> = <span class="string">"document.pdf"</span>
<span class="keyword">url</span> = <span class="string">"https://www.example.com"</span>

<span class="comment"># 开头判断</span>
<span class="function">print</span>(<span class="keyword">url</span>.<span class="function">startswith</span>(<span class="string">"https"</span>))    <span class="comment"># True</span>
<span class="function">print</span>(<span class="keyword">url</span>.<span class="function">startswith</span>(<span class="string">"http"</span>))     <span class="comment"># True</span>

<span class="comment"># 结尾判断</span>
<span class="function">print</span>(<span class="keyword">filename</span>.<span class="function">endswith</span>(<span class="string">".pdf"</span>))   <span class="comment"># True</span>
<span class="function">print</span>(<span class="keyword">filename</span>.<span class="function">endswith</span>(<span class="string">".txt"</span>))   <span class="comment"># False</span>

<span class="comment"># 多个选项判断</span>
<span class="keyword">image_file</span> = <span class="string">"photo.jpg"</span>
<span class="keyword">image_extensions</span> = (<span class="string">".jpg"</span>, <span class="string">".png"</span>, <span class="string">".gif"</span>, <span class="string">".bmp"</span>)
<span class="function">print</span>(<span class="keyword">image_file</span>.<span class="function">endswith</span>(<span class="keyword">image_extensions</span>))  <span class="comment"># True</span>
                </div>

                <h3><span class="emoji">🛠️</span>实际应用</h3>
                <div class="code-block">
<span class="comment"># 验证用户输入</span>
<span class="keyword">def</span> <span class="function">validate_username</span>(<span class="keyword">username</span>):
    <span class="keyword">if</span> <span class="keyword">not</span> <span class="keyword">username</span>:
        <span class="keyword">return</span> <span class="string">"用户名不能为空"</span>
    <span class="keyword">if</span> <span class="keyword">not</span> <span class="keyword">username</span>.<span class="function">isalnum</span>():
        <span class="keyword">return</span> <span class="string">"用户名只能包含字母和数字"</span>
    <span class="keyword">if</span> <span class="keyword">username</span>[<span class="number">0</span>].<span class="function">isdigit</span>():
        <span class="keyword">return</span> <span class="string">"用户名不能以数字开头"</span>
    <span class="keyword">return</span> <span class="string">"用户名有效"</span>

<span class="function">print</span>(<span class="function">validate_username</span>(<span class="string">"user123"</span>))   <span class="comment"># 用户名有效</span>
<span class="function">print</span>(<span class="function">validate_username</span>(<span class="string">"123user"</span>))   <span class="comment"># 用户名不能以数字开头</span>

<span class="comment"># 文件类型检测</span>
<span class="keyword">def</span> <span class="function">get_file_type</span>(<span class="keyword">filename</span>):
    <span class="keyword">if</span> <span class="keyword">filename</span>.<span class="function">endswith</span>((<span class="string">".jpg"</span>, <span class="string">".png"</span>, <span class="string">".gif"</span>)):
        <span class="keyword">return</span> <span class="string">"图片文件"</span>
    <span class="keyword">elif</span> <span class="keyword">filename</span>.<span class="function">endswith</span>((<span class="string">".mp4"</span>, <span class="string">".avi"</span>, <span class="string">".mov"</span>)):
        <span class="keyword">return</span> <span class="string">"视频文件"</span>
    <span class="keyword">elif</span> <span class="keyword">filename</span>.<span class="function">endswith</span>((<span class="string">".txt"</span>, <span class="string">".md"</span>, <span class="string">".py"</span>)):
        <span class="keyword">return</span> <span class="string">"文本文件"</span>
    <span class="keyword">else</span>:
        <span class="keyword">return</span> <span class="string">"未知类型"</span>

<span class="function">print</span>(<span class="function">get_file_type</span>(<span class="string">"photo.jpg"</span>))    <span class="comment"># 图片文件</span>
<span class="function">print</span>(<span class="function">get_file_type</span>(<span class="string">"script.py"</span>))    <span class="comment"># 文本文件</span>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI 提示词模板</h3>
                    <p>"请介绍Python字符串的判断方法，包括isdigit()、isalpha()、isalnum()、startswith()、endswith()等，并提供数据验证和文件处理的实际应用案例。"</p>
                </div>
            </div>
        </div>

        <!-- 第11页：字符串格式化 - % 格式化 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">📝</span>字符串格式化 - % 方式</h1>
            </div>
            <div class="content">
                <h2>传统的字符串格式化</h2>
                
                <h3><span class="emoji">🔢</span>基本格式化符号</h3>
                <div class="code-block">
<span class="comment"># 字符串格式化</span>
<span class="keyword">name</span> = <span class="string">"张三"</span>
<span class="keyword">age</span> = <span class="number">25</span>
<span class="keyword">height</span> = <span class="number">175.5</span>

<span class="comment"># %s - 字符串</span>
<span class="function">print</span>(<span class="string">"姓名：%s"</span> % <span class="keyword">name</span>)  <span class="comment"># 姓名：张三</span>

<span class="comment"># %d - 整数</span>
<span class="function">print</span>(<span class="string">"年龄：%d岁"</span> % <span class="keyword">age</span>)  <span class="comment"># 年龄：25岁</span>

<span class="comment"># %f - 浮点数</span>
<span class="function">print</span>(<span class="string">"身高：%.1f厘米"</span> % <span class="keyword">height</span>)  <span class="comment"># 身高：175.5厘米</span>

<span class="comment"># 多个参数</span>
<span class="function">print</span>(<span class="string">"我叫%s，今年%d岁，身高%.1f厘米"</span> % (<span class="keyword">name</span>, <span class="keyword">age</span>, <span class="keyword">height</span>))
                </div>

                <h3><span class="emoji">🎯</span>格式化控制</h3>
                <div class="code-block">
<span class="keyword">number</span> = <span class="number">42</span>
<span class="keyword">pi</span> = <span class="number">3.14159</span>

<span class="comment"># 宽度控制</span>
<span class="function">print</span>(<span class="string">"|%5d|"</span> % <span class="keyword">number</span>)     <span class="comment"># |   42|</span>
<span class="function">print</span>(<span class="string">"|%-5d|"</span> % <span class="keyword">number</span>)    <span class="comment"># |42   |</span>

<span class="comment"># 零填充</span>
<span class="function">print</span>(<span class="string">"|%05d|"</span> % <span class="keyword">number</span>)     <span class="comment"># |00042|</span>

<span class="comment"># 精度控制</span>
<span class="function">print</span>(<span class="string">"%.2f"</span> % <span class="keyword">pi</span>)          <span class="comment"># 3.14</span>
<span class="function">print</span>(<span class="string">"%.4f"</span> % <span class="keyword">pi</span>)          <span class="comment"># 3.1416</span>

<span class="comment"># 科学计数法</span>
<span class="function">print</span>(<span class="string">"%.2e"</span> % <span class="number">1234.5</span>)      <span class="comment"># 1.23e+03</span>
                </div>

                <h3><span class="emoji">📊</span>实际应用</h3>
                <div class="code-block">
<span class="comment"># 生成报表</span>
<span class="keyword">products</span> = [
    (<span class="string">"苹果"</span>, <span class="number">5.50</span>, <span class="number">100</span>),
    (<span class="string">"香蕉"</span>, <span class="number">3.20</span>, <span class="number">80</span>),
    (<span class="string">"橙子"</span>, <span class="number">4.80</span>, <span class="number">60</span>)
]

<span class="function">print</span>(<span class="string">"%-8s %8s %8s %10s"</span> % (<span class="string">"商品"</span>, <span class="string">"单价"</span>, <span class="string">"数量"</span>, <span class="string">"总价"</span>))
<span class="function">print</span>(<span class="string">"-"</span> * <span class="number">35</span>)

<span class="keyword">for</span> <span class="keyword">name</span>, <span class="keyword">price</span>, <span class="keyword">qty</span> <span class="keyword">in</span> <span class="keyword">products</span>:
    <span class="keyword">total</span> = <span class="keyword">price</span> * <span class="keyword">qty</span>
    <span class="function">print</span>(<span class="string">"%-8s %8.2f %8d %10.2f"</span> % (<span class="keyword">name</span>, <span class="keyword">price</span>, <span class="keyword">qty</span>, <span class="keyword">total</span>))
                </div>

                <div class="tip-box">
                    <p><span class="emoji">⚠️</span><strong>注意：</strong>% 格式化是较老的方式，现在推荐使用 format() 方法或 f-string。</p>
                </div>
            </div>
        </div>

        <!-- 第12页：字符串格式化 - format() 方法 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">🎨</span>字符串格式化 - format() 方法</h1>
            </div>
            <div class="content">
                <h2>更强大的格式化方式</h2>
                
                <h3><span class="emoji">🔧</span>基本用法</h3>
                <div class="code-block">
<span class="keyword">name</span> = <span class="string">"李四"</span>
<span class="keyword">age</span> = <span class="number">28</span>
<span class="keyword">salary</span> = <span class="number">8500.75</span>

<span class="comment"># 位置参数</span>
<span class="function">print</span>(<span class="string">"我叫{}，今年{}岁"</span>.<span class="function">format</span>(<span class="keyword">name</span>, <span class="keyword">age</span>))

<span class="comment"># 索引参数</span>
<span class="function">print</span>(<span class="string">"我叫{0}，今年{1}岁，{0}很高兴认识你"</span>.<span class="function">format</span>(<span class="keyword">name</span>, <span class="keyword">age</span>))

<span class="comment"># 关键字参数</span>
<span class="function">print</span>(<span class="string">"姓名：{name}，年龄：{age}，工资：{salary}"</span>.<span class="function">format</span>(
    <span class="keyword">name</span>=<span class="keyword">name</span>, <span class="keyword">age</span>=<span class="keyword">age</span>, <span class="keyword">salary</span>=<span class="keyword">salary</span>
))
                </div>

                <h3><span class="emoji">🎯</span>格式化规范</h3>
                <div class="code-block">
<span class="keyword">number</span> = <span class="number">1234.5678</span>

<span class="comment"># 数字格式化</span>
<span class="function">print</span>(<span class="string">"{:.2f}"</span>.<span class="function">format</span>(<span class="keyword">number</span>))      <span class="comment"># 1234.57</span>
<span class="function">print</span>(<span class="string">"{:,.2f}"</span>.<span class="function">format</span>(<span class="keyword">number</span>))     <span class="comment"># 1,234.57</span>
<span class="function">print</span>(<span class="string">"{:.2%}"</span>.<span class="function">format</span>(<span class="number">0.85</span>))       <span class="comment"># 85.00%</span>

<span class="comment"># 对齐和填充</span>
<span class="keyword">text</span> = <span class="string">"Python"</span>
<span class="function">print</span>(<span class="string">"|{:>10}|"</span>.<span class="function">format</span>(<span class="keyword">text</span>))    <span class="comment"># |    Python|</span>
<span class="function">print</span>(<span class="string">"|{:<10}|"</span>.<span class="function">format</span>(<span class="keyword">text</span>))    <span class="comment"># |Python    |</span>
<span class="function">print</span>(<span class="string">"|{:^10}|"</span>.<span class="function">format</span>(<span class="keyword">text</span>))    <span class="comment"># |  Python  |</span>
<span class="function">print</span>(<span class="string">"|{:*^10}|"</span>.<span class="function">format</span>(<span class="keyword">text</span>))   <span class="comment"># |**Python**|</span>
                </div>

                <h3><span class="emoji">🌟</span>高级功能</h3>
                <div class="code-block">
<span class="comment"># 字典格式化</span>
<span class="keyword">person</span> = {<span class="string">"name"</span>: <span class="string">"王五"</span>, <span class="string">"age"</span>: <span class="number">30</span>, <span class="string">"city"</span>: <span class="string">"上海"</span>}
<span class="function">print</span>(<span class="string">"我叫{name}，今年{age}岁，住在{city}"</span>.<span class="function">format</span>(**<span class="keyword">person</span>))

<span class="comment"># 列表格式化</span>
<span class="keyword">data</span> = [<span class="string">"Python"</span>, <span class="number">3.9</span>, <span class="string">"编程语言"</span>]
<span class="function">print</span>(<span class="string">"{0[0]}版本{0[1]}是最好的{0[2]}"</span>.<span class="function">format</span>(<span class="keyword">data</span>))

<span class="comment"># 对象属性格式化</span>
<span class="keyword">import</span> <span class="keyword">datetime</span>
<span class="keyword">now</span> = <span class="keyword">datetime</span>.<span class="function">datetime</span>.<span class="function">now</span>()
<span class="function">print</span>(<span class="string">"现在是{0.year}年{0.month}月{0.day}日"</span>.<span class="function">format</span>(<span class="keyword">now</span>))
                </div>

                <h3><span class="emoji">📋</span>实际应用案例</h3>
                <div class="code-block">
<span class="comment"># 生成HTML表格</span>
<span class="keyword">def</span> <span class="function">generate_table_row</span>(<span class="keyword">name</span>, <span class="keyword">score</span>, <span class="keyword">grade</span>):
    <span class="keyword">template</span> = <span class="string">"<tr><td>{name}</td><td>{score:.1f}</td><td>{grade}</td></tr>"</span>
    <span class="keyword">return</span> <span class="keyword">template</span>.<span class="function">format</span>(<span class="keyword">name</span>=<span class="keyword">name</span>, <span class="keyword">score</span>=<span class="keyword">score</span>, <span class="keyword">grade</span>=<span class="keyword">grade</span>)

<span class="function">print</span>(<span class="function">generate_table_row</span>(<span class="string">"张三"</span>, <span class="number">95.5</span>, <span class="string">"A"</span>))

<span class="comment"># 日志格式化</span>
<span class="keyword">log_template</span> = <span class="string">"[{timestamp}] {level:>8} - {message}"</span>
<span class="function">print</span>(<span class="keyword">log_template</span>.<span class="function">format</span>(
    <span class="keyword">timestamp</span>=<span class="string">"2024-01-15 10:30:45"</span>,
    <span class="keyword">level</span>=<span class="string">"ERROR"</span>,
    <span class="keyword">message</span>=<span class="string">"数据库连接失败"</span>
))
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI 提示词模板</h3>
                    <p>"请详细介绍Python的format()方法进行字符串格式化，包括位置参数、关键字参数、格式化规范、对齐填充等功能，并提供生成报表和日志的实际应用示例。"</p>
                </div>
            </div>
        </div>

        <!-- 第13页：f-string 格式化 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">🚀</span>f-string 格式化</h1>
            </div>
            <div class="content">
                <h2>最现代的字符串格式化方式</h2>
                
                <h3><span class="emoji">⚡</span>基本语法</h3>
                <div class="code-block">
<span class="keyword">name</span> = <span class="string">"赵六"</span>
<span class="keyword">age</span> = <span class="number">32</span>
<span class="keyword">height</span> = <span class="number">178.5</span>

<span class="comment"># f-string 基本用法</span>
<span class="function">print</span>(<span class="string">f"我叫{name}，今年{age}岁"</span>)
<span class="function">print</span>(<span class="string">f"身高：{height}厘米"</span>)

<span class="comment"># 表达式计算</span>
<span class="function">print</span>(<span class="string">f"明年我{age + 1}岁"</span>)
<span class="function">print</span>(<span class="string">f"BMI指数：{70 / (height/100)**2:.1f}"</span>)

<span class="comment"># 函数调用</span>
<span class="function">print</span>(<span class="string">f"姓名长度：{len(name)}个字符"</span>)
<span class="function">print</span>(<span class="string">f"大写姓名：{name.upper()}"</span>)
                </div>

                <h3><span class="emoji">🎨</span>格式化选项</h3>
                <div class="code-block">
<span class="keyword">number</span> = <span class="number">1234.5678</span>
<span class="keyword">percentage</span> = <span class="number">0.8567</span>

<span class="comment"># 数字格式化</span>
<span class="function">print</span>(<span class="string">f"保留2位小数：{number:.2f}"</span>)        <span class="comment"># 1234.57</span>
<span class="function">print</span>(<span class="string">f"千分位分隔：{number:,.2f}"</span>)       <span class="comment"># 1,234.57</span>
<span class="function">print</span>(<span class="string">f"百分比：{percentage:.1%}"</span>)        <span class="comment"># 85.7%</span>
<span class="function">print</span>(<span class="string">f"科学计数法：{number:.2e}"</span>)        <span class="comment"># 1.23e+03</span>

<span class="comment"># 进制转换</span>
<span class="keyword">num</span> = <span class="number">255</span>
<span class="function">print</span>(<span class="string">f"十进制：{num}"</span>)                  <span class="comment"># 255</span>
<span class="function">print</span>(<span class="string">f"二进制：{num:b}"</span>)                <span class="comment"># 11111111</span>
<span class="function">print</span>(<span class="string">f"八进制：{num:o}"</span>)                <span class="comment"># 377</span>
<span class="function">print</span>(<span class="string">f"十六进制：{num:x}"</span>)              <span class="comment"># ff</span>
                </div>

                <h3><span class="emoji">📐</span>对齐和填充</h3>
                <div class="code-block">
<span class="keyword">text</span> = <span class="string">"Python"</span>
<span class="keyword">number</span> = <span class="number">42</span>

<span class="comment"># 字符串对齐</span>
<span class="function">print</span>(<span class="string">f"|{text:>10}|"</span>)     <span class="comment"># |    Python|</span>
<span class="function">print</span>(<span class="string">f"|{text:<10}|"</span>)     <span class="comment"># |Python    |</span>
<span class="function">print</span>(<span class="string">f"|{text:^10}|"</span>)     <span class="comment"># |  Python  |</span>

<span class="comment"># 自定义填充字符</span>
<span class="function">print</span>(<span class="string">f"|{text:*^12}|"</span>)    <span class="comment"># |***Python***|</span>
<span class="function">print</span>(<span class="string">f"|{number:0>5}|"</span>)    <span class="comment"># |00042|</span>

<span class="comment"># 动态宽度</span>
<span class="keyword">width</span> = <span class="number">15</span>
<span class="function">print</span>(<span class="string">f"|{text:^{width}}|"</span>)  <span class="comment"># |    Python     |</span>
                </div>

                <h3><span class="emoji">🔥</span>高级技巧</h3>
                <div class="code-block">
<span class="comment"># 嵌套f-string</span>
<span class="keyword">precision</span> = <span class="number">3</span>
<span class="keyword">value</span> = <span class="number">3.14159</span>
<span class="function">print</span>(<span class="string">f"π ≈ {value:.{precision}f}"</span>)  <span class="comment"># π ≈ 3.142</span>

<span class="comment"># 条件表达式</span>
<span class="keyword">score</span> = <span class="number">85</span>
<span class="function">print</span>(<span class="string">f"成绩：{score}分，{'及格' if score >= 60 else '不及格'}"</span>)

<span class="comment"># 字典和列表</span>
<span class="keyword">person</span> = {<span class="string">"name"</span>: <span class="string">"小明"</span>, <span class="string">"age"</span>: <span class="number">20</span>}
<span class="keyword">scores</span> = [<span class="number">90</span>, <span class="number">85</span>, <span class="number">92</span>]
<span class="function">print</span>(<span class="string">f"{person['name']}的平均分是{sum(scores)/len(scores):.1f}"</span>)

<span class="comment"># 日期时间格式化</span>
<span class="keyword">from</span> <span class="keyword">datetime</span> <span class="keyword">import</span> <span class="keyword">datetime</span>
<span class="keyword">now</span> = <span class="function">datetime</span>.<span class="function">now</span>()
<span class="function">print</span>(<span class="string">f"当前时间：{now:%Y-%m-%d %H:%M:%S}"</span>)
                </div>

                <h3><span class="emoji">💼</span>实际应用</h3>
                <div class="code-block">
<span class="comment"># 生成SQL查询</span>
<span class="keyword">def</span> <span class="function">build_query</span>(<span class="keyword">table</span>, <span class="keyword">fields</span>, <span class="keyword">condition</span>):
    <span class="keyword">return</span> <span class="string">f"SELECT {', '.join(fields)} FROM {table} WHERE {condition}"</span>

<span class="keyword">query</span> = <span class="function">build_query</span>(<span class="string">"users"</span>, [<span class="string">"name"</span>, <span class="string">"email"</span>], <span class="string">"age > 18"</span>)
<span class="function">print</span>(<span class="keyword">query</span>)

<span class="comment"># 进度条显示</span>
<span class="keyword">def</span> <span class="function">show_progress</span>(<span class="keyword">current</span>, <span class="keyword">total</span>):
    <span class="keyword">percentage</span> = <span class="keyword">current</span> / <span class="keyword">total</span>
    <span class="keyword">bar_length</span> = <span class="number">20</span>
    <span class="keyword">filled_length</span> = <span class="function">int</span>(<span class="keyword">bar_length</span> * <span class="keyword">percentage</span>)
    <span class="keyword">bar</span> = <span class="string">"█"</span> * <span class="keyword">filled_length</span> + <span class="string">"░"</span> * (<span class="keyword">bar_length</span> - <span class="keyword">filled_length</span>)
    <span class="keyword">return</span> <span class="string">f"进度: |{bar}| {percentage:.1%} ({current}/{total})"</span>

<span class="function">print</span>(<span class="function">show_progress</span>(<span class="number">75</span>, <span class="number">100</span>))
                </div>

                <div class="tip-box">
                    <p><span class="emoji">🌟</span><strong>推荐：</strong>f-string 是Python 3.6+推荐的字符串格式化方式，性能最好，语法最简洁。</p>
                </div>
            </div>
        </div>

        <!-- 第14页：字符串编码和解码 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">🔐</span>字符串编码和解码</h1>
            </div>
            <div class="content">
                <h2>处理不同字符编码</h2>
                
                <h3><span class="emoji">📚</span>编码基础概念</h3>
                <div class="code-block">
<span class="comment"># Python 3 中字符串默认是 Unicode</span>
<span class="keyword">text</span> = <span class="string">"Hello 世界 🐍"</span>
<span class="function">print</span>(<span class="function">type</span>(<span class="keyword">text</span>))        <span class="comment"># <class 'str'></span>
<span class="function">print</span>(<span class="function">len</span>(<span class="keyword">text</span>))         <span class="comment"># 10 (字符数)</span>

<span class="comment"># 编码为字节</span>
<span class="keyword">encoded</span> = <span class="keyword">text</span>.<span class="function">encode</span>(<span class="string">"utf-8"</span>)
<span class="function">print</span>(<span class="function">type</span>(<span class="keyword">encoded</span>))     <span class="comment"># <class 'bytes'></span>
<span class="function">print</span>(<span class="function">len</span>(<span class="keyword">encoded</span>))      <span class="comment"># 14 (字节数)</span>
<span class="function">print</span>(<span class="keyword">encoded</span>)         <span class="comment"># b'Hello \xe4\xb8\x96\xe7\x95\x8c \xf0\x9f\x90\x8d'</span>

<span class="comment"># 解码为字符串</span>
<span class="keyword">decoded</span> = <span class="keyword">encoded</span>.<span class="function">decode</span>(<span class="string">"utf-8"</span>)
<span class="function">print</span>(<span class="keyword">decoded</span>)         <span class="comment"># Hello 世界 🐍</span>
                </div>

                <h3><span class="emoji">🌍</span>常见编码格式</h3>
                <div class="code-block">
<span class="keyword">chinese_text</span> = <span class="string">"你好，Python！"</span>

<span class="comment"># UTF-8 编码（推荐）</span>
<span class="keyword">utf8_bytes</span> = <span class="keyword">chinese_text</span>.<span class="function">encode</span>(<span class="string">"utf-8"</span>)
<span class="function">print</span>(<span class="string">f"UTF-8: {utf8_bytes}"</span>)

<span class="comment"># GBK 编码（中文）</span>
<span class="keyword">gbk_bytes</span> = <span class="keyword">chinese_text</span>.<span class="function">encode</span>(<span class="string">"gbk"</span>)
<span class="function">print</span>(<span class="string">f"GBK: {gbk_bytes}"</span>)

<span class="comment"># ASCII 编码（只支持英文）</span>
<span class="keyword">english_text</span> = <span class="string">"Hello Python"</span>
<span class="keyword">ascii_bytes</span> = <span class="keyword">english_text</span>.<span class="function">encode</span>(<span class="string">"ascii"</span>)
<span class="function">print</span>(<span class="string">f"ASCII: {ascii_bytes}"</span>)

<span class="comment"># 编码长度比较</span>
<span class="function">print</span>(<span class="string">f"UTF-8字节数: {len(utf8_bytes)}"</span>)
<span class="function">print</span>(<span class="string">f"GBK字节数: {len(gbk_bytes)}"</span>)
                </div>

                <h3><span class="emoji">⚠️</span>错误处理</h3>
                <div class="code-block">
<span class="keyword">text</span> = <span class="string">"Hello 世界"</span>

<span class="comment"># 编码错误处理</span>
<span class="keyword">try</span>:
    <span class="keyword">ascii_bytes</span> = <span class="keyword">text</span>.<span class="function">encode</span>(<span class="string">"ascii"</span>)
except <span class="keyword">UnicodeEncodeError</span> <span class="keyword">as</span> <span class="keyword">e</span>:
    <span class="function">print</span>(<span class="string">f"编码错误: {e}"</span>)

<span class="comment"># 错误处理策略</span>
<span class="keyword">ascii_ignore</span> = <span class="keyword">text</span>.<span class="function">encode</span>(<span class="string">"ascii"</span>, <span class="keyword">errors</span>=<span class="string">"ignore"</span>)
<span class="function">print</span>(<span class="string">f"忽略错误: {ascii_ignore}"</span>)  <span class="comment"># b'Hello '</span>

<span class="keyword">ascii_replace</span> = <span class="keyword">text</span>.<span class="function">encode</span>(<span class="string">"ascii"</span>, <span class="keyword">errors</span>=<span class="string">"replace"</span>)
<span class="function">print</span>(<span class="string">f"替换错误: {ascii_replace}"</span>)  <span class="comment"># b'Hello ??'</span>

<span class="comment"># 解码错误处理</span>
<span class="keyword">bad_bytes</span> = <span class="string">b"\xff\xfe"</span>
<span class="keyword">try</span>:
    <span class="keyword">decoded</span> = <span class="keyword">bad_bytes</span>.<span class="function">decode</span>(<span class="string">"utf-8"</span>)
except <span class="keyword">UnicodeDecodeError</span>:
    <span class="keyword">decoded</span> = <span class="keyword">bad_bytes</span>.<span class="function">decode</span>(<span class="string">"utf-8"</span>, <span class="keyword">errors</span>=<span class="string">"replace"</span>)
    <span class="function">print</span>(<span class="string">f"解码结果: {decoded}"</span>)
                </div>

                <h3><span class="emoji">📁</span>文件编码处理</h3>
                <div class="code-block">
<span class="comment"># 写入不同编码的文件</span>
<span class="keyword">content</span> = <span class="string">"Python编程学习笔记\n包含中文内容"</span>

<span class="comment"># UTF-8 文件</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"utf8_file.txt"</span>, <span class="string">"w"</span>, <span class="keyword">encoding</span>=<span class="string">"utf-8"</span>) <span class="keyword">as</span> <span class="keyword">f</span>:
    <span class="keyword">f</span>.<span class="function">write</span>(<span class="keyword">content</span>)

<span class="comment"># GBK 文件</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"gbk_file.txt"</span>, <span class="string">"w"</span>, <span class="keyword">encoding</span>=<span class="string">"gbk"</span>) <span class="keyword">as</span> <span class="keyword">f</span>:
    <span class="keyword">f</span>.<span class="function">write</span>(<span class="keyword">content</span>)

<span class="comment"># 读取文件时指定编码</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"utf8_file.txt"</span>, <span class="string">"r"</span>, <span class="keyword">encoding</span>=<span class="string">"utf-8"</span>) <span class="keyword">as</span> <span class="keyword">f</span>:
    <span class="keyword">data</span> = <span class="keyword">f</span>.<span class="function">read</span>()
    <span class="function">print</span>(<span class="keyword">data</span>)

<span class="comment"># 自动检测编码（需要安装 chardet 库）</span>
<span class="keyword">import</span> <span class="keyword">chardet</span>

<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"gbk_file.txt"</span>, <span class="string">"rb"</span>) <span class="keyword">as</span> <span class="keyword">f</span>:
    <span class="keyword">raw_data</span> = <span class="keyword">f</span>.<span class="function">read</span>()
    <span class="keyword">encoding</span> = <span class="keyword">chardet</span>.<span class="function">detect</span>(<span class="keyword">raw_data</span>)[<span class="string">"encoding"</span>]
    <span class="function">print</span>(<span class="string">f"检测到的编码: {encoding}"</span>)
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI 提示词模板</h3>
                    <p>"请解释Python中字符串的编码和解码概念，包括UTF-8、GBK、ASCII等编码格式的区别，以及在文件处理和网络传输中如何正确处理字符编码问题。"</p>
                </div>
            </div>
        </div>

        <!-- 第15页：正则表达式基础 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">🔍</span>正则表达式基础</h1>
            </div>
            <div class="content">
                <h2>强大的文本模式匹配</h2>
                
                <h3><span class="emoji">📖</span>正则表达式简介</h3>
                <div class="code-block">
<span class="keyword">import</span> <span class="keyword">re</span>

<span class="comment"># 基本匹配</span>
<span class="keyword">text</span> = <span class="string">"我的电话是13812345678，邮箱是****************"</span>

<span class="comment"># 查找手机号</span>
<span class="keyword">phone_pattern</span> = <span class="string">r"1[3-9]\d{9}"</span>
<span class="keyword">phone_match</span> = <span class="keyword">re</span>.<span class="function">search</span>(<span class="keyword">phone_pattern</span>, <span class="keyword">text</span>)
<span class="keyword">if</span> <span class="keyword">phone_match</span>:
    <span class="function">print</span>(<span class="string">f"找到手机号: {phone_match.group()}"</span>)  <span class="comment"># 13812345678</span>

<span class="comment"># 查找邮箱</span>
<span class="keyword">email_pattern</span> = <span class="string">r"\w+@\w+\.\w+"</span>
<span class="keyword">email_match</span> = <span class="keyword">re</span>.<span class="function">search</span>(<span class="keyword">email_pattern</span>, <span class="keyword">text</span>)
<span class="keyword">if</span> <span class="keyword">email_match</span>:
    <span class="function">print</span>(<span class="string">f"找到邮箱: {email_match.group()}"</span>)    <span class="comment"># <EMAIL></span>
                </div>

                <h3><span class="emoji">🔤</span>基本元字符</h3>
                <div class="code-block">
<span class="comment"># 常用元字符示例</span>
<span class="keyword">test_strings</span> = [
    <span class="string">"abc123"</span>,
    <span class="string">"hello world"</span>,
    <span class="string">"Python3.9"</span>,
    <span class="string">"<EMAIL>"</span>
]

<span class="comment"># \d 匹配数字</span>
<span class="keyword">digit_pattern</span> = <span class="string">r"\d+"</span>
<span class="keyword">for</span> <span class="keyword">s</span> <span class="keyword">in</span> <span class="keyword">test_strings</span>:
    <span class="keyword">matches</span> = <span class="keyword">re</span>.<span class="function">findall</span>(<span class="keyword">digit_pattern</span>, <span class="keyword">s</span>)
    <span class="function">print</span>(<span class="string">f"{s} 中的数字: {matches}"</span>)

<span class="comment"># \w 匹配字母数字下划线</span>
<span class="keyword">word_pattern</span> = <span class="string">r"\w+"</span>
<span class="keyword">text</span> = <span class="string">"hello-world_123"</span>
<span class="keyword">words</span> = <span class="keyword">re</span>.<span class="function">findall</span>(<span class="keyword">word_pattern</span>, <span class="keyword">text</span>)
<span class="function">print</span>(<span class="string">f"单词: {words}"</span>)  <span class="comment"># ['hello', 'world_123']</span>

<span class="comment"># \s 匹配空白字符</span>
<span class="keyword">space_pattern</span> = <span class="string">r"\s+"</span>
<span class="keyword">text</span> = <span class="string">"Python   is\tvery\ngood"</span>
<span class="keyword">spaces</span> = <span class="keyword">re</span>.<span class="function">findall</span>(<span class="keyword">space_pattern</span>, <span class="keyword">text</span>)
<span class="function">print</span>(<span class="string">f"空白字符: {spaces}"</span>)
                </div>

                <h3><span class="emoji">🎯</span>量词和分组</h3>
                <div class="code-block">
<span class="comment"># 量词示例</span>
<span class="keyword">text</span> = <span class="string">"color colour colouur"</span>

<span class="comment"># ? 表示0或1次</span>
<span class="keyword">pattern1</span> = <span class="string">r"colou?r"</span>
<span class="keyword">matches1</span> = <span class="keyword">re</span>.<span class="function">findall</span>(<span class="keyword">pattern1</span>, <span class="keyword">text</span>)
<span class="function">print</span>(<span class="string">f"colou?r: {matches1}"</span>)  <span class="comment"># ['color', 'colour']</span>

<span class="comment"># + 表示1次或多次</span>
<span class="keyword">pattern2</span> = <span class="string">r"colou+r"</span>
<span class="keyword">matches2</span> = <span class="keyword">re</span>.<span class="function">findall</span>(<span class="keyword">pattern2</span>, <span class="keyword">text</span>)
<span class="function">print</span>(<span class="string">f"colou+r: {matches2}"</span>)  <span class="comment"># ['colour', 'colouur']</span>

<span class="comment"># * 表示0次或多次</span>
<span class="keyword">pattern3</span> = <span class="string">r"colou*r"</span>
<span class="keyword">matches3</span> = <span class="keyword">re</span>.<span class="function">findall</span>(<span class="keyword">pattern3</span>, <span class="keyword">text</span>)
<span class="function">print</span>(<span class="string">f"colou*r: {matches3}"</span>)  <span class="comment"># ['color', 'colour', 'colouur']</span>

<span class="comment"># 分组捕获</span>
<span class="keyword">email_text</span> = <span class="string">"联系邮箱：<EMAIL>"</span>
<span class="keyword">email_pattern</span> = <span class="string">r"(\w+)@(\w+\.[\w.]+)"</span>
<span class="keyword">match</span> = <span class="keyword">re</span>.<span class="function">search</span>(<span class="keyword">email_pattern</span>, <span class="keyword">email_text</span>)
<span class="keyword">if</span> <span class="keyword">match</span>:
    <span class="function">print</span>(<span class="string">f"用户名: {match.group(1)}"</span>)    <span class="comment"># zhang</span>
    <span class="function">print</span>(<span class="string">f"域名: {match.group(2)}"</span>)      <span class="comment"># company.com.cn</span>
                </div>

                <h3><span class="emoji">🛠️</span>常用正则函数</h3>
                <div class="code-block">
<span class="keyword">text</span> = <span class="string">"Python很好，Java也不错，但我更喜欢Python"</span>

<span class="comment"># re.findall() - 查找所有匹配</span>
<span class="keyword">languages</span> = <span class="keyword">re</span>.<span class="function">findall</span>(<span class="string">r"Python|Java"</span>, <span class="keyword">text</span>)
<span class="function">print</span>(<span class="string">f"编程语言: {languages}"</span>)  <span class="comment"># ['Python', 'Java', 'Python']</span>

<span class="comment"># re.sub() - 替换</span>
<span class="keyword">new_text</span> = <span class="keyword">re</span>.<span class="function">sub</span>(<span class="string">r"Java"</span>, <span class="string">"JavaScript"</span>, <span class="keyword">text</span>)
<span class="function">print</span>(<span class="keyword">new_text</span>)

<span class="comment"># re.split() - 分割</span>
<span class="keyword">data</span> = <span class="string">"apple,banana;orange:grape"</span>
<span class="keyword">fruits</span> = <span class="keyword">re</span>.<span class="function">split</span>(<span class="string">r"[,;:]"</span>, <span class="keyword">data</span>)
<span class="function">print</span>(<span class="string">f"水果列表: {fruits}"</span>)  <span class="comment"># ['apple', 'banana', 'orange', 'grape']</span>

<span class="comment"># re.match() vs re.search()</span>
<span class="keyword">text</span> = <span class="string">"Hello Python World"</span>
<span class="keyword">match_result</span> = <span class="keyword">re</span>.<span class="function">match</span>(<span class="string">r"Python"</span>, <span class="keyword">text</span>)     <span class="comment"># None (从开头匹配)</span>
<span class="keyword">search_result</span> = <span class="keyword">re</span>.<span class="function">search</span>(<span class="string">r"Python"</span>, <span class="keyword">text</span>)   <span class="comment"># 找到匹配</span>
<span class="function">print</span>(<span class="string">f"match结果: {match_result}"</span>)
<span class="function">print</span>(<span class="string">f"search结果: {search_result.group() if search_result else None}"</span>)
                </div>

                <div class="tip-box">
                    <p><span class="emoji">💡</span><strong>提示：</strong>使用原始字符串（r""）来编写正则表达式，避免转义字符的问题。</p>
                </div>
            </div>
        </div>

        <!-- 第16页：正则表达式高级应用 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">🎯</span>正则表达式高级应用</h1>
            </div>
            <div class="content">
                <h2>复杂模式匹配和实际应用</h2>
                
                <h3><span class="emoji">📱</span>常用验证模式</h3>
                <div class="code-block">
<span class="keyword">import</span> <span class="keyword">re</span>

<span class="comment"># 手机号验证</span>
<span class="keyword">def</span> <span class="function">validate_phone</span>(<span class="keyword">phone</span>):
    <span class="keyword">pattern</span> = <span class="string">r"^1[3-9]\d{9}$"</span>
    <span class="keyword">return</span> <span class="function">bool</span>(<span class="keyword">re</span>.<span class="function">match</span>(<span class="keyword">pattern</span>, <span class="keyword">phone</span>))

<span class="comment"># 邮箱验证</span>
<span class="keyword">def</span> <span class="function">validate_email</span>(<span class="keyword">email</span>):
    <span class="keyword">pattern</span> = <span class="string">r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"</span>
    <span class="keyword">return</span> <span class="function">bool</span>(<span class="keyword">re</span>.<span class="function">match</span>(<span class="keyword">pattern</span>, <span class="keyword">email</span>))

<span class="comment"># 身份证验证</span>
<span class="keyword">def</span> <span class="function">validate_id_card</span>(<span class="keyword">id_card</span>):
    <span class="keyword">pattern</span> = <span class="string">r"^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[0-9Xx]$"</span>
    <span class="keyword">return</span> <span class="function">bool</span>(<span class="keyword">re</span>.<span class="function">match</span>(<span class="keyword">pattern</span>, <span class="keyword">id_card</span>))

<span class="comment"># 测试验证</span>
<span class="function">print</span>(<span class="function">validate_phone</span>(<span class="string">"13812345678"</span>))  <span class="comment"># True</span>
<span class="function">print</span>(<span class="function">validate_email</span>(<span class="string">"<EMAIL>"</span>))  <span class="comment"># True</span>
<span class="function">print</span>(<span class="function">validate_id_card</span>(<span class="string">"110101199001011234"</span>))  <span class="comment"># True</span>
                </div>

                <h3><span class="emoji">🔧</span>文本处理和清洗</h3>
                <div class="code-block">
<span class="comment"># 提取网页链接</span>
<span class="keyword">html_text</span> = <span class="string">"""访问我们的网站 https://www.example.com 或者
联系我们 http://support.example.com/help"""</span>

<span class="keyword">url_pattern</span> = <span class="string">r"https?://[\w.-]+(?:/[\w._~:/?#[\]@!$&'()*+,;=-]*)?"</span>
<span class="keyword">urls</span> = <span class="keyword">re</span>.<span class="function">findall</span>(<span class="keyword">url_pattern</span>, <span class="keyword">html_text</span>)
<span class="function">print</span>(<span class="string">f"找到的链接: {urls}"</span>)

<span class="comment"># 清理HTML标签</span>
<span class="keyword">html_content</span> = <span class="string">"<p>这是<strong>重要</strong>的<em>内容</em></p>"</span>
<span class="keyword">clean_text</span> = <span class="keyword">re</span>.<span class="function">sub</span>(<span class="string">r"<[^>]+>"</span>, <span class="string">""</span>, <span class="keyword">html_content</span>)
<span class="function">print</span>(<span class="string">f"清理后的文本: {clean_text}"</span>)  <span class="comment"># 这是重要的内容</span>

<span class="comment"># 提取中文字符</span>
<span class="keyword">mixed_text</span> = <span class="string">"Hello 你好 123 世界 ABC"</span>
<span class="keyword">chinese_chars</span> = <span class="keyword">re</span>.<span class="function">findall</span>(<span class="string">r"[\u4e00-\u9fff]+"</span>, <span class="keyword">mixed_text</span>)
<span class="function">print</span>(<span class="string">f"中文字符: {chinese_chars}"</span>)  <span class="comment"># ['你好', '世界']</span>
                </div>

                <h3><span class="emoji">💰</span>数据提取和格式化</h3>
                <div class="code-block">
<span class="comment"># 提取价格信息</span>
<span class="keyword">price_text</span> = <span class="string">"商品价格：￥199.99，折扣价：$89.50，原价：€120.00"</span>

<span class="comment"># 提取所有价格</span>
<span class="keyword">price_pattern</span> = <span class="string">r"[￥$€]?\d+(?:\.\d{2})?"</span>
<span class="keyword">prices</span> = <span class="keyword">re</span>.<span class="function">findall</span>(<span class="keyword">price_pattern</span>, <span class="keyword">price_text</span>)
<span class="function">print</span>(<span class="string">f"价格列表: {prices}"</span>)

<span class="comment"># 提取日期时间</span>
<span class="keyword">datetime_text</span> = <span class="string">"""会议时间：2024-01-15 14:30:00
另一个时间：2024/02/20 09:15
还有：15/03/2024"""</span>

<span class="keyword">date_patterns</span> = [
    <span class="string">r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}"</span>,  <span class="comment"># YYYY-MM-DD HH:MM:SS</span>
    <span class="string">r"\d{4}/\d{2}/\d{2} \d{2}:\d{2}"</span>,      <span class="comment"># YYYY/MM/DD HH:MM</span>
    <span class="string">r"\d{2}/\d{2}/\d{4}"</span>                 <span class="comment"># DD/MM/YYYY</span>
]

<span class="keyword">for</span> <span class="keyword">pattern</span> <span class="keyword">in</span> <span class="keyword">date_patterns</span>:
    <span class="keyword">dates</span> = <span class="keyword">re</span>.<span class="function">findall</span>(<span class="keyword">pattern</span>, <span class="keyword">datetime_text</span>)
    <span class="keyword">if</span> <span class="keyword">dates</span>:
        <span class="function">print</span>(<span class="string">f"找到日期: {dates}"</span>)
                </div>

                <h3><span class="emoji">🔄</span>高级替换技巧</h3>
                <div class="code-block">
<span class="comment"># 使用函数进行替换</span>
<span class="keyword">def</span> <span class="function">capitalize_match</span>(<span class="keyword">match</span>):
    <span class="keyword">return</span> <span class="keyword">match</span>.<span class="function">group</span>().<span class="function">upper</span>()

<span class="keyword">text</span> = <span class="string">"python is great, java is good"</span>
<span class="keyword">result</span> = <span class="keyword">re</span>.<span class="function">sub</span>(<span class="string">r"python|java"</span>, <span class="keyword">capitalize_match</span>, <span class="keyword">text</span>)
<span class="function">print</span>(<span class="keyword">result</span>)  <span class="comment"># PYTHON is great, JAVA is good</span>

<span class="comment"># 使用命名组进行替换</span>
<span class="keyword">phone_text</span> = <span class="string">"联系电话：138-1234-5678"</span>
<span class="keyword">formatted_phone</span> = <span class="keyword">re</span>.<span class="function">sub</span>(
    <span class="string">r"(?P<area>\d{3})-(?P<prefix>\d{4})-(?P<suffix>\d{4})"</span>,
    <span class="string">r"(\g<area>) \g<prefix>-\g<suffix>"</span>,
    <span class="keyword">phone_text</span>
)
<span class="function">print</span>(<span class="keyword">formatted_phone</span>)  <span class="comment"># 联系电话：(138) 1234-5678</span>

<span class="comment"># 条件替换</span>
<span class="keyword">def</span> <span class="function">mask_sensitive</span>(<span class="keyword">match</span>):
    <span class="keyword">phone</span> = <span class="keyword">match</span>.<span class="function">group</span>()
    <span class="keyword">return</span> <span class="keyword">phone</span>[:<span class="number">3</span>] + <span class="string">"****"</span> + <span class="keyword">phone</span>[-<span class="number">4</span>:]

<span class="keyword">sensitive_text</span> = <span class="string">"我的手机号是13812345678"</span>
<span class="keyword">masked_text</span> = <span class="keyword">re</span>.<span class="function">sub</span>(<span class="string">r"1[3-9]\d{9}"</span>, <span class="keyword">mask_sensitive</span>, <span class="keyword">sensitive_text</span>)
<span class="function">print</span>(<span class="keyword">masked_text</span>)  <span class="comment"># 我的手机号是138****5678</span>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI 提示词模板</h3>
                    <p>"请提供Python正则表达式的高级应用案例，包括数据验证、文本清洗、信息提取和敏感数据脱敏等实际场景的解决方案。"</p>
                </div>
            </div>
        </div>

        <!-- 第17页：字符串性能优化 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">⚡</span>字符串性能优化</h1>
            </div>
            <div class="content">
                <h2>提升字符串操作效率</h2>
                
                <h3><span class="emoji">🔗</span>字符串连接优化</h3>
                <div class="code-block">
<span class="keyword">import</span> <span class="keyword">time</span>

<span class="comment"># 低效的字符串连接</span>
<span class="keyword">def</span> <span class="function">slow_concat</span>(<span class="keyword">items</span>):
    <span class="keyword">result</span> = <span class="string">""</span>
    <span class="keyword">for</span> <span class="keyword">item</span> <span class="keyword">in</span> <span class="keyword">items</span>:
        <span class="keyword">result</span> += <span class="keyword">item</span>  <span class="comment"># 每次都创建新字符串</span>
    <span class="keyword">return</span> <span class="keyword">result</span>

<span class="comment"># 高效的字符串连接</span>
<span class="keyword">def</span> <span class="function">fast_concat</span>(<span class="keyword">items</span>):
    <span class="keyword">return</span> <span class="string">""</span>.<span class="function">join</span>(<span class="keyword">items</span>)  <span class="comment"># 一次性连接</span>

<span class="comment"># 使用列表推导式和join</span>
<span class="keyword">def</span> <span class="function">optimized_concat</span>(<span class="keyword">numbers</span>):
    <span class="keyword">return</span> <span class="string">", "</span>.<span class="function">join</span>(<span class="function">str</span>(<span class="keyword">n</span>) <span class="keyword">for</span> <span class="keyword">n</span> <span class="keyword">in</span> <span class="keyword">numbers</span>)

<span class="comment"># 性能测试</span>
<span class="keyword">test_data</span> = [<span class="function">str</span>(<span class="keyword">i</span>) <span class="keyword">for</span> <span class="keyword">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1000</span>)]

<span class="keyword">start</span> = <span class="keyword">time</span>.<span class="function">time</span>()
<span class="keyword">result1</span> = <span class="function">slow_concat</span>(<span class="keyword">test_data</span>)
<span class="keyword">time1</span> = <span class="keyword">time</span>.<span class="function">time</span>() - <span class="keyword">start</span>

<span class="keyword">start</span> = <span class="keyword">time</span>.<span class="function">time</span>()
<span class="keyword">result2</span> = <span class="function">fast_concat</span>(<span class="keyword">test_data</span>)
<span class="keyword">time2</span> = <span class="keyword">time</span>.<span class="function">time</span>() - <span class="keyword">start</span>

<span class="function">print</span>(<span class="string">f"慢速连接耗时: {time1:.4f}秒"</span>)
<span class="function">print</span>(<span class="string">f"快速连接耗时: {time2:.4f}秒"</span>)
<span class="function">print</span>(<span class="string">f"性能提升: {time1/time2:.1f}倍"</span>)
                </div>

                <h3><span class="emoji">🔍</span>字符串查找优化</h3>
                <div class="code-block">
<span class="comment"># 使用集合进行快速查找</span>
<span class="keyword">def</span> <span class="function">check_keywords_slow</span>(<span class="keyword">text</span>, <span class="keyword">keywords</span>):
    <span class="keyword">for</span> <span class="keyword">keyword</span> <span class="keyword">in</span> <span class="keyword">keywords</span>:
        <span class="keyword">if</span> <span class="keyword">keyword</span> <span class="keyword">in</span> <span class="keyword">text</span>:
            <span class="keyword">return</span> <span class="keyword">True</span>
    <span class="keyword">return</span> <span class="keyword">False</span>

<span class="keyword">def</span> <span class="function">check_keywords_fast</span>(<span class="keyword">text</span>, <span class="keyword">keywords_set</span>):
    <span class="keyword">words</span> = <span class="keyword">text</span>.<span class="function">split</span>()
    <span class="keyword">return</span> <span class="function">bool</span>(<span class="keyword">keywords_set</span> & <span class="function">set</span>(<span class="keyword">words</span>))

<span class="comment"># 预编译正则表达式</span>
<span class="keyword">import</span> <span class="keyword">re</span>

<span class="comment"># 低效：每次都编译</span>
<span class="keyword">def</span> <span class="function">validate_slow</span>(<span class="keyword">emails</span>):
    <span class="keyword">results</span> = []
    <span class="keyword">for</span> <span class="keyword">email</span> <span class="keyword">in</span> <span class="keyword">emails</span>:
        <span class="keyword">if</span> <span class="keyword">re</span>.<span class="function">match</span>(<span class="string">r"^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}$"</span>, <span class="keyword">email</span>):
            <span class="keyword">results</span>.<span class="function">append</span>(<span class="keyword">email</span>)
    <span class="keyword">return</span> <span class="keyword">results</span>

<span class="comment"># 高效：预编译正则</span>
<span class="keyword">EMAIL_PATTERN</span> = <span class="keyword">re</span>.<span class="function">compile</span>(<span class="string">r"^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}$"</span>)

<span class="keyword">def</span> <span class="function">validate_fast</span>(<span class="keyword">emails</span>):
    <span class="keyword">return</span> [<span class="keyword">email</span> <span class="keyword">for</span> <span class="keyword">email</span> <span class="keyword">in</span> <span class="keyword">emails</span> <span class="keyword">if</span> <span class="keyword">EMAIL_PATTERN</span>.<span class="function">match</span>(<span class="keyword">email</span>)]
                </div>

                <h3><span class="emoji">💾</span>内存优化技巧</h3>
                <div class="code-block">
<span class="comment"># 使用生成器处理大文件</span>
<span class="keyword">def</span> <span class="function">process_large_file_memory_heavy</span>(<span class="keyword">filename</span>):
    <span class="keyword">with</span> <span class="function">open</span>(<span class="keyword">filename</span>, <span class="string">"r"</span>, <span class="keyword">encoding</span>=<span class="string">"utf-8"</span>) <span class="keyword">as</span> <span class="keyword">f</span>:
        <span class="keyword">lines</span> = <span class="keyword">f</span>.<span class="function">readlines</span>()  <span class="comment"># 一次性读取所有行</span>
        <span class="keyword">return</span> [<span class="keyword">line</span>.<span class="function">strip</span>().<span class="function">upper</span>() <span class="keyword">for</span> <span class="keyword">line</span> <span class="keyword">in</span> <span class="keyword">lines</span>]

<span class="keyword">def</span> <span class="function">process_large_file_memory_efficient</span>(<span class="keyword">filename</span>):
    <span class="keyword">with</span> <span class="function">open</span>(<span class="keyword">filename</span>, <span class="string">"r"</span>, <span class="keyword">encoding</span>=<span class="string">"utf-8"</span>) <span class="keyword">as</span> <span class="keyword">f</span>:
        <span class="keyword">for</span> <span class="keyword">line</span> <span class="keyword">in</span> <span class="keyword">f</span>:  <span class="comment"># 逐行处理</span>
            <span class="keyword">yield</span> <span class="keyword">line</span>.<span class="function">strip</span>().<span class="function">upper</span>()

<span class="comment"># 字符串intern优化（相同字符串共享内存）</span>
<span class="keyword">import</span> <span class="keyword">sys</span>

<span class="keyword">def</span> <span class="function">optimize_repeated_strings</span>(<span class="keyword">data</span>):
    <span class="comment"># 对于重复出现的字符串，使用intern可以节省内存</span>
    <span class="keyword">return</span> [<span class="keyword">sys</span>.<span class="function">intern</span>(<span class="keyword">item</span>) <span class="keyword">for</span> <span class="keyword">item</span> <span class="keyword">in</span> <span class="keyword">data</span>]

<span class="comment"># 使用__slots__优化字符串对象</span>
<span class="keyword">class</span> <span class="function">OptimizedString</span>:
    <span class="keyword">__slots__</span> = [<span class="string">"value"</span>, <span class="string">"length"</span>]
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>, <span class="keyword">value</span>):
        <span class="keyword">self</span>.<span class="keyword">value</span> = <span class="keyword">value</span>
        <span class="keyword">self</span>.<span class="keyword">length</span> = <span class="function">len</span>(<span class="keyword">value</span>)
                </div>

                <h3><span class="emoji">📊</span>性能测试工具</h3>
                <div class="code-block">
<span class="keyword">import</span> <span class="keyword">timeit</span>
<span class="keyword">import</span> <span class="keyword">cProfile</span>

<span class="comment"># 使用timeit进行精确计时</span>
<span class="keyword">def</span> <span class="function">benchmark_string_operations</span>():
    <span class="comment"># 测试字符串连接</span>
    <span class="keyword">setup_code</span> = <span class="string">"data = ['item' + str(i) for i in range(100)]"</span>
    
    <span class="keyword">time_plus</span> = <span class="keyword">timeit</span>.<span class="function">timeit</span>(
        <span class="string">"result = ''; [result := result + item for item in data]"</span>,
        <span class="keyword">setup</span>=<span class="keyword">setup_code</span>,
        <span class="keyword">number</span>=<span class="number">1000</span>
    )
    
    <span class="keyword">time_join</span> = <span class="keyword">timeit</span>.<span class="function">timeit</span>(
        <span class="string">"result = ''.join(data)"</span>,
        <span class="keyword">setup</span>=<span class="keyword">setup_code</span>,
        <span class="keyword">number</span>=<span class="number">1000</span>
    )
    
    <span class="function">print</span>(<span class="string">f"+ 操作符: {time_plus:.4f}秒"</span>)
    <span class="function">print</span>(<span class="string">f"join方法: {time_join:.4f}秒"</span>)
    <span class="function">print</span>(<span class="string">f"join快 {time_plus/time_join:.1f} 倍"</span>)

<span class="comment"># 使用cProfile分析性能瓶颈</span>
<span class="keyword">def</span> <span class="function">profile_string_function</span>():
    <span class="keyword">def</span> <span class="function">test_function</span>():
        <span class="keyword">data</span> = []
        <span class="keyword">for</span> <span class="keyword">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1000</span>):
            <span class="keyword">data</span>.<span class="function">append</span>(<span class="string">f"item_{i}"</span>)
        <span class="keyword">return</span> <span class="string">", "</span>.<span class="function">join</span>(<span class="keyword">data</span>)
    
    <span class="keyword">cProfile</span>.<span class="function">run</span>(<span class="string">"test_function()"</span>)

<span class="function">benchmark_string_operations</span>()
                </div>

                <div class="tip-box">
                    <p><span class="emoji">🚀</span><strong>性能要点：</strong>使用join()连接字符串，预编译正则表达式，使用生成器处理大数据，避免不必要的字符串复制。</p>
                </div>
            </div>
        </div>

        <!-- 第18页：字符串安全性 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">🔒</span>字符串安全性</h1>
            </div>
            <div class="content">
                <h2>防范字符串相关的安全风险</h2>
                
                <h3><span class="emoji">💉</span>SQL注入防护</h3>
                <div class="code-block">
<span class="comment"># 危险的字符串拼接（容易SQL注入）</span>
<span class="keyword">def</span> <span class="function">unsafe_query</span>(<span class="keyword">username</span>, <span class="keyword">password</span>):
    <span class="comment"># 永远不要这样做！</span>
    <span class="keyword">query</span> = <span class="string">f"SELECT * FROM users WHERE username='{username}' AND password='{password}'"</span>
    <span class="keyword">return</span> <span class="keyword">query</span>

<span class="comment"># 恶意输入示例</span>
<span class="keyword">malicious_input</span> = <span class="string">"admin'; DROP TABLE users; --"</span>
<span class="keyword">dangerous_query</span> = <span class="function">unsafe_query</span>(<span class="keyword">malicious_input</span>, <span class="string">"any"</span>)
<span class="function">print</span>(<span class="string">f"危险查询: {dangerous_query}"</span>)

<span class="comment"># 安全的参数化查询</span>
<span class="keyword">import</span> <span class="keyword">sqlite3</span>

<span class="keyword">def</span> <span class="function">safe_query</span>(<span class="keyword">username</span>, <span class="keyword">password</span>):
    <span class="keyword">conn</span> = <span class="keyword">sqlite3</span>.<span class="function">connect</span>(<span class="string">":memory:"</span>)
    <span class="keyword">cursor</span> = <span class="keyword">conn</span>.<span class="function">cursor</span>()
    
    <span class="comment"># 使用参数化查询</span>
    <span class="keyword">cursor</span>.<span class="function">execute</span>(
        <span class="string">"SELECT * FROM users WHERE username=? AND password=?"</span>,
        (<span class="keyword">username</span>, <span class="keyword">password</span>)
    )
    <span class="keyword">return</span> <span class="keyword">cursor</span>.<span class="function">fetchall</span>()

<span class="comment"># 输入验证和清理</span>
<span class="keyword">import</span> <span class="keyword">re</span>

<span class="keyword">def</span> <span class="function">sanitize_input</span>(<span class="keyword">user_input</span>):
    <span class="comment"># 移除危险字符</span>
    <span class="keyword">cleaned</span> = <span class="keyword">re</span>.<span class="function">sub</span>(<span class="string">r"[';\-\-/\*]"</span>, <span class="string">""</span>, <span class="keyword">user_input</span>)
    <span class="comment"># 限制长度</span>
    <span class="keyword">return</span> <span class="keyword">cleaned</span>[:<span class="number">50</span>]
                </div>

                <h3><span class="emoji">🌐</span>XSS攻击防护</h3>
                <div class="code-block">
<span class="keyword">import</span> <span class="keyword">html</span>

<span class="comment"># HTML转义防止XSS</span>
<span class="keyword">def</span> <span class="function">escape_html</span>(<span class="keyword">user_input</span>):
    <span class="keyword">return</span> <span class="keyword">html</span>.<span class="function">escape</span>(<span class="keyword">user_input</span>)

<span class="comment"># 危险的用户输入</span>
<span class="keyword">malicious_script</span> = <span class="string">"&lt;script&gt;alert('XSS攻击!');&lt;/script&gt;"</span>
<span class="keyword">safe_output</span> = <span class="function">escape_html</span>(<span class="keyword">malicious_script</span>)
<span class="function">print</span>(<span class="string">f"转义后: {safe_output}"</span>)

<span class="comment"># 自定义HTML清理函数</span>
<span class="keyword">def</span> <span class="function">clean_html_tags</span>(<span class="keyword">text</span>):
    <span class="comment"># 移除所有HTML标签</span>
    <span class="keyword">clean_text</span> = <span class="keyword">re</span>.<span class="function">sub</span>(<span class="string">r"<[^>]+>"</span>, <span class="string">""</span>, <span class="keyword">text</span>)
    <span class="comment"># 转义特殊字符</span>
    <span class="keyword">return</span> <span class="keyword">html</span>.<span class="function">escape</span>(<span class="keyword">clean_text</span>)

<span class="comment"># 白名单方式过滤</span>
<span class="keyword">def</span> <span class="function">whitelist_filter</span>(<span class="keyword">text</span>):
    <span class="comment"># 只允许字母、数字、空格和基本标点</span>
    <span class="keyword">allowed_chars</span> = <span class="keyword">re</span>.<span class="function">sub</span>(<span class="string">r"[^a-zA-Z0-9\s.,!?\u4e00-\u9fff]"</span>, <span class="string">""</span>, <span class="keyword">text</span>)
    <span class="keyword">return</span> <span class="keyword">allowed_chars</span>
                </div>

                <h3><span class="emoji">🔐</span>敏感信息处理</h3>
                <div class="code-block">
<span class="keyword">import</span> <span class="keyword">hashlib</span>
<span class="keyword">import</span> <span class="keyword">secrets</span>

<span class="comment"># 密码哈希处理</span>
<span class="keyword">def</span> <span class="function">hash_password</span>(<span class="keyword">password</span>):
    <span class="comment"># 生成随机盐值</span>
    <span class="keyword">salt</span> = <span class="keyword">secrets</span>.<span class="function">token_hex</span>(<span class="number">16</span>)
    <span class="comment"># 使用盐值哈希密码</span>
    <span class="keyword">password_hash</span> = <span class="keyword">hashlib</span>.<span class="function">pbkdf2_hmac</span>(
        <span class="string">"sha256"</span>,
        <span class="keyword">password</span>.<span class="function">encode</span>(<span class="string">"utf-8"</span>),
        <span class="keyword">salt</span>.<span class="function">encode</span>(<span class="string">"utf-8"</span>),
        <span class="number">100000</span>  <span class="comment"># 迭代次数</span>
    )
    <span class="keyword">return</span> <span class="keyword">salt</span> + <span class="keyword">password_hash</span>.<span class="function">hex</span>()

<span class="comment"># 敏感信息脱敏</span>
<span class="keyword">def</span> <span class="function">mask_sensitive_data</span>(<span class="keyword">data</span>, <span class="keyword">data_type</span>):
    <span class="keyword">if</span> <span class="keyword">data_type</span> == <span class="string">"phone"</span>:
        <span class="keyword">return</span> <span class="keyword">data</span>[:<span class="number">3</span>] + <span class="string">"****"</span> + <span class="keyword">data</span>[-<span class="number">4</span>:]
    <span class="keyword">elif</span> <span class="keyword">data_type</span> == <span class="string">"email"</span>:
        <span class="keyword">username</span>, <span class="keyword">domain</span> = <span class="keyword">data</span>.<span class="function">split</span>(<span class="string">"@"</span>)
        <span class="keyword">masked_username</span> = <span class="keyword">username</span>[:<span class="number">2</span>] + <span class="string">"***"</span>
        <span class="keyword">return</span> <span class="string">f"{masked_username}@{domain}"</span>
    <span class="keyword">elif</span> <span class="keyword">data_type</span> == <span class="string">"id_card"</span>:
        <span class="keyword">return</span> <span class="keyword">data</span>[:<span class="number">6</span>] + <span class="string">"********"</span> + <span class="keyword">data</span>[-<span class="number">4</span>:]
    <span class="keyword">return</span> <span class="keyword">data</span>

<span class="comment"># 测试脱敏功能</span>
<span class="function">print</span>(<span class="function">mask_sensitive_data</span>(<span class="string">"13812345678"</span>, <span class="string">"phone"</span>))
<span class="function">print</span>(<span class="function">mask_sensitive_data</span>(<span class="string">"<EMAIL>"</span>, <span class="string">"email"</span>))
<span class="function">print</span>(<span class="function">mask_sensitive_data</span>(<span class="string">"110101199001011234"</span>, <span class="string">"id_card"</span>))
                </div>

                <h3><span class="emoji">🛡️</span>输入验证最佳实践</h3>
                <div class="code-block">
<span class="comment"># 综合输入验证类</span>
<span class="keyword">class</span> <span class="function">InputValidator</span>:
    <span class="keyword">@staticmethod</span>
    <span class="keyword">def</span> <span class="function">validate_length</span>(<span class="keyword">text</span>, <span class="keyword">min_len</span>=<span class="number">1</span>, <span class="keyword">max_len</span>=<span class="number">100</span>):
        <span class="keyword">return</span> <span class="keyword">min_len</span> <= <span class="function">len</span>(<span class="keyword">text</span>) <= <span class="keyword">max_len</span>
    
    <span class="keyword">@staticmethod</span>
    <span class="keyword">def</span> <span class="function">validate_charset</span>(<span class="keyword">text</span>, <span class="keyword">allowed_chars</span>):
        <span class="keyword">return</span> <span class="function">all</span>(<span class="keyword">c</span> <span class="keyword">in</span> <span class="keyword">allowed_chars</span> <span class="keyword">for</span> <span class="keyword">c</span> <span class="keyword">in</span> <span class="keyword">text</span>)
    
    <span class="keyword">@staticmethod</span>
    <span class="keyword">def</span> <span class="function">validate_pattern</span>(<span class="keyword">text</span>, <span class="keyword">pattern</span>):
        <span class="keyword">return</span> <span class="function">bool</span>(<span class="keyword">re</span>.<span class="function">match</span>(<span class="keyword">pattern</span>, <span class="keyword">text</span>))
    
    <span class="keyword">@classmethod</span>
    <span class="keyword">def</span> <span class="function">validate_username</span>(<span class="keyword">cls</span>, <span class="keyword">username</span>):
        <span class="comment"># 用户名：3-20位，字母数字下划线</span>
        <span class="keyword">if</span> <span class="keyword">not</span> <span class="keyword">cls</span>.<span class="function">validate_length</span>(<span class="keyword">username</span>, <span class="number">3</span>, <span class="number">20</span>):
            <span class="keyword">return</span> <span class="keyword">False</span>, <span class="string">"用户名长度必须在3-20位之间"</span>
        
        <span class="keyword">if</span> <span class="keyword">not</span> <span class="keyword">cls</span>.<span class="function">validate_pattern</span>(<span class="keyword">username</span>, <span class="string">r"^[a-zA-Z0-9_]+$"</span>):
            <span class="keyword">return</span> <span class="keyword">False</span>, <span class="string">"用户名只能包含字母、数字和下划线"</span>
        
        <span class="keyword">return</span> <span class="keyword">True</span>, <span class="string">"验证通过"</span>

<span class="comment"># 使用验证器</span>
<span class="keyword">validator</span> = <span class="function">InputValidator</span>()
<span class="keyword">is_valid</span>, <span class="keyword">message</span> = <span class="keyword">validator</span>.<span class="function">validate_username</span>(<span class="string">"user123"</span>)
<span class="function">print</span>(<span class="string">f"验证结果: {is_valid}, 消息: {message}"</span>)
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI 提示词模板</h3>
                    <p>"请详细介绍Python字符串处理中的安全问题，包括SQL注入、XSS攻击的防护方法，以及敏感信息脱敏和输入验证的最佳实践。"</p>
                </div>
            </div>
        </div>

        <!-- 第19页：AI辅助字符串学习 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">🤖</span>AI辅助字符串学习</h1>
            </div>
            <div class="content">
                <h2>利用AI提升字符串编程技能</h2>
                
                <h3><span class="emoji">💡</span>AI学习策略</h3>
                <div class="code-block">
<span class="comment"># AI辅助代码生成示例</span>
<span class="comment"># 提示词："请生成一个Python函数，用于验证中国大陆手机号格式"</span>

<span class="keyword">import</span> <span class="keyword">re</span>

<span class="keyword">def</span> <span class="function">validate_china_mobile</span>(<span class="keyword">phone</span>):
    <span class="string">"""验证中国大陆手机号格式
    
    Args:
        phone (str): 待验证的手机号
    
    Returns:
        bool: 验证结果
    """</span>
    <span class="comment"># 中国大陆手机号规则：1开头，第二位3-9，总共11位数字</span>
    <span class="keyword">pattern</span> = <span class="string">r"^1[3-9]\d{9}$"</span>
    <span class="keyword">return</span> <span class="function">bool</span>(<span class="keyword">re</span>.<span class="function">match</span>(<span class="keyword">pattern</span>, <span class="keyword">phone</span>))

<span class="comment"># 测试用例</span>
<span class="keyword">test_cases</span> = [
    <span class="string">"13812345678"</span>,  <span class="comment"># 有效</span>
    <span class="string">"12812345678"</span>,  <span class="comment"># 无效：第二位是2</span>
    <span class="string">"1381234567"</span>,   <span class="comment"># 无效：只有10位</span>
    <span class="string">"138123456789"</span>, <span class="comment"># 无效：12位</span>
]

<span class="keyword">for</span> <span class="keyword">phone</span> <span class="keyword">in</span> <span class="keyword">test_cases</span>:
    <span class="keyword">result</span> = <span class="function">validate_china_mobile</span>(<span class="keyword">phone</span>)
    <span class="function">print</span>(<span class="string">f"{phone}: {result}"</span>)
                </div>

                <h3><span class="emoji">📚</span>高效提示词模板</h3>
                <div class="ai-prompt">
                    <h4>代码生成类提示词</h4>
                    <p>"请用Python编写一个函数，功能是[具体需求]，要求：1）包含完整的文档字符串；2）添加适当的错误处理；3）提供测试用例。"</p>
                </div>
                
                <div class="ai-prompt">
                    <h4>代码优化类提示词</h4>
                    <p>"请优化以下Python字符串处理代码的性能，并解释优化原理：[贴入代码]"</p>
                </div>
                
                <div class="ai-prompt">
                    <h4>问题解决类提示词</h4>
                    <p>"我在处理字符串时遇到了[具体问题]，当前代码是[贴入代码]，期望结果是[描述期望]，请帮我分析问题并提供解决方案。"</p>
                </div>

                <h3><span class="emoji">🔍</span>AI代码审查</h3>
                <div class="code-block">
<span class="comment"># 原始代码（可能存在问题）</span>
<span class="keyword">def</span> <span class="function">process_user_input</span>(<span class="keyword">data</span>):
    <span class="comment"># 问题1：没有输入验证</span>
    <span class="keyword">result</span> = <span class="string">""</span>
    <span class="keyword">for</span> <span class="keyword">item</span> <span class="keyword">in</span> <span class="keyword">data</span>:
        <span class="comment"># 问题2：低效的字符串连接</span>
        <span class="keyword">result</span> += <span class="keyword">item</span>.<span class="function">upper</span>() + <span class="string">", "</span>
    <span class="comment"># 问题3：没有处理末尾逗号</span>
    <span class="keyword">return</span> <span class="keyword">result</span>

<span class="comment"># AI优化后的代码</span>
<span class="keyword">def</span> <span class="function">process_user_input_optimized</span>(<span class="keyword">data</span>):
    <span class="string">"""处理用户输入数据
    
    Args:
        data: 可迭代的字符串数据
    
    Returns:
        str: 处理后的字符串
    
    Raises:
        TypeError: 当输入不是可迭代对象时
        ValueError: 当输入为空时
    """</span>
    <span class="keyword">if</span> <span class="keyword">not</span> <span class="keyword">data</span>:
        <span class="keyword">raise</span> <span class="function">ValueError</span>(<span class="string">"输入数据不能为空"</span>)
    
    <span class="keyword">try</span>:
        <span class="comment"># 使用join提高性能，去除末尾逗号</span>
        <span class="keyword">return</span> <span class="string">", "</span>.<span class="function">join</span>(<span class="keyword">item</span>.<span class="function">upper</span>() <span class="keyword">for</span> <span class="keyword">item</span> <span class="keyword">in</span> <span class="keyword">data</span> <span class="keyword">if</span> <span class="keyword">item</span>)
    <span class="keyword">except</span> <span class="function">AttributeError</span>:
        <span class="keyword">raise</span> <span class="function">TypeError</span>(<span class="string">"输入数据必须是字符串的可迭代对象"</span>)
                </div>

                <h3><span class="emoji">🎯</span>学习路径规划</h3>
                <div class="tip-box">
                    <p><span class="emoji">📈</span><strong>进阶学习建议：</strong></p>
                    <ul>
                        <li>基础阶段：掌握字符串基本操作和方法</li>
                        <li>进阶阶段：学习正则表达式和性能优化</li>
                        <li>高级阶段：了解字符串安全性和国际化</li>
                        <li>实战阶段：结合AI工具解决实际项目问题</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第20页：字符串国际化 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">🌍</span>字符串国际化</h1>
            </div>
            <div class="content">
                <h2>多语言字符串处理</h2>
                
                <h3><span class="emoji">🔤</span>Unicode处理</h3>
                <div class="code-block">
<span class="keyword">import</span> <span class="keyword">unicodedata</span>

<span class="comment"># Unicode字符分析</span>
<span class="keyword">def</span> <span class="function">analyze_unicode</span>(<span class="keyword">text</span>):
    <span class="keyword">for</span> <span class="keyword">char</span> <span class="keyword">in</span> <span class="keyword">text</span>:
        <span class="keyword">name</span> = <span class="keyword">unicodedata</span>.<span class="function">name</span>(<span class="keyword">char</span>, <span class="string">"UNKNOWN"</span>)
        <span class="keyword">category</span> = <span class="keyword">unicodedata</span>.<span class="function">category</span>(<span class="keyword">char</span>)
        <span class="keyword">code_point</span> = <span class="function">ord</span>(<span class="keyword">char</span>)
        <span class="function">print</span>(<span class="string">f"字符: {char}, 名称: {name}, 类别: {category}, 码点: U+{code_point:04X}"</span>)

<span class="comment"># 测试多语言字符</span>
<span class="keyword">multilingual_text</span> = <span class="string">"Hello 你好 こんにちは 🌍"</span>
<span class="function">analyze_unicode</span>(<span class="keyword">multilingual_text</span>)

<span class="comment"># Unicode规范化</span>
<span class="keyword">def</span> <span class="function">normalize_text</span>(<span class="keyword">text</span>, <span class="keyword">form</span>=<span class="string">"NFC"</span>):
    <span class="string">"""Unicode文本规范化
    
    Args:
        text: 待规范化的文本
        form: 规范化形式 (NFC, NFD, NFKC, NFKD)
    
    Returns:
        规范化后的文本
    """</span>
    <span class="keyword">return</span> <span class="keyword">unicodedata</span>.<span class="function">normalize</span>(<span class="keyword">form</span>, <span class="keyword">text</span>)

<span class="comment"># 测试规范化</span>
<span class="keyword">text1</span> = <span class="string">"café"</span>  <span class="comment"># é 是单个字符</span>
<span class="keyword">text2</span> = <span class="string">"cafe\u0301"</span>  <span class="comment"># e + 重音符</span>

<span class="function">print</span>(<span class="string">f"text1 == text2: {text1 == text2}"</span>)  <span class="comment"># False</span>
<span class="function">print</span>(<span class="string">f"规范化后相等: {normalize_text(text1) == normalize_text(text2)}"</span>)  <span class="comment"># True</span>
                </div>

                <h3><span class="emoji">🌐</span>多语言文本处理</h3>
                <div class="code-block">
<span class="comment"># 语言检测和处理</span>
<span class="keyword">import</span> <span class="keyword">re</span>

<span class="keyword">class</span> <span class="function">MultiLanguageProcessor</span>:
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>):
        <span class="keyword">self</span>.<span class="keyword">patterns</span> = {
            <span class="string">"chinese"</span>: <span class="keyword">re</span>.<span class="function">compile</span>(<span class="string">r"[\u4e00-\u9fff]+"</span>),
            <span class="string">"japanese"</span>: <span class="keyword">re</span>.<span class="function">compile</span>(<span class="string">r"[\u3040-\u309f\u30a0-\u30ff]+"</span>),
            <span class="string">"korean"</span>: <span class="keyword">re</span>.<span class="function">compile</span>(<span class="string">r"[\uac00-\ud7af]+"</span>),
            <span class="string">"arabic"</span>: <span class="keyword">re</span>.<span class="function">compile</span>(<span class="string">r"[\u0600-\u06ff]+"</span>),
            <span class="string">"cyrillic"</span>: <span class="keyword">re</span>.<span class="function">compile</span>(<span class="string">r"[\u0400-\u04ff]+"</span>)
        }
    
    <span class="keyword">def</span> <span class="function">detect_languages</span>(<span class="keyword">self</span>, <span class="keyword">text</span>):
        <span class="keyword">detected</span> = []
        <span class="keyword">for</span> <span class="keyword">lang</span>, <span class="keyword">pattern</span> <span class="keyword">in</span> <span class="keyword">self</span>.<span class="keyword">patterns</span>.<span class="function">items</span>():
            <span class="keyword">if</span> <span class="keyword">pattern</span>.<span class="function">search</span>(<span class="keyword">text</span>):
                <span class="keyword">detected</span>.<span class="function">append</span>(<span class="keyword">lang</span>)
        <span class="keyword">return</span> <span class="keyword">detected</span>
    
    <span class="keyword">def</span> <span class="function">extract_by_language</span>(<span class="keyword">self</span>, <span class="keyword">text</span>, <span class="keyword">language</span>):
        <span class="keyword">if</span> <span class="keyword">language</span> <span class="keyword">in</span> <span class="keyword">self</span>.<span class="keyword">patterns</span>:
            <span class="keyword">return</span> <span class="keyword">self</span>.<span class="keyword">patterns</span>[<span class="keyword">language</span>].<span class="function">findall</span>(<span class="keyword">text</span>)
        <span class="keyword">return</span> []

<span class="comment"># 使用示例</span>
<span class="keyword">processor</span> = <span class="function">MultiLanguageProcessor</span>()
<span class="keyword">mixed_text</span> = <span class="string">"Hello 你好世界 こんにちは 안녕하세요 مرحبا Привет"</span>

<span class="keyword">languages</span> = <span class="keyword">processor</span>.<span class="function">detect_languages</span>(<span class="keyword">mixed_text</span>)
<span class="function">print</span>(<span class="string">f"检测到的语言: {languages}"</span>)

<span class="keyword">chinese_text</span> = <span class="keyword">processor</span>.<span class="function">extract_by_language</span>(<span class="keyword">mixed_text</span>, <span class="string">"chinese"</span>)
<span class="function">print</span>(<span class="string">f"中文文本: {chinese_text}"</span>)
                </div>

                <h3><span class="emoji">📅</span>日期时间国际化</h3>
                <div class="code-block">
<span class="keyword">import</span> <span class="keyword">datetime</span>
<span class="keyword">import</span> <span class="keyword">locale</span>

<span class="comment"># 多语言日期格式化</span>
<span class="keyword">class</span> <span class="function">DateTimeFormatter</span>:
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>):
        <span class="keyword">self</span>.<span class="keyword">formats</span> = {
            <span class="string">"zh_CN"</span>: <span class="string">"%Y年%m月%d日 %H:%M:%S"</span>,
            <span class="string">"en_US"</span>: <span class="string">"%B %d, %Y %I:%M:%S %p"</span>,
            <span class="string">"ja_JP"</span>: <span class="string">"%Y年%m月%d日 %H時%M分%S秒"</span>,
            <span class="string">"de_DE"</span>: <span class="string">"%d. %B %Y %H:%M:%S"</span>
        }
    
    <span class="keyword">def</span> <span class="function">format_datetime</span>(<span class="keyword">self</span>, <span class="keyword">dt</span>, <span class="keyword">locale_code</span>):
        <span class="keyword">if</span> <span class="keyword">locale_code</span> <span class="keyword">in</span> <span class="keyword">self</span>.<span class="keyword">formats</span>:
            <span class="keyword">return</span> <span class="keyword">dt</span>.<span class="function">strftime</span>(<span class="keyword">self</span>.<span class="keyword">formats</span>[<span class="keyword">locale_code</span>])
        <span class="keyword">return</span> <span class="keyword">dt</span>.<span class="function">isoformat</span>()

<span class="comment"># 使用示例</span>
<span class="keyword">formatter</span> = <span class="function">DateTimeFormatter</span>()
<span class="keyword">now</span> = <span class="keyword">datetime</span>.<span class="function">datetime</span>.<span class="function">now</span>()

<span class="keyword">for</span> <span class="keyword">locale_code</span> <span class="keyword">in</span> [<span class="string">"zh_CN"</span>, <span class="string">"en_US"</span>, <span class="string">"ja_JP"</span>, <span class="string">"de_DE"</span>]:
    <span class="keyword">formatted</span> = <span class="keyword">formatter</span>.<span class="function">format_datetime</span>(<span class="keyword">now</span>, <span class="keyword">locale_code</span>)
    <span class="function">print</span>(<span class="string">f"{locale_code}: {formatted}"</span>)
                </div>

                <h3><span class="emoji">💱</span>货币和数字格式化</h3>
                <div class="code-block">
<span class="comment"># 多语言数字和货币格式化</span>
<span class="keyword">class</span> <span class="function">NumberFormatter</span>:
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>):
        <span class="keyword">self</span>.<span class="keyword">currency_symbols</span> = {
            <span class="string">"CNY"</span>: <span class="string">"¥"</span>,
            <span class="string">"USD"</span>: <span class="string">"$"</span>,
            <span class="string">"EUR"</span>: <span class="string">"€"</span>,
            <span class="string">"JPY"</span>: <span class="string">"¥"</span>,
            <span class="string">"GBP"</span>: <span class="string">"£"</span>
        }
        
        <span class="keyword">self</span>.<span class="keyword">decimal_separators</span> = {
            <span class="string">"US"</span>: <span class="string">"."</span>,
            <span class="string">"EU"</span>: <span class="string">","</span>,
            <span class="string">"CN"</span>: <span class="string">"."</span>
        }
        
        <span class="keyword">self</span>.<span class="keyword">thousand_separators</span> = {
            <span class="string">"US"</span>: <span class="string">","</span>,
            <span class="string">"EU"</span>: <span class="string">"."</span>,
            <span class="string">"CN"</span>: <span class="string">","</span>
        }
    
    <span class="keyword">def</span> <span class="function">format_currency</span>(<span class="keyword">self</span>, <span class="keyword">amount</span>, <span class="keyword">currency</span>, <span class="keyword">locale</span>=<span class="string">"US"</span>):
        <span class="keyword">symbol</span> = <span class="keyword">self</span>.<span class="keyword">currency_symbols</span>.<span class="function">get</span>(<span class="keyword">currency</span>, <span class="keyword">currency</span>)
        <span class="keyword">formatted_number</span> = <span class="keyword">self</span>.<span class="function">format_number</span>(<span class="keyword">amount</span>, <span class="keyword">locale</span>)
        <span class="keyword">return</span> <span class="string">f"{symbol}{formatted_number}"</span>
    
    <span class="keyword">def</span> <span class="function">format_number</span>(<span class="keyword">self</span>, <span class="keyword">number</span>, <span class="keyword">locale</span>=<span class="string">"US"</span>):
        <span class="keyword">decimal_sep</span> = <span class="keyword">self</span>.<span class="keyword">decimal_separators</span>.<span class="function">get</span>(<span class="keyword">locale</span>, <span class="string">"."</span>)
        <span class="keyword">thousand_sep</span> = <span class="keyword">self</span>.<span class="keyword">thousand_separators</span>.<span class="function">get</span>(<span class="keyword">locale</span>, <span class="string">","</span>)
        
        <span class="comment"># 简单的数字格式化实现</span>
        <span class="keyword">parts</span> = <span class="string">f"{number:.2f}"</span>.<span class="function">split</span>(<span class="string">"."</span>)
        <span class="keyword">integer_part</span> = <span class="keyword">parts</span>[<span class="number">0</span>]
        <span class="keyword">decimal_part</span> = <span class="keyword">parts</span>[<span class="number">1</span>]
        
        <span class="comment"># 添加千位分隔符</span>
        <span class="keyword">formatted_integer</span> = <span class="string">""</span>
        <span class="keyword">for</span> <span class="keyword">i</span>, <span class="keyword">digit</span> <span class="keyword">in</span> <span class="function">enumerate</span>(<span class="keyword">integer_part</span>[:<span class="number">-1</span>:-<span class="number">1</span>]):
            <span class="keyword">if</span> <span class="keyword">i</span> > <span class="number">0</span> <span class="keyword">and</span> <span class="keyword">i</span> % <span class="number">3</span> == <span class="number">0</span>:
                <span class="keyword">formatted_integer</span> = <span class="keyword">thousand_sep</span> + <span class="keyword">formatted_integer</span>
            <span class="keyword">formatted_integer</span> = <span class="keyword">digit</span> + <span class="keyword">formatted_integer</span>
        
        <span class="keyword">return</span> <span class="string">f"{formatted_integer}{decimal_sep}{decimal_part}"</span>

<span class="comment"># 使用示例</span>
<span class="keyword">formatter</span> = <span class="function">NumberFormatter</span>()
<span class="keyword">amount</span> = <span class="number">1234567.89</span>

<span class="function">print</span>(<span class="string">f"美式格式: {formatter.format_currency(amount, 'USD', 'US')}"</span>)
<span class="function">print</span>(<span class="string">f"欧式格式: {formatter.format_currency(amount, 'EUR', 'EU')}"</span>)
<span class="function">print</span>(<span class="string">f"中式格式: {formatter.format_currency(amount, 'CNY', 'CN')}"</span>)
                </div>

                <div class="ai-prompt">
                     <h3><span class="emoji">🤖</span>AI 提示词模板</h3>
                     <p>"请帮我设计一个Python多语言文本处理系统，包括Unicode处理、语言检测、文本规范化和本地化格式化功能。"</p>
                 </div>
             </div>
         </div>

        <!-- 第21页：实际项目应用 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">🚀</span>实际项目应用</h1>
            </div>
            <div class="content">
                <h2>字符串在真实项目中的应用</h2>
                
                <h3><span class="emoji">📊</span>数据清洗项目</h3>
                <div class="code-block">
<span class="comment"># 真实数据清洗案例：处理用户输入数据</span>
<span class="keyword">import</span> <span class="keyword">re</span>
<span class="keyword">import</span> <span class="keyword">pandas</span> <span class="keyword">as</span> <span class="keyword">pd</span>

<span class="keyword">class</span> <span class="function">DataCleaner</span>:
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>):
        <span class="keyword">self</span>.<span class="keyword">phone_pattern</span> = <span class="keyword">re</span>.<span class="function">compile</span>(<span class="string">r'1[3-9]\d{9}'</span>)
        <span class="keyword">self</span>.<span class="keyword">email_pattern</span> = <span class="keyword">re</span>.<span class="function">compile</span>(<span class="string">r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'</span>)
        <span class="keyword">self</span>.<span class="keyword">id_pattern</span> = <span class="keyword">re</span>.<span class="function">compile</span>(<span class="string">r'\d{17}[\dXx]'</span>)
    
    <span class="keyword">def</span> <span class="function">clean_user_data</span>(<span class="keyword">self</span>, <span class="keyword">data</span>):
        <span class="string">"""清洗用户数据
        
        Args:
            data (dict): 原始用户数据
        
        Returns:
            dict: 清洗后的数据
        """</span>
        <span class="keyword">cleaned_data</span> = {}
        
        <span class="comment"># 清洗姓名</span>
        <span class="keyword">if</span> <span class="string">'name'</span> <span class="keyword">in</span> <span class="keyword">data</span>:
            <span class="keyword">name</span> = <span class="keyword">data</span>[<span class="string">'name'</span>].<span class="function">strip</span>()
            <span class="comment"># 移除特殊字符，只保留中文、英文和空格</span>
            <span class="keyword">name</span> = <span class="keyword">re</span>.<span class="function">sub</span>(<span class="string">r'[^\u4e00-\u9fa5a-zA-Z\s]'</span>, <span class="string">''</span>, <span class="keyword">name</span>)
            <span class="keyword">cleaned_data</span>[<span class="string">'name'</span>] = <span class="keyword">name</span>.<span class="function">title</span>() <span class="keyword">if</span> <span class="keyword">name</span> <span class="keyword">else</span> <span class="string">''</span>
        
        <span class="comment"># 清洗手机号</span>
        <span class="keyword">if</span> <span class="string">'phone'</span> <span class="keyword">in</span> <span class="keyword">data</span>:
            <span class="keyword">phone</span> = <span class="keyword">re</span>.<span class="function">sub</span>(<span class="string">r'\D'</span>, <span class="string">''</span>, <span class="keyword">data</span>[<span class="string">'phone'</span>])
            <span class="keyword">if</span> <span class="keyword">self</span>.<span class="keyword">phone_pattern</span>.<span class="function">match</span>(<span class="keyword">phone</span>):
                <span class="keyword">cleaned_data</span>[<span class="string">'phone'</span>] = <span class="keyword">phone</span>
            <span class="keyword">else</span>:
                <span class="keyword">cleaned_data</span>[<span class="string">'phone'</span>] = <span class="string">''</span>
        
        <span class="comment"># 清洗邮箱</span>
        <span class="keyword">if</span> <span class="string">'email'</span> <span class="keyword">in</span> <span class="keyword">data</span>:
            <span class="keyword">email</span> = <span class="keyword">data</span>[<span class="string">'email'</span>].<span class="function">strip</span>().<span class="function">lower</span>()
            <span class="keyword">if</span> <span class="keyword">self</span>.<span class="keyword">email_pattern</span>.<span class="function">match</span>(<span class="keyword">email</span>):
                <span class="keyword">cleaned_data</span>[<span class="string">'email'</span>] = <span class="keyword">email</span>
            <span class="keyword">else</span>:
                <span class="keyword">cleaned_data</span>[<span class="string">'email'</span>] = <span class="string">''</span>
        
        <span class="keyword">return</span> <span class="keyword">cleaned_data</span>
    
    <span class="keyword">def</span> <span class="function">batch_clean</span>(<span class="keyword">self</span>, <span class="keyword">csv_file</span>):
        <span class="string">"""批量清洗CSV文件中的数据"""</span>
        <span class="keyword">df</span> = <span class="keyword">pd</span>.<span class="function">read_csv</span>(<span class="keyword">csv_file</span>)
        <span class="keyword">cleaned_records</span> = []
        
        <span class="keyword">for</span> <span class="keyword">index</span>, <span class="keyword">row</span> <span class="keyword">in</span> <span class="keyword">df</span>.<span class="function">iterrows</span>():
            <span class="keyword">cleaned</span> = <span class="keyword">self</span>.<span class="function">clean_user_data</span>(<span class="keyword">row</span>.<span class="function">to_dict</span>())
            <span class="keyword">cleaned_records</span>.<span class="function">append</span>(<span class="keyword">cleaned</span>)
        
        <span class="keyword">return</span> <span class="keyword">pd</span>.<span class="function">DataFrame</span>(<span class="keyword">cleaned_records</span>)

<span class="comment"># 使用示例</span>
<span class="keyword">cleaner</span> = <span class="function">DataCleaner</span>()
<span class="keyword">sample_data</span> = {
    <span class="string">'name'</span>: <span class="string">'  张三@#$  '</span>,
    <span class="string">'phone'</span>: <span class="string">'138-1234-5678'</span>,
    <span class="string">'email'</span>: <span class="string">'  <EMAIL>  '</span>
}

<span class="keyword">cleaned</span> = <span class="keyword">cleaner</span>.<span class="function">clean_user_data</span>(<span class="keyword">sample_data</span>)
<span class="function">print</span>(<span class="string">f"清洗结果: {cleaned}"</span>)
                </div>

                <h3><span class="emoji">🌐</span>Web爬虫项目</h3>
                <div class="code-block">
<span class="comment"># 网页内容提取和清洗</span>
<span class="keyword">import</span> <span class="keyword">requests</span>
<span class="keyword">from</span> <span class="keyword">bs4</span> <span class="keyword">import</span> <span class="keyword">BeautifulSoup</span>
<span class="keyword">import</span> <span class="keyword">re</span>

<span class="keyword">class</span> <span class="function">WebContentExtractor</span>:
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>):
        <span class="keyword">self</span>.<span class="keyword">session</span> = <span class="keyword">requests</span>.<span class="function">Session</span>()
        <span class="keyword">self</span>.<span class="keyword">session</span>.<span class="keyword">headers</span>.<span class="function">update</span>({
            <span class="string">'User-Agent'</span>: <span class="string">'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'</span>
        })
    
    <span class="keyword">def</span> <span class="function">extract_text</span>(<span class="keyword">self</span>, <span class="keyword">url</span>):
        <span class="string">"""提取网页文本内容"""</span>
        <span class="keyword">try</span>:
            <span class="keyword">response</span> = <span class="keyword">self</span>.<span class="keyword">session</span>.<span class="function">get</span>(<span class="keyword">url</span>)
            <span class="keyword">response</span>.<span class="keyword">encoding</span> = <span class="string">'utf-8'</span>
            
            <span class="keyword">soup</span> = <span class="function">BeautifulSoup</span>(<span class="keyword">response</span>.<span class="keyword">text</span>, <span class="string">'html.parser'</span>)
            
            <span class="comment"># 移除脚本和样式标签</span>
            <span class="keyword">for</span> <span class="keyword">script</span> <span class="keyword">in</span> <span class="keyword">soup</span>([<span class="string">"script"</span>, <span class="string">"style"</span>]):
                <span class="keyword">script</span>.<span class="function">decompose</span>()
            
            <span class="comment"># 提取文本</span>
            <span class="keyword">text</span> = <span class="keyword">soup</span>.<span class="function">get_text</span>()
            
            <span class="comment"># 清洗文本</span>
            <span class="keyword">text</span> = <span class="keyword">self</span>.<span class="function">clean_text</span>(<span class="keyword">text</span>)
            
            <span class="keyword">return</span> <span class="keyword">text</span>
        
        <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> <span class="keyword">e</span>:
            <span class="function">print</span>(<span class="string">f"提取失败: {e}"</span>)
            <span class="keyword">return</span> <span class="string">""</span>
    
    <span class="keyword">def</span> <span class="function">clean_text</span>(<span class="keyword">self</span>, <span class="keyword">text</span>):
        <span class="string">"""清洗提取的文本"""</span>
        <span class="comment"># 移除多余的空白字符</span>
        <span class="keyword">text</span> = <span class="keyword">re</span>.<span class="function">sub</span>(<span class="string">r'\s+'</span>, <span class="string">' '</span>, <span class="keyword">text</span>)
        
        <span class="comment"># 移除特殊字符</span>
        <span class="keyword">text</span> = <span class="keyword">re</span>.<span class="function">sub</span>(<span class="string">r'[^\u4e00-\u9fa5a-zA-Z0-9\s.,!?;:()\[\]{}"\'-]'</span>, <span class="string">''</span>, <span class="keyword">text</span>)
        
        <span class="comment"># 移除首尾空白</span>
        <span class="keyword">text</span> = <span class="keyword">text</span>.<span class="function">strip</span>()
        
        <span class="keyword">return</span> <span class="keyword">text</span>
    
    <span class="keyword">def</span> <span class="function">extract_emails</span>(<span class="keyword">self</span>, <span class="keyword">text</span>):
        <span class="string">"""从文本中提取邮箱地址"""</span>
        <span class="keyword">email_pattern</span> = <span class="keyword">re</span>.<span class="function">compile</span>(<span class="string">r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'</span>)
        <span class="keyword">return</span> <span class="keyword">email_pattern</span>.<span class="function">findall</span>(<span class="keyword">text</span>)
    
    <span class="keyword">def</span> <span class="function">extract_urls</span>(<span class="keyword">self</span>, <span class="keyword">text</span>):
        <span class="string">"""从文本中提取URL"""</span>
        <span class="keyword">url_pattern</span> = <span class="keyword">re</span>.<span class="function">compile</span>(<span class="string">r'https?://[^\s<>"{}|\\^`\[\]]+'</span>)
        <span class="keyword">return</span> <span class="keyword">url_pattern</span>.<span class="function">findall</span>(<span class="keyword">text</span>)

<span class="comment"># 使用示例</span>
<span class="keyword">extractor</span> = <span class="function">WebContentExtractor</span>()
<span class="keyword">sample_text</span> = <span class="string">"联系我们：<EMAIL> 或访问 https://www.example.com"</span>

<span class="keyword">emails</span> = <span class="keyword">extractor</span>.<span class="function">extract_emails</span>(<span class="keyword">sample_text</span>)
<span class="keyword">urls</span> = <span class="keyword">extractor</span>.<span class="function">extract_urls</span>(<span class="keyword">sample_text</span>)

<span class="function">print</span>(<span class="string">f"提取的邮箱: {emails}"</span>)
<span class="function">print</span>(<span class="string">f"提取的URL: {urls}"</span>)
                </div>

                <h3><span class="emoji">📝</span>日志分析项目</h3>
                <div class="code-block">
<span class="comment"># 服务器日志分析</span>
<span class="keyword">import</span> <span class="keyword">re</span>
<span class="keyword">from</span> <span class="keyword">datetime</span> <span class="keyword">import</span> <span class="keyword">datetime</span>
<span class="keyword">from</span> <span class="keyword">collections</span> <span class="keyword">import</span> <span class="keyword">Counter</span>, <span class="keyword">defaultdict</span>

<span class="keyword">class</span> <span class="function">LogAnalyzer</span>:
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>):
        <span class="comment"># Apache/Nginx 日志格式</span>
        <span class="keyword">self</span>.<span class="keyword">log_pattern</span> = <span class="keyword">re</span>.<span class="function">compile</span>(
            <span class="string">r'(?P<ip>\d+\.\d+\.\d+\.\d+) - - \[(?P<datetime>[^\]]+)\] '</span>
            <span class="string">r'"(?P<method>\w+) (?P<url>[^"]*) HTTP/[^"]*" '</span>
            <span class="string">r'(?P<status>\d+) (?P<size>\d+|-) "(?P<referer>[^"]*)" '</span>
            <span class="string">r'"(?P<user_agent>[^"]*)'</span>
        )
        
        <span class="keyword">self</span>.<span class="keyword">ip_stats</span> = <span class="function">Counter</span>()
        <span class="keyword">self</span>.<span class="keyword">url_stats</span> = <span class="function">Counter</span>()
        <span class="keyword">self</span>.<span class="keyword">status_stats</span> = <span class="function">Counter</span>()
        <span class="keyword">self</span>.<span class="keyword">hourly_stats</span> = <span class="function">defaultdict</span>(<span class="function">int</span>)
    
    <span class="keyword">def</span> <span class="function">parse_log_line</span>(<span class="keyword">self</span>, <span class="keyword">line</span>):
        <span class="string">"""解析单行日志"""</span>
        <span class="keyword">match</span> = <span class="keyword">self</span>.<span class="keyword">log_pattern</span>.<span class="function">match</span>(<span class="keyword">line</span>.<span class="function">strip</span>())
        <span class="keyword">if</span> <span class="keyword">match</span>:
            <span class="keyword">return</span> <span class="keyword">match</span>.<span class="function">groupdict</span>()
        <span class="keyword">return</span> <span class="keyword">None</span>
    
    <span class="keyword">def</span> <span class="function">analyze_file</span>(<span class="keyword">self</span>, <span class="keyword">log_file</span>):
        <span class="string">"""分析日志文件"""</span>
        <span class="keyword">with</span> <span class="function">open</span>(<span class="keyword">log_file</span>, <span class="string">'r'</span>, <span class="keyword">encoding</span>=<span class="string">'utf-8'</span>) <span class="keyword">as</span> <span class="keyword">f</span>:
            <span class="keyword">for</span> <span class="keyword">line_num</span>, <span class="keyword">line</span> <span class="keyword">in</span> <span class="function">enumerate</span>(<span class="keyword">f</span>, <span class="number">1</span>):
                <span class="keyword">parsed</span> = <span class="keyword">self</span>.<span class="function">parse_log_line</span>(<span class="keyword">line</span>)
                <span class="keyword">if</span> <span class="keyword">parsed</span>:
                    <span class="keyword">self</span>.<span class="function">update_stats</span>(<span class="keyword">parsed</span>)
                <span class="keyword">else</span>:
                    <span class="function">print</span>(<span class="string">f"无法解析第{line_num}行: {line.strip()[:50]}..."</span>)
    
    <span class="keyword">def</span> <span class="function">update_stats</span>(<span class="keyword">self</span>, <span class="keyword">parsed_data</span>):
        <span class="string">"""更新统计信息"""</span>
        <span class="keyword">self</span>.<span class="keyword">ip_stats</span>[<span class="keyword">parsed_data</span>[<span class="string">'ip'</span>]] += <span class="number">1</span>
        <span class="keyword">self</span>.<span class="keyword">url_stats</span>[<span class="keyword">parsed_data</span>[<span class="string">'url'</span>]] += <span class="number">1</span>
        <span class="keyword">self</span>.<span class="keyword">status_stats</span>[<span class="keyword">parsed_data</span>[<span class="string">'status'</span>]] += <span class="number">1</span>
        
        <span class="comment"># 提取小时信息</span>
        <span class="keyword">datetime_str</span> = <span class="keyword">parsed_data</span>[<span class="string">'datetime'</span>]
        <span class="keyword">try</span>:
            <span class="keyword">dt</span> = <span class="keyword">datetime</span>.<span class="function">strptime</span>(<span class="keyword">datetime_str</span>.<span class="function">split</span>(<span class="string">' '</span>)[<span class="number">0</span>], <span class="string">'%d/%b/%Y:%H:%M:%S'</span>)
            <span class="keyword">hour</span> = <span class="keyword">dt</span>.<span class="keyword">hour</span>
            <span class="keyword">self</span>.<span class="keyword">hourly_stats</span>[<span class="keyword">hour</span>] += <span class="number">1</span>
        <span class="keyword">except</span> <span class="function">ValueError</span>:
            <span class="keyword">pass</span>
    
    <span class="keyword">def</span> <span class="function">generate_report</span>(<span class="keyword">self</span>):
        <span class="string">"""生成分析报告"""</span>
        <span class="keyword">report</span> = []
        <span class="keyword">report</span>.<span class="function">append</span>(<span class="string">"=== 日志分析报告 ==="</span>)
        
        <span class="comment"># Top 10 IP地址</span>
        <span class="keyword">report</span>.<span class="function">append</span>(<span class="string">"\n访问量最高的10个IP地址:"</span>)
        <span class="keyword">for</span> <span class="keyword">ip</span>, <span class="keyword">count</span> <span class="keyword">in</span> <span class="keyword">self</span>.<span class="keyword">ip_stats</span>.<span class="function">most_common</span>(<span class="number">10</span>):
            <span class="keyword">report</span>.<span class="function">append</span>(<span class="string">f"{ip}: {count}次"</span>)
        
        <span class="comment"># Top 10 访问页面</span>
        <span class="keyword">report</span>.<span class="function">append</span>(<span class="string">"\n访问量最高的10个页面:"</span>)
        <span class="keyword">for</span> <span class="keyword">url</span>, <span class="keyword">count</span> <span class="keyword">in</span> <span class="keyword">self</span>.<span class="keyword">url_stats</span>.<span class="function">most_common</span>(<span class="number">10</span>):
            <span class="keyword">report</span>.<span class="function">append</span>(<span class="string">f"{url}: {count}次"</span>)
        
        <span class="comment"># 状态码统计</span>
        <span class="keyword">report</span>.<span class="function">append</span>(<span class="string">"\n状态码统计:"</span>)
        <span class="keyword">for</span> <span class="keyword">status</span>, <span class="keyword">count</span> <span class="keyword">in</span> <span class="keyword">self</span>.<span class="keyword">status_stats</span>.<span class="function">most_common</span>():
            <span class="keyword">report</span>.<span class="function">append</span>(<span class="string">f"{status}: {count}次"</span>)
        
        <span class="comment"># 小时访问统计</span>
        <span class="keyword">report</span>.<span class="function">append</span>(<span class="string">"\n24小时访问分布:"</span>)
        <span class="keyword">for</span> <span class="keyword">hour</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">24</span>):
            <span class="keyword">count</span> = <span class="keyword">self</span>.<span class="keyword">hourly_stats</span>[<span class="keyword">hour</span>]
            <span class="keyword">bar</span> = <span class="string">'█'</span> * (<span class="keyword">count</span> // <span class="number">10</span>)  <span class="comment"># 简单的条形图</span>
            <span class="keyword">report</span>.<span class="function">append</span>(<span class="string">f"{hour:02d}:00 {count:>6} {bar}"</span>)
        
        <span class="keyword">return</span> <span class="string">'\n'</span>.<span class="function">join</span>(<span class="keyword">report</span>)

<span class="comment"># 使用示例</span>
<span class="keyword">analyzer</span> = <span class="function">LogAnalyzer</span>()
<span class="comment"># analyzer.analyze_file('access.log')</span>
<span class="comment"># print(analyzer.generate_report())</span>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI 提示词模板</h3>
                    <p>"请帮我设计一个Python项目，用于[具体应用场景]，需要处理[数据类型]，实现[核心功能]，并包含错误处理和性能优化。"</p>
                </div>
            </div>
        </div>

        <!-- 第22页：字符串最佳实践 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">⭐</span>字符串最佳实践</h1>
            </div>
            <div class="content">
                <h2>编写高质量字符串代码的指南</h2>
                
                <h3><span class="emoji">📋</span>代码规范</h3>
                <div class="code-block">
<span class="comment"># 好的实践：使用有意义的变量名</span>
<span class="keyword">user_email</span> = <span class="string">"<EMAIL>"</span>
<span class="keyword">error_message</span> = <span class="string">"用户名不能为空"</span>
<span class="keyword">sql_query</span> = <span class="string">"SELECT * FROM users WHERE active = 1"</span>

<span class="comment"># 避免：使用无意义的变量名</span>
<span class="comment"># s = "<EMAIL>"</span>
<span class="comment"># msg = "用户名不能为空"</span>
<span class="comment"># q = "SELECT * FROM users WHERE active = 1"</span>

<span class="comment"># 好的实践：使用常量定义重复的字符串</span>
<span class="keyword">class</span> <span class="function">Constants</span>:
    <span class="keyword">DEFAULT_ENCODING</span> = <span class="string">"utf-8"</span>
    <span class="keyword">EMAIL_PATTERN</span> = <span class="string">r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"</span>
    <span class="keyword">DATE_FORMAT</span> = <span class="string">"%Y-%m-%d %H:%M:%S"</span>
    
    <span class="keyword">class</span> <span class="function">ErrorMessages</span>:
        <span class="keyword">INVALID_EMAIL</span> = <span class="string">"邮箱格式不正确"</span>
        <span class="keyword">EMPTY_FIELD</span> = <span class="string">"字段不能为空"</span>
        <span class="keyword">PASSWORD_TOO_SHORT</span> = <span class="string">"密码长度至少8位"</span>

<span class="comment"># 好的实践：使用文档字符串</span>
<span class="keyword">def</span> <span class="function">validate_email</span>(<span class="keyword">email</span>: <span class="keyword">str</span>) -> <span class="keyword">bool</span>:
    <span class="string">"""验证邮箱格式是否正确
    
    Args:
        email (str): 待验证的邮箱地址
    
    Returns:
        bool: 验证结果，True表示格式正确
    
    Examples:
        >>> validate_email("<EMAIL>")
        True
        >>> validate_email("invalid-email")
        False
    """</span>
    <span class="keyword">import</span> <span class="keyword">re</span>
    <span class="keyword">return</span> <span class="function">bool</span>(<span class="keyword">re</span>.<span class="function">match</span>(<span class="keyword">Constants</span>.<span class="keyword">EMAIL_PATTERN</span>, <span class="keyword">email</span>))

<span class="comment"># 好的实践：使用类型提示</span>
<span class="keyword">from</span> <span class="keyword">typing</span> <span class="keyword">import</span> <span class="keyword">List</span>, <span class="keyword">Optional</span>, <span class="keyword">Dict</span>

<span class="keyword">def</span> <span class="function">process_user_data</span>(
    <span class="keyword">users</span>: <span class="keyword">List</span>[<span class="keyword">Dict</span>[<span class="keyword">str</span>, <span class="keyword">str</span>]], 
    <span class="keyword">encoding</span>: <span class="keyword">Optional</span>[<span class="keyword">str</span>] = <span class="keyword">None</span>
) -> <span class="keyword">List</span>[<span class="keyword">str</span>]:
    <span class="string">"""处理用户数据列表"""</span>
    <span class="keyword">encoding</span> = <span class="keyword">encoding</span> <span class="keyword">or</span> <span class="keyword">Constants</span>.<span class="keyword">DEFAULT_ENCODING</span>
    <span class="keyword">result</span> = []
    
    <span class="keyword">for</span> <span class="keyword">user</span> <span class="keyword">in</span> <span class="keyword">users</span>:
        <span class="keyword">if</span> <span class="function">validate_email</span>(<span class="keyword">user</span>.<span class="function">get</span>(<span class="string">'email'</span>, <span class="string">''</span>)):
            <span class="keyword">result</span>.<span class="function">append</span>(<span class="keyword">user</span>[<span class="string">'email'</span>])
    
    <span class="keyword">return</span> <span class="keyword">result</span>
                </div>

                <h3><span class="emoji">🔒</span>安全实践</h3>
                <div class="code-block">
<span class="comment"># 好的实践：使用参数化查询防止SQL注入</span>
<span class="keyword">import</span> <span class="keyword">sqlite3</span>

<span class="keyword">def</span> <span class="function">get_user_by_email_safe</span>(<span class="keyword">email</span>: <span class="keyword">str</span>):
    <span class="string">"""安全的数据库查询"""</span>
    <span class="keyword">conn</span> = <span class="keyword">sqlite3</span>.<span class="function">connect</span>(<span class="string">'users.db'</span>)
    <span class="keyword">cursor</span> = <span class="keyword">conn</span>.<span class="function">cursor</span>()
    
    <span class="comment"># 使用参数化查询</span>
    <span class="keyword">cursor</span>.<span class="function">execute</span>(<span class="string">"SELECT * FROM users WHERE email = ?"</span>, (<span class="keyword">email</span>,))
    <span class="keyword">result</span> = <span class="keyword">cursor</span>.<span class="function">fetchone</span>()
    
    <span class="keyword">conn</span>.<span class="function">close</span>()
    <span class="keyword">return</span> <span class="keyword">result</span>

<span class="comment"># 避免：字符串拼接（容易SQL注入）</span>
<span class="comment"># def get_user_by_email_unsafe(email: str):</span>
<span class="comment">#     query = f"SELECT * FROM users WHERE email = '{email}'"</span>
<span class="comment">#     # 如果email = "'; DROP TABLE users; --"，会删除整个表！</span>

<span class="comment"># 好的实践：输入验证和清理</span>
<span class="keyword">import</span> <span class="keyword">html</span>
<span class="keyword">import</span> <span class="keyword">re</span>

<span class="keyword">def</span> <span class="function">sanitize_user_input</span>(<span class="keyword">user_input</span>: <span class="keyword">str</span>) -> <span class="keyword">str</span>:
    <span class="string">"""清理用户输入，防止XSS攻击"""</span>
    <span class="comment"># 移除HTML标签</span>
    <span class="keyword">cleaned</span> = <span class="keyword">re</span>.<span class="function">sub</span>(<span class="string">r'<[^>]+>'</span>, <span class="string">''</span>, <span class="keyword">user_input</span>)
    
    <span class="comment"># HTML转义</span>
    <span class="keyword">cleaned</span> = <span class="keyword">html</span>.<span class="function">escape</span>(<span class="keyword">cleaned</span>)
    
    <span class="comment"># 限制长度</span>
    <span class="keyword">cleaned</span> = <span class="keyword">cleaned</span>[:<span class="number">1000</span>]
    
    <span class="keyword">return</span> <span class="keyword">cleaned</span>.<span class="function">strip</span>()

<span class="comment"># 好的实践：敏感信息处理</span>
<span class="keyword">import</span> <span class="keyword">hashlib</span>
<span class="keyword">import</span> <span class="keyword">secrets</span>

<span class="keyword">def</span> <span class="function">hash_password</span>(<span class="keyword">password</span>: <span class="keyword">str</span>) -> <span class="keyword">str</span>:
    <span class="string">"""安全地哈希密码"""</span>
    <span class="keyword">salt</span> = <span class="keyword">secrets</span>.<span class="function">token_hex</span>(<span class="number">16</span>)
    <span class="keyword">password_hash</span> = <span class="keyword">hashlib</span>.<span class="function">pbkdf2_hmac</span>(
        <span class="string">'sha256'</span>, 
        <span class="keyword">password</span>.<span class="function">encode</span>(<span class="string">'utf-8'</span>), 
        <span class="keyword">salt</span>.<span class="function">encode</span>(<span class="string">'utf-8'</span>), 
        <span class="number">100000</span>
    )
    <span class="keyword">return</span> <span class="keyword">salt</span> + <span class="keyword">password_hash</span>.<span class="function">hex</span>()

<span class="keyword">def</span> <span class="function">mask_sensitive_data</span>(<span class="keyword">data</span>: <span class="keyword">str</span>, <span class="keyword">mask_char</span>: <span class="keyword">str</span> = <span class="string">'*'</span>) -> <span class="keyword">str</span>:
    <span class="string">"""遮蔽敏感数据"""</span>
    <span class="keyword">if</span> <span class="function">len</span>(<span class="keyword">data</span>) <= <span class="number">4</span>:
        <span class="keyword">return</span> <span class="keyword">mask_char</span> * <span class="function">len</span>(<span class="keyword">data</span>)
    
    <span class="keyword">return</span> <span class="keyword">data</span>[:<span class="number">2</span>] + <span class="keyword">mask_char</span> * (<span class="function">len</span>(<span class="keyword">data</span>) - <span class="number">4</span>) + <span class="keyword">data</span>[-<span class="number">2</span>:]

<span class="comment"># 使用示例</span>
<span class="keyword">phone</span> = <span class="string">"13812345678"</span>
<span class="keyword">masked_phone</span> = <span class="function">mask_sensitive_data</span>(<span class="keyword">phone</span>)
<span class="function">print</span>(<span class="string">f"原始: {phone}, 遮蔽: {masked_phone}"</span>)  <span class="comment"># 原始: 13812345678, 遮蔽: 13*******78</span>
                </div>

                <h3><span class="emoji">⚡</span>性能实践</h3>
                <div class="code-block">
<span class="comment"># 好的实践：使用join而不是+进行字符串连接</span>
<span class="keyword">def</span> <span class="function">build_csv_line_efficient</span>(<span class="keyword">fields</span>: <span class="keyword">List</span>[<span class="keyword">str</span>]) -> <span class="keyword">str</span>:
    <span class="string">"""高效构建CSV行"""</span>
    <span class="keyword">return</span> <span class="string">","</span>.<span class="function">join</span>(<span class="keyword">fields</span>)

<span class="comment"># 避免：使用+连接大量字符串</span>
<span class="comment"># def build_csv_line_slow(fields: List[str]) -> str:</span>
<span class="comment">#     result = ""</span>
<span class="comment">#     for field in fields:</span>
<span class="comment">#         result += field + ","</span>
<span class="comment">#     return result[:-1]  # 移除最后的逗号</span>

<span class="comment"># 好的实践：使用生成器处理大文件</span>
<span class="keyword">def</span> <span class="function">process_large_file</span>(<span class="keyword">filename</span>: <span class="keyword">str</span>):
    <span class="string">"""内存友好的大文件处理"""</span>
    <span class="keyword">with</span> <span class="function">open</span>(<span class="keyword">filename</span>, <span class="string">'r'</span>, <span class="keyword">encoding</span>=<span class="string">'utf-8'</span>) <span class="keyword">as</span> <span class="keyword">f</span>:
        <span class="keyword">for</span> <span class="keyword">line</span> <span class="keyword">in</span> <span class="keyword">f</span>:
            <span class="comment"># 逐行处理，不会将整个文件加载到内存</span>
            <span class="keyword">processed_line</span> = <span class="keyword">line</span>.<span class="function">strip</span>().<span class="function">upper</span>()
            <span class="keyword">yield</span> <span class="keyword">processed_line</span>

<span class="comment"># 好的实践：预编译正则表达式</span>
<span class="keyword">import</span> <span class="keyword">re</span>

<span class="keyword">class</span> <span class="function">TextProcessor</span>:
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>):
        <span class="comment"># 预编译正则表达式</span>
        <span class="keyword">self</span>.<span class="keyword">email_pattern</span> = <span class="keyword">re</span>.<span class="function">compile</span>(<span class="string">r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'</span>)
        <span class="keyword">self</span>.<span class="keyword">phone_pattern</span> = <span class="keyword">re</span>.<span class="function">compile</span>(<span class="string">r'1[3-9]\d{9}'</span>)
        <span class="keyword">self</span>.<span class="keyword">whitespace_pattern</span> = <span class="keyword">re</span>.<span class="function">compile</span>(<span class="string">r'\s+'</span>)
    
    <span class="keyword">def</span> <span class="function">extract_contacts</span>(<span class="keyword">self</span>, <span class="keyword">text</span>: <span class="keyword">str</span>) -> <span class="keyword">Dict</span>[<span class="keyword">str</span>, <span class="keyword">List</span>[<span class="keyword">str</span>]]:
        <span class="string">"""高效提取联系信息"""</span>
        <span class="keyword">return</span> {
            <span class="string">'emails'</span>: <span class="keyword">self</span>.<span class="keyword">email_pattern</span>.<span class="function">findall</span>(<span class="keyword">text</span>),
            <span class="string">'phones'</span>: <span class="keyword">self</span>.<span class="keyword">phone_pattern</span>.<span class="function">findall</span>(<span class="keyword">text</span>)
        }
    
    <span class="keyword">def</span> <span class="function">normalize_whitespace</span>(<span class="keyword">self</span>, <span class="keyword">text</span>: <span class="keyword">str</span>) -> <span class="keyword">str</span>:
        <span class="string">"""规范化空白字符"""</span>
        <span class="keyword">return</span> <span class="keyword">self</span>.<span class="keyword">whitespace_pattern</span>.<span class="function">sub</span>(<span class="string">' '</span>, <span class="keyword">text</span>).<span class="function">strip</span>()

<span class="comment"># 好的实践：使用字符串缓存</span>
<span class="keyword">from</span> <span class="keyword">functools</span> <span class="keyword">import</span> <span class="keyword">lru_cache</span>

<span class="keyword">@lru_cache</span>(<span class="keyword">maxsize</span>=<span class="number">1000</span>)
<span class="keyword">def</span> <span class="function">expensive_string_operation</span>(<span class="keyword">text</span>: <span class="keyword">str</span>) -> <span class="keyword">str</span>:
    <span class="string">"""缓存昂贵的字符串操作结果"""</span>
    <span class="comment"># 模拟复杂的字符串处理</span>
    <span class="keyword">import</span> <span class="keyword">time</span>
    <span class="keyword">time</span>.<span class="function">sleep</span>(<span class="number">0.1</span>)  <span class="comment"># 模拟耗时操作</span>
    <span class="keyword">return</span> <span class="keyword">text</span>.<span class="function">upper</span>().<span class="function">replace</span>(<span class="string">' '</span>, <span class="string">'_'</span>)
                </div>

                <div class="tip-box">
                     <p><span class="emoji">💡</span><strong>最佳实践总结：</strong></p>
                     <ul>
                         <li>使用有意义的变量名和常量</li>
                         <li>编写完整的文档字符串和类型提示</li>
                         <li>始终验证和清理用户输入</li>
                         <li>使用参数化查询防止注入攻击</li>
                         <li>选择高效的字符串操作方法</li>
                         <li>预编译正则表达式提高性能</li>
                         <li>使用生成器处理大数据</li>
                         <li>适当使用缓存优化重复操作</li>
                     </ul>
                 </div>
             </div>
         </div>

        <!-- 第23页：常见错误和陷阱 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">⚠️</span>常见错误和陷阱</h1>
            </div>
            <div class="content">
                <h2>避免字符串处理中的常见问题</h2>
                
                <h3><span class="emoji">🚫</span>编码错误</h3>
                <div class="code-block">
<span class="comment"># 错误：未指定编码</span>
<span class="comment"># with open('file.txt', 'r') as f:  # 可能导致编码错误</span>
<span class="comment">#     content = f.read()</span>

<span class="comment"># 正确：明确指定编码</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'file.txt'</span>, <span class="string">'r'</span>, <span class="keyword">encoding</span>=<span class="string">'utf-8'</span>) <span class="keyword">as</span> <span class="keyword">f</span>:
    <span class="keyword">content</span> = <span class="keyword">f</span>.<span class="function">read</span>()

<span class="comment"># 错误：混合字节和字符串</span>
<span class="comment"># text = "Hello"</span>
<span class="comment"># bytes_data = b"World"</span>
<span class="comment"># result = text + bytes_data  # TypeError!</span>

<span class="comment"># 正确：统一数据类型</span>
<span class="keyword">text</span> = <span class="string">"Hello"</span>
<span class="keyword">bytes_data</span> = <span class="string">b"World"</span>
<span class="keyword">result</span> = <span class="keyword">text</span> + <span class="keyword">bytes_data</span>.<span class="function">decode</span>(<span class="string">'utf-8'</span>)
<span class="function">print</span>(<span class="keyword">result</span>)  <span class="comment"># HelloWorld</span>

<span class="comment"># 错误：忽略编码错误</span>
<span class="keyword">def</span> <span class="function">unsafe_decode</span>(<span class="keyword">data</span>):
    <span class="keyword">try</span>:
        <span class="keyword">return</span> <span class="keyword">data</span>.<span class="function">decode</span>(<span class="string">'utf-8'</span>)
    <span class="keyword">except</span> <span class="function">UnicodeDecodeError</span>:
        <span class="keyword">return</span> <span class="string">""</span>  <span class="comment"># 丢失数据！</span>

<span class="comment"># 正确：妥善处理编码错误</span>
<span class="keyword">def</span> <span class="function">safe_decode</span>(<span class="keyword">data</span>, <span class="keyword">encoding</span>=<span class="string">'utf-8'</span>):
    <span class="keyword">try</span>:
        <span class="keyword">return</span> <span class="keyword">data</span>.<span class="function">decode</span>(<span class="keyword">encoding</span>)
    <span class="keyword">except</span> <span class="function">UnicodeDecodeError</span>:
        <span class="comment"># 尝试其他编码</span>
        <span class="keyword">for</span> <span class="keyword">enc</span> <span class="keyword">in</span> [<span class="string">'gbk'</span>, <span class="string">'latin-1'</span>]:
            <span class="keyword">try</span>:
                <span class="keyword">return</span> <span class="keyword">data</span>.<span class="function">decode</span>(<span class="keyword">enc</span>)
            <span class="keyword">except</span> <span class="function">UnicodeDecodeError</span>:
                <span class="keyword">continue</span>
        <span class="comment"># 最后使用错误处理</span>
        <span class="keyword">return</span> <span class="keyword">data</span>.<span class="function">decode</span>(<span class="keyword">encoding</span>, <span class="keyword">errors</span>=<span class="string">'replace'</span>)
                </div>

                <h3><span class="emoji">🔍</span>字符串比较陷阱</h3>
                <div class="code-block">
<span class="comment"># 错误：忽略大小写差异</span>
<span class="keyword">user_input</span> = <span class="string">"YES"</span>
<span class="keyword">if</span> <span class="keyword">user_input</span> == <span class="string">"yes"</span>:  <span class="comment"># False!</span>
    <span class="function">print</span>(<span class="string">"用户同意"</span>)

<span class="comment"># 正确：统一大小写比较</span>
<span class="keyword">if</span> <span class="keyword">user_input</span>.<span class="function">lower</span>() == <span class="string">"yes"</span>:
    <span class="function">print</span>(<span class="string">"用户同意"</span>)

<span class="comment"># 错误：忽略空白字符</span>
<span class="keyword">password</span> = <span class="string">" secret "</span>
<span class="keyword">if</span> <span class="keyword">password</span> == <span class="string">"secret"</span>:  <span class="comment"># False!</span>
    <span class="function">print</span>(<span class="string">"密码正确"</span>)

<span class="comment"># 正确：去除空白字符</span>
<span class="keyword">if</span> <span class="keyword">password</span>.<span class="function">strip</span>() == <span class="string">"secret"</span>:
    <span class="function">print</span>(<span class="string">"密码正确"</span>)

<span class="comment"># 错误：使用is比较字符串内容</span>
<span class="keyword">a</span> = <span class="string">"hello"</span>
<span class="keyword">b</span> = <span class="string">"hel"</span> + <span class="string">"lo"</span>
<span class="keyword">if</span> <span class="keyword">a</span> <span class="keyword">is</span> <span class="keyword">b</span>:  <span class="comment"># 可能为False（取决于Python实现）</span>
    <span class="function">print</span>(<span class="string">"相同"</span>)

<span class="comment"># 正确：使用==比较字符串内容</span>
<span class="keyword">if</span> <span class="keyword">a</span> == <span class="keyword">b</span>:  <span class="comment"># True</span>
    <span class="function">print</span>(<span class="string">"相同"</span>)

<span class="comment"># 错误：浮点数直接转字符串比较</span>
<span class="keyword">price1</span> = <span class="number">19.99</span>
<span class="keyword">price2</span> = <span class="number">19.990000000001</span>
<span class="keyword">if</span> <span class="function">str</span>(<span class="keyword">price1</span>) == <span class="function">str</span>(<span class="keyword">price2</span>):  <span class="comment"># False</span>
    <span class="function">print</span>(<span class="string">"价格相同"</span>)

<span class="comment"># 正确：格式化后比较</span>
<span class="keyword">if</span> <span class="string">f"{price1:.2f}"</span> == <span class="string">f"{price2:.2f}"</span>:  <span class="comment"># True</span>
    <span class="function">print</span>(<span class="string">"价格相同"</span>)
                </div>

                <h3><span class="emoji">🔄</span>可变性误解</h3>
                <div class="code-block">
<span class="comment"># 错误：认为字符串是可变的</span>
<span class="keyword">text</span> = <span class="string">"hello"</span>
<span class="keyword">original_id</span> = <span class="function">id</span>(<span class="keyword">text</span>)
<span class="keyword">text</span> += <span class="string">" world"</span>  <span class="comment"># 创建了新字符串对象</span>
<span class="keyword">new_id</span> = <span class="function">id</span>(<span class="keyword">text</span>)
<span class="function">print</span>(<span class="string">f"ID变化: {original_id != new_id}"</span>)  <span class="comment"># True</span>

<span class="comment"># 正确：理解字符串不可变性</span>
<span class="keyword">parts</span> = [<span class="string">"hello"</span>, <span class="string">" "</span>, <span class="string">"world"</span>]
<span class="keyword">text</span> = <span class="string">""</span>.<span class="function">join</span>(<span class="keyword">parts</span>)  <span class="comment"># 更高效的字符串构建</span>

<span class="comment"># 错误：在循环中使用+=连接字符串</span>
<span class="keyword">result</span> = <span class="string">""</span>
<span class="keyword">for</span> <span class="keyword">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1000</span>):
    <span class="keyword">result</span> += <span class="function">str</span>(<span class="keyword">i</span>) + <span class="string">","</span>  <span class="comment"># 每次都创建新对象，效率低</span>

<span class="comment"># 正确：使用列表收集后join</span>
<span class="keyword">parts</span> = []
<span class="keyword">for</span> <span class="keyword">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1000</span>):
    <span class="keyword">parts</span>.<span class="function">append</span>(<span class="function">str</span>(<span class="keyword">i</span>))
<span class="keyword">result</span> = <span class="string">","</span>.<span class="function">join</span>(<span class="keyword">parts</span>)
                </div>

                <h3><span class="emoji">🎯</span>正则表达式陷阱</h3>
                <div class="code-block">
<span class="comment"># 错误：忘记转义特殊字符</span>
<span class="keyword">import</span> <span class="keyword">re</span>
<span class="keyword">text</span> = <span class="string">"价格: $19.99"</span>
<span class="keyword">pattern</span> = <span class="string">r"$19.99"</span>  <span class="comment"># $和.是特殊字符</span>
<span class="keyword">match</span> = <span class="keyword">re</span>.<span class="function">search</span>(<span class="keyword">pattern</span>, <span class="keyword">text</span>)  <span class="comment"># 可能不匹配</span>

<span class="comment"># 正确：转义特殊字符</span>
<span class="keyword">pattern</span> = <span class="string">r"\$19\.99"</span>
<span class="keyword">match</span> = <span class="keyword">re</span>.<span class="function">search</span>(<span class="keyword">pattern</span>, <span class="keyword">text</span>)

<span class="comment"># 错误：贪婪匹配导致意外结果</span>
<span class="keyword">html</span> = <span class="string">"<div>内容1</div><div>内容2</div>"</span>
<span class="keyword">pattern</span> = <span class="string">r"<div>.*</div>"</span>  <span class="comment"># 贪婪匹配</span>
<span class="keyword">match</span> = <span class="keyword">re</span>.<span class="function">search</span>(<span class="keyword">pattern</span>, <span class="keyword">html</span>)
<span class="function">print</span>(<span class="keyword">match</span>.<span class="function">group</span>())  <span class="comment"># <div>内容1</div><div>内容2</div></span>

<span class="comment"># 正确：使用非贪婪匹配</span>
<span class="keyword">pattern</span> = <span class="string">r"<div>.*?</div>"</span>  <span class="comment"># 非贪婪匹配</span>
<span class="keyword">matches</span> = <span class="keyword">re</span>.<span class="function">findall</span>(<span class="keyword">pattern</span>, <span class="keyword">html</span>)
<span class="function">print</span>(<span class="keyword">matches</span>)  <span class="comment"># ['<div>内容1</div>', '<div>内容2</div>']</span>

<span class="comment"># 错误：重复编译正则表达式</span>
<span class="keyword">def</span> <span class="function">validate_emails_slow</span>(<span class="keyword">emails</span>):
    <span class="keyword">valid_emails</span> = []
    <span class="keyword">for</span> <span class="keyword">email</span> <span class="keyword">in</span> <span class="keyword">emails</span>:
        <span class="keyword">if</span> <span class="keyword">re</span>.<span class="function">match</span>(<span class="string">r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"</span>, <span class="keyword">email</span>):
            <span class="keyword">valid_emails</span>.<span class="function">append</span>(<span class="keyword">email</span>)
    <span class="keyword">return</span> <span class="keyword">valid_emails</span>

<span class="comment"># 正确：预编译正则表达式</span>
<span class="keyword">EMAIL_PATTERN</span> = <span class="keyword">re</span>.<span class="function">compile</span>(<span class="string">r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"</span>)

<span class="keyword">def</span> <span class="function">validate_emails_fast</span>(<span class="keyword">emails</span>):
    <span class="keyword">return</span> [<span class="keyword">email</span> <span class="keyword">for</span> <span class="keyword">email</span> <span class="keyword">in</span> <span class="keyword">emails</span> <span class="keyword">if</span> <span class="keyword">EMAIL_PATTERN</span>.<span class="function">match</span>(<span class="keyword">email</span>)]
                </div>

                <div class="warning-box">
                    <p><span class="emoji">⚠️</span><strong>常见陷阱提醒：</strong></p>
                    <ul>
                        <li>始终明确指定文件编码</li>
                        <li>字符串比较前统一格式（大小写、空白）</li>
                        <li>使用==而不是is比较字符串内容</li>
                        <li>理解字符串不可变性，避免低效操作</li>
                        <li>正则表达式要转义特殊字符</li>
                        <li>注意贪婪与非贪婪匹配的区别</li>
                        <li>预编译频繁使用的正则表达式</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第24页：调试技巧 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">🔧</span>调试技巧</h1>
            </div>
            <div class="content">
                <h2>高效调试字符串相关问题</h2>
                
                <h3><span class="emoji">🔍</span>字符串内容检查</h3>
                <div class="code-block">
<span class="comment"># 检查字符串的详细信息</span>
<span class="keyword">def</span> <span class="function">debug_string</span>(<span class="keyword">s</span>):
    <span class="string">"""调试字符串的详细信息"""</span>
    <span class="function">print</span>(<span class="string">f"字符串内容: {repr(s)}"</span>)  <span class="comment"># 显示转义字符</span>
    <span class="function">print</span>(<span class="string">f"字符串长度: {len(s)}"</span>)
    <span class="function">print</span>(<span class="string">f"字符串类型: {type(s)}"</span>)
    <span class="function">print</span>(<span class="string">f"是否为空: {not s}"</span>)
    <span class="function">print</span>(<span class="string">f"是否只有空白: {s.isspace() if s else False}"</span>)
    <span class="function">print</span>(<span class="string">f"编码后字节: {s.encode('utf-8')}"</span>)
    <span class="function">print</span>(<span class="string">f"字符列表: {list(s)}"</span>)
    <span class="function">print</span>(<span class="string">f"ASCII码列表: {[ord(c) for c in s]}"</span>)
    <span class="function">print</span>(<span class="string">"="</span> * <span class="number">50</span>)

<span class="comment"># 使用示例</span>
<span class="keyword">test_strings</span> = [
    <span class="string">"hello"</span>,
    <span class="string">"  hello  "</span>,
    <span class="string">"hello\nworld"</span>,
    <span class="string">""</span>,
    <span class="string">"   "</span>,
    <span class="string">"你好"</span>
]

<span class="keyword">for</span> <span class="keyword">s</span> <span class="keyword">in</span> <span class="keyword">test_strings</span>:
    <span class="function">debug_string</span>(<span class="keyword">s</span>)

<span class="comment"># 检查不可见字符</span>
<span class="keyword">def</span> <span class="function">show_invisible_chars</span>(<span class="keyword">s</span>):
    <span class="string">"""显示字符串中的不可见字符"""</span>
    <span class="keyword">replacements</span> = {
        <span class="string">'\n'</span>: <span class="string">'\\n'</span>,
        <span class="string">'\r'</span>: <span class="string">'\\r'</span>,
        <span class="string">'\t'</span>: <span class="string">'\\t'</span>,
        <span class="string">' '</span>: <span class="string">'·'</span>,  <span class="comment"># 用·表示空格</span>
        <span class="string">'\u00a0'</span>: <span class="string">'[NBSP]'</span>,  <span class="comment"># 不间断空格</span>
        <span class="string">'\u200b'</span>: <span class="string">'[ZWSP]'</span>,  <span class="comment"># 零宽空格</span>
    }
    
    <span class="keyword">result</span> = <span class="keyword">s</span>
    <span class="keyword">for</span> <span class="keyword">char</span>, <span class="keyword">replacement</span> <span class="keyword">in</span> <span class="keyword">replacements</span>.<span class="function">items</span>():
        <span class="keyword">result</span> = <span class="keyword">result</span>.<span class="function">replace</span>(<span class="keyword">char</span>, <span class="keyword">replacement</span>)
    
    <span class="keyword">return</span> <span class="keyword">result</span>

<span class="comment"># 示例</span>
<span class="keyword">problematic_string</span> = <span class="string">"hello\tworld\n  test  "</span>
<span class="function">print</span>(<span class="string">f"原始: '{problematic_string}'"</span>)
<span class="function">print</span>(<span class="string">f"可视: '{show_invisible_chars(problematic_string)}'"</span>)
                </div>

                <h3><span class="emoji">🎯</span>正则表达式调试</h3>
                <div class="code-block">
<span class="comment"># 正则表达式调试工具</span>
<span class="keyword">import</span> <span class="keyword">re</span>

<span class="keyword">def</span> <span class="function">debug_regex</span>(<span class="keyword">pattern</span>, <span class="keyword">text</span>, <span class="keyword">flags</span>=<span class="number">0</span>):
    <span class="string">"""调试正则表达式匹配"""</span>
    <span class="function">print</span>(<span class="string">f"模式: {pattern}"</span>)
    <span class="function">print</span>(<span class="string">f"文本: {repr(text)}"</span>)
    <span class="function">print</span>(<span class="string">f"标志: {flags}"</span>)
    
    <span class="keyword">try</span>:
        <span class="keyword">compiled_pattern</span> = <span class="keyword">re</span>.<span class="function">compile</span>(<span class="keyword">pattern</span>, <span class="keyword">flags</span>)
        <span class="function">print</span>(<span class="string">"✓ 模式编译成功"</span>)
        
        <span class="comment"># 测试不同的匹配方法</span>
        <span class="keyword">match_result</span> = <span class="keyword">compiled_pattern</span>.<span class="function">match</span>(<span class="keyword">text</span>)
        <span class="keyword">search_result</span> = <span class="keyword">compiled_pattern</span>.<span class="function">search</span>(<span class="keyword">text</span>)
        <span class="keyword">findall_result</span> = <span class="keyword">compiled_pattern</span>.<span class="function">findall</span>(<span class="keyword">text</span>)
        <span class="keyword">finditer_result</span> = <span class="function">list</span>(<span class="keyword">compiled_pattern</span>.<span class="function">finditer</span>(<span class="keyword">text</span>))
        
        <span class="function">print</span>(<span class="string">f"match(): {match_result.group() if match_result else None}"</span>)
        <span class="function">print</span>(<span class="string">f"search(): {search_result.group() if search_result else None}"</span>)
        <span class="function">print</span>(<span class="string">f"findall(): {findall_result}"</span>)
        
        <span class="keyword">if</span> <span class="keyword">finditer_result</span>:
            <span class="function">print</span>(<span class="string">"finditer() 详细结果:"</span>)
            <span class="keyword">for</span> <span class="keyword">i</span>, <span class="keyword">match</span> <span class="keyword">in</span> <span class="function">enumerate</span>(<span class="keyword">finditer_result</span>):
                <span class="function">print</span>(<span class="string">f"  匹配{i+1}: '{match.group()}' 位置({match.start()}, {match.end()})"</span>)
                <span class="keyword">if</span> <span class="keyword">match</span>.<span class="function">groups</span>():
                    <span class="function">print</span>(<span class="string">f"    分组: {match.groups()}"</span>)
        
    <span class="keyword">except</span> <span class="keyword">re</span>.<span class="keyword">error</span> <span class="keyword">as</span> <span class="keyword">e</span>:
        <span class="function">print</span>(<span class="string">f"✗ 正则表达式错误: {e}"</span>)
    
    <span class="function">print</span>(<span class="string">"="</span> * <span class="number">50</span>)

<span class="comment"># 使用示例</span>
<span class="function">debug_regex</span>(<span class="string">r"\d+"</span>, <span class="string">"价格123元，折扣456"</span>)
<span class="function">debug_regex</span>(<span class="string">r"(\w+)@(\w+\.\w+)"</span>, <span class="string">"联系邮箱：<EMAIL>"</span>)

<span class="comment"># 可视化正则表达式匹配</span>
<span class="keyword">def</span> <span class="function">visualize_matches</span>(<span class="keyword">pattern</span>, <span class="keyword">text</span>):
    <span class="string">"""可视化正则表达式匹配结果"""</span>
    <span class="keyword">matches</span> = <span class="function">list</span>(<span class="keyword">re</span>.<span class="function">finditer</span>(<span class="keyword">pattern</span>, <span class="keyword">text</span>))
    
    <span class="keyword">if</span> <span class="keyword">not</span> <span class="keyword">matches</span>:
        <span class="function">print</span>(<span class="string">"无匹配"</span>)
        <span class="keyword">return</span>
    
    <span class="comment"># 创建标记字符串</span>
    <span class="keyword">marked_text</span> = <span class="function">list</span>(<span class="keyword">text</span>)
    <span class="keyword">offset</span> = <span class="number">0</span>
    
    <span class="keyword">for</span> <span class="keyword">match</span> <span class="keyword">in</span> <span class="keyword">matches</span>:
        <span class="keyword">start</span>, <span class="keyword">end</span> = <span class="keyword">match</span>.<span class="function">span</span>()
        <span class="comment"># 在匹配的文本前后添加标记</span>
        <span class="keyword">marked_text</span>.<span class="function">insert</span>(<span class="keyword">start</span> + <span class="keyword">offset</span>, <span class="string">'['</span>)
        <span class="keyword">marked_text</span>.<span class="function">insert</span>(<span class="keyword">end</span> + <span class="keyword">offset</span> + <span class="number">1</span>, <span class="string">']'</span>)
        <span class="keyword">offset</span> += <span class="number">2</span>
    
    <span class="function">print</span>(<span class="string">f"匹配可视化: {''.join(marked_text)}"</span>)

<span class="comment"># 示例</span>
<span class="function">visualize_matches</span>(<span class="string">r"\d+"</span>, <span class="string">"订单号123，金额456.78元"</span>)
                </div>

                <h3><span class="emoji">📊</span>性能调试</h3>
                <div class="code-block">
<span class="comment"># 字符串操作性能测试</span>
<span class="keyword">import</span> <span class="keyword">timeit</span>
<span class="keyword">import</span> <span class="keyword">sys</span>

<span class="keyword">def</span> <span class="function">compare_string_operations</span>():
    <span class="string">"""比较不同字符串操作的性能"""</span>
    <span class="keyword">data</span> = [<span class="function">str</span>(<span class="keyword">i</span>) <span class="keyword">for</span> <span class="keyword">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1000</span>)]
    
    <span class="comment"># 方法1：使用+连接</span>
    <span class="keyword">def</span> <span class="function">method1</span>():
        <span class="keyword">result</span> = <span class="string">""</span>
        <span class="keyword">for</span> <span class="keyword">item</span> <span class="keyword">in</span> <span class="keyword">data</span>:
            <span class="keyword">result</span> += <span class="keyword">item</span> + <span class="string">","</span>
        <span class="keyword">return</span> <span class="keyword">result</span>
    
    <span class="comment"># 方法2：使用join</span>
    <span class="keyword">def</span> <span class="function">method2</span>():
        <span class="keyword">return</span> <span class="string">","</span>.<span class="function">join</span>(<span class="keyword">data</span>) + <span class="string">","</span>
    
    <span class="comment"># 方法3：使用列表收集</span>
    <span class="keyword">def</span> <span class="function">method3</span>():
        <span class="keyword">parts</span> = []
        <span class="keyword">for</span> <span class="keyword">item</span> <span class="keyword">in</span> <span class="keyword">data</span>:
            <span class="keyword">parts</span>.<span class="function">append</span>(<span class="keyword">item</span>)
            <span class="keyword">parts</span>.<span class="function">append</span>(<span class="string">","</span>)
        <span class="keyword">return</span> <span class="string">""</span>.<span class="function">join</span>(<span class="keyword">parts</span>)
    
    <span class="comment"># 性能测试</span>
    <span class="keyword">methods</span> = [(<span class="string">"+ 连接"</span>, <span class="keyword">method1</span>), (<span class="string">"join"</span>, <span class="keyword">method2</span>), (<span class="string">"列表+join"</span>, <span class="keyword">method3</span>)]
    
    <span class="keyword">for</span> <span class="keyword">name</span>, <span class="keyword">method</span> <span class="keyword">in</span> <span class="keyword">methods</span>:
        <span class="keyword">time_taken</span> = <span class="keyword">timeit</span>.<span class="function">timeit</span>(<span class="keyword">method</span>, <span class="keyword">number</span>=<span class="number">100</span>)
        <span class="function">print</span>(<span class="string">f"{name}: {time_taken:.4f}秒"</span>)

<span class="comment"># 内存使用监控</span>
<span class="keyword">def</span> <span class="function">monitor_memory_usage</span>(<span class="keyword">func</span>, <span class="keyword">*args</span>, <span class="keyword">**kwargs</span>):
    <span class="string">"""监控函数的内存使用"""</span>
    <span class="keyword">import</span> <span class="keyword">tracemalloc</span>
    
    <span class="keyword">tracemalloc</span>.<span class="function">start</span>()
    <span class="keyword">result</span> = <span class="keyword">func</span>(<span class="keyword">*args</span>, <span class="keyword">**kwargs</span>)
    <span class="keyword">current</span>, <span class="keyword">peak</span> = <span class="keyword">tracemalloc</span>.<span class="function">get_traced_memory</span>()
    <span class="keyword">tracemalloc</span>.<span class="function">stop</span>()
    
    <span class="function">print</span>(<span class="string">f"当前内存: {current / 1024 / 1024:.2f} MB"</span>)
    <span class="function">print</span>(<span class="string">f"峰值内存: {peak / 1024 / 1024:.2f} MB"</span>)
    
    <span class="keyword">return</span> <span class="keyword">result</span>

<span class="comment"># 使用示例</span>
<span class="function">compare_string_operations</span>()
                </div>

                <div class="ai-prompt">
                     <h3><span class="emoji">🤖</span>AI 提示词模板</h3>
                     <p>"我的Python字符串代码出现了[具体问题]，请帮我分析可能的原因并提供调试方法和解决方案。"</p>
                 </div>
             </div>
         </div>

        <!-- 第25页：测试方法 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">🧪</span>测试方法</h1>
            </div>
            <div class="content">
                <h2>字符串功能的全面测试策略</h2>
                
                <h3><span class="emoji">✅</span>单元测试基础</h3>
                <div class="code-block">
<span class="comment"># 字符串处理函数的单元测试</span>
<span class="keyword">import</span> <span class="keyword">unittest</span>
<span class="keyword">import</span> <span class="keyword">re</span>

<span class="comment"># 被测试的字符串处理函数</span>
<span class="keyword">def</span> <span class="function">clean_phone_number</span>(<span class="keyword">phone</span>):
    <span class="string">"""清理电话号码格式"""</span>
    <span class="keyword">if</span> <span class="keyword">not</span> <span class="keyword">phone</span> <span class="keyword">or</span> <span class="keyword">not</span> <span class="function">isinstance</span>(<span class="keyword">phone</span>, <span class="function">str</span>):
        <span class="keyword">return</span> <span class="string">""</span>
    
    <span class="comment"># 移除所有非数字字符</span>
    <span class="keyword">cleaned</span> = <span class="keyword">re</span>.<span class="function">sub</span>(<span class="string">r'[^\d]'</span>, <span class="string">''</span>, <span class="keyword">phone</span>)
    
    <span class="comment"># 验证长度</span>
    <span class="keyword">if</span> <span class="function">len</span>(<span class="keyword">cleaned</span>) == <span class="number">11</span> <span class="keyword">and</span> <span class="keyword">cleaned</span>.<span class="function">startswith</span>(<span class="string">'1'</span>):
        <span class="keyword">return</span> <span class="keyword">cleaned</span>
    <span class="keyword">elif</span> <span class="function">len</span>(<span class="keyword">cleaned</span>) == <span class="number">10</span>:
        <span class="keyword">return</span> <span class="string">'1'</span> + <span class="keyword">cleaned</span>
    <span class="keyword">else</span>:
        <span class="keyword">return</span> <span class="string">""</span>

<span class="keyword">def</span> <span class="function">validate_email</span>(<span class="keyword">email</span>):
    <span class="string">"""验证邮箱格式"""</span>
    <span class="keyword">if</span> <span class="keyword">not</span> <span class="keyword">email</span> <span class="keyword">or</span> <span class="keyword">not</span> <span class="function">isinstance</span>(<span class="keyword">email</span>, <span class="function">str</span>):
        <span class="keyword">return</span> <span class="keyword">False</span>
    
    <span class="keyword">pattern</span> = <span class="string">r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'</span>
    <span class="keyword">return</span> <span class="function">bool</span>(<span class="keyword">re</span>.<span class="function">match</span>(<span class="keyword">pattern</span>, <span class="keyword">email</span>.<span class="function">strip</span>()))

<span class="comment"># 测试类</span>
<span class="keyword">class</span> <span class="function">TestStringFunctions</span>(<span class="keyword">unittest</span>.<span class="keyword">TestCase</span>):
    
    <span class="keyword">def</span> <span class="function">test_clean_phone_number_valid</span>(<span class="keyword">self</span>):
        <span class="string">"""测试有效电话号码"""</span>
        <span class="keyword">test_cases</span> = [
            (<span class="string">'13812345678'</span>, <span class="string">'13812345678'</span>),
            (<span class="string">'138-1234-5678'</span>, <span class="string">'13812345678'</span>),
            (<span class="string">'(138) 1234-5678'</span>, <span class="string">'13812345678'</span>),
            (<span class="string">'+86 138 1234 5678'</span>, <span class="string">'8613812345678'</span>),
            (<span class="string">'3812345678'</span>, <span class="string">'13812345678'</span>),  <span class="comment"># 10位数字</span>
        ]
        
        <span class="keyword">for</span> <span class="keyword">input_phone</span>, <span class="keyword">expected</span> <span class="keyword">in</span> <span class="keyword">test_cases</span>:
            <span class="keyword">with</span> <span class="keyword">self</span>.<span class="function">subTest</span>(<span class="keyword">input_phone</span>=<span class="keyword">input_phone</span>):
                <span class="keyword">result</span> = <span class="function">clean_phone_number</span>(<span class="keyword">input_phone</span>)
                <span class="keyword">self</span>.<span class="function">assertEqual</span>(<span class="keyword">result</span>, <span class="keyword">expected</span>)
    
    <span class="keyword">def</span> <span class="function">test_clean_phone_number_invalid</span>(<span class="keyword">self</span>):
        <span class="string">"""测试无效电话号码"""</span>
        <span class="keyword">invalid_cases</span> = [
            <span class="string">''</span>,
            <span class="keyword">None</span>,
            <span class="number">123456789</span>,  <span class="comment"># 非字符串</span>
            <span class="string">'12345'</span>,  <span class="comment"># 太短</span>
            <span class="string">'123456789012345'</span>,  <span class="comment"># 太长</span>
            <span class="string">'abcdefghijk'</span>,  <span class="comment"># 非数字</span>
        ]
        
        <span class="keyword">for</span> <span class="keyword">invalid_phone</span> <span class="keyword">in</span> <span class="keyword">invalid_cases</span>:
            <span class="keyword">with</span> <span class="keyword">self</span>.<span class="function">subTest</span>(<span class="keyword">invalid_phone</span>=<span class="keyword">invalid_phone</span>):
                <span class="keyword">result</span> = <span class="function">clean_phone_number</span>(<span class="keyword">invalid_phone</span>)
                <span class="keyword">self</span>.<span class="function">assertEqual</span>(<span class="keyword">result</span>, <span class="string">''</span>)
    
    <span class="keyword">def</span> <span class="function">test_validate_email_valid</span>(<span class="keyword">self</span>):
        <span class="string">"""测试有效邮箱"""</span>
        <span class="keyword">valid_emails</span> = [
            <span class="string">'<EMAIL>'</span>,
            <span class="string">'<EMAIL>'</span>,
            <span class="string">'<EMAIL>'</span>,
            <span class="string">'  <EMAIL>  '</span>,  <span class="comment"># 带空格</span>
        ]
        
        <span class="keyword">for</span> <span class="keyword">email</span> <span class="keyword">in</span> <span class="keyword">valid_emails</span>:
            <span class="keyword">with</span> <span class="keyword">self</span>.<span class="function">subTest</span>(<span class="keyword">email</span>=<span class="keyword">email</span>):
                <span class="keyword">self</span>.<span class="function">assertTrue</span>(<span class="function">validate_email</span>(<span class="keyword">email</span>))
    
    <span class="keyword">def</span> <span class="function">test_validate_email_invalid</span>(<span class="keyword">self</span>):
        <span class="string">"""测试无效邮箱"""</span>
        <span class="keyword">invalid_emails</span> = [
            <span class="string">''</span>,
            <span class="keyword">None</span>,
            <span class="string">'invalid-email'</span>,
            <span class="string">'@example.com'</span>,
            <span class="string">'user@'</span>,
            <span class="string">'user@.com'</span>,
            <span class="string">'user@example'</span>,
        ]
        
        <span class="keyword">for</span> <span class="keyword">email</span> <span class="keyword">in</span> <span class="keyword">invalid_emails</span>:
            <span class="keyword">with</span> <span class="keyword">self</span>.<span class="function">subTest</span>(<span class="keyword">email</span>=<span class="keyword">email</span>):
                <span class="keyword">self</span>.<span class="function">assertFalse</span>(<span class="function">validate_email</span>(<span class="keyword">email</span>))

<span class="comment"># 运行测试</span>
<span class="keyword">if</span> <span class="keyword">__name__</span> == <span class="string">'__main__'</span>:
    <span class="keyword">unittest</span>.<span class="function">main</span>(<span class="keyword">verbosity</span>=<span class="number">2</span>)
                </div>

                <h3><span class="emoji">🎯</span>属性测试</h3>
                <div class="code-block">
<span class="comment"># 使用hypothesis进行属性测试</span>
<span class="keyword">from</span> <span class="keyword">hypothesis</span> <span class="keyword">import</span> <span class="keyword">given</span>, <span class="keyword">strategies</span> <span class="keyword">as</span> <span class="keyword">st</span>
<span class="keyword">import</span> <span class="keyword">unittest</span>

<span class="keyword">class</span> <span class="function">TestStringProperties</span>(<span class="keyword">unittest</span>.<span class="keyword">TestCase</span>):
    
    <span class="keyword">@given</span>(<span class="keyword">st</span>.<span class="function">text</span>())
    <span class="keyword">def</span> <span class="function">test_strip_idempotent</span>(<span class="keyword">self</span>, <span class="keyword">s</span>):
        <span class="string">"""测试strip操作的幂等性"""</span>
        <span class="keyword">stripped_once</span> = <span class="keyword">s</span>.<span class="function">strip</span>()
        <span class="keyword">stripped_twice</span> = <span class="keyword">stripped_once</span>.<span class="function">strip</span>()
        <span class="keyword">self</span>.<span class="function">assertEqual</span>(<span class="keyword">stripped_once</span>, <span class="keyword">stripped_twice</span>)
    
    <span class="keyword">@given</span>(<span class="keyword">st</span>.<span class="function">text</span>(), <span class="keyword">st</span>.<span class="function">text</span>())
    <span class="keyword">def</span> <span class="function">test_string_concatenation_length</span>(<span class="keyword">self</span>, <span class="keyword">s1</span>, <span class="keyword">s2</span>):
        <span class="string">"""测试字符串连接的长度属性"""</span>
        <span class="keyword">result</span> = <span class="keyword">s1</span> + <span class="keyword">s2</span>
        <span class="keyword">self</span>.<span class="function">assertEqual</span>(<span class="function">len</span>(<span class="keyword">result</span>), <span class="function">len</span>(<span class="keyword">s1</span>) + <span class="function">len</span>(<span class="keyword">s2</span>))
    
    <span class="keyword">@given</span>(<span class="keyword">st</span>.<span class="function">text</span>())
    <span class="keyword">def</span> <span class="function">test_upper_lower_inverse</span>(<span class="keyword">self</span>, <span class="keyword">s</span>):
        <span class="string">"""测试大小写转换的逆操作"""</span>
        <span class="comment"># 对于只包含字母的字符串，upper和lower应该是逆操作</span>
        <span class="keyword">if</span> <span class="keyword">s</span>.<span class="function">isalpha</span>():
            <span class="keyword">self</span>.<span class="function">assertEqual</span>(<span class="keyword">s</span>.<span class="function">upper</span>().<span class="function">lower</span>(), <span class="keyword">s</span>.<span class="function">lower</span>())
            <span class="keyword">self</span>.<span class="function">assertEqual</span>(<span class="keyword">s</span>.<span class="function">lower</span>().<span class="function">upper</span>(), <span class="keyword">s</span>.<span class="function">upper</span>())
    
    <span class="keyword">@given</span>(<span class="keyword">st</span>.<span class="function">lists</span>(<span class="keyword">st</span>.<span class="function">text</span>()))
    <span class="keyword">def</span> <span class="function">test_join_split_inverse</span>(<span class="keyword">self</span>, <span class="keyword">strings</span>):
        <span class="string">"""测试join和split的逆操作"""</span>
        <span class="keyword">separator</span> = <span class="string">'|'</span>  <span class="comment"># 使用不太可能出现在随机字符串中的分隔符</span>
        <span class="keyword">if</span> <span class="function">all</span>(<span class="keyword">separator</span> <span class="keyword">not</span> <span class="keyword">in</span> <span class="keyword">s</span> <span class="keyword">for</span> <span class="keyword">s</span> <span class="keyword">in</span> <span class="keyword">strings</span>):
            <span class="keyword">joined</span> = <span class="keyword">separator</span>.<span class="function">join</span>(<span class="keyword">strings</span>)
            <span class="keyword">split_result</span> = <span class="keyword">joined</span>.<span class="function">split</span>(<span class="keyword">separator</span>)
            <span class="keyword">self</span>.<span class="function">assertEqual</span>(<span class="keyword">split_result</span>, <span class="keyword">strings</span>)
                </div>

                <h3><span class="emoji">🔄</span>集成测试</h3>
                <div class="code-block">
<span class="comment"># 字符串处理流水线的集成测试</span>
<span class="keyword">import</span> <span class="keyword">unittest</span>
<span class="keyword">from</span> <span class="keyword">unittest.mock</span> <span class="keyword">import</span> <span class="keyword">patch</span>, <span class="keyword">mock_open</span>

<span class="keyword">class</span> <span class="function">DataProcessor</span>:
    <span class="string">"""数据处理类"""</span>
    
    <span class="keyword">def</span> <span class="function">process_user_data</span>(<span class="keyword">self</span>, <span class="keyword">raw_data</span>):
        <span class="string">"""处理用户数据"""</span>
        <span class="keyword">lines</span> = <span class="keyword">raw_data</span>.<span class="function">strip</span>().<span class="function">split</span>(<span class="string">'\n'</span>)
        <span class="keyword">processed_users</span> = []
        
        <span class="keyword">for</span> <span class="keyword">line</span> <span class="keyword">in</span> <span class="keyword">lines</span>:
            <span class="keyword">if</span> <span class="keyword">not</span> <span class="keyword">line</span>.<span class="function">strip</span>():
                <span class="keyword">continue</span>
            
            <span class="keyword">parts</span> = <span class="keyword">line</span>.<span class="function">split</span>(<span class="string">','</span>)
            <span class="keyword">if</span> <span class="function">len</span>(<span class="keyword">parts</span>) >= <span class="number">3</span>:
                <span class="keyword">name</span> = <span class="keyword">parts</span>[<span class="number">0</span>].<span class="function">strip</span>().<span class="function">title</span>()
                <span class="keyword">email</span> = <span class="keyword">parts</span>[<span class="number">1</span>].<span class="function">strip</span>().<span class="function">lower</span>()
                <span class="keyword">phone</span> = <span class="function">clean_phone_number</span>(<span class="keyword">parts</span>[<span class="number">2</span>].<span class="function">strip</span>())
                
                <span class="keyword">if</span> <span class="keyword">name</span> <span class="keyword">and</span> <span class="function">validate_email</span>(<span class="keyword">email</span>) <span class="keyword">and</span> <span class="keyword">phone</span>:
                    <span class="keyword">processed_users</span>.<span class="function">append</span>({
                        <span class="string">'name'</span>: <span class="keyword">name</span>,
                        <span class="string">'email'</span>: <span class="keyword">email</span>,
                        <span class="string">'phone'</span>: <span class="keyword">phone</span>
                    })
        
        <span class="keyword">return</span> <span class="keyword">processed_users</span>
    
    <span class="keyword">def</span> <span class="function">load_and_process_file</span>(<span class="keyword">self</span>, <span class="keyword">filename</span>):
        <span class="string">"""从文件加载并处理数据"""</span>
        <span class="keyword">with</span> <span class="function">open</span>(<span class="keyword">filename</span>, <span class="string">'r'</span>, <span class="keyword">encoding</span>=<span class="string">'utf-8'</span>) <span class="keyword">as</span> <span class="keyword">f</span>:
            <span class="keyword">raw_data</span> = <span class="keyword">f</span>.<span class="function">read</span>()
        <span class="keyword">return</span> <span class="keyword">self</span>.<span class="function">process_user_data</span>(<span class="keyword">raw_data</span>)

<span class="keyword">class</span> <span class="function">TestDataProcessorIntegration</span>(<span class="keyword">unittest</span>.<span class="keyword">TestCase</span>):
    
    <span class="keyword">def</span> <span class="function">setUp</span>(<span class="keyword">self</span>):
        <span class="keyword">self</span>.<span class="keyword">processor</span> = <span class="function">DataProcessor</span>()
    
    <span class="keyword">def</span> <span class="function">test_process_user_data_complete_flow</span>(<span class="keyword">self</span>):
        <span class="string">"""测试完整的数据处理流程"""</span>
        <span class="keyword">raw_data</span> = <span class="string">"""john doe,<EMAIL>,138-1234-5678
jane smith,<EMAIL>,(139) 8765-4321
invalid line
bob wilson,invalid-email,12345
alice brown,<EMAIL>,  159 0000 1111  
"""</span>
        
        <span class="keyword">result</span> = <span class="keyword">self</span>.<span class="keyword">processor</span>.<span class="function">process_user_data</span>(<span class="keyword">raw_data</span>)
        
        <span class="keyword">expected</span> = [
            {<span class="string">'name'</span>: <span class="string">'John Doe'</span>, <span class="string">'email'</span>: <span class="string">'<EMAIL>'</span>, <span class="string">'phone'</span>: <span class="string">'13812345678'</span>},
            {<span class="string">'name'</span>: <span class="string">'Jane Smith'</span>, <span class="string">'email'</span>: <span class="string">'<EMAIL>'</span>, <span class="string">'phone'</span>: <span class="string">'13987654321'</span>},
            {<span class="string">'name'</span>: <span class="string">'Alice Brown'</span>, <span class="string">'email'</span>: <span class="string">'<EMAIL>'</span>, <span class="string">'phone'</span>: <span class="string">'15900001111'</span>}
        ]
        
        <span class="keyword">self</span>.<span class="function">assertEqual</span>(<span class="keyword">result</span>, <span class="keyword">expected</span>)
    
    <span class="keyword">@patch</span>(<span class="string">'builtins.open'</span>, <span class="function">new_callable</span>=<span class="keyword">mock_open</span>)
    <span class="keyword">def</span> <span class="function">test_load_and_process_file</span>(<span class="keyword">self</span>, <span class="keyword">mock_file</span>):
        <span class="string">"""测试文件加载和处理"""</span>
        <span class="keyword">mock_file</span>.<span class="function">return_value</span>.<span class="function">read</span>.<span class="function">return_value</span> = <span class="string">"test user,<EMAIL>,13800000000"</span>
        
        <span class="keyword">result</span> = <span class="keyword">self</span>.<span class="keyword">processor</span>.<span class="function">load_and_process_file</span>(<span class="string">'test.csv'</span>)
        
        <span class="keyword">mock_file</span>.<span class="function">assert_called_once_with</span>(<span class="string">'test.csv'</span>, <span class="string">'r'</span>, <span class="keyword">encoding</span>=<span class="string">'utf-8'</span>)
        <span class="keyword">self</span>.<span class="function">assertEqual</span>(<span class="function">len</span>(<span class="keyword">result</span>), <span class="number">1</span>)
        <span class="keyword">self</span>.<span class="function">assertEqual</span>(<span class="keyword">result</span>[<span class="number">0</span>][<span class="string">'name'</span>], <span class="string">'Test User'</span>)
                </div>

                <h3><span class="emoji">📊</span>性能测试</h3>
                <div class="code-block">
<span class="comment"># 字符串操作的性能测试</span>
<span class="keyword">import</span> <span class="keyword">unittest</span>
<span class="keyword">import</span> <span class="keyword">time</span>
<span class="keyword">import</span> <span class="keyword">timeit</span>

<span class="keyword">class</span> <span class="function">TestStringPerformance</span>(<span class="keyword">unittest</span>.<span class="keyword">TestCase</span>):
    
    <span class="keyword">def</span> <span class="function">test_string_concatenation_performance</span>(<span class="keyword">self</span>):
        <span class="string">"""测试字符串连接性能"""</span>
        <span class="keyword">data</span> = [<span class="function">str</span>(<span class="keyword">i</span>) <span class="keyword">for</span> <span class="keyword">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1000</span>)]
        
        <span class="comment"># 测试+连接</span>
        <span class="keyword">def</span> <span class="function">concat_with_plus</span>():
            <span class="keyword">result</span> = <span class="string">""</span>
            <span class="keyword">for</span> <span class="keyword">item</span> <span class="keyword">in</span> <span class="keyword">data</span>:
                <span class="keyword">result</span> += <span class="keyword">item</span>
            <span class="keyword">return</span> <span class="keyword">result</span>
        
        <span class="comment"># 测试join</span>
        <span class="keyword">def</span> <span class="function">concat_with_join</span>():
            <span class="keyword">return</span> <span class="string">""</span>.<span class="function">join</span>(<span class="keyword">data</span>)
        
        <span class="keyword">plus_time</span> = <span class="keyword">timeit</span>.<span class="function">timeit</span>(<span class="keyword">concat_with_plus</span>, <span class="keyword">number</span>=<span class="number">10</span>)
        <span class="keyword">join_time</span> = <span class="keyword">timeit</span>.<span class="function">timeit</span>(<span class="keyword">concat_with_join</span>, <span class="keyword">number</span>=<span class="number">10</span>)
        
        <span class="function">print</span>(<span class="string">f"+ 连接时间: {plus_time:.4f}秒"</span>)
        <span class="function">print</span>(<span class="string">f"join 时间: {join_time:.4f}秒"</span>)
        
        <span class="comment"># join应该明显更快</span>
        <span class="keyword">self</span>.<span class="function">assertLess</span>(<span class="keyword">join_time</span>, <span class="keyword">plus_time</span>)
    
    <span class="keyword">def</span> <span class="function">test_regex_compilation_performance</span>(<span class="keyword">self</span>):
        <span class="string">"""测试正则表达式编译性能"""</span>
        <span class="keyword">import</span> <span class="keyword">re</span>
        
        <span class="keyword">text</span> = <span class="string">"<EMAIL>, <EMAIL>, invalid-email"</span> * <span class="number">100</span>
        <span class="keyword">pattern</span> = <span class="string">r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'</span>
        
        <span class="comment"># 每次编译</span>
        <span class="keyword">def</span> <span class="function">without_compilation</span>():
            <span class="keyword">return</span> <span class="keyword">re</span>.<span class="function">findall</span>(<span class="keyword">pattern</span>, <span class="keyword">text</span>)
        
        <span class="comment"># 预编译</span>
        <span class="keyword">compiled_pattern</span> = <span class="keyword">re</span>.<span class="function">compile</span>(<span class="keyword">pattern</span>)
        <span class="keyword">def</span> <span class="function">with_compilation</span>():
            <span class="keyword">return</span> <span class="keyword">compiled_pattern</span>.<span class="function">findall</span>(<span class="keyword">text</span>)
        
        <span class="keyword">without_time</span> = <span class="keyword">timeit</span>.<span class="function">timeit</span>(<span class="keyword">without_compilation</span>, <span class="keyword">number</span>=<span class="number">100</span>)
        <span class="keyword">with_time</span> = <span class="keyword">timeit</span>.<span class="function">timeit</span>(<span class="keyword">with_compilation</span>, <span class="keyword">number</span>=<span class="number">100</span>)
        
        <span class="function">print</span>(<span class="string">f"未预编译时间: {without_time:.4f}秒"</span>)
        <span class="function">print</span>(<span class="string">f"预编译时间: {with_time:.4f}秒"</span>)
        
        <span class="comment"># 预编译应该更快</span>
        <span class="keyword">self</span>.<span class="function">assertLess</span>(<span class="keyword">with_time</span>, <span class="keyword">without_time</span>)
                </div>

                <div class="tip-box">
                    <p><span class="emoji">💡</span><strong>测试最佳实践：</strong></p>
                    <ul>
                        <li>编写全面的单元测试覆盖边界情况</li>
                        <li>使用属性测试验证函数的数学性质</li>
                        <li>集成测试确保组件协同工作</li>
                        <li>性能测试监控关键操作的效率</li>
                        <li>使用mock对象隔离外部依赖</li>
                        <li>测试异常情况和错误处理</li>
                        <li>保持测试代码的可读性和可维护性</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第26页：文档编写 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">📚</span>文档编写</h1>
            </div>
            <div class="content">
                <h2>编写清晰的字符串处理文档</h2>
                
                <h3><span class="emoji">📝</span>函数文档字符串</h3>
                <div class="code-block">
<span class="comment"># 完整的函数文档示例</span>
<span class="keyword">def</span> <span class="function">extract_phone_numbers</span>(<span class="keyword">text</span>: <span class="function">str</span>, <span class="keyword">country_code</span>: <span class="function">str</span> = <span class="string">'CN'</span>) -> <span class="function">list</span>[<span class="function">str</span>]:
    <span class="string">"""从文本中提取电话号码。
    
    该函数使用正则表达式从给定文本中提取符合指定国家格式的电话号码。
    支持多种常见的电话号码格式，包括带分隔符和不带分隔符的格式。
    
    Args:
        text (str): 要搜索的文本内容。不能为None。
        country_code (str, optional): 国家代码，用于确定电话号码格式。
            支持的值：'CN'（中国）、'US'（美国）、'UK'（英国）。
            默认为'CN'。
    
    Returns:
        list[str]: 提取到的电话号码列表，已标准化格式。
            中国号码格式：'13812345678'
            美国号码格式：'******-123-4567'
            英国号码格式：'+44-20-1234-5678'
    
    Raises:
        ValueError: 当country_code不在支持列表中时抛出。
        TypeError: 当text不是字符串类型时抛出。
    
    Examples:
        >>> extract_phone_numbers("联系电话：138-1234-5678")
        ['13812345678']
        
        >>> extract_phone_numbers("Call me at (*************", 'US')
        ['******-123-4567']
        
        >>> extract_phone_numbers("没有电话号码的文本")
        []
    
    Note:
        - 函数会自动过滤掉明显无效的号码（如长度不符合要求）
        - 对于中国手机号，只识别以1开头的11位号码
        - 提取结果已去重并按出现顺序排列
    
    See Also:
        validate_phone_number: 验证单个电话号码的有效性
        format_phone_number: 格式化电话号码显示
    
    Version:
        Added in version 1.0.0
        Modified in version 1.2.0: 添加了对英国号码的支持
    """
    <span class="keyword">if</span> <span class="keyword">not</span> <span class="function">isinstance</span>(<span class="keyword">text</span>, <span class="function">str</span>):
        <span class="keyword">raise</span> <span class="function">TypeError</span>(<span class="string">f"text必须是字符串类型，得到{type(text)}"</span>)
    
    <span class="keyword">supported_countries</span> = [<span class="string">'CN'</span>, <span class="string">'US'</span>, <span class="string">'UK'</span>]
    <span class="keyword">if</span> <span class="keyword">country_code</span> <span class="keyword">not</span> <span class="keyword">in</span> <span class="keyword">supported_countries</span>:
        <span class="keyword">raise</span> <span class="function">ValueError</span>(<span class="string">f"不支持的国家代码：{country_code}，支持：{supported_countries}"</span>)
    
    <span class="comment"># 实现细节...</span>
    <span class="keyword">import</span> <span class="keyword">re</span>
    
    <span class="keyword">patterns</span> = {
        <span class="string">'CN'</span>: <span class="string">r'1[3-9]\d{9}'</span>,
        <span class="string">'US'</span>: <span class="string">r'\(?\d{3}\)?[-\s]?\d{3}[-\s]?\d{4}'</span>,
        <span class="string">'UK'</span>: <span class="string">r'\+?44[-\s]?\d{2,4}[-\s]?\d{6,8}'</span>
    }
    
    <span class="keyword">matches</span> = <span class="keyword">re</span>.<span class="function">findall</span>(<span class="keyword">patterns</span>[<span class="keyword">country_code</span>], <span class="keyword">text</span>)
    <span class="keyword">return</span> <span class="function">list</span>(<span class="function">set</span>(<span class="keyword">matches</span>))  <span class="comment"># 去重</span>

<span class="comment"># 类文档示例</span>
<span class="keyword">class</span> <span class="function">TextProcessor</span>:
    <span class="string">"""文本处理工具类。
    
    提供一系列文本清理、格式化和分析功能。该类设计为线程安全，
    可以在多线程环境中使用。
    
    Attributes:
        encoding (str): 默认文本编码，默认为'utf-8'。
        max_length (int): 处理文本的最大长度限制。
        _stats (dict): 内部统计信息，记录处理的文本数量等。
    
    Example:
        >>> processor = TextProcessor(encoding='utf-8')
        >>> cleaned = processor.clean_text("  Hello World!  ")
        >>> print(cleaned)
        'Hello World!'
    """
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>, <span class="keyword">encoding</span>: <span class="function">str</span> = <span class="string">'utf-8'</span>, <span class="keyword">max_length</span>: <span class="function">int</span> = <span class="number">10000</span>):
        <span class="string">"""初始化文本处理器。
        
        Args:
            encoding: 文本编码格式
            max_length: 最大处理长度
        """\span>
        <span class="keyword">self</span>.<span class="keyword">encoding</span> = <span class="keyword">encoding</span>
        <span class="keyword">self</span>.<span class="keyword">max_length</span> = <span class="keyword">max_length</span>
        <span class="keyword">self</span>.<span class="keyword">_stats</span> = {<span class="string">'processed_count'</span>: <span class="number">0</span>}
                </div>

                <h3><span class="emoji">📖</span>模块文档</h3>
                <div class="code-block">
<span class="comment"># string_utils.py</span>
<span class="string">"""字符串处理工具模块。

该模块提供了一系列用于字符串处理的实用函数和类，包括：
- 文本清理和标准化
- 电话号码和邮箱验证
- 正则表达式工具
- 编码转换
- 性能优化的字符串操作

主要组件：
    TextProcessor: 主要的文本处理类
    ValidationError: 自定义验证异常
    
    函数：
    - clean_text(): 清理文本中的多余空白和特殊字符
    - validate_email(): 验证邮箱地址格式
    - extract_phone_numbers(): 从文本中提取电话号码
    - safe_decode(): 安全的字符串解码
    - normalize_whitespace(): 标准化空白字符

使用示例：
    >>> from string_utils import TextProcessor, validate_email
    >>> processor = TextProcessor()
    >>> clean_text = processor.clean_text("  Hello   World!  ")
    >>> is_valid = validate_email("<EMAIL>")

依赖：
    - re: 正则表达式支持
    - unicodedata: Unicode字符处理
    - typing: 类型提示支持

作者：开发团队
版本：2.1.0
许可：MIT License
创建日期：2024-01-01
最后修改：2024-03-15

See Also:
    - Python官方re模块文档
    - Unicode标准文档
    - 项目Wiki：https://github.com/project/wiki
"""</span>

<span class="keyword">__version__</span> = <span class="string">'2.1.0'</span>
<span class="keyword">__author__</span> = <span class="string">'开发团队'</span>
<span class="keyword">__email__</span> = <span class="string">'<EMAIL>'</span>
<span class="keyword">__all__</span> = [
    <span class="string">'TextProcessor'</span>,
    <span class="string">'ValidationError'</span>,
    <span class="string">'clean_text'</span>,
    <span class="string">'validate_email'</span>,
    <span class="string">'extract_phone_numbers'</span>,
    <span class="string">'safe_decode'</span>,
    <span class="string">'normalize_whitespace'</span>
]

<span class="keyword">import</span> <span class="keyword">re</span>
<span class="keyword">import</span> <span class="keyword">unicodedata</span>
<span class="keyword">from</span> <span class="keyword">typing</span> <span class="keyword">import</span> <span class="keyword">List</span>, <span class="keyword">Optional</span>, <span class="keyword">Union</span>

<span class="comment"># 模块级常量</span>
<span class="keyword">DEFAULT_ENCODING</span> = <span class="string">'utf-8'</span>
<span class="keyword">MAX_TEXT_LENGTH</span> = <span class="number">1000000</span>  <span class="comment"># 1MB文本限制</span>

<span class="comment"># 预编译的正则表达式</span>
<span class="keyword">EMAIL_PATTERN</span> = <span class="keyword">re</span>.<span class="function">compile</span>(
    <span class="string">r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'</span>,
    <span class="keyword">re</span>.<span class="keyword">IGNORECASE</span>
)

<span class="keyword">PHONE_PATTERNS</span> = {
    <span class="string">'CN'</span>: <span class="keyword">re</span>.<span class="function">compile</span>(<span class="string">r'1[3-9]\d{9}'</span>),
    <span class="string">'US'</span>: <span class="keyword">re</span>.<span class="function">compile</span>(<span class="string">r'\(?\d{3}\)?[-\s]?\d{3}[-\s]?\d{4}'</span>),
}

<span class="keyword">class</span> <span class="function">ValidationError</span>(<span class="function">ValueError</span>):
    <span class="string">"""字符串验证错误。
    
    当字符串验证失败时抛出此异常。
    
    Attributes:
        message: 错误消息
        field: 验证失败的字段名
        value: 导致验证失败的值
    """\span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>, <span class="keyword">message</span>: <span class="function">str</span>, <span class="keyword">field</span>: <span class="function">str</span> = <span class="keyword">None</span>, <span class="keyword">value</span> = <span class="keyword">None</span>):
        <span class="function">super</span>().<span class="function">__init__</span>(<span class="keyword">message</span>)
        <span class="keyword">self</span>.<span class="keyword">field</span> = <span class="keyword">field</span>
        <span class="keyword">self</span>.<span class="keyword">value</span> = <span class="keyword">value</span>
                </div>

                <h3><span class="emoji">📋</span>README文档</h3>
                <div class="code-block">
<span class="comment"># README.md 示例</span>
<span class="string">"""# 字符串处理工具库

一个功能强大、易于使用的Python字符串处理工具库。

## 特性

- 🚀 高性能的字符串操作
- 🛡️ 安全的编码处理
- 📱 多种格式的电话号码验证
- 📧 邮箱地址验证和提取
- 🔍 强大的正则表达式工具
- 🧪 100%测试覆盖率
- 📚 完整的文档和示例

## 安装

```bash
pip install string-utils-pro
```

## 快速开始

```python
from string_utils import TextProcessor, validate_email

# 创建处理器
processor = TextProcessor()

# 清理文本
clean_text = processor.clean_text("  Hello   World!  ")
print(clean_text)  # "Hello World!"

# 验证邮箱
is_valid = validate_email("<EMAIL>")
print(is_valid)  # True
```

## API文档

### TextProcessor类

主要的文本处理类，提供以下方法：

#### clean_text(text: str) -> str
清理文本中的多余空白和特殊字符。

**参数：**
- `text`: 要清理的文本

**返回：**
- 清理后的文本

**示例：**
```python
processor = TextProcessor()
result = processor.clean_text("  hello\n\nworld  ")
print(result)  # "hello world"
```

### 验证函数

#### validate_email(email: str) -> bool
验证邮箱地址格式。

#### extract_phone_numbers(text: str, country: str = 'CN') -> List[str]
从文本中提取电话号码。

## 性能

本库经过性能优化，主要操作的基准测试结果：

| 操作 | 处理速度 | 内存使用 |
|------|----------|----------|
| 文本清理 | 1M字符/秒 | 低 |
| 邮箱验证 | 100K次/秒 | 极低 |
| 电话提取 | 500K字符/秒 | 低 |

## 贡献

欢迎贡献代码！请查看[贡献指南](CONTRIBUTING.md)。

## 许可证

MIT License - 详见[LICENSE](LICENSE)文件。

## 更新日志

### v2.1.0 (2024-03-15)
- 添加英国电话号码支持
- 性能优化：提升20%处理速度
- 修复编码处理bug

### v2.0.0 (2024-02-01)
- 重构API，提供更好的类型提示
- 添加异步支持
- 破坏性变更：移除已废弃的函数

## 支持

- 📖 [完整文档](https://docs.example.com)
- 🐛 [问题反馈](https://github.com/project/issues)
- 💬 [讨论区](https://github.com/project/discussions)
- 📧 邮箱：<EMAIL>
"""</span>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI 提示词模板</h3>
                    <p>"请帮我为这个字符串处理函数编写完整的文档，包括参数说明、返回值、异常处理、使用示例和注意事项。"</p>
                </div>
            </div>
        </div>

        <!-- 第26页：AI学习助手 - 基础概念复习 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">🤖</span>AI学习助手 - 基础概念复习</h1>
            </div>
            <div class="content">
                <h2>使用AI巩固字符串基础知识</h2>
                
                <h3><span class="emoji">📚</span>概念理解提示词</h3>
                <div class="ai-prompt">
                    <h4>基础概念解释</h4>
                    <p>"请用简单易懂的语言解释Python字符串的[具体概念]，并提供3个实际应用场景的例子。请确保解释包含：定义、特点、使用场景和注意事项。"</p>
                </div>

                <div class="ai-prompt">
                    <h4>概念对比分析</h4>
                    <p>"请对比Python中字符串的[方法A]和[方法B]的区别，包括：功能差异、性能对比、适用场景、优缺点分析，并提供代码示例说明。"</p>
                </div>

                <div class="ai-prompt">
                    <h4>知识点串联</h4>
                    <p>"请帮我梳理Python字符串处理的知识体系，从基础操作到高级应用，形成一个完整的学习路径图，并标注每个阶段的重点和难点。"</p>
                </div>

                <h3><span class="emoji">🔍</span>实例分析提示词</h3>
                <div class="ai-prompt">
                    <h4>代码解读</h4>
                    <p>"请逐行分析这段字符串处理代码：[粘贴代码]。解释每一步的作用、为什么这样写、有什么替代方案，以及可能的优化建议。"</p>
                </div>

                <div class="ai-prompt">
                    <h4>错误诊断</h4>
                    <p>"我的字符串处理代码出现了[具体错误]，代码如下：[粘贴代码]。请帮我分析错误原因、提供修复方案，并解释如何避免类似错误。"</p>
                </div>

                <div class="tip-box">
                    <p><span class="emoji">💡</span><strong>使用技巧：</strong></p>
                    <ul>
                        <li>提问时要具体明确，避免过于宽泛的问题</li>
                        <li>提供完整的代码上下文，便于AI理解</li>
                        <li>主动要求AI提供多种解决方案</li>
                        <li>请AI解释"为什么"而不仅仅是"怎么做"</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第27页：AI学习助手 - 实践练习 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">💪</span>AI学习助手 - 实践练习</h1>
            </div>
            <div class="content">
                <h2>通过AI获得个性化练习</h2>
                
                <h3><span class="emoji">🎯</span>练习题生成提示词</h3>
                <div class="ai-prompt">
                    <h4>基础练习</h4>
                    <p>"请为我生成5道关于Python字符串[具体主题]的练习题，难度从易到难，每题包含：题目描述、输入输出示例、解题思路提示、标准答案和扩展思考。"</p>
                </div>

                <div class="ai-prompt">
                    <h4>实战项目</h4>
                    <p>"请设计一个使用Python字符串处理的小项目，要求：1)有实际应用价值 2)涵盖[指定知识点] 3)提供完整的需求分析、设计思路、实现步骤和测试用例。"</p>
                </div>

                <div class="ai-prompt">
                    <h4>算法挑战</h4>
                    <p>"请提供一个字符串算法题目，难度为[初级/中级/高级]，要求：详细的问题描述、多种解法对比、时间空间复杂度分析、相关变形题目。"</p>
                </div>

                <h3><span class="emoji">🔧</span>代码优化提示词</h3>
                <div class="ai-prompt">
                    <h4>性能优化</h4>
                    <p>"请帮我优化这段字符串处理代码：[粘贴代码]。从性能、可读性、内存使用等角度分析，提供优化建议和改进后的代码，并解释优化原理。"</p>
                </div>

                <div class="ai-prompt">
                    <h4>代码重构</h4>
                    <p>"请帮我重构这个字符串处理函数：[粘贴代码]。要求：提高代码可读性、增强可维护性、遵循Python最佳实践、添加适当的错误处理和文档。"</p>
                </div>

                <div class="ai-prompt">
                    <h4>测试用例设计</h4>
                    <p>"为这个字符串处理函数设计全面的测试用例：[粘贴函数]。包括：正常情况、边界条件、异常情况、性能测试，并提供完整的pytest代码。"</p>
                </div>

                <div class="tip-box">
                    <p><span class="emoji">🎯</span><strong>练习策略：</strong></p>
                    <ul>
                        <li>从简单到复杂，循序渐进</li>
                        <li>结合实际应用场景练习</li>
                        <li>重视代码质量而非仅仅实现功能</li>
                        <li>定期回顾和总结学习成果</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第28页：AI学习助手 - 进阶学习 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">🚀</span>AI学习助手 - 进阶学习</h1>
            </div>
            <div class="content">
                <h2>深入掌握高级字符串技术</h2>
                
                <h3><span class="emoji">🔬</span>深度学习提示词</h3>
                <div class="ai-prompt">
                    <h4>源码分析</h4>
                    <p>"请分析Python字符串[具体方法]的底层实现原理，包括：C语言实现细节、时间复杂度分析、内存管理机制、与其他语言的对比。"</p>
                </div>

                <div class="ai-prompt">
                    <h4>设计模式应用</h4>
                    <p>"在字符串处理中如何应用[具体设计模式]？请提供：模式介绍、适用场景、实现示例、优缺点分析、实际项目应用案例。"</p>
                </div>

                <div class="ai-prompt">
                    <h4>性能调优</h4>
                    <p>"在处理大量字符串数据时，如何进行性能优化？请从：算法选择、内存管理、并发处理、缓存策略等角度提供详细的优化方案。"</p>
                </div>

                <h3><span class="emoji">🌐</span>实际应用提示词</h3>
                <div class="ai-prompt">
                    <h4>项目架构设计</h4>
                    <p>"设计一个文本处理系统的架构，要求处理[具体需求]。请提供：系统架构图、模块划分、接口设计、数据流程、技术选型理由。"</p>
                </div>

                <div class="ai-prompt">
                    <h4>安全性分析</h4>
                    <p>"在字符串处理中有哪些安全风险？针对[具体场景]，请分析：潜在威胁、防护措施、最佳实践、安全测试方法。"</p>
                </div>

                <div class="ai-prompt">
                    <h4>跨语言对比</h4>
                    <p>"对比Python、Java、JavaScript在字符串处理方面的差异，包括：语法特性、性能表现、生态支持、适用场景，并提供迁移指南。"</p>
                </div>

                <h3><span class="emoji">📈</span>学习路径规划</h3>
                <div class="ai-prompt">
                    <h4>个性化学习计划</h4>
                    <p>"根据我的当前水平[描述水平]和学习目标[描述目标]，制定一个3个月的字符串处理学习计划，包括：学习阶段、知识点、实践项目、评估标准。"</p>
                </div>

                <div class="tip-box">
                    <p><span class="emoji">🎓</span><strong>进阶建议：</strong></p>
                    <ul>
                        <li>关注底层原理，理解实现机制</li>
                        <li>学习相关的计算机科学理论</li>
                        <li>参与开源项目，积累实战经验</li>
                        <li>关注技术发展趋势和新特性</li>
                        <li>建立自己的技术博客和知识库</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第29页：AI学习助手 - 学习总结与规划 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title"><span class="emoji">📋</span>AI学习助手 - 学习总结与规划</h1>
            </div>
            <div class="content">
                <h2>系统化总结与持续学习</h2>
                
                <h3><span class="emoji">📝</span>学习总结提示词</h3>
                <div class="ai-prompt">
                    <h4>知识点梳理</h4>
                    <p>"请帮我总结Python字符串处理的核心知识点，按照：基础操作→高级技巧→实际应用→性能优化的层次结构，形成完整的知识图谱。"</p>
                </div>

                <div class="ai-prompt">
                    <h4>学习成果评估</h4>
                    <p>"根据我学习的内容[列出学习内容]，设计一套评估测试，包括：理论测试、编程实践、项目应用，帮我评估掌握程度和薄弱环节。"</p>
                </div>

                <div class="ai-prompt">
                    <h4>错误总结分析</h4>
                    <p>"分析我在学习过程中遇到的常见错误[列出错误类型]，总结：错误原因、正确做法、预防措施、相关知识点补强建议。"</p>
                </div>

                <h3><span class="emoji">🎯</span>学习规划提示词</h3>
                <div class="ai-prompt">
                    <h4>下阶段学习计划</h4>
                    <p>"基于我当前的字符串处理水平，制定下一阶段的学习计划，包括：学习目标、重点内容、实践项目、时间安排、评估标准。"</p>
                </div>

                <div class="ai-prompt">
                    <h4>技能拓展建议</h4>
                    <p>"在掌握字符串处理的基础上，推荐相关的技术领域进行拓展学习，如：正则表达式、文本挖掘、自然语言处理等，并提供学习路径。"</p>
                </div>

                <div class="ai-prompt">
                    <h4>实战项目规划</h4>
                    <p>"设计3个递进式的实战项目来巩固字符串处理技能：初级项目（基础应用）、中级项目（综合运用）、高级项目（创新应用），每个项目包含详细规划。"</p>
                </div>

                <h3><span class="emoji">🔄</span>持续学习提示词</h3>
                <div class="ai-prompt">
                    <h4>技术跟踪</h4>
                    <p>"如何跟踪Python字符串处理相关的技术发展？请推荐：学习资源、技术博客、开源项目、社区论坛、会议活动等。"</p>
                </div>

                <div class="ai-prompt">
                    <h4>知识更新</h4>
                    <p>"制定一个知识更新机制，确保字符串处理技能与时俱进，包括：定期复习计划、新技术学习、实践验证、知识分享。"</p>
                </div>

                <div class="ai-prompt">
                    <h4>教学相长</h4>
                    <p>"如何通过教授他人来深化自己的字符串处理知识？请提供：教学方法、内容组织、互动设计、效果评估的建议。"</p>
                </div>

                <div class="tip-box">
                    <p><span class="emoji">🌟</span><strong>学习心得：</strong></p>
                    <ul>
                        <li>定期总结和反思，形成知识体系</li>
                        <li>理论与实践相结合，注重应用能力</li>
                        <li>保持好奇心，持续探索新技术</li>
                        <li>积极参与技术社区，分享交流经验</li>
                        <li>建立个人技术品牌，记录成长历程</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🎓</span>终极学习提示词</h3>
                    <p>"作为我的AI学习伙伴，请根据我的学习进度和目标，持续为我提供个性化的学习建议、练习题目、项目想法和技术指导，帮助我成为字符串处理专家。"</p>
                </div>
            </div>
        </div>

       </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">← 上一页</button>
        <button class="nav-btn" onclick="nextSlide()">下一页 →</button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        
        document.getElementById('total-slides').textContent = totalSlides;
        
        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            document.getElementById('current-slide').textContent = currentSlide + 1;
            
            // 重置滚动位置
            slides[currentSlide].scrollTop = 0;
        }
        
        function nextSlide() {
            showSlide(currentSlide + 1);
        }
        
        function previousSlide() {
            showSlide(currentSlide - 1);
        }
        
        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                previousSlide();
            }
        });
        
        // 触摸滑动支持
        let startX = 0;
        let startY = 0;
        
        document.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });
        
        document.addEventListener('touchend', function(e) {
            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            const diffX = startX - endX;
            const diffY = startY - endY;
            
            // 只有水平滑动距离大于垂直滑动距离时才切换幻灯片
            if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                if (diffX > 0) {
                    nextSlide();
                } else {
                    previousSlide();
                }
            }
        });
    </script>
</body>
</html>