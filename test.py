from openai import OpenAI

# 在这里填入你的API密钥和基础URL
client = OpenAI(
    api_key="sk-VYm6MEaqoYFgvtVLzX03ZhnUCIZz599rAlivoH9lWTJl5gX7",  # 请替换为你的实际API密钥
    base_url="https://api.tu-zi.com/v1"  # 请替换为你的实际基础URL
)

completion = client.chat.completions.create(
    model="claude-sonnet-4-20250514",
    messages=[
        {
            "role": "user",
            "content": "Python 怎么建立列表"
        }
    ]
)

print(completion.choices[0].message.content)