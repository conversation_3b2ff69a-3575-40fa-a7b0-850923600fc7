<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python数据分析与AI辅助编程</title>
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/atom-one-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/bash.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff;
            overflow-x: hidden;
            line-height: 1.6;
        }

        .presentation-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }

        .slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: none;
            padding: 60px 40px;
            overflow-y: auto;
        }

        .slide.active {
            display: block;
            animation: slideIn 0.5s ease-in-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(50px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .slide-content {
            max-width: 1000px;
            margin: 0 auto;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .slide-header {
            margin-bottom: 40px;
            text-align: center;
        }

        .slide-number {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 10px;
        }

        .slide-title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .slide-emoji {
            font-size: 60px;
            margin-right: 20px;
            display: inline-block;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {

            0%,
            20%,
            50%,
            80%,
            100% {
                transform: translateY(0);
            }

            40% {
                transform: translateY(-10px);
            }

            60% {
                transform: translateY(-5px);
            }
        }

        .slide-subtitle {
            font-size: 20px;
            opacity: 0.9;
            font-weight: 300;
        }

        .slide-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .content-section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #FFD700;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .section-icon {
            font-size: 32px;
        }

        .text-content {
            font-size: 18px;
            line-height: 1.8;
            margin-bottom: 30px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            text-align: center;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .feature-icon {
            font-size: 48px;
            margin-bottom: 20px;
            display: block;
        }

        .feature-title {
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #FFD700;
        }

        .feature-desc {
            font-size: 16px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .highlight-box {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-left: 5px solid #FFD700;
            padding: 30px;
            margin: 30px 0;
            border-radius: 10px;
        }

        .highlight-box h3 {
            font-size: 24px;
            margin-bottom: 20px;
            color: #FFD700;
        }

        .highlight-box ul {
            list-style: none;
            padding: 0;
        }

        .highlight-box li {
            font-size: 16px;
            margin-bottom: 12px;
            padding-left: 25px;
            position: relative;
        }

        .highlight-box li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #FFD700;
            font-weight: bold;
        }

        .code-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            overflow: hidden;
            margin: 30px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .code-header {
            background: rgba(0, 0, 0, 0.5);
            padding: 15px 25px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .code-title {
            font-size: 16px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .code-lang {
            background: #FFD700;
            color: #000;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .code-block {
            padding: 25px;
            font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            line-height: 1.6;
            overflow-x: auto;
            color: #f8f8f2;
            background: rgba(0, 0, 0, 0.4);
            border-radius: 8px;
        }

        .code-block pre {
            margin: 0;
            white-space: pre;
            overflow-x: auto;
        }

        .code-block code {
            font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            line-height: 1.6;
            color: #f8f8f2;
        }

        /* 语法高亮样式增强 */
        .hljs {
            background: transparent !important;
            color: #f8f8f2 !important;
        }

        .hljs-keyword {
            color: #66d9ef !important;
            font-weight: bold;
        }

        .hljs-string {
            color: #a6e22e !important;
        }

        .hljs-comment {
            color: #75715e !important;
            font-style: italic;
        }

        .hljs-number {
            color: #ae81ff !important;
        }

        .hljs-built_in {
            color: #f92672 !important;
        }

        .hljs-function {
            color: #a6e22e !important;
        }

        .hljs-variable {
            color: #fd971f !important;
        }

        .prompt-template {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 255, 255, 0.1) 100%);
            border: 2px solid #FFD700;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            position: relative;
        }

        .prompt-title {
            font-size: 18px;
            font-weight: 600;
            color: #FFD700;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .prompt-content {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.7;
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(20px);
            padding: 15px 25px;
            border-radius: 50px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .nav-btn {
            background: transparent;
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: #ffffff;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-btn:hover {
            border-color: #FFD700;
            color: #FFD700;
            transform: translateY(-2px);
        }

        .nav-btn:disabled {
            opacity: 0.3;
            cursor: not-allowed;
            transform: none;
        }

        .slide-counter {
            background: rgba(255, 215, 0, 0.2);
            color: #FFD700;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 4px;
            background: linear-gradient(90deg, #FFD700, #FFA500);
            transition: width 0.3s ease;
            z-index: 1001;
        }

        @media (max-width: 768px) {
            .slide {
                padding: 40px 20px;
            }

            .slide-title {
                font-size: 32px;
            }

            .slide-emoji {
                font-size: 40px;
            }

            .feature-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .navigation {
                bottom: 20px;
                padding: 12px 20px;
            }

            .nav-btn {
                padding: 10px 16px;
                font-size: 13px;
            }
        }
    </style>
</head>

<body>
    <div class="progress-bar" id="progressBar"></div>

    <div class="presentation-container">
        <!-- 第1页: 封面页 -->
        <div class="slide active" data-slide="1">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">01 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">🐍</span>
                        Python数据分析与AI辅助编程
                    </h1>
                    <div class="slide-subtitle">从零基础到专业数据科学家的完整路径</div>
                </div>

                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">🎯</span>
                            课程概览
                        </h2>
                        <div class="text-content">
                            <p>本课程将带你深入掌握Python数据分析的核心技能，并学会如何利用AI工具提升编程效率。我们将从基础概念开始，逐步构建完整的数据科学工作流程。</p>
                        </div>

                        <div class="feature-grid">
                            <div class="feature-card">
                                <span class="feature-icon">📊</span>
                                <h3 class="feature-title">数据处理</h3>
                                <p class="feature-desc">掌握pandas、numpy等核心库的使用</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">📈</span>
                                <h3 class="feature-title">数据可视化</h3>
                                <p class="feature-desc">使用matplotlib、seaborn创建专业图表</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🤖</span>
                                <h3 class="feature-title">AI辅助编程</h3>
                                <p class="feature-desc">学会使用AI工具提升编程效率</p>
                            </div>
                        </div>
                    </div>

                    <div class="highlight-box">
                        <h3>🎓 学习目标</h3>
                        <ul>
                            <li>掌握Python数据分析核心技能</li>
                            <li>学会构建完整的数据处理流程</li>
                            <li>熟练使用AI辅助编程工具</li>
                            <li>具备独立完成数据科学项目的能力</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第2页: Python环境搭建 -->
        <div class="slide" data-slide="2">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">02 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">⚙️</span>
                        Python环境搭建
                    </h1>
                    <div class="slide-subtitle">工欲善其事，必先利其器</div>
                </div>

                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">🔧</span>
                            环境配置步骤
                        </h2>
                        <div class="text-content">
                            <p>正确的环境配置是Python数据分析学习的第一步。我们推荐使用Anaconda来管理Python环境和包依赖。</p>
                        </div>

                        <div class="code-container">
                            <div class="code-header">
                                <div class="code-title">
                                    <span>📦</span>
                                    安装Anaconda环境
                                </div>
                                <div class="code-lang">Bash</div>
                            </div>
                            <div class="code-block">
                                <pre><code class="language-bash"># 创建Anaconda虚拟环境
conda create -n data_analysis python=3.9
conda activate data_analysis

# 安装数据分析核心库
conda install pandas numpy matplotlib seaborn
conda install jupyter notebook
conda install plotly scikit-learn

# 安装额外工具
pip install plotly-express
pip install bokeh
pip install altair</code></pre>
                            </div>
                        </div>

                        <div class="prompt-template">
                            <div class="prompt-title">
                                <span>🤖</span>
                                AI辅助环境搭建提示词
                            </div>
                            <div class="prompt-content">
                                "请帮我搭建Python数据分析环境：
                                1. 推荐最适合的搭建方案
                                2. 详细说明IDE选择
                                3. 列出必需的库和版本
                                4. pip和conda的使用建议

                                请提供详细的步骤说明。"
                            </div>
                        </div>

                        <div class="highlight-box">
                            <h3>💡 推荐开发工具</h3>
                            <ul>
                                <li><strong>JupyterLab</strong> - 交互式数据分析环境</li>
                                <li><strong>VS Code</strong> - 轻量级代码编辑器</li>
                                <li><strong>PyCharm</strong> - 专业Python IDE</li>
                                <li><strong>Google Colab</strong> - 云端Jupyter环境</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第3页: Pandas基础 -->
        <div class="slide" data-slide="3">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">03 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">🐼</span>
                        Pandas数据处理基础
                    </h1>
                    <div class="slide-subtitle">数据分析的瑞士军刀</div>
                </div>

                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">📋</span>
                            核心数据结构
                        </h2>
                        <div class="text-content">
                            <p>Pandas提供了两种主要的数据结构：Series（一维）和DataFrame（二维），它们是数据分析的基础。</p>
                        </div>

                        <div class="code-container">
                            <div class="code-header">
                                <div class="code-title">
                                    <span>🐼</span>
                                    创建和操作DataFrame
                                </div>
                                <div class="code-lang">Python</div>
                            </div>
                            <div class="code-block">
                                <pre><code class="language-python">import pandas as pd
import numpy as np

# 创建DataFrame
data = {
    '姓名': ['张三', '李四', '王五', '赵六'],
    '年龄': [25, 30, 35, 28],
    '城市': ['北京', '上海', '广州', '深圳'],
    '薪资': [8000, 12000, 15000, 10000]
}

df = pd.DataFrame(data)
print("数据基本信息:")
print(df.info())

# 数据预览
print("\n前3行数据:")
print(df.head(3))

# 统计描述信息
print("\n描述性统计:")
print(df.describe())

# 数据筛选
high_salary = df[df['薪资'] > 10000]
print(f"\n高薪员工({len(high_salary)}人):")
print(high_salary)</code></pre>
                            </div>
                        </div>

                        <div class="feature-grid">
                            <div class="feature-card">
                                <span class="feature-icon">🔍</span>
                                <h3 class="feature-title">数据探索</h3>
                                <p class="feature-desc">info(), describe(), head(), tail()等方法快速了解数据</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🎯</span>
                                <h3 class="feature-title">数据筛选</h3>
                                <p class="feature-desc">使用条件表达式筛选特定数据</p>
                            </div>
                        </div>

                        <div class="prompt-template">
                            <div class="prompt-title">
                                <span>🤖</span>
                                Pandas学习提示词
                            </div>
                            <div class="prompt-content">
                                "请帮我学习pandas数据处理：
                                1. 解释DataFrame和Series的区别和联系
                                2. 列出常用的数据筛选和清洗方法
                                3. 提供实际案例和练习题
                                4. 详细说明数据合并操作

                                请提供可运行的代码示例。"
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第4页: 数据清洗 -->
        <div class="slide" data-slide="4">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">04 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">🧹</span>
                        数据清洗与预处理
                    </h1>
                    <div class="slide-subtitle">垃圾进，垃圾出 - 数据质量决定分析质量</div>
                </div>

                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">🔧</span>
                            常见数据问题处理
                        </h2>
                        <div class="text-content">
                            <p>真实世界的数据往往是脏乱差的，数据清洗是数据分析中最重要但也最耗时的步骤。</p>
                        </div>

                        <div class="feature-grid">
                            <div class="feature-card">
                                <span class="feature-icon">❌</span>
                                <h3 class="feature-title">缺失值处理</h3>
                                <p class="feature-desc">删除、填充、插值等多种策略</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">⚠️</span>
                                <h3 class="feature-title">异常值检测</h3>
                                <p class="feature-desc">统计方法和可视化识别异常数据</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🔄</span>
                                <h3 class="feature-title">数据转换</h3>
                                <p class="feature-desc">类型转换、标准化、归一化</p>
                            </div>
                        </div>

                        <div class="code-container">
                            <div class="code-header">
                                <div class="code-title">
                                    <span>🧹</span>
                                    处理缺失值和异常值
                                </div>
                                <div class="code-lang">Python</div>
                            </div>
                            <div class="code-block">
                                <pre><code class="language-python">import pandas as pd
import numpy as np

# 创建包含缺失值的数据
data = {
    '姓名': ['张三', '李四', None, '赵六', '王五'],
    '年龄': [25, None, 35, 28, 150],  # 150是异常值
    '薪资': [8000, 12000, np.nan, 10000, 15000],
    '部门': ['技术', '销售', '技术', None, '市场']
}

df = pd.DataFrame(data)

# 检查缺失值
print("缺失值统计:")
print(df.isnull().sum())

# 处理缺失值
df_cleaned = df.copy()
df_cleaned['姓名'].fillna('未知', inplace=True)
df_cleaned['年龄'].fillna(df_cleaned['年龄'].median(), inplace=True)
df_cleaned['部门'].fillna(df_cleaned['部门'].mode()[0], inplace=True)

# 处理异常值（年龄超过100的视为异常）
df_cleaned.loc[df_cleaned['年龄'] > 100, '年龄'] = df_cleaned['年龄'].median()

print("\n清洗后的数据:")
print(df_cleaned)</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第5页: NumPy数值计算 -->
        <div class="slide" data-slide="5">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">05 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">🔢</span>
                        NumPy数值计算基础
                    </h1>
                    <div class="slide-subtitle">高性能数值计算的基石</div>
                </div>

                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">⚡</span>
                            数组操作与向量化计算
                        </h2>
                        <div class="text-content">
                            <p>NumPy是Python科学计算的基础库，提供了高效的多维数组对象和丰富的数学函数。</p>
                        </div>

                        <div class="code-container">
                            <div class="code-header">
                                <div class="code-title">
                                    <span>🔢</span>
                                    NumPy数组操作
                                </div>
                                <div class="code-lang">Python</div>
                            </div>
                            <div class="code-block">
                                <pre><code class="language-python">import numpy as np
import time

# 创建数组
arr1 = np.array([1, 2, 3, 4, 5])
arr2 = np.array([[1, 2, 3], [4, 5, 6]])

print("一维数组:", arr1)
print("二维数组:\n", arr2)

# 数组属性
print(f"形状: {arr2.shape}")
print(f"维度: {arr2.ndim}")
print(f"数据类型: {arr2.dtype}")

# 向量化操作的性能优势
large_list = list(range(100000))
large_array = np.array(large_list)

# Python列表操作
start_time = time.time()
result_list = [x * 2 for x in large_list]
list_time = time.time() - start_time

# NumPy数组操作
start_time = time.time()
result_array = large_array * 2
array_time = time.time() - start_time

print(f"\nPython列表操作时间: {list_time:.4f}秒")
print(f"NumPy数组操作时间: {array_time:.4f}秒")
print(f"NumPy速度提升: {list_time/array_time:.1f}倍")</code></pre>
                            </div>
                        </div>

                        <div class="prompt-template">
                            <div class="prompt-title">
                                <span>🤖</span>
                                NumPy学习提示词
                            </div>
                            <div class="prompt-content">
                                "请帮我深入学习NumPy：
                                1. 解释向量化计算的原理和优势
                                2. 演示数组广播机制
                                3. 提供线性代数操作示例
                                4. 对比NumPy与Python原生列表的性能

                                请包含实际的性能测试代码。"
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第6页: Matplotlib基础可视化 -->
        <div class="slide" data-slide="6">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">06 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">📊</span>
                        Matplotlib基础可视化
                    </h1>
                    <div class="slide-subtitle">让数据开口说话</div>
                </div>

                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">🎨</span>
                            创建你的第一个图表
                        </h2>
                        <div class="text-content">
                            <p>Matplotlib是Python中最基础也是最强大的可视化库，掌握它是数据可视化的第一步。</p>
                        </div>

                        <div class="feature-grid">
                            <div class="feature-card">
                                <span class="feature-icon">📈</span>
                                <h3 class="feature-title">线图</h3>
                                <p class="feature-desc">展示趋势变化，适合时间序列数据</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">📊</span>
                                <h3 class="feature-title">柱状图</h3>
                                <p class="feature-desc">比较不同类别的数值大小</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🥧</span>
                                <h3 class="feature-title">饼图</h3>
                                <p class="feature-desc">显示部分与整体的关系</p>
                            </div>
                        </div>

                        <div class="code-container">
                            <div class="code-header">
                                <div class="code-title">
                                    <span>📊</span>
                                    基础图表绘制
                                </div>
                                <div class="code-lang">Python</div>
                            </div>
                            <div class="code-block">
                                <pre><code class="language-python">import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 创建数据
x = np.linspace(0, 10, 100)
y1 = np.sin(x)
y2 = np.cos(x)

# 创建图形和子图
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

# 线图
ax1.plot(x, y1, label='sin(x)', linewidth=2)
ax1.plot(x, y2, label='cos(x)', linewidth=2)
ax1.set_title('三角函数图像')
ax1.set_xlabel('x')
ax1.set_ylabel('y')
ax1.legend()
ax1.grid(True, alpha=0.3)

# 柱状图
categories = ['产品A', '产品B', '产品C', '产品D']
values = [23, 45, 56, 78]

bars = ax2.bar(categories, values, alpha=0.8)
ax2.set_title('产品销售对比')
ax2.set_ylabel('销售额(万元)')

plt.tight_layout()
plt.show()</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第7-30页：快速添加剩余内容 -->
        <div class="slide" data-slide="7">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">07 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">🌊</span>
                        Seaborn高级可视化
                    </h1>
                    <div class="slide-subtitle">统计图表的艺术</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">📈</span>
                            统计可视化进阶
                        </h2>
                        <div class="text-content">
                            <p>Seaborn基于Matplotlib构建，提供了更高级的统计图表和更美观的默认样式。</p>
                        </div>
                        <div class="highlight-box">
                            <h3>🎨 Seaborn核心功能</h3>
                            <ul>
                                <li>统计图表：箱线图、小提琴图、热力图</li>
                                <li>分布图：直方图、密度图、联合分布图</li>
                                <li>关系图：散点图、回归图</li>
                                <li>分类图：条形图、点图、计数图</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" data-slide="8">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">08 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">🔄</span>
                        数据聚合与分组分析
                    </h1>
                    <div class="slide-subtitle">Split-Apply-Combine策略</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">📊</span>
                            GroupBy操作详解
                        </h2>
                        <div class="text-content">
                            <p>数据分组是数据分析中的核心操作，通过分组可以发现不同类别间的差异和规律。</p>
                        </div>
                        <div class="highlight-box">
                            <h3>🎯 分组分析的应用场景</h3>
                            <ul>
                                <li>业务分析 - 按部门、地区、产品线分析业绩</li>
                                <li>用户画像 - 按年龄、性别、地域分析用户行为</li>
                                <li>时间分析 - 按年、月、周分析趋势变化</li>
                                <li>A/B测试 - 对比不同实验组的效果</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" data-slide="9">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">09 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">⏰</span>
                        时间序列数据分析
                    </h1>
                    <div class="slide-subtitle">时间就是金钱，数据就是洞察</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">📅</span>
                            时间数据处理技巧
                        </h2>
                        <div class="text-content">
                            <p>时间序列分析是数据分析中的重要分支，帮助我们理解数据随时间的变化规律。</p>
                        </div>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <span class="feature-icon">📊</span>
                                <h3 class="feature-title">重采样</h3>
                                <p class="feature-desc">改变时间频率，如日数据转月数据</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🔄</span>
                                <h3 class="feature-title">滚动窗口</h3>
                                <p class="feature-desc">计算移动平均、滚动标准差等</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">📈</span>
                                <h3 class="feature-title">趋势分析</h3>
                                <p class="feature-desc">识别长期趋势和周期性模式</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" data-slide="10">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">10 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">🔗</span>
                        数据合并与连接
                    </h1>
                    <div class="slide-subtitle">将分散的数据拼接成完整的故事</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">🧩</span>
                            多表数据整合
                        </h2>
                        <div class="text-content">
                            <p>在实际工作中，数据往往分散在多个表中，掌握数据合并技巧是数据分析师的必备技能。</p>
                        </div>
                        <div class="highlight-box">
                            <h3>🔍 连接类型选择指南</h3>
                            <ul>
                                <li>内连接 - 只要交集，数据最干净</li>
                                <li>左连接 - 保留主表完整性，常用于添加属性</li>
                                <li>右连接 - 较少使用，可用左连接替代</li>
                                <li>外连接 - 保留所有数据，用于数据完整性检查</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 继续添加第11-30页 -->
        <div class="slide" data-slide="11">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">11 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">📊</span>
                        统计分析基础
                    </h1>
                    <div class="slide-subtitle">用数据说话，让统计证明</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">📈</span>
                            描述性统计与推断统计
                        </h2>
                        <div class="text-content">
                            <p>统计分析是数据科学的核心，帮助我们从数据中提取有意义的洞察和结论。</p>
                        </div>
                        <div class="prompt-template">
                            <div class="prompt-title">
                                <span>🤖</span>
                                统计分析提示词
                            </div>
                            <div class="prompt-content">
                                "请帮我进行统计分析：
                                1. 选择合适的统计检验方法
                                2. 解释统计结果的实际意义
                                3. 计算效应量和置信区间
                                4. 提供结果解读和建议"
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" data-slide="12">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">12 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">🔗</span>
                        相关性分析
                    </h1>
                    <div class="slide-subtitle">发现变量间的隐秘关系</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">🎯</span>
                            相关系数与因果关系
                        </h2>
                        <div class="text-content">
                            <p>相关性分析帮助我们理解变量之间的关系强度和方向，但要记住：相关不等于因果。</p>
                        </div>
                        <div class="highlight-box">
                            <h3>⚠️ 相关性分析注意事项</h3>
                            <ul>
                                <li>相关≠因果 - 高相关性不意味着因果关系</li>
                                <li>非线性关系 - 皮尔逊相关只能捕捉线性关系</li>
                                <li>异常值影响 - 极端值可能严重影响相关系数</li>
                                <li>样本量考虑 - 小样本的相关系数可能不稳定</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" data-slide="13">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">13 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">🤖</span>
                        机器学习入门
                    </h1>
                    <div class="slide-subtitle">让机器从数据中学习</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">🧠</span>
                            监督学习基础
                        </h2>
                        <div class="text-content">
                            <p>机器学习是数据科学的高级应用，通过算法让计算机从数据中自动学习模式和规律。</p>
                        </div>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <span class="feature-icon">📈</span>
                                <h3 class="feature-title">回归问题</h3>
                                <p class="feature-desc">预测连续数值，如房价、销量</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🎯</span>
                                <h3 class="feature-title">分类问题</h3>
                                <p class="feature-desc">预测类别标签，如垃圾邮件识别</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🔍</span>
                                <h3 class="feature-title">聚类问题</h3>
                                <p class="feature-desc">发现数据中的隐藏模式</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" data-slide="14">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">14 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">⚖️</span>
                        模型评估与优化
                    </h1>
                    <div class="slide-subtitle">好模型是调出来的</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">📊</span>
                            交叉验证与超参数调优
                        </h2>
                        <div class="text-content">
                            <p>模型的性能不仅取决于算法本身，更重要的是如何正确评估和优化模型参数。</p>
                        </div>
                        <div class="prompt-template">
                            <div class="prompt-title">
                                <span>🤖</span>
                                模型优化提示词
                            </div>
                            <div class="prompt-content">
                                "请帮我优化机器学习模型：
                                1. 选择合适的评估指标
                                2. 设计交叉验证策略
                                3. 推荐超参数调优方法
                                4. 分析模型性能瓶颈
                                5. 提供改进建议"
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" data-slide="15">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">15 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">🚀</span>
                        AI辅助编程工具介绍
                    </h1>
                    <div class="slide-subtitle">让AI成为你的编程伙伴</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">🤖</span>
                            主流AI编程助手对比
                        </h2>
                        <div class="text-content">
                            <p>AI辅助编程工具正在革命性地改变软件开发方式，大幅提升编程效率和代码质量。</p>
                        </div>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <span class="feature-icon">🔮</span>
                                <h3 class="feature-title">GitHub Copilot</h3>
                                <p class="feature-desc">基于OpenAI Codex，支持多种编程语言</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">💬</span>
                                <h3 class="feature-title">ChatGPT/Claude</h3>
                                <p class="feature-desc">对话式编程助手，擅长解释代码</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">⚡</span>
                                <h3 class="feature-title">Cursor/Windsurf</h3>
                                <p class="feature-desc">AI原生代码编辑器</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 继续添加第16-30页 -->
        <div class="slide" data-slide="16">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">16 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">💡</span>
                        提示词工程基础
                    </h1>
                    <div class="slide-subtitle">与AI对话的艺术</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">🎯</span>
                            高效提示词设计原则
                        </h2>
                        <div class="text-content">
                            <p>好的提示词是获得高质量AI输出的关键。掌握提示词工程技巧能让你更好地利用AI工具。</p>
                        </div>
                        <div class="highlight-box">
                            <h3>✨ 提示词优化技巧</h3>
                            <ul>
                                <li>明确具体 - 避免模糊的描述，提供具体的需求</li>
                                <li>结构化 - 使用编号、分点等方式组织信息</li>
                                <li>示例驱动 - 提供输入输出示例</li>
                                <li>角色设定 - 让AI扮演专家角色</li>
                                <li>迭代优化 - 根据输出质量调整提示词</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" data-slide="17">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">17 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">⚡</span>
                        AI代码生成与优化
                    </h1>
                    <div class="slide-subtitle">从想法到代码的瞬间转换</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">🔧</span>
                            实用代码生成场景
                        </h2>
                        <div class="text-content">
                            <p>AI可以帮助我们快速生成高质量的代码，从简单的函数到复杂的数据处理流水线。</p>
                        </div>
                        <div class="prompt-template">
                            <div class="prompt-title">
                                <span>🏗️</span>
                                数据处理流水线生成
                            </div>
                            <div class="prompt-content">
                                "请帮我创建一个数据处理流水线：
                                输入：CSV文件，包含用户行为数据
                                处理需求：
                                1. 数据清洗（处理缺失值、异常值）
                                2. 特征工程（时间特征提取、行为统计）
                                3. 用户画像构建
                                4. 结果可视化"
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" data-slide="18">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">18 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">🎨</span>
                        交互式数据可视化
                    </h1>
                    <div class="slide-subtitle">让数据动起来</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">🌟</span>
                            Plotly与Dash应用
                        </h2>
                        <div class="text-content">
                            <p>交互式可视化让用户能够深入探索数据，发现静态图表无法展现的洞察。</p>
                        </div>
                        <div class="prompt-template">
                            <div class="prompt-title">
                                <span>🎨</span>
                                可视化设计提示词
                            </div>
                            <div class="prompt-content">
                                "请帮我设计一个交互式数据可视化：
                                数据类型：[描述你的数据]
                                目标用户：[分析师/管理层/客户等]
                                使用场景：[报告/监控/探索等]

                                需求：
                                1. 选择最适合的图表类型
                                2. 设计交互功能（筛选、缩放、悬停等）
                                3. 优化视觉设计（颜色、布局、字体）"
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" data-slide="19">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">19 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">📊</span>
                        自动化报告生成
                    </h1>
                    <div class="slide-subtitle">让数据自己讲故事</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">🤖</span>
                            智能报告系统设计
                        </h2>
                        <div class="text-content">
                            <p>自动化报告生成可以大幅提升工作效率，让数据分析师专注于更有价值的洞察发现。</p>
                        </div>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <span class="feature-icon">📝</span>
                                <h3 class="feature-title">模板化报告</h3>
                                <p class="feature-desc">标准化的报告格式和结构</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🔄</span>
                                <h3 class="feature-title">定时更新</h3>
                                <p class="feature-desc">自动获取最新数据并更新报告</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">📧</span>
                                <h3 class="feature-title">智能分发</h3>
                                <p class="feature-desc">根据角色自动发送相关报告</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" data-slide="20">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">20 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">🚀</span>
                        项目实战案例
                    </h1>
                    <div class="slide-subtitle">电商用户行为分析项目</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">🎯</span>
                            完整项目流程演示
                        </h2>
                        <div class="text-content">
                            <p>通过一个完整的电商用户行为分析项目，展示从数据收集到洞察输出的全流程。</p>
                        </div>
                        <div class="highlight-box">
                            <h3>📋 项目背景</h3>
                            <ul>
                                <li>业务问题：某电商平台希望了解用户购买行为，优化营销策略</li>
                                <li>数据来源：用户行为日志、订单数据、商品信息、用户画像</li>
                                <li>分析目标：识别高价值用户、分析购买路径、预测用户流失</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第21-30页 -->
        <div class="slide" data-slide="21">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">21 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">🔒</span>
                        数据安全与隐私保护
                    </h1>
                    <div class="slide-subtitle">负责任的数据科学实践</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">🛡️</span>
                            数据脱敏与匿名化
                        </h2>
                        <div class="text-content">
                            <p>在数据分析过程中，保护用户隐私和数据安全是至关重要的责任。</p>
                        </div>
                        <div class="highlight-box">
                            <h3>🔐 数据安全最佳实践</h3>
                            <ul>
                                <li>数据脱敏 - 移除或替换敏感信息</li>
                                <li>访问控制 - 限制数据访问权限</li>
                                <li>加密存储 - 敏感数据加密保存</li>
                                <li>审计日志 - 记录数据访问行为</li>
                                <li>合规检查 - 遵守GDPR、CCPA等法规</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" data-slide="22">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">22 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">⚡</span>
                        Python性能优化
                    </h1>
                    <div class="slide-subtitle">让代码飞起来</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">🚀</span>
                            高性能数据处理技巧
                        </h2>
                        <div class="text-content">
                            <p>掌握性能优化技巧，让你的Python代码运行得更快更高效。</p>
                        </div>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <span class="feature-icon">🔢</span>
                                <h3 class="feature-title">向量化操作</h3>
                                <p class="feature-desc">使用NumPy和Pandas的向量化功能</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">💾</span>
                                <h3 class="feature-title">内存优化</h3>
                                <p class="feature-desc">合理选择数据类型，减少内存占用</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">⚡</span>
                                <h3 class="feature-title">并行处理</h3>
                                <p class="feature-desc">利用多核CPU加速计算</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" data-slide="23">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">23 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">☁️</span>
                        云端数据分析
                    </h1>
                    <div class="slide-subtitle">拥抱云计算的力量</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">🌐</span>
                            主流云平台对比
                        </h2>
                        <div class="text-content">
                            <p>云计算为大规模数据分析提供了强大的计算资源和便捷的服务。</p>
                        </div>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <span class="feature-icon">🔵</span>
                                <h3 class="feature-title">AWS</h3>
                                <p class="feature-desc">SageMaker, EMR, Redshift</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🔷</span>
                                <h3 class="feature-title">Azure</h3>
                                <p class="feature-desc">Machine Learning, Synapse</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🟡</span>
                                <h3 class="feature-title">Google Cloud</h3>
                                <p class="feature-desc">BigQuery, Vertex AI</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" data-slide="24">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">24 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">⚡</span>
                        实时数据处理
                    </h1>
                    <div class="slide-subtitle">流式数据分析技术</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">🌊</span>
                            流处理框架介绍
                        </h2>
                        <div class="text-content">
                            <p>实时数据处理让我们能够即时响应数据变化，做出快速决策。</p>
                        </div>
                        <div class="highlight-box">
                            <h3>🔧 主要技术栈</h3>
                            <ul>
                                <li>Apache Kafka - 分布式流处理平台</li>
                                <li>Apache Spark Streaming - 大规模流处理</li>
                                <li>Apache Flink - 低延迟流处理</li>
                                <li>Redis Streams - 轻量级流处理</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" data-slide="25">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">25 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">🧠</span>
                        深度学习入门
                    </h1>
                    <div class="slide-subtitle">神经网络的魅力</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">🔬</span>
                            TensorFlow与PyTorch
                        </h2>
                        <div class="text-content">
                            <p>深度学习是人工智能的核心技术，让机器具备了类似人类的学习能力。</p>
                        </div>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <span class="feature-icon">🔥</span>
                                <h3 class="feature-title">PyTorch</h3>
                                <p class="feature-desc">动态图，研究友好</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">📊</span>
                                <h3 class="feature-title">TensorFlow</h3>
                                <p class="feature-desc">生产部署，生态完善</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" data-slide="26">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">26 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">💬</span>
                        自然语言处理
                    </h1>
                    <div class="slide-subtitle">让机器理解人类语言</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">📝</span>
                            文本分析技术
                        </h2>
                        <div class="text-content">
                            <p>自然语言处理让机器能够理解、分析和生成人类语言。</p>
                        </div>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <span class="feature-icon">🔤</span>
                                <h3 class="feature-title">文本预处理</h3>
                                <p class="feature-desc">分词、去停用词、词性标注</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">😊</span>
                                <h3 class="feature-title">情感分析</h3>
                                <p class="feature-desc">识别文本情感倾向</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🏷️</span>
                                <h3 class="feature-title">文本分类</h3>
                                <p class="feature-desc">自动标注文本类别</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" data-slide="27">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">27 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">👁️</span>
                        计算机视觉
                    </h1>
                    <div class="slide-subtitle">让机器看懂世界</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">📷</span>
                            图像处理与分析
                        </h2>
                        <div class="text-content">
                            <p>计算机视觉让机器能够理解和分析视觉信息，就像人类的眼睛一样。</p>
                        </div>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <span class="feature-icon">🖼️</span>
                                <h3 class="feature-title">图像分类</h3>
                                <p class="feature-desc">识别图像中的对象</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">📍</span>
                                <h3 class="feature-title">目标检测</h3>
                                <p class="feature-desc">定位图像中的多个对象</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">✂️</span>
                                <h3 class="feature-title">图像分割</h3>
                                <p class="feature-desc">像素级别的图像理解</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" data-slide="28">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">28 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">🎯</span>
                        推荐系统设计
                    </h1>
                    <div class="slide-subtitle">个性化推荐的艺术</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">🔍</span>
                            推荐算法类型
                        </h2>
                        <div class="text-content">
                            <p>推荐系统通过分析用户行为和偏好，为用户提供个性化的内容推荐。</p>
                        </div>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <span class="feature-icon">👥</span>
                                <h3 class="feature-title">协同过滤</h3>
                                <p class="feature-desc">基于用户或物品的相似性</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">📋</span>
                                <h3 class="feature-title">内容推荐</h3>
                                <p class="feature-desc">基于物品特征匹配</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🤖</span>
                                <h3 class="feature-title">深度学习</h3>
                                <p class="feature-desc">神经网络推荐模型</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" data-slide="29">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">29 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">🚀</span>
                        数据科学职业发展
                    </h1>
                    <div class="slide-subtitle">规划你的数据科学之路</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">📈</span>
                            职业发展路径
                        </h2>
                        <div class="text-content">
                            <p>数据科学领域提供了多样化的职业发展机会，选择适合自己的方向很重要。</p>
                        </div>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <span class="feature-icon">📊</span>
                                <h3 class="feature-title">数据分析师</h3>
                                <p class="feature-desc">专注数据洞察和报告</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🔬</span>
                                <h3 class="feature-title">数据科学家</h3>
                                <p class="feature-desc">建模预测和算法开发</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">⚙️</span>
                                <h3 class="feature-title">数据工程师</h3>
                                <p class="feature-desc">数据基础设施建设</p>
                            </div>
                        </div>
                        <div class="highlight-box">
                            <h3>💡 技能发展建议</h3>
                            <ul>
                                <li>技术技能 - 持续学习新技术和工具</li>
                                <li>业务理解 - 深入了解行业和业务逻辑</li>
                                <li>沟通能力 - 将技术结果转化为业务语言</li>
                                <li>项目管理 - 端到端项目执行能力</li>
                                <li>终身学习 - 保持对新技术的敏感度</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" data-slide="30">
            <div class="slide-content">
                <div class="slide-header">
                    <div class="slide-number">30 / 30</div>
                    <h1 class="slide-title">
                        <span class="slide-emoji">🎓</span>
                        课程总结与展望
                    </h1>
                    <div class="slide-subtitle">数据科学之旅的新起点</div>
                </div>
                <div class="slide-body">
                    <div class="content-section">
                        <h2 class="section-title">
                            <span class="section-icon">✨</span>
                            学习成果回顾
                        </h2>
                        <div class="text-content">
                            <p>恭喜你完成了这个完整的Python数据分析与AI辅助编程课程！</p>
                        </div>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <span class="feature-icon">🐍</span>
                                <h3 class="feature-title">Python基础</h3>
                                <p class="feature-desc">掌握数据分析核心库</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">📊</span>
                                <h3 class="feature-title">数据处理</h3>
                                <p class="feature-desc">完整的数据分析流程</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🤖</span>
                                <h3 class="feature-title">AI辅助</h3>
                                <p class="feature-desc">高效的编程工作流</p>
                            </div>
                            <div class="feature-card">
                                <span class="feature-icon">🚀</span>
                                <h3 class="feature-title">项目实战</h3>
                                <p class="feature-desc">端到端项目经验</p>
                            </div>
                        </div>
                        <div class="highlight-box">
                            <h3>🌟 下一步学习建议</h3>
                            <ul>
                                <li>深入专业领域 - 选择感兴趣的方向深入学习</li>
                                <li>参与开源项目 - 贡献代码，积累经验</li>
                                <li>建立作品集 - 展示你的项目和技能</li>
                                <li>持续实践 - 用真实数据解决实际问题</li>
                                <li>社区参与 - 加入数据科学社区交流</li>
                            </ul>
                        </div>
                        <div
                            style="text-align: center; margin-top: 50px; padding: 30px; background: rgba(255, 215, 0, 0.2); border-radius: 15px; border: 2px solid #FFD700;">
                            <h2 style="margin-bottom: 20px; color: #FFD700;">🎉 恭喜完成课程！</h2>
                            <p style="font-size: 18px; margin-bottom: 15px;">你已经掌握了Python数据分析和AI辅助编程的核心技能</p>
                            <p style="font-size: 16px;">现在是时候将这些知识应用到实际项目中，开启你的数据科学职业生涯！</p>
                            <div style="margin-top: 25px;">
                                <span style="font-size: 24px;">🚀 数据改变世界，你改变数据！</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" id="prevBtn" onclick="changeSlide(-1)">
            <span>←</span> 上一页
        </button>
        <div class="slide-counter">
            <span id="currentSlide">1</span> / 30
        </div>
        <button class="nav-btn" id="nextBtn" onclick="changeSlide(1)">
            下一页 <span>→</span>
        </button>
    </div>

    <script>
        let currentSlideIndex = 1;
        const totalSlides = 30;

        function updateProgressBar() {
            const progress = (currentSlideIndex / totalSlides) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
        }

        function updateNavigation() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const slideCounter = document.getElementById('currentSlide');

            prevBtn.disabled = currentSlideIndex === 1;
            nextBtn.disabled = currentSlideIndex === totalSlides;
            slideCounter.textContent = currentSlideIndex;

            updateProgressBar();
        }

        function changeSlide(direction) {
            const newIndex = currentSlideIndex + direction;

            if (newIndex < 1 || newIndex > totalSlides) return;

            const currentSlide = document.querySelector(`.slide[data-slide="${currentSlideIndex}"]`);
            const nextSlide = document.querySelector(`.slide[data-slide="${newIndex}"]`);

            if (!nextSlide) return;

            currentSlide.classList.remove('active');
            nextSlide.classList.add('active');

            currentSlideIndex = newIndex;
            updateNavigation();
        }

        // 键盘导航
        document.addEventListener('keydown', function (e) {
            if (e.key === 'ArrowLeft') {
                changeSlide(-1);
            } else if (e.key === 'ArrowRight') {
                changeSlide(1);
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function () {
            updateNavigation();
            // 初始化语法高亮
            hljs.highlightAll();
        });

        // 触摸滑动支持
        let touchStartX = 0;
        let touchEndX = 0;

        document.addEventListener('touchstart', function (e) {
            touchStartX = e.changedTouches[0].screenX;
        });

        document.addEventListener('touchend', function (e) {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        });

        function handleSwipe() {
            const swipeThreshold = 50;
            const diff = touchStartX - touchEndX;

            if (Math.abs(diff) > swipeThreshold) {
                if (diff > 0) {
                    changeSlide(1);
                } else {
                    changeSlide(-1);
                }
            }
        }
    </script>
</body>

</html>