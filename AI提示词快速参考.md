# 🚀 AI编程学习提示词快速参考

## 📋 基础模板

### 🎓 概念学习
```
请详细解释Python中的[概念]：
1. 定义和原理
2. 使用场景
3. 代码示例（3个，由浅入深）
4. 常见错误
5. 最佳实践

我的水平：[初学者/中级/高级]
重点关注：[具体方面]
```

### 🔍 对比分析
```
请对比[概念A]和[概念B]：
1. 异同点
2. 优缺点
3. 使用场景
4. 代码示例
5. 选择建议

用表格总结差异。
```

### 💻 代码生成
```
请编写[功能]的Python代码：

需求：
- [需求1]
- [需求2]

要求：
1. 完整异常处理
2. 中文注释
3. PEP 8规范
4. 使用示例
5. 测试用例

风格：[简洁/详细/企业级]
```

### 🔧 代码审查
```
请审查以下代码：

```python
[代码]
```

分析角度：
1. 异常处理
2. 可读性
3. 性能
4. 安全性
5. 最佳实践

提供改进版本。
```

### 🐛 错误诊断
```
Python错误求助：

错误信息：
```
[错误信息]
```

代码：
```python
[代码]
```

情况：
- 期望：[期望结果]
- 实际：[实际结果]
- 触发：[触发条件]

需要：
1. 原因分析
2. 解决方案
3. 预防方法
4. 最佳实践
```

### 📝 练习生成
```
为[知识点]设计练习：

难度：[初级/中级/高级]
数量：[数量]
类型：[选择/编程/调试]

要求：
1. 详细解析
2. 多种方案
3. 常见错误
4. 扩展思考

考查：[技能点]
```

## 🎯 专项模板

### 🚀 性能优化
```
优化以下代码性能：

```python
[代码]
```

问题：
- 执行时间：[时间]
- 内存使用：[情况]
- 瓶颈：[位置]

目标：[优化目标]

提供：分析+方案+代码+对比+注意事项
```

### 📚 学习规划
```
制定[领域]学习计划：

现状：
- 已掌握：[技能]
- 薄弱点：[问题]
- 目标：[水平]

时间：
- 可用：[时间]
- 期限：[日期]

偏好：[理论/实践]

需要：路线图+阶段目标+资源+项目+检查方法
```

### 🎪 项目指导
```
[项目类型]项目指导：

需求：[项目需求]
技术栈：[技术选择]
难点：[预期困难]

指导内容：
1. 架构设计
2. 关键技术点
3. 异常处理策略
4. 测试方案
5. 部署建议

我的水平：[水平描述]
```

## 💡 高效提问技巧

### ✅ 好的提问
- **具体明确**：提供完整上下文
- **分步骤**：复杂问题分解
- **有背景**：说明技能水平
- **明目标**：清楚表达期望
- **可迭代**：基于回答深入

### ❌ 避免的问题
- 过于宽泛
- 缺乏上下文
- 只要答案不要理解
- 不提供错误信息
- 一次问太多

## 🔄 学习流程

```
理论学习 → 代码实践 → 遇到问题 → 寻求帮助 → 代码审查 → 知识巩固 → 项目应用
```

## 📱 快速示例

### 异常处理学习
```
请详细解释Python异常处理：
1. 定义和原理
2. 使用场景
3. 代码示例（3个，由浅入深）
4. 常见错误
5. 与断言的区别

我的水平：中级
重点关注：如何设计健壮的异常处理架构
```

### 代码调试
```
Python错误求助：

错误信息：
```
FileNotFoundError: [Errno 2] No such file or directory: 'data.txt'
```

代码：
```python
with open('data.txt', 'r') as f:
    content = f.read()
```

情况：
- 期望：读取文件内容
- 实际：文件不存在错误
- 触发：运行时

需要：
1. 原因分析
2. 解决方案
3. 预防方法
4. 最佳实践
```

### 性能优化
```
优化以下代码性能：

```python
result = []
for i in range(1000000):
    if i % 2 == 0:
        result.append(i * 2)
```

问题：
- 执行时间：较慢
- 内存使用：较高
- 瓶颈：循环和列表操作

目标：提升执行速度，减少内存使用

提供：分析+方案+代码+对比+注意事项
```

## 🛠️ 工具推荐

### 对话式AI
- ChatGPT/GPT-4
- Claude
- Gemini
- 文心一言

### 编程助手
- GitHub Copilot
- Cursor
- Tabnine
- CodeWhisperer

## 📈 进阶技巧

1. **上下文管理**：在对话中保持上下文连贯
2. **角色设定**：让AI扮演特定角色（导师、代码审查员等）
3. **格式要求**：明确指定输出格式
4. **示例驱动**：提供期望输出的示例
5. **迭代优化**：基于结果不断优化提示词

## 🎯 验证学习效果

- **教学测试**：向AI解释概念
- **代码挑战**：要求出题测试
- **项目评估**：审查实际代码
- **面试模拟**：模拟技术面试

---

**提示**：将此文档保存为书签，随时查阅常用模板！
