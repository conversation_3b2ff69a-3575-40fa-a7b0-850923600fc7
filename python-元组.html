<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python元组与元组生成式教学PPT</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #2c1810 0%, #8b4513 50%, #2c1810 100%);
            overflow: hidden;
            height: 100vh;
            color: #333;
        }

        .ppt-container {
            position: relative;
            width: 100%;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slide {
            position: absolute;
            width: 90%;
            max-width: 1200px;
            height: 85vh;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
            display: none;
            opacity: 0;
            transform: translateX(100px);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .slide:first-child {
            display: block;
            opacity: 1;
            transform: translateX(0);
        }

        .slide.active {
            display: block !important;
            opacity: 1 !important;
            transform: translateX(0) !important;
        }

        .slide-content {
            padding: 40px;
            height: 100%;
            overflow-y: auto;
            text-align: left;
        }

        .slide-content::-webkit-scrollbar {
            width: 8px;
        }

        .slide-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .slide-content::-webkit-scrollbar-thumb {
            background: #8b4513;
            border-radius: 4px;
        }

        .slide-content::-webkit-scrollbar-thumb:hover {
            background: #654321;
        }

        .title-slide h1 {
            font-size: 3.5em;
            color: #2c1810;
            margin-bottom: 30px;
            text-align: center;
            animation: titleSlideIn 0.8s ease-out;
        }

        h1 {
            font-size: 2.8em;
            color: #2c1810;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            animation: titleSlideIn 0.6s ease-out;
        }

        h2 {
            font-size: 2.2em;
            color: #8b4513;
            margin-bottom: 25px;
            text-align: left;
            border-bottom: 3px solid #d2691e;
            padding-bottom: 10px;
            animation: fadeInUp 0.4s ease-out 0.1s both;
        }

        h3 {
            font-size: 1.6em;
            color: #654321;
            margin-bottom: 20px;
            animation: fadeInUp 0.4s ease-out 0.2s both;
        }

        p, li {
            font-size: 1.2em;
            line-height: 1.8;
            margin-bottom: 15px;
            color: #333;
            animation: fadeInUp 0.4s ease-out 0.3s both;
        }

        ul, ol {
            margin-left: 30px;
            margin-bottom: 25px;
        }

        .emoji {
            font-size: 1.5em;
            margin-right: 10px;
            animation: bounce 1.5s infinite;
        }

        /* 代码块样式 - 黑色背景 */
        .code-block {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 1.1em;
            line-height: 1.6;
            border-left: 5px solid #d2691e;
            animation: fadeInLeft 0.5s ease-out 0.25s both;
            overflow-x: auto;
        }

        .code-line {
            display: block;
            margin-bottom: 5px;
            animation: typewriter 0.6s ease-out;
            text-align: left;
        }

        /* Python语法高亮 */
        .keyword { color: #569cd6; font-weight: bold; }
        .string { color: #ce9178; }
        .number { color: #b5cea8; }
        .comment { color: #6a9955; font-style: italic; }
        .function { color: #dcdcaa; }
        .variable { color: #9cdcfe; }
        .operator { color: #d4d4d4; }
        .bracket { color: #ffd700; }

        .highlight-box {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            border-left: 5px solid #ff8f00;
            text-align: left;
            animation: fadeInUp 0.4s ease-out 0.2s both;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            animation: fadeInUp 0.4s ease-out 0.3s both;
        }

        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }

        .comparison-table th {
            background: #8b4513;
            color: white;
        }

        .comparison-table tr:nth-child(even) {
            background: #f9f9f9;
        }

        .ai-tool {
            background: linear-gradient(135deg, #2c1810 0%, #8b4513 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: left;
            animation: fadeInUp 0.4s ease-out 0.2s both;
        }

        .prompt-template {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            border-left: 4px solid #d2691e;
            font-family: 'Consolas', monospace;
            animation: fadeInUp 0.4s ease-out 0.3s both;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            color: #2c1810;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .page-indicator {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: rgba(44, 24, 16, 0.9);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 1001;
        }

        .slide-number {
            position: absolute;
            top: 20px;
            right: 30px;
            background: rgba(44, 24, 16, 0.8);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
        }

        /* 动画效果 */
        @keyframes titleSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        @keyframes typewriter {
            from {
                width: 0;
                opacity: 0;
            }
            to {
                width: 100%;
                opacity: 1;
            }
        }

        .tutorial-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #d2691e;
            animation: fadeInUp 0.4s ease-out 0.2s both;
        }

        .best-practice {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            border-left: 5px solid #4caf50;
            animation: fadeInUp 0.4s ease-out 0.3s both;
        }

        .warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            border-left: 5px solid #ffc107;
            animation: fadeInUp 0.4s ease-out 0.3s both;
        }

        .performance-chart {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            text-align: center;
            animation: fadeInUp 0.4s ease-out 0.4s both;
        }
    </style>
</head>
<body>
    <div class="ppt-container">
        <!-- 第1页：封面页 -->
        <div class="slide title-slide">
            <div class="slide-content">
                <div class="slide-number">1/30</div>
                <h1><span class="emoji">📦</span>Python元组与元组生成式</h1>
                <div style="text-align: center; margin-top: 60px;">
                    <h2>掌握Python中最重要的不可变序列类型</h2>
                    <div class="highlight-box">
                        <h3>课程亮点</h3>
                        <ul>
                            <li><span class="emoji">🎯</span>从基础概念到高级应用</li>
                            <li><span class="emoji">⚡</span>元组生成式的优雅写法</li>
                            <li><span class="emoji">🤖</span>AI辅助学习方法</li>
                            <li><span class="emoji">💡</span>实际项目应用案例</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第2页：课程概览 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">2/30</div>
                <h1><span class="emoji">📚</span>课程概览</h1>
                
                <h2>学习路径</h2>
                <div class="tutorial-card">
                    <h3>第一阶段：基础认知</h3>
                    <ul>
                        <li>元组的定义与特性</li>
                        <li>创建和访问元组</li>
                        <li>元组 vs 列表的区别</li>
                    </ul>
                </div>

                <div class="tutorial-card">
                    <h3>第二阶段：操作技巧</h3>
                    <ul>
                        <li>元组解包与多重赋值</li>
                        <li>嵌套元组处理</li>
                        <li>元组作为函数参数和返回值</li>
                    </ul>
                </div>

                <div class="tutorial-card">
                    <h3>第三阶段：高级应用</h3>
                    <ul>
                        <li>命名元组的威力</li>
                        <li>元组生成式精通</li>
                        <li>性能优化与最佳实践</li>
                    </ul>
                </div>

                <div class="tutorial-card">
                    <h3>第四阶段：AI辅助学习</h3>
                    <ul>
                        <li>编程助手工具使用</li>
                        <li>提示词工程技巧</li>
                        <li>持续学习方法</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第3页：什么是元组 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">3/30</div>
                <h1><span class="emoji">🤔</span>什么是元组？</h1>
                
                <h2>基本定义</h2>
                <p>元组（tuple）是Python中的一种<strong>不可变序列类型</strong>，用于存储多个有序的元素。</p>

                <div class="code-block">
                    <span class="code-line"><span class="comment"># 创建元组的几种方式</span></span>
                    <span class="code-line"><span class="variable">empty_tuple</span> <span class="operator">=</span> <span class="bracket">()</span></span>
                    <span class="code-line"><span class="variable">single_tuple</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">42</span>,<span class="bracket">)</span>  <span class="comment"># 注意逗号</span></span>
                    <span class="code-line"><span class="variable">coordinates</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">3</span>, <span class="number">4</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="variable">colors</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"red"</span>, <span class="string">"green"</span>, <span class="string">"blue"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="variable">mixed_tuple</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">1</span>, <span class="string">"hello"</span>, <span class="number">3.14</span>, <span class="keyword">True</span><span class="bracket">)</span></span>
                </div>

                <div class="highlight-box">
                    <h3><span class="emoji">💡</span>关键特性</h3>
                    <ul>
                        <li><strong>有序性</strong>：元素有固定的位置和顺序</li>
                        <li><strong>不可变性</strong>：创建后不能修改元素</li>
                        <li><strong>可重复</strong>：允许包含重复的元素</li>
                        <li><strong>异构性</strong>：可以包含不同类型的数据</li>
                    </ul>
                </div>

                <div class="code-block">
                    <span class="code-line"><span class="comment"># 验证元组的基本特性</span></span>
                    <span class="code-line"><span class="variable">point</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">10</span>, <span class="number">20</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="function">type</span><span class="bracket">(</span><span class="variable">point</span><span class="bracket">))</span>  <span class="comment"># &lt;class 'tuple'&gt;</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="function">len</span><span class="bracket">(</span><span class="variable">point</span><span class="bracket">))</span>   <span class="comment"># 2</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">point</span><span class="bracket">[</span><span class="number">0</span><span class="bracket">])</span>    <span class="comment"># 10</span></span>
                </div>
            </div>
        </div>

        <!-- 第4页：元组的特性 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">4/30</div>
                <h1><span class="emoji">⭐</span>元组的核心特性</h1>
                
                <h2>1. 不可变性（Immutability）</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">coordinates</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">3</span>, <span class="number">4</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="comment"># coordinates[0] = 5  # TypeError: 不支持修改</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 但可以重新赋值整个元组</span></span>
                    <span class="code-line"><span class="variable">coordinates</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">5</span>, <span class="number">6</span><span class="bracket">)</span>  <span class="comment"># 这是创建新元组</span></span>
                </div>

                <h2>2. 可哈希性（Hashable）</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 元组可以作为字典的键</span></span>
                    <span class="code-line"><span class="variable">locations</span> <span class="operator">=</span> <span class="bracket">{</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="number">0</span>, <span class="number">0</span><span class="bracket">)</span>: <span class="string">"原点"</span>,</span>
                    <span class="code-line">    <span class="bracket">(</span><span class="number">1</span>, <span class="number">1</span><span class="bracket">)</span>: <span class="string">"右上"</span>,</span>
                    <span class="code-line">    <span class="bracket">(</span><span class="number">-1</span>, <span class="number">-1</span><span class="bracket">)</span>: <span class="string">"左下"</span></span>
                    <span class="code-line"><span class="bracket">}</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 元组可以放入集合</span></span>
                    <span class="code-line"><span class="variable">points_set</span> <span class="operator">=</span> <span class="bracket">{(</span><span class="number">1</span>,<span class="number">2</span><span class="bracket">)</span>, <span class="bracket">(</span><span class="number">3</span>,<span class="number">4</span><span class="bracket">)</span>, <span class="bracket">(</span><span class="number">1</span>,<span class="number">2</span><span class="bracket">)}</span>  <span class="comment"># 自动去重</span></span>
                </div>

                <h2>3. 内存效率</h2>
                <div class="performance-chart">
                    <h3><span class="emoji">📊</span>内存占用对比</h3>
                    <div class="code-block">
                        <span class="code-line"><span class="keyword">import</span> <span class="variable">sys</span></span>
                        <span class="code-line"></span>
                        <span class="code-line"><span class="variable">tuple_data</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span><span class="bracket">)</span></span>
                        <span class="code-line"><span class="variable">list_data</span> <span class="operator">=</span> <span class="bracket">[</span><span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span><span class="bracket">]</span></span>
                        <span class="code-line"></span>
                        <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">sys</span>.<span class="function">getsizeof</span><span class="bracket">(</span><span class="variable">tuple_data</span><span class="bracket">))</span>  <span class="comment"># 更小的内存占用</span></span>
                        <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">sys</span>.<span class="function">getsizeof</span><span class="bracket">(</span><span class="variable">list_data</span><span class="bracket">))</span>   <span class="comment"># 更大的内存占用</span></span>
                    </div>
                </div>

                <div class="best-practice">
                    <h3><span class="emoji">✅</span>何时使用元组？</h3>
                    <ul>
                        <li>存储不会改变的相关数据（如坐标、配置信息）</li>
                        <li>函数需要返回多个值时</li>
                        <li>作为字典的键或集合的元素</li>
                        <li>性能要求较高的场景</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第5页：创建元组 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">5/30</div>
                <h1><span class="emoji">🔨</span>创建元组的多种方式</h1>
                
                <h2>1. 使用圆括号</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 标准语法</span></span>
                    <span class="code-line"><span class="variable">empty</span> <span class="operator">=</span> <span class="bracket">()</span></span>
                    <span class="code-line"><span class="variable">numbers</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="variable">single</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">42</span>,<span class="bracket">)</span>  <span class="comment"># 单元素元组必须有逗号</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 多行定义（提高可读性）</span></span>
                    <span class="code-line"><span class="variable">config</span> <span class="operator">=</span> <span class="bracket">(</span></span>
                    <span class="code-line">    <span class="string">"database_host"</span>,</span>
                    <span class="code-line">    <span class="number">5432</span>,</span>
                    <span class="code-line">    <span class="string">"production"</span>,</span>
                    <span class="code-line">    <span class="keyword">True</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                </div>

                <h2>2. 不使用圆括号</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 元组打包（tuple packing）</span></span>
                    <span class="code-line"><span class="variable">point</span> <span class="operator">=</span> <span class="number">10</span>, <span class="number">20</span>  <span class="comment"># 自动创建元组</span></span>
                    <span class="code-line"><span class="variable">rgb</span> <span class="operator">=</span> <span class="number">255</span>, <span class="number">128</span>, <span class="number">0</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="function">type</span><span class="bracket">(</span><span class="variable">point</span><span class="bracket">))</span>  <span class="comment"># &lt;class 'tuple'&gt;</span></span>
                </div>

                <h2>3. 使用 tuple() 构造函数</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 从其他可迭代对象创建</span></span>
                    <span class="code-line"><span class="variable">from_list</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">([</span><span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span><span class="bracket">])</span></span>
                    <span class="code-line"><span class="variable">from_string</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span><span class="string">"hello"</span><span class="bracket">)</span>  <span class="comment"># ('h', 'e', 'l', 'l', 'o')</span></span>
                    <span class="code-line"><span class="variable">from_range</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span><span class="function">range</span><span class="bracket">(</span><span class="number">5</span><span class="bracket">))</span>  <span class="comment"># (0, 1, 2, 3, 4)</span></span>
                    <span class="code-line"><span class="variable">from_set</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">({</span><span class="number">3</span>, <span class="number">1</span>, <span class="number">4</span>, <span class="number">1</span><span class="bracket">})</span>  <span class="comment"># 注意：集合会去重和排序</span></span>
                </div>

                <div class="warning">
                    <h3><span class="emoji">⚠️</span>常见陷阱</h3>
                    <div class="code-block">
                        <span class="code-line"><span class="comment"># 错误：单元素元组</span></span>
                        <span class="code-line"><span class="variable">not_tuple</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">42</span><span class="bracket">)</span>   <span class="comment"># 这只是括号，不是元组！</span></span>
                        <span class="code-line"><span class="variable">is_tuple</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">42</span>,<span class="bracket">)</span>  <span class="comment"># 正确：需要逗号</span></span>
                        <span class="code-line"></span>
                        <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="function">type</span><span class="bracket">(</span><span class="variable">not_tuple</span><span class="bracket">))</span>  <span class="comment"># &lt;class 'int'&gt;</span></span>
                        <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="function">type</span><span class="bracket">(</span><span class="variable">is_tuple</span><span class="bracket">))</span>   <span class="comment"># &lt;class 'tuple'&gt;</span></span>
                    </div>
                </div>

                <h2>4. 特殊场景的创建</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 嵌套元组</span></span>
                    <span class="code-line"><span class="variable">matrix</span> <span class="operator">=</span> <span class="bracket">((</span><span class="number">1</span>, <span class="number">2</span><span class="bracket">)</span>, <span class="bracket">(</span><span class="number">3</span>, <span class="number">4</span><span class="bracket">)</span>, <span class="bracket">(</span><span class="number">5</span>, <span class="number">6</span><span class="bracket">))</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 混合数据类型</span></span>
                    <span class="code-line"><span class="variable">person</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"张三"</span>, <span class="number">25</span>, <span class="string">"工程师"</span>, <span class="keyword">True</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 从生成器创建</span></span>
                    <span class="code-line"><span class="variable">squares</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span><span class="variable">x</span><span class="operator">**</span><span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="number">5</span><span class="bracket">))</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">squares</span><span class="bracket">)</span>  <span class="comment"># (0, 1, 4, 9, 16)</span></span>
                </div>
            </div>
        </div>

        <!-- 第6页：元组 vs 列表 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">6/30</div>
                <h1><span class="emoji">⚖️</span>元组 vs 列表对比</h1>
                
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>特性</th>
                            <th>元组 (tuple)</th>
                            <th>列表 (list)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>可变性</strong></td>
                            <td>不可变 (immutable)</td>
                            <td>可变 (mutable)</td>
                        </tr>
                        <tr>
                            <td><strong>语法</strong></td>
                            <td>(1, 2, 3)</td>
                            <td>[1, 2, 3]</td>
                        </tr>
                        <tr>
                            <td><strong>性能</strong></td>
                            <td>更快，内存效率高</td>
                            <td>较慢，内存开销大</td>
                        </tr>
                        <tr>
                            <td><strong>哈希性</strong></td>
                            <td>可哈希，可作字典键</td>
                            <td>不可哈希</td>
                        </tr>
                        <tr>
                            <td><strong>方法数量</strong></td>
                            <td>2个（count, index）</td>
                            <td>11个（append, extend等）</td>
                        </tr>
                    </tbody>
                </table>

                <h2>性能对比演示</h2>
                <div class="code-block">
                    <span class="code-line"><span class="keyword">import</span> <span class="variable">timeit</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 创建大量数据的性能测试</span></span>
                    <span class="code-line"><span class="variable">tuple_time</span> <span class="operator">=</span> <span class="variable">timeit</span>.<span class="function">timeit</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="keyword">lambda</span>: <span class="function">tuple</span><span class="bracket">(</span><span class="function">range</span><span class="bracket">(</span><span class="number">1000</span><span class="bracket">))</span>,</span>
                    <span class="code-line">    <span class="variable">number</span><span class="operator">=</span><span class="number">10000</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="variable">list_time</span> <span class="operator">=</span> <span class="variable">timeit</span>.<span class="function">timeit</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="keyword">lambda</span>: <span class="function">list</span><span class="bracket">(</span><span class="function">range</span><span class="bracket">(</span><span class="number">1000</span><span class="bracket">))</span>,</span>
                    <span class="code-line">    <span class="variable">number</span><span class="operator">=</span><span class="number">10000</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"元组创建时间: </span><span class="bracket">{</span><span class="variable">tuple_time</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"列表创建时间: </span><span class="bracket">{</span><span class="variable">list_time</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>

                <div class="best-practice">
                    <h3><span class="emoji">📋</span>选择原则</h3>
                    <ul>
                        <li><strong>选择元组</strong>：数据不变、需要哈希、性能优先</li>
                        <li><strong>选择列表</strong>：需要修改、动态增删、方法丰富</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第7页：元组的索引访问 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">7/30</div>
                <h1><span class="emoji">📍</span>元组的索引访问</h1>
                
                <h2>正向索引</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">colors</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"红"</span>, <span class="string">"绿"</span>, <span class="string">"蓝"</span>, <span class="string">"黄"</span>, <span class="string">"紫"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="comment">#           0     1     2     3     4</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">colors</span><span class="bracket">[</span><span class="number">0</span><span class="bracket">])</span>   <span class="comment"># "红"</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">colors</span><span class="bracket">[</span><span class="number">2</span><span class="bracket">])</span>   <span class="comment"># "蓝"</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">colors</span><span class="bracket">[</span><span class="number">4</span><span class="bracket">])</span>   <span class="comment"># "紫"</span></span>
                </div>

                <h2>负向索引</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">colors</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"红"</span>, <span class="string">"绿"</span>, <span class="string">"蓝"</span>, <span class="string">"黄"</span>, <span class="string">"紫"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="comment">#          -5    -4    -3    -2    -1</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">colors</span><span class="bracket">[</span><span class="number">-1</span><span class="bracket">])</span>  <span class="comment"># "紫" (最后一个)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">colors</span><span class="bracket">[</span><span class="number">-2</span><span class="bracket">])</span>  <span class="comment"># "黄" (倒数第二个)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">colors</span><span class="bracket">[</span><span class="number">-5</span><span class="bracket">])</span>  <span class="comment"># "红" (第一个)</span></span>
                </div>

                <h2>嵌套元组索引</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">matrix</span> <span class="operator">=</span> <span class="bracket">(</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span><span class="bracket">)</span>,</span>
                    <span class="code-line">    <span class="bracket">(</span><span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span><span class="bracket">)</span>,</span>
                    <span class="code-line">    <span class="bracket">(</span><span class="number">7</span>, <span class="number">8</span>, <span class="number">9</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">matrix</span><span class="bracket">[</span><span class="number">0</span><span class="bracket">])</span>      <span class="comment"># (1, 2, 3)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">matrix</span><span class="bracket">[</span><span class="number">1</span><span class="bracket">][</span><span class="number">2</span><span class="bracket">])</span>   <span class="comment"># 6</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">matrix</span><span class="bracket">[</span><span class="number">-1</span><span class="bracket">][</span><span class="number">-1</span><span class="bracket">])</span>  <span class="comment"># 9 (最后一行最后一列)</span></span>
                </div>

                <div class="warning">
                    <h3><span class="emoji">⚠️</span>索引错误处理</h3>
                    <div class="code-block">
                        <span class="code-line"><span class="variable">numbers</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span><span class="bracket">)</span></span>
                        <span class="code-line"></span>
                        <span class="code-line"><span class="keyword">try</span>:</span>
                        <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="variable">numbers</span><span class="bracket">[</span><span class="number">10</span><span class="bracket">])</span>  <span class="comment"># 索引超出范围</span></span>
                        <span class="code-line"><span class="keyword">except</span> <span class="variable">IndexError</span> <span class="keyword">as</span> <span class="variable">e</span>:</span>
                        <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"索引错误: </span><span class="bracket">{</span><span class="variable">e</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第8页：元组切片操作 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">8/30</div>
                <h1><span class="emoji">✂️</span>元组切片操作</h1>
                
                <h2>基本切片语法</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">data</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">0</span>, <span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>, <span class="number">7</span>, <span class="number">8</span>, <span class="number">9</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 基本切片：[开始:结束:步长]</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">data</span><span class="bracket">[</span><span class="number">2</span>:<span class="number">7</span><span class="bracket">])</span>     <span class="comment"># (2, 3, 4, 5, 6)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">data</span><span class="bracket">[</span>:<span class="number">5</span><span class="bracket">])</span>      <span class="comment"># (0, 1, 2, 3, 4)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">data</span><span class="bracket">[</span><span class="number">5</span>:<span class="bracket">])</span>      <span class="comment"># (5, 6, 7, 8, 9)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">data</span><span class="bracket">[:])</span>       <span class="comment"># (0, 1, 2, 3, 4, 5, 6, 7, 8, 9)</span></span>
                </div>

                <h2>步长切片</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 每隔一个取值</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">data</span><span class="bracket">[</span>::<span class="number">2</span><span class="bracket">])</span>     <span class="comment"># (0, 2, 4, 6, 8)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 每隔两个取值</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">data</span><span class="bracket">[</span><span class="number">1</span>::<span class="number">3</span><span class="bracket">])</span>   <span class="comment"># (1, 4, 7)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 反向切片</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">data</span><span class="bracket">[</span>::<span class="number">-1</span><span class="bracket">])</span>    <span class="comment"># (9, 8, 7, 6, 5, 4, 3, 2, 1, 0)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">data</span><span class="bracket">[</span><span class="number">-3</span>:<span class="number">-1</span><span class="bracket">])</span>   <span class="comment"># (7, 8)</span></span>
                </div>

                <h2>实用切片模式</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">sentence</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"Python"</span>, <span class="string">"是"</span>, <span class="string">"一门"</span>, <span class="string">"优秀的"</span>, <span class="string">"编程"</span>, <span class="string">"语言"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 获取前三个词</span></span>
                    <span class="code-line"><span class="variable">first_three</span> <span class="operator">=</span> <span class="variable">sentence</span><span class="bracket">[</span>:<span class="number">3</span><span class="bracket">]</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">first_three</span><span class="bracket">)</span>  <span class="comment"># ('Python', '是', '一门')</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 获取最后两个词</span></span>
                    <span class="code-line"><span class="variable">last_two</span> <span class="operator">=</span> <span class="variable">sentence</span><span class="bracket">[</span><span class="number">-2</span>:<span class="bracket">]</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">last_two</span><span class="bracket">)</span>    <span class="comment"># ('编程', '语言')</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 中间部分</span></span>
                    <span class="code-line"><span class="variable">middle</span> <span class="operator">=</span> <span class="variable">sentence</span><span class="bracket">[</span><span class="number">2</span>:<span class="number">-2</span><span class="bracket">]</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">middle</span><span class="bracket">)</span>       <span class="comment"># ('一门', '优秀的')</span></span>
                </div>

                <div class="highlight-box">
                    <h3><span class="emoji">💡</span>切片返回新元组</h3>
                    <p>切片操作总是返回一个新的元组对象，不会修改原始元组。</p>
                    <div class="code-block">
                        <span class="code-line"><span class="variable">original</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span><span class="bracket">)</span></span>
                        <span class="code-line"><span class="variable">sliced</span> <span class="operator">=</span> <span class="variable">original</span><span class="bracket">[</span><span class="number">1</span>:<span class="number">4</span><span class="bracket">]</span></span>
                        <span class="code-line"></span>
                        <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="function">id</span><span class="bracket">(</span><span class="variable">original</span><span class="bracket">))</span>  <span class="comment"># 不同的内存地址</span></span>
                        <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="function">id</span><span class="bracket">(</span><span class="variable">sliced</span><span class="bracket">))</span>    <span class="comment"># 不同的内存地址</span></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第9页：元组的不可变性 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">9/30</div>
                <h1><span class="emoji">🔒</span>元组的不可变性</h1>
                
                <h2>不能修改元素</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">point</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">10</span>, <span class="number">20</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 以下操作都会报错</span></span>
                    <span class="code-line"><span class="comment"># point[0] = 30        # TypeError</span></span>
                    <span class="code-line"><span class="comment"># point.append(30)     # AttributeError</span></span>
                    <span class="code-line"><span class="comment"># del point[0]         # TypeError</span></span>
                </div>

                <h2>可以重新赋值</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">coordinates</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">1</span>, <span class="number">2</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"原始: </span><span class="bracket">{</span><span class="variable">coordinates</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 重新赋值（创建新元组）</span></span>
                    <span class="code-line"><span class="variable">coordinates</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">3</span>, <span class="number">4</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"更新: </span><span class="bracket">{</span><span class="variable">coordinates</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>

                <h2>嵌套可变对象的特殊情况</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 元组包含列表时</span></span>
                    <span class="code-line"><span class="variable">data</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">1</span>, <span class="bracket">[</span><span class="number">2</span>, <span class="number">3</span><span class="bracket">]</span>, <span class="number">4</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 不能修改元组结构</span></span>
                    <span class="code-line"><span class="comment"># data[1] = [5, 6]     # TypeError</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 但可以修改列表内容</span></span>
                    <span class="code-line"><span class="variable">data</span><span class="bracket">[</span><span class="number">1</span><span class="bracket">]</span>.<span class="function">append</span><span class="bracket">(</span><span class="number">5</span><span class="bracket">)</span>  <span class="comment"># 这是允许的！</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">data</span><span class="bracket">)</span>              <span class="comment"># (1, [2, 3, 5], 4)</span></span>
                </div>

                <div class="warning">
                    <h3><span class="emoji">⚠️</span>不可变的真正含义</h3>
                    <p>元组的不可变性指的是元组的<strong>结构不可变</strong>，即不能更改元组的长度和元素的引用关系。但如果元组包含可变对象，这些对象的内容仍然可以修改。</p>
                </div>

                <h2>创建真正不可变的元组</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 使用 frozenset 或其他不可变类型</span></span>
                    <span class="code-line"><span class="variable">immutable_data</span> <span class="operator">=</span> <span class="bracket">(</span></span>
                    <span class="code-line">    <span class="number">1</span>,</span>
                    <span class="code-line">    <span class="function">frozenset</span><span class="bracket">([</span><span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span><span class="bracket">])</span>,  <span class="comment"># 不可变集合</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="number">5</span>, <span class="number">6</span><span class="bracket">)</span>,                <span class="comment"># 嵌套元组</span></span>
                    <span class="code-line">    <span class="string">"hello"</span>              <span class="comment"># 字符串</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 检查是否可哈希</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="function">hash</span><span class="bracket">(</span><span class="variable">immutable_data</span><span class="bracket">))</span>  <span class="comment"># 可以计算哈希值</span></span>
                </div>

                <div class="best-practice">
                    <h3><span class="emoji">✅</span>不可变性的优势</h3>
                    <ul>
                        <li><strong>线程安全</strong>：多线程环境下无需担心竞态条件</li>
                        <li><strong>缓存友好</strong>：可以安全地缓存哈希值</li>
                        <li><strong>函数式编程</strong>：符合函数式编程的不可变原则</li>
                        <li><strong>调试容易</strong>：数据不会意外改变，便于调试</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第10页：元组解包 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">10/30</div>
                <h1><span class="emoji">📦➡️</span>元组解包</h1>
                
                <h2>基础解包</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 基本解包语法</span></span>
                    <span class="code-line"><span class="variable">point</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">10</span>, <span class="number">20</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="variable">x</span>, <span class="variable">y</span> <span class="operator">=</span> <span class="variable">point</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"x = </span><span class="bracket">{</span><span class="variable">x</span><span class="bracket">}</span><span class="string">, y = </span><span class="bracket">{</span><span class="variable">y</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># x = 10, y = 20</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 无需圆括号</span></span>
                    <span class="code-line"><span class="variable">name</span>, <span class="variable">age</span>, <span class="variable">city</span> <span class="operator">=</span> <span class="string">"张三"</span>, <span class="number">25</span>, <span class="string">"北京"</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">name</span>, <span class="variable">age</span>, <span class="variable">city</span><span class="bracket">)</span></span>
                </div>

                <h2>忽略不需要的值</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">rgb_alpha</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">255</span>, <span class="number">128</span>, <span class="number">0</span>, <span class="number">0.8</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 使用下划线忽略不需要的值</span></span>
                    <span class="code-line"><span class="variable">r</span>, <span class="variable">g</span>, <span class="variable">b</span>, <span class="variable">_</span> <span class="operator">=</span> <span class="variable">rgb_alpha</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"RGB: </span><span class="bracket">{</span><span class="variable">r</span><span class="bracket">}</span><span class="string">, </span><span class="bracket">{</span><span class="variable">g</span><span class="bracket">}</span><span class="string">, </span><span class="bracket">{</span><span class="variable">b</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 只要前两个值</span></span>
                    <span class="code-line"><span class="variable">first</span>, <span class="variable">second</span>, <span class="variable">*_</span> <span class="operator">=</span> <span class="variable">rgb_alpha</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">first</span>, <span class="variable">second</span><span class="bracket">)</span>  <span class="comment"># 255 128</span></span>
                </div>

                <h2>扩展解包（*操作符）</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">numbers</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 获取第一个、最后一个和中间的所有</span></span>
                    <span class="code-line"><span class="variable">first</span>, <span class="variable">*middle</span>, <span class="variable">last</span> <span class="operator">=</span> <span class="variable">numbers</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"第一个: </span><span class="bracket">{</span><span class="variable">first</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>      <span class="comment"># 1</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"中间的: </span><span class="bracket">{</span><span class="variable">middle</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>     <span class="comment"># [2, 3, 4, 5]</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"最后一个: </span><span class="bracket">{</span><span class="variable">last</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>     <span class="comment"># 6</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 获取前三个</span></span>
                    <span class="code-line"><span class="variable">a</span>, <span class="variable">b</span>, <span class="variable">c</span>, <span class="variable">*rest</span> <span class="operator">=</span> <span class="variable">numbers</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">a</span>, <span class="variable">b</span>, <span class="variable">c</span><span class="bracket">)</span>        <span class="comment"># 1 2 3</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">rest</span><span class="bracket">)</span>           <span class="comment"># [4, 5, 6]</span></span>
                </div>

                <h2>嵌套元组解包</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">person_data</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"李明"</span>, <span class="bracket">(</span><span class="number">30</span>, <span class="string">"男"</span><span class="bracket">)</span>, <span class="bracket">(</span><span class="string">"北京"</span>, <span class="string">"朝阳区"</span><span class="bracket">))</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 嵌套解包</span></span>
                    <span class="code-line"><span class="variable">name</span>, <span class="bracket">(</span><span class="variable">age</span>, <span class="variable">gender</span><span class="bracket">)</span>, <span class="bracket">(</span><span class="variable">city</span>, <span class="variable">district</span><span class="bracket">)</span> <span class="operator">=</span> <span class="variable">person_data</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"姓名: </span><span class="bracket">{</span><span class="variable">name</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"年龄: </span><span class="bracket">{</span><span class="variable">age</span><span class="bracket">}</span><span class="string">, 性别: </span><span class="bracket">{</span><span class="variable">gender</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"地址: </span><span class="bracket">{</span><span class="variable">city</span><span class="bracket">}</span><span class="string"></span><span class="bracket">{</span><span class="variable">district</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>

                <div class="highlight-box">
                    <h3><span class="emoji">💡</span>解包的应用场景</h3>
                    <ul>
                        <li><strong>函数返回多值</strong>：<code>x, y = get_coordinates()</code></li>
                        <li><strong>交换变量</strong>：<code>a, b = b, a</code></li>
                        <li><strong>遍历键值对</strong>：<code>for k, v in items:</code></li>
                        <li><strong>处理CSV数据</strong>：解析结构化数据</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第11页：多重赋值 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">11/30</div>
                <h1><span class="emoji">↔️</span>多重赋值</h1>
                
                <h2>基础多重赋值</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 同时给多个变量赋值</span></span>
                    <span class="code-line"><span class="variable">a</span>, <span class="variable">b</span>, <span class="variable">c</span> <span class="operator">=</span> <span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">a</span>, <span class="variable">b</span>, <span class="variable">c</span><span class="bracket">)</span>  <span class="comment"># 1 2 3</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 交换变量的优雅方式</span></span>
                    <span class="code-line"><span class="variable">x</span>, <span class="variable">y</span> <span class="operator">=</span> <span class="number">10</span>, <span class="number">20</span></span>
                    <span class="code-line"><span class="variable">x</span>, <span class="variable">y</span> <span class="operator">=</span> <span class="variable">y</span>, <span class="variable">x</span>  <span class="comment"># 交换完成！</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">x</span>, <span class="variable">y</span><span class="bracket">)</span>  <span class="comment"># 20 10</span></span>
                </div>

                <h2>函数返回多个值</h2>
                <div class="code-block">
                    <span class="code-line"><span class="keyword">def</span> <span class="function">get_user_info</span><span class="bracket">()</span>:</span>
                    <span class="code-line">    <span class="keyword">return</span> <span class="string">"张三"</span>, <span class="number">28</span>, <span class="string">"工程师"</span>  <span class="comment"># 返回元组</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="keyword">def</span> <span class="function">calculate</span><span class="bracket">(</span><span class="variable">a</span>, <span class="variable">b</span><span class="bracket">)</span>:</span>
                    <span class="code-line">    <span class="keyword">return</span> <span class="variable">a</span> <span class="operator">+</span> <span class="variable">b</span>, <span class="variable">a</span> <span class="operator">-</span> <span class="variable">b</span>, <span class="variable">a</span> <span class="operator">*</span> <span class="variable">b</span>, <span class="variable">a</span> <span class="operator">/</span> <span class="variable">b</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 接收多个返回值</span></span>
                    <span class="code-line"><span class="variable">name</span>, <span class="variable">age</span>, <span class="variable">job</span> <span class="operator">=</span> <span class="function">get_user_info</span><span class="bracket">()</span></span>
                    <span class="code-line"><span class="variable">sum_val</span>, <span class="variable">diff</span>, <span class="variable">prod</span>, <span class="variable">quot</span> <span class="operator">=</span> <span class="function">calculate</span><span class="bracket">(</span><span class="number">10</span>, <span class="number">3</span><span class="bracket">)</span></span>
                </div>
            </div>
        </div>

        <!-- 第12页：嵌套元组 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">12/30</div>
                <h1><span class="emoji">🪆</span>嵌套元组</h1>
                
                <h2>二维数据结构</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 矩阵表示</span></span>
                    <span class="code-line"><span class="variable">matrix</span> <span class="operator">=</span> <span class="bracket">(</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span><span class="bracket">)</span>,</span>
                    <span class="code-line">    <span class="bracket">(</span><span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span><span class="bracket">)</span>,</span>
                    <span class="code-line">    <span class="bracket">(</span><span class="number">7</span>, <span class="number">8</span>, <span class="number">9</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 学生信息表</span></span>
                    <span class="code-line"><span class="variable">students</span> <span class="operator">=</span> <span class="bracket">(</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="string">"张三"</span>, <span class="number">20</span>, <span class="string">"计算机"</span><span class="bracket">)</span>,</span>
                    <span class="code-line">    <span class="bracket">(</span><span class="string">"李四"</span>, <span class="number">21</span>, <span class="string">"数学"</span><span class="bracket">)</span>,</span>
                    <span class="code-line">    <span class="bracket">(</span><span class="string">"王五"</span>, <span class="number">19</span>, <span class="string">"物理"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 遍历嵌套元组</span></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">name</span>, <span class="variable">age</span>, <span class="variable">major</span> <span class="keyword">in</span> <span class="variable">students</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"</span><span class="bracket">{</span><span class="variable">name</span><span class="bracket">}</span><span class="string">, </span><span class="bracket">{</span><span class="variable">age</span><span class="bracket">}</span><span class="string">岁, </span><span class="bracket">{</span><span class="variable">major</span><span class="bracket">}</span><span class="string">专业"</span><span class="bracket">)</span></span>
                </div>
            </div>
        </div>

        <!-- 第13页：元组作为函数参数 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">13/30</div>
                <h1><span class="emoji">📝</span>元组作为函数参数</h1>
                
                <h2>传递元组参数</h2>
                <div class="code-block">
                    <span class="code-line"><span class="keyword">def</span> <span class="function">calculate_distance</span><span class="bracket">(</span><span class="variable">point1</span>, <span class="variable">point2</span><span class="bracket">)</span>:</span>
                    <span class="code-line">    <span class="keyword">import</span> <span class="variable">math</span></span>
                    <span class="code-line">    <span class="variable">x1</span>, <span class="variable">y1</span> <span class="operator">=</span> <span class="variable">point1</span></span>
                    <span class="code-line">    <span class="variable">x2</span>, <span class="variable">y2</span> <span class="operator">=</span> <span class="variable">point2</span></span>
                    <span class="code-line">    <span class="keyword">return</span> <span class="variable">math</span>.<span class="function">sqrt</span><span class="bracket">((</span><span class="variable">x2</span><span class="operator">-</span><span class="variable">x1</span><span class="bracket">)</span><span class="operator">**</span><span class="number">2</span> <span class="operator">+</span> <span class="bracket">(</span><span class="variable">y2</span><span class="operator">-</span><span class="variable">y1</span><span class="bracket">)</span><span class="operator">**</span><span class="number">2</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 使用元组作为参数</span></span>
                    <span class="code-line"><span class="variable">p1</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">0</span>, <span class="number">0</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="variable">p2</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">3</span>, <span class="number">4</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="variable">distance</span> <span class="operator">=</span> <span class="function">calculate_distance</span><span class="bracket">(</span><span class="variable">p1</span>, <span class="variable">p2</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"距离: </span><span class="bracket">{</span><span class="variable">distance</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># 5.0</span></span>
                </div>

                <h2>*args 参数解包</h2>
                <div class="code-block">
                    <span class="code-line"><span class="keyword">def</span> <span class="function">process_coordinates</span><span class="bracket">(</span><span class="variable">*points</span><span class="bracket">)</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"接收到 </span><span class="bracket">{</span><span class="function">len</span><span class="bracket">(</span><span class="variable">points</span><span class="bracket">)</span><span class="bracket">}</span><span class="string"> 个点:"</span><span class="bracket">)</span></span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">i</span>, <span class="variable">point</span> <span class="keyword">in</span> <span class="function">enumerate</span><span class="bracket">(</span><span class="variable">points</span><span class="bracket">)</span>:</span>
                    <span class="code-line">        <span class="variable">x</span>, <span class="variable">y</span> <span class="operator">=</span> <span class="variable">point</span></span>
                    <span class="code-line">        <span class="function">print</span><span class="bracket">(</span><span class="string">f"点</span><span class="bracket">{</span><span class="variable">i</span><span class="operator">+</span><span class="number">1</span><span class="bracket">}</span><span class="string">: (</span><span class="bracket">{</span><span class="variable">x</span><span class="bracket">}</span><span class="string">, </span><span class="bracket">{</span><span class="variable">y</span><span class="bracket">}</span><span class="string">)"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 调用方式</span></span>
                    <span class="code-line"><span class="function">process_coordinates</span><span class="bracket">((</span><span class="number">1</span>,<span class="number">2</span><span class="bracket">)</span>, <span class="bracket">(</span><span class="number">3</span>,<span class="number">4</span><span class="bracket">)</span>, <span class="bracket">(</span><span class="number">5</span>,<span class="number">6</span><span class="bracket">))</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 解包元组作为参数</span></span>
                    <span class="code-line"><span class="variable">coords</span> <span class="operator">=</span> <span class="bracket">((</span><span class="number">0</span>,<span class="number">0</span><span class="bracket">)</span>, <span class="bracket">(</span><span class="number">2</span>,<span class="number">2</span><span class="bracket">)</span>, <span class="bracket">(</span><span class="number">4</span>,<span class="number">4</span><span class="bracket">))</span></span>
                    <span class="code-line"><span class="function">process_coordinates</span><span class="bracket">(</span><span class="variable">*coords</span><span class="bracket">)</span>  <span class="comment"># 解包元组</span></span>
                </div>
            </div>
        </div>

        <!-- 第14页：元组作为返回值 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">14/30</div>
                <h1><span class="emoji">🔄</span>元组作为返回值</h1>
                
                <h2>返回多个相关值</h2>
                <div class="code-block">
                    <span class="code-line"><span class="keyword">def</span> <span class="function">get_statistics</span><span class="bracket">(</span><span class="variable">numbers</span><span class="bracket">)</span>:</span>
                    <span class="code-line">    <span class="keyword">if</span> <span class="keyword">not</span> <span class="variable">numbers</span>:</span>
                    <span class="code-line">        <span class="keyword">return</span> <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span></span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="variable">total</span> <span class="operator">=</span> <span class="function">sum</span><span class="bracket">(</span><span class="variable">numbers</span><span class="bracket">)</span></span>
                    <span class="code-line">    <span class="variable">count</span> <span class="operator">=</span> <span class="function">len</span><span class="bracket">(</span><span class="variable">numbers</span><span class="bracket">)</span></span>
                    <span class="code-line">    <span class="variable">average</span> <span class="operator">=</span> <span class="variable">total</span> <span class="operator">/</span> <span class="variable">count</span></span>
                    <span class="code-line">    <span class="variable">maximum</span> <span class="operator">=</span> <span class="function">max</span><span class="bracket">(</span><span class="variable">numbers</span><span class="bracket">)</span></span>
                    <span class="code-line">    <span class="variable">minimum</span> <span class="operator">=</span> <span class="function">min</span><span class="bracket">(</span><span class="variable">numbers</span><span class="bracket">)</span></span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="keyword">return</span> <span class="variable">total</span>, <span class="variable">average</span>, <span class="variable">maximum</span>, <span class="variable">minimum</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 使用返回的元组</span></span>
                    <span class="code-line"><span class="variable">data</span> <span class="operator">=</span> <span class="bracket">[</span><span class="number">1</span>, <span class="number">5</span>, <span class="number">3</span>, <span class="number">9</span>, <span class="number">2</span>, <span class="number">7</span><span class="bracket">]</span></span>
                    <span class="code-line"><span class="variable">sum_val</span>, <span class="variable">avg</span>, <span class="variable">max_val</span>, <span class="variable">min_val</span> <span class="operator">=</span> <span class="function">get_statistics</span><span class="bracket">(</span><span class="variable">data</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"总和: </span><span class="bracket">{</span><span class="variable">sum_val</span><span class="bracket">}</span><span class="string">, 平均: </span><span class="bracket">{</span><span class="variable">avg</span><span class="bracket">:</span><span class="string">.2f</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"最大: </span><span class="bracket">{</span><span class="variable">max_val</span><span class="bracket">}</span><span class="string">, 最小: </span><span class="bracket">{</span><span class="variable">min_val</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>

                <h2>错误处理中的元组返回</h2>
                <div class="code-block">
                    <span class="code-line"><span class="keyword">def</span> <span class="function">safe_divide</span><span class="bracket">(</span><span class="variable">a</span>, <span class="variable">b</span><span class="bracket">)</span>:</span>
                    <span class="code-line">    <span class="keyword">if</span> <span class="variable">b</span> <span class="operator">==</span> <span class="number">0</span>:</span>
                    <span class="code-line">        <span class="keyword">return</span> <span class="keyword">None</span>, <span class="string">"除数不能为零"</span></span>
                    <span class="code-line">    <span class="keyword">else</span>:</span>
                    <span class="code-line">        <span class="keyword">return</span> <span class="variable">a</span> <span class="operator">/</span> <span class="variable">b</span>, <span class="string">"计算成功"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 使用方式</span></span>
                    <span class="code-line"><span class="variable">result</span>, <span class="variable">message</span> <span class="operator">=</span> <span class="function">safe_divide</span><span class="bracket">(</span><span class="number">10</span>, <span class="number">3</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="keyword">if</span> <span class="variable">result</span> <span class="keyword">is</span> <span class="keyword">not</span> <span class="keyword">None</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"结果: </span><span class="bracket">{</span><span class="variable">result</span><span class="bracket">:</span><span class="string">.2f</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="keyword">else</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"错误: </span><span class="bracket">{</span><span class="variable">message</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>
            </div>
        </div>

        <!-- 第15页：元组的内置方法 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">15/30</div>
                <h1><span class="emoji">🔧</span>元组的内置方法</h1>
                
                <h2>count() 方法</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">numbers</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">2</span>, <span class="number">4</span>, <span class="number">2</span>, <span class="number">5</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 统计元素出现次数</span></span>
                    <span class="code-line"><span class="variable">count_2</span> <span class="operator">=</span> <span class="variable">numbers</span>.<span class="function">count</span><span class="bracket">(</span><span class="number">2</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"数字2出现了 </span><span class="bracket">{</span><span class="variable">count_2</span><span class="bracket">}</span><span class="string"> 次"</span><span class="bracket">)</span>  <span class="comment"># 3次</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="variable">words</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"python"</span>, <span class="string">"java"</span>, <span class="string">"python"</span>, <span class="string">"c++"</span>, <span class="string">"python"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="variable">python_count</span> <span class="operator">=</span> <span class="variable">words</span>.<span class="function">count</span><span class="bracket">(</span><span class="string">"python"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"Python出现了 </span><span class="bracket">{</span><span class="variable">python_count</span><span class="bracket">}</span><span class="string"> 次"</span><span class="bracket">)</span>  <span class="comment"># 3次</span></span>
                </div>

                <h2>index() 方法</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">colors</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"红"</span>, <span class="string">"绿"</span>, <span class="string">"蓝"</span>, <span class="string">"黄"</span>, <span class="string">"绿"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 查找元素第一次出现的位置</span></span>
                    <span class="code-line"><span class="variable">blue_index</span> <span class="operator">=</span> <span class="variable">colors</span>.<span class="function">index</span><span class="bracket">(</span><span class="string">"蓝"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"蓝色在位置: </span><span class="bracket">{</span><span class="variable">blue_index</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># 2</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 注意：只返回第一次出现的位置</span></span>
                    <span class="code-line"><span class="variable">green_index</span> <span class="operator">=</span> <span class="variable">colors</span>.<span class="function">index</span><span class="bracket">(</span><span class="string">"绿"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"绿色在位置: </span><span class="bracket">{</span><span class="variable">green_index</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># 1 (不是4)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 指定搜索范围</span></span>
                    <span class="code-line"><span class="variable">green_index2</span> <span class="operator">=</span> <span class="variable">colors</span>.<span class="function">index</span><span class="bracket">(</span><span class="string">"绿"</span>, <span class="number">2</span><span class="bracket">)</span>  <span class="comment"># 从位置2开始搜索</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"第二个绿色在位置: </span><span class="bracket">{</span><span class="variable">green_index2</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># 4</span></span>
                </div>

                <h2>错误处理</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">data</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 查找不存在的元素会报错</span></span>
                    <span class="code-line"><span class="keyword">try</span>:</span>
                    <span class="code-line">    <span class="variable">index</span> <span class="operator">=</span> <span class="variable">data</span>.<span class="function">index</span><span class="bracket">(</span><span class="number">10</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="keyword">except</span> <span class="variable">ValueError</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">"元素不存在"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 安全的查找方法</span></span>
                    <span class="code-line"><span class="keyword">def</span> <span class="function">safe_index</span><span class="bracket">(</span><span class="variable">tuple_obj</span>, <span class="variable">item</span><span class="bracket">)</span>:</span>
                    <span class="code-line">    <span class="keyword">try</span>:</span>
                    <span class="code-line">        <span class="keyword">return</span> <span class="variable">tuple_obj</span>.<span class="function">index</span><span class="bracket">(</span><span class="variable">item</span><span class="bracket">)</span></span>
                    <span class="code-line">    <span class="keyword">except</span> <span class="variable">ValueError</span>:</span>
                    <span class="code-line">        <span class="keyword">return</span> <span class="number">-1</span>  <span class="comment"># 表示未找到</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="variable">pos</span> <span class="operator">=</span> <span class="function">safe_index</span><span class="bracket">(</span><span class="variable">data</span>, <span class="number">10</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"位置: </span><span class="bracket">{</span><span class="variable">pos</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># -1</span></span>
                </div>

                <div class="highlight-box">
                    <h3><span class="emoji">💡</span>方法总结</h3>
                    <p>元组只有2个内置方法，体现了其简洁性：</p>
                    <ul>
                        <li><strong>count()</strong>：统计元素出现次数</li>
                        <li><strong>index()</strong>：查找元素位置</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第16页：元组的遍历 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">16/30</div>
                <h1><span class="emoji">🔄</span>元组的遍历</h1>
                
                <h2>基础遍历方式</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">fruits</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"苹果"</span>, <span class="string">"香蕉"</span>, <span class="string">"橙子"</span>, <span class="string">"葡萄"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 直接遍历元素</span></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">fruit</span> <span class="keyword">in</span> <span class="variable">fruits</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"我喜欢吃</span><span class="bracket">{</span><span class="variable">fruit</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 带索引的遍历</span></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">index</span>, <span class="variable">fruit</span> <span class="keyword">in</span> <span class="function">enumerate</span><span class="bracket">(</span><span class="variable">fruits</span><span class="bracket">)</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"</span><span class="bracket">{</span><span class="variable">index</span><span class="operator">+</span><span class="number">1</span><span class="bracket">}</span><span class="string">. </span><span class="bracket">{</span><span class="variable">fruit</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 倒序遍历</span></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">fruit</span> <span class="keyword">in</span> <span class="function">reversed</span><span class="bracket">(</span><span class="variable">fruits</span><span class="bracket">)</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"倒序: </span><span class="bracket">{</span><span class="variable">fruit</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>

                <h2>同时遍历多个元组</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">names</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"张三"</span>, <span class="string">"李四"</span>, <span class="string">"王五"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="variable">ages</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">25</span>, <span class="number">30</span>, <span class="number">35</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="variable">cities</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"北京"</span>, <span class="string">"上海"</span>, <span class="string">"深圳"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 使用 zip 同时遍历</span></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">name</span>, <span class="variable">age</span>, <span class="variable">city</span> <span class="keyword">in</span> <span class="function">zip</span><span class="bracket">(</span><span class="variable">names</span>, <span class="variable">ages</span>, <span class="variable">cities</span><span class="bracket">)</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"</span><span class="bracket">{</span><span class="variable">name</span><span class="bracket">}</span><span class="string">, </span><span class="bracket">{</span><span class="variable">age</span><span class="bracket">}</span><span class="string">岁, 住在</span><span class="bracket">{</span><span class="variable">city</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>

                <h2>条件遍历与过滤</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">scores</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">85</span>, <span class="number">92</span>, <span class="number">78</span>, <span class="number">96</span>, <span class="number">88</span>, <span class="number">73</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 找出优秀成绩（90分以上）</span></span>
                    <span class="code-line"><span class="variable">excellent_scores</span> <span class="operator">=</span> <span class="bracket">[]</span></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">score</span> <span class="keyword">in</span> <span class="variable">scores</span>:</span>
                    <span class="code-line">    <span class="keyword">if</span> <span class="variable">score</span> <span class="operator">>=</span> <span class="number">90</span>:</span>
                    <span class="code-line">        <span class="variable">excellent_scores</span>.<span class="function">append</span><span class="bracket">(</span><span class="variable">score</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"优秀成绩: </span><span class="bracket">{</span><span class="variable">excellent_scores</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># [92, 96]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 更pythonic的写法</span></span>
                    <span class="code-line"><span class="variable">excellent</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span><span class="variable">score</span> <span class="keyword">for</span> <span class="variable">score</span> <span class="keyword">in</span> <span class="variable">scores</span> <span class="keyword">if</span> <span class="variable">score</span> <span class="operator">>=</span> <span class="number">90</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"优秀成绩(元组): </span><span class="bracket">{</span><span class="variable">excellent</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>
            </div>
        </div>

        <!-- 第17页：判断元素存在 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">17/30</div>
                <h1><span class="emoji">❓</span>判断元素存在</h1>
                
                <h2>使用 in 和 not in</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">programming_languages</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"Python"</span>, <span class="string">"Java"</span>, <span class="string">"C++"</span>, <span class="string">"JavaScript"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 检查元素是否存在</span></span>
                    <span class="code-line"><span class="keyword">if</span> <span class="string">"Python"</span> <span class="keyword">in</span> <span class="variable">programming_languages</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">"Python 是一门编程语言"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="keyword">if</span> <span class="string">"Go"</span> <span class="keyword">not</span> <span class="keyword">in</span> <span class="variable">programming_languages</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">"Go 不在列表中"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 批量检查</span></span>
                    <span class="code-line"><span class="variable">to_check</span> <span class="operator">=</span> <span class="bracket">[</span><span class="string">"Python"</span>, <span class="string">"Ruby"</span>, <span class="string">"Java"</span>, <span class="string">"Rust"</span><span class="bracket">]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">lang</span> <span class="keyword">in</span> <span class="variable">to_check</span>:</span>
                    <span class="code-line">    <span class="variable">status</span> <span class="operator">=</span> <span class="string">"✓"</span> <span class="keyword">if</span> <span class="variable">lang</span> <span class="keyword">in</span> <span class="variable">programming_languages</span> <span class="keyword">else</span> <span class="string">"✗"</span></span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"</span><span class="bracket">{</span><span class="variable">status</span><span class="bracket">}</span><span class="string"> </span><span class="bracket">{</span><span class="variable">lang</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>

                <h2>性能对比：in vs count vs index</h2>
                <div class="code-block">
                    <span class="code-line"><span class="keyword">import</span> <span class="variable">timeit</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="variable">large_tuple</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span><span class="function">range</span><span class="bracket">(</span><span class="number">10000</span><span class="bracket">))</span></span>
                    <span class="code-line"><span class="variable">target</span> <span class="operator">=</span> <span class="number">9999</span>  <span class="comment"># 最后一个元素</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 方法1：使用 in（推荐）</span></span>
                    <span class="code-line"><span class="variable">time_in</span> <span class="operator">=</span> <span class="variable">timeit</span>.<span class="function">timeit</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="keyword">lambda</span>: <span class="variable">target</span> <span class="keyword">in</span> <span class="variable">large_tuple</span>,</span>
                    <span class="code-line">    <span class="variable">number</span><span class="operator">=</span><span class="number">1000</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 方法2：使用 count</span></span>
                    <span class="code-line"><span class="variable">time_count</span> <span class="operator">=</span> <span class="variable">timeit</span>.<span class="function">timeit</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="keyword">lambda</span>: <span class="variable">large_tuple</span>.<span class="function">count</span><span class="bracket">(</span><span class="variable">target</span><span class="bracket">)</span> <span class="operator">></span> <span class="number">0</span>,</span>
                    <span class="code-line">    <span class="variable">number</span><span class="operator">=</span><span class="number">1000</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"使用 in: </span><span class="bracket">{</span><span class="variable">time_in</span><span class="bracket">:</span><span class="string">.4f</span><span class="bracket">}</span><span class="string">秒"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"使用 count: </span><span class="bracket">{</span><span class="variable">time_count</span><span class="bracket">:</span><span class="string">.4f</span><span class="bracket">}</span><span class="string">秒"</span><span class="bracket">)</span></span>
                </div>

                <h2>复杂条件判断</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">coordinates</span> <span class="operator">=</span> <span class="bracket">((</span><span class="number">0</span>, <span class="number">0</span><span class="bracket">)</span>, <span class="bracket">(</span><span class="number">1</span>, <span class="number">1</span><span class="bracket">)</span>, <span class="bracket">(</span><span class="number">2</span>, <span class="number">2</span><span class="bracket">)</span>, <span class="bracket">(</span><span class="number">3</span>, <span class="number">3</span><span class="bracket">))</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 检查特定坐标是否存在</span></span>
                    <span class="code-line"><span class="variable">target_point</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">2</span>, <span class="number">2</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="keyword">if</span> <span class="variable">target_point</span> <span class="keyword">in</span> <span class="variable">coordinates</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"找到坐标 </span><span class="bracket">{</span><span class="variable">target_point</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 检查是否存在满足条件的元素</span></span>
                    <span class="code-line"><span class="variable">has_origin</span> <span class="operator">=</span> <span class="function">any</span><span class="bracket">(</span><span class="variable">x</span> <span class="operator">==</span> <span class="number">0</span> <span class="keyword">and</span> <span class="variable">y</span> <span class="operator">==</span> <span class="number">0</span> <span class="keyword">for</span> <span class="variable">x</span>, <span class="variable">y</span> <span class="keyword">in</span> <span class="variable">coordinates</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"包含原点: </span><span class="bracket">{</span><span class="variable">has_origin</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 检查所有元素是否满足条件</span></span>
                    <span class="code-line"><span class="variable">all_positive</span> <span class="operator">=</span> <span class="function">all</span><span class="bracket">(</span><span class="variable">x</span> <span class="operator">>=</span> <span class="number">0</span> <span class="keyword">and</span> <span class="variable">y</span> <span class="operator">>=</span> <span class="number">0</span> <span class="keyword">for</span> <span class="variable">x</span>, <span class="variable">y</span> <span class="keyword">in</span> <span class="variable">coordinates</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"全部非负: </span><span class="bracket">{</span><span class="variable">all_positive</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>
            </div>
        </div>

        <!-- 第18页：元组的长度和计数 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">18/30</div>
                <h1><span class="emoji">📏</span>元组的长度和计数</h1>
                
                <h2>基础统计信息</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">data</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">1</span>, <span class="number">3</span>, <span class="number">5</span>, <span class="number">3</span>, <span class="number">7</span>, <span class="number">3</span>, <span class="number">9</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 获取长度</span></span>
                    <span class="code-line"><span class="variable">length</span> <span class="operator">=</span> <span class="function">len</span><span class="bracket">(</span><span class="variable">data</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"元组长度: </span><span class="bracket">{</span><span class="variable">length</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># 7</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 统计特定元素出现次数</span></span>
                    <span class="code-line"><span class="variable">count_3</span> <span class="operator">=</span> <span class="variable">data</span>.<span class="function">count</span><span class="bracket">(</span><span class="number">3</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"数字3出现次数: </span><span class="bracket">{</span><span class="variable">count_3</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># 3</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 计算唯一元素个数</span></span>
                    <span class="code-line"><span class="variable">unique_count</span> <span class="operator">=</span> <span class="function">len</span><span class="bracket">(</span><span class="function">set</span><span class="bracket">(</span><span class="variable">data</span><span class="bracket">))</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"唯一元素个数: </span><span class="bracket">{</span><span class="variable">unique_count</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># 5</span></span>
                </div>

                <h2>详细统计分析</h2>
                <div class="code-block">
                    <span class="code-line"><span class="keyword">def</span> <span class="function">analyze_tuple</span><span class="bracket">(</span><span class="variable">t</span><span class="bracket">)</span>:</span>
                    <span class="code-line">    <span class="variable">analysis</span> <span class="operator">=</span> <span class="bracket">{}</span></span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="comment"># 基本信息</span></span>
                    <span class="code-line">    <span class="variable">analysis</span><span class="bracket">[</span><span class="string">'总长度'</span><span class="bracket">]</span> <span class="operator">=</span> <span class="function">len</span><span class="bracket">(</span><span class="variable">t</span><span class="bracket">)</span></span>
                    <span class="code-line">    <span class="variable">analysis</span><span class="bracket">[</span><span class="string">'唯一元素数'</span><span class="bracket">]</span> <span class="operator">=</span> <span class="function">len</span><span class="bracket">(</span><span class="function">set</span><span class="bracket">(</span><span class="variable">t</span><span class="bracket">))</span></span>
                    <span class="code-line">    <span class="variable">analysis</span><span class="bracket">[</span><span class="string">'是否为空'</span><span class="bracket">]</span> <span class="operator">=</span> <span class="function">len</span><span class="bracket">(</span><span class="variable">t</span><span class="bracket">)</span> <span class="operator">==</span> <span class="number">0</span></span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="comment"># 元素频率统计</span></span>
                    <span class="code-line">    <span class="variable">frequency</span> <span class="operator">=</span> <span class="bracket">{}</span></span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">item</span> <span class="keyword">in</span> <span class="function">set</span><span class="bracket">(</span><span class="variable">t</span><span class="bracket">)</span>:</span>
                    <span class="code-line">        <span class="variable">frequency</span><span class="bracket">[</span><span class="variable">item</span><span class="bracket">]</span> <span class="operator">=</span> <span class="variable">t</span>.<span class="function">count</span><span class="bracket">(</span><span class="variable">item</span><span class="bracket">)</span></span>
                    <span class="code-line">    <span class="variable">analysis</span><span class="bracket">[</span><span class="string">'频率统计'</span><span class="bracket">]</span> <span class="operator">=</span> <span class="variable">frequency</span></span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="comment"># 最频繁元素</span></span>
                    <span class="code-line">    <span class="keyword">if</span> <span class="variable">frequency</span>:</span>
                    <span class="code-line">        <span class="variable">most_common</span> <span class="operator">=</span> <span class="function">max</span><span class="bracket">(</span><span class="variable">frequency</span>, <span class="variable">key</span><span class="operator">=</span><span class="variable">frequency</span>.<span class="function">get</span><span class="bracket">)</span></span>
                    <span class="code-line">        <span class="variable">analysis</span><span class="bracket">[</span><span class="string">'最频繁元素'</span><span class="bracket">]</span> <span class="operator">=</span> <span class="bracket">(</span><span class="variable">most_common</span>, <span class="variable">frequency</span><span class="bracket">[</span><span class="variable">most_common</span><span class="bracket">])</span></span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="keyword">return</span> <span class="variable">analysis</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 示例使用</span></span>
                    <span class="code-line"><span class="variable">sample_data</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"a"</span>, <span class="string">"b"</span>, <span class="string">"a"</span>, <span class="string">"c"</span>, <span class="string">"a"</span>, <span class="string">"b"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="variable">result</span> <span class="operator">=</span> <span class="function">analyze_tuple</span><span class="bracket">(</span><span class="variable">sample_data</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">key</span>, <span class="variable">value</span> <span class="keyword">in</span> <span class="variable">result</span>.<span class="function">items</span><span class="bracket">()</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"</span><span class="bracket">{</span><span class="variable">key</span><span class="bracket">}</span><span class="string">: </span><span class="bracket">{</span><span class="variable">value</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>

                <h2>空元组的处理</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">empty_tuple</span> <span class="operator">=</span> <span class="bracket">()</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 检查是否为空</span></span>
                    <span class="code-line"><span class="keyword">if</span> <span class="keyword">not</span> <span class="variable">empty_tuple</span>:  <span class="comment"># 空元组为 False</span></span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">"元组为空"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 更明确的检查方式</span></span>
                    <span class="code-line"><span class="keyword">if</span> <span class="function">len</span><span class="bracket">(</span><span class="variable">empty_tuple</span><span class="bracket">)</span> <span class="operator">==</span> <span class="number">0</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">"长度为0的元组"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 安全的统计操作</span></span>
                    <span class="code-line"><span class="keyword">def</span> <span class="function">safe_statistics</span><span class="bracket">(</span><span class="variable">numbers_tuple</span><span class="bracket">)</span>:</span>
                    <span class="code-line">    <span class="keyword">if</span> <span class="keyword">not</span> <span class="variable">numbers_tuple</span>:</span>
                    <span class="code-line">        <span class="keyword">return</span> <span class="string">"空元组，无法计算统计信息"</span></span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="keyword">return</span> <span class="bracket">{</span></span>
                    <span class="code-line">        <span class="string">'长度'</span>: <span class="function">len</span><span class="bracket">(</span><span class="variable">numbers_tuple</span><span class="bracket">)</span>,</span>
                    <span class="code-line">        <span class="string">'最小值'</span>: <span class="function">min</span><span class="bracket">(</span><span class="variable">numbers_tuple</span><span class="bracket">)</span>,</span>
                    <span class="code-line">        <span class="string">'最大值'</span>: <span class="function">max</span><span class="bracket">(</span><span class="variable">numbers_tuple</span><span class="bracket">)</span>,</span>
                    <span class="code-line">        <span class="string">'总和'</span>: <span class="function">sum</span><span class="bracket">(</span><span class="variable">numbers_tuple</span><span class="bracket">)</span></span>
                    <span class="code-line">    <span class="bracket">}</span></span>
                </div>
            </div>
        </div>

        <!-- 第19页：元组转换操作 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">19/30</div>
                <h1><span class="emoji">🔄</span>元组转换操作</h1>
                
                <h2>与其他数据类型的转换</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">original_tuple</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 转换为列表</span></span>
                    <span class="code-line"><span class="variable">to_list</span> <span class="operator">=</span> <span class="function">list</span><span class="bracket">(</span><span class="variable">original_tuple</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"列表: </span><span class="bracket">{</span><span class="variable">to_list</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># [1, 2, 3, 4, 5]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 转换为集合（自动去重）</span></span>
                    <span class="code-line"><span class="variable">duplicate_tuple</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">1</span>, <span class="number">2</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">3</span>, <span class="number">3</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="variable">to_set</span> <span class="operator">=</span> <span class="function">set</span><span class="bracket">(</span><span class="variable">duplicate_tuple</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"集合: </span><span class="bracket">{</span><span class="variable">to_set</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># {1, 2, 3}</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 转换为字符串</span></span>
                    <span class="code-line"><span class="variable">words_tuple</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"Hello"</span>, <span class="string">"World"</span>, <span class="string">"Python"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="variable">to_string</span> <span class="operator">=</span> <span class="string">" "</span>.<span class="function">join</span><span class="bracket">(</span><span class="variable">words_tuple</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"字符串: </span><span class="bracket">{</span><span class="variable">to_string</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># "Hello World Python"</span></span>
                </div>

                <h2>创建字典</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 从键值对元组创建字典</span></span>
                    <span class="code-line"><span class="variable">key_value_pairs</span> <span class="operator">=</span> <span class="bracket">((</span><span class="string">"name"</span>, <span class="string">"张三"</span><span class="bracket">)</span>, <span class="bracket">(</span><span class="string">"age"</span>, <span class="number">25</span><span class="bracket">)</span>, <span class="bracket">(</span><span class="string">"city"</span>, <span class="string">"北京"</span><span class="bracket">))</span></span>
                    <span class="code-line"><span class="variable">to_dict</span> <span class="operator">=</span> <span class="function">dict</span><span class="bracket">(</span><span class="variable">key_value_pairs</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"字典: </span><span class="bracket">{</span><span class="variable">to_dict</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 使用 zip 创建字典</span></span>
                    <span class="code-line"><span class="variable">keys</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"x"</span>, <span class="string">"y"</span>, <span class="string">"z"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="variable">values</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">10</span>, <span class="number">20</span>, <span class="number">30</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="variable">coordinate_dict</span> <span class="operator">=</span> <span class="function">dict</span><span class="bracket">(</span><span class="function">zip</span><span class="bracket">(</span><span class="variable">keys</span>, <span class="variable">values</span><span class="bracket">))</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"坐标字典: </span><span class="bracket">{</span><span class="variable">coordinate_dict</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>

                <h2>复杂转换操作</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 嵌套元组展平</span></span>
                    <span class="code-line"><span class="variable">nested</span> <span class="operator">=</span> <span class="bracket">((</span><span class="number">1</span>, <span class="number">2</span><span class="bracket">)</span>, <span class="bracket">(</span><span class="number">3</span>, <span class="number">4</span><span class="bracket">)</span>, <span class="bracket">(</span><span class="number">5</span>, <span class="number">6</span><span class="bracket">))</span></span>
                    <span class="code-line"><span class="variable">flattened</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span><span class="variable">item</span> <span class="keyword">for</span> <span class="variable">subtuple</span> <span class="keyword">in</span> <span class="variable">nested</span> <span class="keyword">for</span> <span class="variable">item</span> <span class="keyword">in</span> <span class="variable">subtuple</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"展平后: </span><span class="bracket">{</span><span class="variable">flattened</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># (1, 2, 3, 4, 5, 6)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 类型转换和过滤</span></span>
                    <span class="code-line"><span class="variable">mixed_data</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"1"</span>, <span class="string">"2"</span>, <span class="string">"hello"</span>, <span class="string">"3"</span>, <span class="string">"world"</span>, <span class="string">"4"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 提取并转换数字</span></span>
                    <span class="code-line"><span class="variable">numbers_only</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="function">int</span><span class="bracket">(</span><span class="variable">item</span><span class="bracket">)</span> <span class="keyword">for</span> <span class="variable">item</span> <span class="keyword">in</span> <span class="variable">mixed_data</span> <span class="keyword">if</span> <span class="variable">item</span>.<span class="function">isdigit</span><span class="bracket">()</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"只包含数字: </span><span class="bracket">{</span><span class="variable">numbers_only</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># (1, 2, 3, 4)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 提取字符串</span></span>
                    <span class="code-line"><span class="variable">strings_only</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="variable">item</span> <span class="keyword">for</span> <span class="variable">item</span> <span class="keyword">in</span> <span class="variable">mixed_data</span> <span class="keyword">if</span> <span class="keyword">not</span> <span class="variable">item</span>.<span class="function">isdigit</span><span class="bracket">()</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"只包含字符串: </span><span class="bracket">{</span><span class="variable">strings_only</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># ('hello', 'world')</span></span>
                </div>

                <h2>数据格式化</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 数据清洗和格式化</span></span>
                    <span class="code-line"><span class="variable">raw_data</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"  Python  "</span>, <span class="string">"JAVA"</span>, <span class="string">"c++"</span>, <span class="string">"  Ruby  "</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 清理和标准化</span></span>
                    <span class="code-line"><span class="variable">cleaned_data</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="variable">item</span>.<span class="function">strip</span><span class="bracket">()</span>.<span class="function">title</span><span class="bracket">()</span> <span class="keyword">for</span> <span class="variable">item</span> <span class="keyword">in</span> <span class="variable">raw_data</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"清洗后: </span><span class="bracket">{</span><span class="variable">cleaned_data</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># ('Python', 'Java', 'C++', 'Ruby')</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 生成CSV格式</span></span>
                    <span class="code-line"><span class="variable">csv_line</span> <span class="operator">=</span> <span class="string">","</span>.<span class="function">join</span><span class="bracket">(</span><span class="variable">cleaned_data</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"CSV格式: </span><span class="bracket">{</span><span class="variable">csv_line</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>
            </div>
        </div>

        <!-- 第20页：命名元组 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">20/30</div>
                <h1><span class="emoji">🏷️</span>命名元组（NamedTuple）</h1>
                
                <h2>创建命名元组</h2>
                <div class="code-block">
                    <span class="code-line"><span class="keyword">from</span> <span class="variable">collections</span> <span class="keyword">import</span> <span class="variable">namedtuple</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 定义学生命名元组</span></span>
                    <span class="code-line"><span class="variable">Student</span> <span class="operator">=</span> <span class="function">namedtuple</span><span class="bracket">(</span><span class="string">'Student'</span>, <span class="bracket">[</span><span class="string">'name'</span>, <span class="string">'age'</span>, <span class="string">'grade'</span><span class="bracket">])</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 创建实例</span></span>
                    <span class="code-line"><span class="variable">student1</span> <span class="operator">=</span> <span class="function">Student</span><span class="bracket">(</span><span class="string">"张三"</span>, <span class="number">20</span>, <span class="string">"A"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="variable">student2</span> <span class="operator">=</span> <span class="function">Student</span><span class="bracket">(</span><span class="variable">name</span><span class="operator">=</span><span class="string">"李四"</span>, <span class="variable">age</span><span class="operator">=</span><span class="number">21</span>, <span class="variable">grade</span><span class="operator">=</span><span class="string">"B"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 访问字段</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"姓名: </span><span class="bracket">{</span><span class="variable">student1</span>.<span class="variable">name</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># 张三</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"年龄: </span><span class="bracket">{</span><span class="variable">student1</span>.<span class="variable">age</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>   <span class="comment"># 20</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"成绩: </span><span class="bracket">{</span><span class="variable">student1</span>.<span class="variable">grade</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span> <span class="comment"># A</span></span>
                </div>

                <h2>命名元组的优势</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 坐标系统的对比</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 普通元组 - 不够直观</span></span>
                    <span class="code-line"><span class="variable">point_tuple</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">3</span>, <span class="number">4</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="variable">distance</span> <span class="operator">=</span> <span class="bracket">(</span><span class="variable">point_tuple</span><span class="bracket">[</span><span class="number">0</span><span class="bracket">]</span><span class="operator">**</span><span class="number">2</span> <span class="operator">+</span> <span class="variable">point_tuple</span><span class="bracket">[</span><span class="number">1</span><span class="bracket">]</span><span class="operator">**</span><span class="number">2</span><span class="bracket">)</span><span class="operator">**</span><span class="number">0.5</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 命名元组 - 更清晰</span></span>
                    <span class="code-line"><span class="variable">Point</span> <span class="operator">=</span> <span class="function">namedtuple</span><span class="bracket">(</span><span class="string">'Point'</span>, <span class="bracket">[</span><span class="string">'x'</span>, <span class="string">'y'</span><span class="bracket">])</span></span>
                    <span class="code-line"><span class="variable">point_named</span> <span class="operator">=</span> <span class="function">Point</span><span class="bracket">(</span><span class="number">3</span>, <span class="number">4</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="variable">distance</span> <span class="operator">=</span> <span class="bracket">(</span><span class="variable">point_named</span>.<span class="variable">x</span><span class="operator">**</span><span class="number">2</span> <span class="operator">+</span> <span class="variable">point_named</span>.<span class="variable">y</span><span class="operator">**</span><span class="number">2</span><span class="bracket">)</span><span class="operator">**</span><span class="number">0.5</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"坐标: </span><span class="bracket">{</span><span class="variable">point_named</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># Point(x=3, y=4)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"距离原点: </span><span class="bracket">{</span><span class="variable">distance</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># 5.0</span></span>
                </div>

                <h2>命名元组的方法</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">Person</span> <span class="operator">=</span> <span class="function">namedtuple</span><span class="bracket">(</span><span class="string">'Person'</span>, <span class="string">'name age city'</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="variable">person</span> <span class="operator">=</span> <span class="function">Person</span><span class="bracket">(</span><span class="string">"王五"</span>, <span class="number">30</span>, <span class="string">"上海"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># _replace() 方法 - 创建修改版本</span></span>
                    <span class="code-line"><span class="variable">older_person</span> <span class="operator">=</span> <span class="variable">person</span>.<span class="function">_replace</span><span class="bracket">(</span><span class="variable">age</span><span class="operator">=</span><span class="number">31</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"原来: </span><span class="bracket">{</span><span class="variable">person</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"修改后: </span><span class="bracket">{</span><span class="variable">older_person</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># _asdict() 方法 - 转换为字典</span></span>
                    <span class="code-line"><span class="variable">person_dict</span> <span class="operator">=</span> <span class="variable">person</span>.<span class="function">_asdict</span><span class="bracket">()</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"字典形式: </span><span class="bracket">{</span><span class="variable">person_dict</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># _fields 属性 - 获取字段名</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"字段: </span><span class="bracket">{</span><span class="variable">Person</span>.<span class="variable">_fields</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># ('name', 'age', 'city')</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># _make() 方法 - 从可迭代对象创建</span></span>
                    <span class="code-line"><span class="variable">data_list</span> <span class="operator">=</span> <span class="bracket">[</span><span class="string">"赵六"</span>, <span class="number">25</span>, <span class="string">"广州"</span><span class="bracket">]</span></span>
                    <span class="code-line"><span class="variable">new_person</span> <span class="operator">=</span> <span class="variable">Person</span>.<span class="function">_make</span><span class="bracket">(</span><span class="variable">data_list</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"从列表创建: </span><span class="bracket">{</span><span class="variable">new_person</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>

                <h2>实际应用场景</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 数据库记录表示</span></span>
                    <span class="code-line"><span class="variable">Employee</span> <span class="operator">=</span> <span class="function">namedtuple</span><span class="bracket">(</span><span class="string">'Employee'</span>, </span>
                    <span class="code-line">    <span class="string">'id name department salary hire_date'</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="variable">employees</span> <span class="operator">=</span> <span class="bracket">[</span></span>
                    <span class="code-line">    <span class="function">Employee</span><span class="bracket">(</span><span class="number">1</span>, <span class="string">"张三"</span>, <span class="string">"技术部"</span>, <span class="number">8000</span>, <span class="string">"2023-01-15"</span><span class="bracket">)</span>,</span>
                    <span class="code-line">    <span class="function">Employee</span><span class="bracket">(</span><span class="number">2</span>, <span class="string">"李四"</span>, <span class="string">"销售部"</span>, <span class="number">6000</span>, <span class="string">"2023-03-20"</span><span class="bracket">)</span>,</span>
                    <span class="code-line">    <span class="function">Employee</span><span class="bracket">(</span><span class="number">3</span>, <span class="string">"王五"</span>, <span class="string">"技术部"</span>, <span class="number">9000</span>, <span class="string">"2022-11-10"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="bracket">]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 查询高薪员工</span></span>
                    <span class="code-line"><span class="variable">high_salary</span> <span class="operator">=</span> <span class="bracket">[</span><span class="variable">emp</span> <span class="keyword">for</span> <span class="variable">emp</span> <span class="keyword">in</span> <span class="variable">employees</span> <span class="keyword">if</span> <span class="variable">emp</span>.<span class="variable">salary</span> <span class="operator">></span> <span class="number">7000</span><span class="bracket">]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">emp</span> <span class="keyword">in</span> <span class="variable">high_salary</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"</span><span class="bracket">{</span><span class="variable">emp</span>.<span class="variable">name</span><span class="bracket">}</span><span class="string"> - </span><span class="bracket">{</span><span class="variable">emp</span>.<span class="variable">department</span><span class="bracket">}</span><span class="string"> - ¥</span><span class="bracket">{</span><span class="variable">emp</span>.<span class="variable">salary</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>
            </div>
        </div>

        <!-- 第21页：元组生成式基础 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">21/30</div>
                <h1><span class="emoji">⚡</span>元组生成式基础</h1>
                
                <h2>基本语法</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 注意：圆括号创建的是生成器，需要用tuple()转换</span></span>
                    <span class="code-line"><span class="variable">generator</span> <span class="operator">=</span> <span class="bracket">(</span><span class="variable">x</span><span class="operator">**</span><span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="number">5</span><span class="bracket">))</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="function">type</span><span class="bracket">(</span><span class="variable">generator</span><span class="bracket">))</span>  <span class="comment"># &lt;class 'generator'&gt;</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 转换为元组</span></span>
                    <span class="code-line"><span class="variable">squares_tuple</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span><span class="variable">x</span><span class="operator">**</span><span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="number">5</span><span class="bracket">))</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">squares_tuple</span><span class="bracket">)</span>  <span class="comment"># (0, 1, 4, 9, 16)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 从列表创建元组</span></span>
                    <span class="code-line"><span class="variable">numbers</span> <span class="operator">=</span> <span class="bracket">[</span><span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span><span class="bracket">]</span></span>
                    <span class="code-line"><span class="variable">doubled</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span><span class="variable">x</span> <span class="operator">*</span> <span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">numbers</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">doubled</span><span class="bracket">)</span>  <span class="comment"># (2, 4, 6, 8, 10)</span></span>
                </div>

                <h2>字符串处理</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 提取字符串中的字符</span></span>
                    <span class="code-line"><span class="variable">text</span> <span class="operator">=</span> <span class="string">"Python"</span></span>
                    <span class="code-line"><span class="variable">chars_tuple</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span><span class="variable">char</span> <span class="keyword">for</span> <span class="variable">char</span> <span class="keyword">in</span> <span class="variable">text</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">chars_tuple</span><span class="bracket">)</span>  <span class="comment"># ('P', 'y', 't', 'h', 'o', 'n')</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 转换为大写</span></span>
                    <span class="code-line"><span class="variable">upper_chars</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span><span class="variable">char</span>.<span class="function">upper</span><span class="bracket">()</span> <span class="keyword">for</span> <span class="variable">char</span> <span class="keyword">in</span> <span class="variable">text</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">upper_chars</span><span class="bracket">)</span>  <span class="comment"># ('P', 'Y', 'T', 'H', 'O', 'N')</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 字符对应的ASCII码</span></span>
                    <span class="code-line"><span class="variable">ascii_codes</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span><span class="function">ord</span><span class="bracket">(</span><span class="variable">char</span><span class="bracket">)</span> <span class="keyword">for</span> <span class="variable">char</span> <span class="keyword">in</span> <span class="variable">text</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">ascii_codes</span><span class="bracket">)</span>  <span class="comment"># (80, 121, 116, 104, 111, 110)</span></span>
                </div>

                <h2>数学运算</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 数学函数应用</span></span>
                    <span class="code-line"><span class="keyword">import</span> <span class="variable">math</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="variable">angles</span> <span class="operator">=</span> <span class="bracket">[</span><span class="number">0</span>, <span class="number">30</span>, <span class="number">45</span>, <span class="number">60</span>, <span class="number">90</span><span class="bracket">]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 转换为弧度并计算正弦值</span></span>
                    <span class="code-line"><span class="variable">sin_values</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="function">round</span><span class="bracket">(</span><span class="variable">math</span>.<span class="function">sin</span><span class="bracket">(</span><span class="variable">math</span>.<span class="function">radians</span><span class="bracket">(</span><span class="variable">angle</span><span class="bracket">))</span>, <span class="number">2</span><span class="bracket">)</span></span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">angle</span> <span class="keyword">in</span> <span class="variable">angles</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"角度: </span><span class="bracket">{</span><span class="variable">angles</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"正弦值: </span><span class="bracket">{</span><span class="variable">sin_values</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 生成斐波那契数列前10项</span></span>
                    <span class="code-line"><span class="keyword">def</span> <span class="function">fibonacci</span><span class="bracket">(</span><span class="variable">n</span><span class="bracket">)</span>:</span>
                    <span class="code-line">    <span class="variable">a</span>, <span class="variable">b</span> <span class="operator">=</span> <span class="number">0</span>, <span class="number">1</span></span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">_</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="variable">n</span><span class="bracket">)</span>:</span>
                    <span class="code-line">        <span class="keyword">yield</span> <span class="variable">a</span></span>
                    <span class="code-line">        <span class="variable">a</span>, <span class="variable">b</span> <span class="operator">=</span> <span class="variable">b</span>, <span class="variable">a</span> <span class="operator">+</span> <span class="variable">b</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="variable">fib_tuple</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span><span class="function">fibonacci</span><span class="bracket">(</span><span class="number">10</span><span class="bracket">))</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"斐波那契数列: </span><span class="bracket">{</span><span class="variable">fib_tuple</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="comment"># (0, 1, 1, 2, 3, 5, 8, 13, 21, 34)</span></span>
                </div>
            </div>
        </div>

        <!-- 第22页：条件筛选 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">22/30</div>
                <h1><span class="emoji">🔍</span>元组生成式中的条件筛选</h1>
                
                <h2>基础筛选</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">numbers</span> <span class="operator">=</span> <span class="function">range</span><span class="bracket">(</span><span class="number">1</span>, <span class="number">21</span><span class="bracket">)</span>  <span class="comment"># 1到20</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 筛选偶数</span></span>
                    <span class="code-line"><span class="variable">evens</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span><span class="variable">x</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">numbers</span> <span class="keyword">if</span> <span class="variable">x</span> <span class="operator">%</span> <span class="number">2</span> <span class="operator">==</span> <span class="number">0</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"偶数: </span><span class="bracket">{</span><span class="variable">evens</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># (2, 4, 6, 8, 10, 12, 14, 16, 18, 20)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 筛选质数</span></span>
                    <span class="code-line"><span class="keyword">def</span> <span class="function">is_prime</span><span class="bracket">(</span><span class="variable">n</span><span class="bracket">)</span>:</span>
                    <span class="code-line">    <span class="keyword">if</span> <span class="variable">n</span> <span class="operator"><</span> <span class="number">2</span>:</span>
                    <span class="code-line">        <span class="keyword">return</span> <span class="keyword">False</span></span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="number">2</span>, <span class="function">int</span><span class="bracket">(</span><span class="variable">n</span><span class="operator">**</span><span class="number">0.5</span><span class="bracket">)</span> <span class="operator">+</span> <span class="number">1</span><span class="bracket">)</span>:</span>
                    <span class="code-line">        <span class="keyword">if</span> <span class="variable">n</span> <span class="operator">%</span> <span class="variable">i</span> <span class="operator">==</span> <span class="number">0</span>:</span>
                    <span class="code-line">            <span class="keyword">return</span> <span class="keyword">False</span></span>
                    <span class="code-line">    <span class="keyword">return</span> <span class="keyword">True</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="variable">primes</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span><span class="variable">x</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">numbers</span> <span class="keyword">if</span> <span class="function">is_prime</span><span class="bracket">(</span><span class="variable">x</span><span class="bracket">))</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"质数: </span><span class="bracket">{</span><span class="variable">primes</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span>  <span class="comment"># (2, 3, 5, 7, 11, 13, 17, 19)</span></span>
                </div>

                <h2>字符串筛选</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">words</span> <span class="operator">=</span> <span class="bracket">[</span><span class="string">"python"</span>, <span class="string">"java"</span>, <span class="string">"javascript"</span>, <span class="string">"go"</span>, <span class="string">"rust"</span>, <span class="string">"c"</span><span class="bracket">]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 筛选长度大于3的单词</span></span>
                    <span class="code-line"><span class="variable">long_words</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span><span class="variable">word</span> <span class="keyword">for</span> <span class="variable">word</span> <span class="keyword">in</span> <span class="variable">words</span> <span class="keyword">if</span> <span class="function">len</span><span class="bracket">(</span><span class="variable">word</span><span class="bracket">)</span> <span class="operator">></span> <span class="number">3</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"长单词: </span><span class="bracket">{</span><span class="variable">long_words</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 筛选包含特定字母的单词</span></span>
                    <span class="code-line"><span class="variable">with_a</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span><span class="variable">word</span> <span class="keyword">for</span> <span class="variable">word</span> <span class="keyword">in</span> <span class="variable">words</span> <span class="keyword">if</span> <span class="string">'a'</span> <span class="keyword">in</span> <span class="variable">word</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"包含'a'的单词: </span><span class="bracket">{</span><span class="variable">with_a</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>

                <h2>复杂条件组合</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">scores</span> <span class="operator">=</span> <span class="bracket">[</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="string">"张三"</span>, <span class="number">85</span>, <span class="string">"数学"</span><span class="bracket">)</span>,</span>
                    <span class="code-line">    <span class="bracket">(</span><span class="string">"李四"</span>, <span class="number">92</span>, <span class="string">"英语"</span><span class="bracket">)</span>,</span>
                    <span class="code-line">    <span class="bracket">(</span><span class="string">"王五"</span>, <span class="number">78</span>, <span class="string">"数学"</span><span class="bracket">)</span>,</span>
                    <span class="code-line">    <span class="bracket">(</span><span class="string">"赵六"</span>, <span class="number">95</span>, <span class="string">"物理"</span><span class="bracket">)</span>,</span>
                    <span class="code-line">    <span class="bracket">(</span><span class="string">"钱七"</span>, <span class="number">88</span>, <span class="string">"数学"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="bracket">]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 筛选数学科目且分数大于80的学生</span></span>
                    <span class="code-line"><span class="variable">math_high_scores</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="variable">name</span> <span class="keyword">for</span> <span class="variable">name</span>, <span class="variable">score</span>, <span class="variable">subject</span> <span class="keyword">in</span> <span class="variable">scores</span></span>
                    <span class="code-line">    <span class="keyword">if</span> <span class="variable">subject</span> <span class="operator">==</span> <span class="string">"数学"</span> <span class="keyword">and</span> <span class="variable">score</span> <span class="operator">></span> <span class="number">80</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"数学高分学生: </span><span class="bracket">{</span><span class="variable">math_high_scores</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 提取所有优秀学生的分数（90分以上）</span></span>
                    <span class="code-line"><span class="variable">excellent_scores</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="variable">score</span> <span class="keyword">for</span> <span class="variable">name</span>, <span class="variable">score</span>, <span class="variable">subject</span> <span class="keyword">in</span> <span class="variable">scores</span></span>
                    <span class="code-line">    <span class="keyword">if</span> <span class="variable">score</span> <span class="operator">>=</span> <span class="number">90</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"优秀分数: </span><span class="bracket">{</span><span class="variable">excellent_scores</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>
            </div>
        </div>

        <!-- 第23页：复杂表达式 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">23/30</div>
                <h1><span class="emoji">🧮</span>元组生成式中的复杂表达式</h1>
                
                <h2>数学表达式</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 生成平方根元组</span></span>
                    <span class="code-line"><span class="keyword">import</span> <span class="variable">math</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="variable">square_roots</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="function">round</span><span class="bracket">(</span><span class="variable">math</span>.<span class="function">sqrt</span><span class="bracket">(</span><span class="variable">x</span><span class="bracket">)</span>, <span class="number">2</span><span class="bracket">)</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="number">1</span>, <span class="number">11</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"平方根: </span><span class="bracket">{</span><span class="variable">square_roots</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 生成复合计算结果</span></span>
                    <span class="code-line"><span class="variable">complex_calc</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="variable">x</span>, <span class="variable">x</span><span class="operator">**</span><span class="number">2</span>, <span class="variable">x</span><span class="operator">**</span><span class="number">3</span>, <span class="variable">x</span><span class="operator">**</span><span class="number">2</span> <span class="operator">+</span> <span class="variable">x</span><span class="operator">**</span><span class="number">3</span><span class="bracket">)</span></span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="number">1</span>, <span class="number">6</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">x</span>, <span class="variable">sq</span>, <span class="variable">cube</span>, <span class="variable">sum_powers</span> <span class="keyword">in</span> <span class="variable">complex_calc</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"x={</span><span class="bracket">{</span><span class="variable">x</span><span class="bracket">}</span><span class="string">: x²={</span><span class="bracket">{</span><span class="variable">sq</span><span class="bracket">}</span><span class="string">}, x³={</span><span class="bracket">{</span><span class="variable">cube</span><span class="bracket">}</span><span class="string">}, x²+x³={</span><span class="bracket">{</span><span class="variable">sum_powers</span><span class="bracket">}</span><span class="string">}"</span><span class="bracket">)</span></span>
                </div>

                <h2>字符串处理表达式</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">sentences</span> <span class="operator">=</span> <span class="bracket">[</span></span>
                    <span class="code-line">    <span class="string">"Python是一门强大的编程语言"</span>,</span>
                    <span class="code-line">    <span class="string">"学习编程需要持之以恒"</span>,</span>
                    <span class="code-line">    <span class="string">"数据结构很重要"</span></span>
                    <span class="code-line"><span class="bracket">]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 提取每个句子的统计信息</span></span>
                    <span class="code-line"><span class="variable">sentence_stats</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="variable">sentence</span><span class="bracket">[</span>:<span class="number">5</span><span class="bracket">]</span> <span class="operator">+</span> <span class="string">"..."</span>, <span class="function">len</span><span class="bracket">(</span><span class="variable">sentence</span><span class="bracket">)</span>, <span class="variable">sentence</span>.<span class="function">count</span><span class="bracket">(</span><span class="string">"的"</span><span class="bracket">))</span></span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">sentence</span> <span class="keyword">in</span> <span class="variable">sentences</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">preview</span>, <span class="variable">length</span>, <span class="variable">de_count</span> <span class="keyword">in</span> <span class="variable">sentence_stats</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"</span><span class="bracket">{</span><span class="variable">preview</span><span class="bracket">}</span><span class="string"> (长度: </span><span class="bracket">{</span><span class="variable">length</span><span class="bracket">}</span><span class="string">, '的'出现次数: </span><span class="bracket">{</span><span class="variable">de_count</span><span class="bracket">}</span><span class="string">)"</span><span class="bracket">)</span></span>
                </div>

                <h2>条件表达式</h2>
                <div class="code-block">
                    <span class="code-line"><span class="variable">numbers</span> <span class="operator">=</span> <span class="function">range</span><span class="bracket">(</span><span class="number">-5</span>, <span class="number">6</span><span class="bracket">)</span>  <span class="comment"># -5 到 5</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 使用条件表达式进行分类</span></span>
                    <span class="code-line"><span class="variable">classified</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="variable">x</span>, <span class="string">"正数"</span> <span class="keyword">if</span> <span class="variable">x</span> <span class="operator">></span> <span class="number">0</span> <span class="keyword">else</span> <span class="string">"负数"</span> <span class="keyword">if</span> <span class="variable">x</span> <span class="operator"><</span> <span class="number">0</span> <span class="keyword">else</span> <span class="string">"零"</span><span class="bracket">)</span></span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">numbers</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">num</span>, <span class="variable">category</span> <span class="keyword">in</span> <span class="variable">classified</span><span class="bracket">[</span>:<span class="number">5</span><span class="bracket">]</span>:  <span class="comment"># 只显示前5个</span></span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"</span><span class="bracket">{</span><span class="variable">num</span><span class="bracket">}</span><span class="string"> 是 </span><span class="bracket">{</span><span class="variable">category</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 复杂的数学分类</span></span>
                    <span class="code-line"><span class="variable">math_analysis</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="bracket">(</span></span>
                    <span class="code-line">        <span class="variable">x</span>,</span>
                    <span class="code-line">        <span class="function">abs</span><span class="bracket">(</span><span class="variable">x</span><span class="bracket">)</span>,</span>
                    <span class="code-line">        <span class="string">"偶数"</span> <span class="keyword">if</span> <span class="variable">x</span> <span class="operator">%</span> <span class="number">2</span> <span class="operator">==</span> <span class="number">0</span> <span class="keyword">else</span> <span class="string">"奇数"</span>,</span>
                    <span class="code-line">        <span class="variable">x</span><span class="operator">**</span><span class="number">2</span> <span class="keyword">if</span> <span class="function">abs</span><span class="bracket">(</span><span class="variable">x</span><span class="bracket">)</span> <span class="operator"><=</span> <span class="number">3</span> <span class="keyword">else</span> <span class="string">"大数"</span></span>
                    <span class="code-line">    <span class="bracket">)</span></span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="number">-5</span>, <span class="number">6</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                </div>
            </div>
        </div>

        <!-- 第24页：嵌套生成式 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">24/30</div>
                <h1><span class="emoji">🔗</span>嵌套元组生成式</h1>
                
                <h2>多层嵌套生成</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 生成乘法表</span></span>
                    <span class="code-line"><span class="variable">multiplication_table</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="function">tuple</span><span class="bracket">(</span><span class="variable">i</span> <span class="operator">*</span> <span class="variable">j</span> <span class="keyword">for</span> <span class="variable">j</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="number">1</span>, <span class="number">6</span><span class="bracket">))</span></span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="number">1</span>, <span class="number">6</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">i</span>, <span class="variable">row</span> <span class="keyword">in</span> <span class="function">enumerate</span><span class="bracket">(</span><span class="variable">multiplication_table</span>, <span class="number">1</span><span class="bracket">)</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"</span><span class="bracket">{</span><span class="variable">i</span><span class="bracket">}</span><span class="string">: </span><span class="bracket">{</span><span class="variable">row</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>

                <h2>坐标系统生成</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 生成网格坐标</span></span>
                    <span class="code-line"><span class="variable">grid_coordinates</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="variable">x</span>, <span class="variable">y</span><span class="bracket">)</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="number">3</span><span class="bracket">)</span> <span class="keyword">for</span> <span class="variable">y</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="number">3</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"网格坐标: </span><span class="bracket">{</span><span class="variable">grid_coordinates</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 筛选满足条件的坐标</span></span>
                    <span class="code-line"><span class="variable">diagonal_coords</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="variable">x</span>, <span class="variable">y</span><span class="bracket">)</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="number">5</span><span class="bracket">)</span> <span class="keyword">for</span> <span class="variable">y</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="number">5</span><span class="bracket">)</span></span>
                    <span class="code-line">    <span class="keyword">if</span> <span class="variable">x</span> <span class="operator">==</span> <span class="variable">y</span> <span class="keyword">or</span> <span class="variable">x</span> <span class="operator">+</span> <span class="variable">y</span> <span class="operator">==</span> <span class="number">4</span>  <span class="comment"># 主对角线或副对角线</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"对角线坐标: </span><span class="bracket">{</span><span class="variable">diagonal_coords</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>

                <h2>复杂数据结构生成</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 生成学生成绩数据</span></span>
                    <span class="code-line"><span class="variable">subjects</span> <span class="operator">=</span> <span class="bracket">[</span><span class="string">"数学"</span>, <span class="string">"英语"</span>, <span class="string">"物理"</span><span class="bracket">]</span></span>
                    <span class="code-line"><span class="variable">students</span> <span class="operator">=</span> <span class="bracket">[</span><span class="string">"张三"</span>, <span class="string">"李四"</span><span class="bracket">]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 生成所有学生-科目组合</span></span>
                    <span class="code-line"><span class="variable">student_subjects</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="variable">student</span>, <span class="variable">subject</span>, <span class="string">f"</span><span class="bracket">{</span><span class="variable">student</span><span class="bracket">}</span><span class="string">的</span><span class="bracket">{</span><span class="variable">subject</span><span class="bracket">}</span><span class="string">课程"</span><span class="bracket">)</span></span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">student</span> <span class="keyword">in</span> <span class="variable">students</span></span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">subject</span> <span class="keyword">in</span> <span class="variable">subjects</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">student</span>, <span class="variable">subject</span>, <span class="variable">description</span> <span class="keyword">in</span> <span class="variable">student_subjects</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="variable">description</span><span class="bracket">)</span></span>
                </div>

                <h2>矩阵操作</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 创建单位矩阵</span></span>
                    <span class="code-line"><span class="variable">size</span> <span class="operator">=</span> <span class="number">3</span></span>
                    <span class="code-line"><span class="variable">identity_matrix</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="function">tuple</span><span class="bracket">(</span><span class="number">1</span> <span class="keyword">if</span> <span class="variable">i</span> <span class="operator">==</span> <span class="variable">j</span> <span class="keyword">else</span> <span class="number">0</span> <span class="keyword">for</span> <span class="variable">j</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="variable">size</span><span class="bracket">))</span></span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="variable">size</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">"单位矩阵:"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">row</span> <span class="keyword">in</span> <span class="variable">identity_matrix</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="variable">row</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 矩阵转置</span></span>
                    <span class="code-line"><span class="variable">original</span> <span class="operator">=</span> <span class="bracket">((</span><span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span><span class="bracket">)</span>, <span class="bracket">(</span><span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span><span class="bracket">))</span></span>
                    <span class="code-line"><span class="variable">transposed</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="function">tuple</span><span class="bracket">(</span><span class="variable">original</span><span class="bracket">[</span><span class="variable">i</span><span class="bracket">][</span><span class="variable">j</span><span class="bracket">]</span> <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="function">len</span><span class="bracket">(</span><span class="variable">original</span><span class="bracket">)))</span></span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">j</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="function">len</span><span class="bracket">(</span><span class="variable">original</span><span class="bracket">[</span><span class="number">0</span><span class="bracket">]))</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"原矩阵: </span><span class="bracket">{</span><span class="variable">original</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"转置矩阵: </span><span class="bracket">{</span><span class="variable">transposed</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>
            </div>
        </div>

        <!-- 第25页：实际应用案例 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">25/30</div>
                <h1><span class="emoji">💼</span>元组的实际应用案例</h1>
                
                <h2>配置管理</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 数据库连接配置</span></span>
                    <span class="code-line"><span class="variable">DB_CONFIG</span> <span class="operator">=</span> <span class="bracket">(</span></span>
                    <span class="code-line">    <span class="string">"localhost"</span>,    <span class="comment"># 主机</span></span>
                    <span class="code-line">    <span class="number">5432</span>,           <span class="comment"># 端口</span></span>
                    <span class="code-line">    <span class="string">"myapp"</span>,       <span class="comment"># 数据库名</span></span>
                    <span class="code-line">    <span class="string">"user"</span>,        <span class="comment"># 用户名</span></span>
                    <span class="code-line">    <span class="string">"password"</span>     <span class="comment"># 密码</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 解包配置</span></span>
                    <span class="code-line"><span class="variable">host</span>, <span class="variable">port</span>, <span class="variable">db_name</span>, <span class="variable">user</span>, <span class="variable">password</span> <span class="operator">=</span> <span class="variable">DB_CONFIG</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="keyword">def</span> <span class="function">connect_database</span><span class="bracket">(</span><span class="variable">config</span><span class="bracket">)</span>:</span>
                    <span class="code-line">    <span class="variable">host</span>, <span class="variable">port</span>, <span class="variable">db</span>, <span class="variable">user</span>, <span class="variable">pwd</span> <span class="operator">=</span> <span class="variable">config</span></span>
                    <span class="code-line">    <span class="variable">connection_string</span> <span class="operator">=</span> <span class="string">f"postgresql://</span><span class="bracket">{</span><span class="variable">user</span><span class="bracket">}</span><span class="string">:</span><span class="bracket">{</span><span class="variable">pwd</span><span class="bracket">}</span><span class="string">@</span><span class="bracket">{</span><span class="variable">host</span><span class="bracket">}</span><span class="string">:</span><span class="bracket">{</span><span class="variable">port</span><span class="bracket">}</span><span class="string">/</span><span class="bracket">{</span><span class="variable">db</span><span class="bracket">}</span><span class="string">"</span></span>
                    <span class="code-line">    <span class="keyword">return</span> <span class="variable">connection_string</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="function">connect_database</span><span class="bracket">(</span><span class="variable">DB_CONFIG</span><span class="bracket">))</span></span>
                </div>

                <h2>数据分析</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 股票数据（代码，名称，价格，涨跌幅）</span></span>
                    <span class="code-line"><span class="variable">stocks</span> <span class="operator">=</span> <span class="bracket">(</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="string">"AAPL"</span>, <span class="string">"苹果"</span>, <span class="number">150.25</span>, <span class="number">2.5</span><span class="bracket">)</span>,</span>
                    <span class="code-line">    <span class="bracket">(</span><span class="string">"GOOGL"</span>, <span class="string">"谷歌"</span>, <span class="number">2800.30</span>, <span class="number">-1.2</span><span class="bracket">)</span>,</span>
                    <span class="code-line">    <span class="bracket">(</span><span class="string">"TSLA"</span>, <span class="string">"特斯拉"</span>, <span class="number">780.50</span>, <span class="number">5.8</span><span class="bracket">)</span>,</span>
                    <span class="code-line">    <span class="bracket">(</span><span class="string">"MSFT"</span>, <span class="string">"微软"</span>, <span class="number">310.75</span>, <span class="number">1.3</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 筛选上涨的股票</span></span>
                    <span class="code-line"><span class="variable">rising_stocks</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="variable">code</span>, <span class="variable">name</span>, <span class="variable">change</span><span class="bracket">)</span> <span class="keyword">for</span> <span class="variable">code</span>, <span class="variable">name</span>, <span class="variable">price</span>, <span class="variable">change</span> <span class="keyword">in</span> <span class="variable">stocks</span></span>
                    <span class="code-line">    <span class="keyword">if</span> <span class="variable">change</span> <span class="operator">></span> <span class="number">0</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">"上涨股票："</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">code</span>, <span class="variable">name</span>, <span class="variable">change</span> <span class="keyword">in</span> <span class="variable">rising_stocks</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"</span><span class="bracket">{</span><span class="variable">code</span><span class="bracket">}</span><span class="string"> (</span><span class="bracket">{</span><span class="variable">name</span><span class="bracket">}</span><span class="string">): +</span><span class="bracket">{</span><span class="variable">change</span><span class="bracket">}</span><span class="string">%"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 计算总市值（假设每只股票1000股）</span></span>
                    <span class="code-line"><span class="variable">total_value</span> <span class="operator">=</span> <span class="function">sum</span><span class="bracket">(</span><span class="variable">price</span> <span class="operator">*</span> <span class="number">1000</span> <span class="keyword">for</span> <span class="variable">_</span>, <span class="variable">_</span>, <span class="variable">price</span>, <span class="variable">_</span> <span class="keyword">in</span> <span class="variable">stocks</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"投资组合总价值: ${</span><span class="bracket">{</span><span class="variable">total_value</span><span class="bracket">:</span><span class="string">,.2f</span><span class="bracket">}</span><span class="string">}"</span><span class="bracket">)</span></span>
                </div>

                <h2>游戏开发</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 扑克牌系统</span></span>
                    <span class="code-line"><span class="variable">SUITS</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"♠"</span>, <span class="string">"♥"</span>, <span class="string">"♦"</span>, <span class="string">"♣"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="variable">RANKS</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"A"</span>, <span class="string">"2"</span>, <span class="string">"3"</span>, <span class="string">"4"</span>, <span class="string">"5"</span>, <span class="string">"6"</span>, <span class="string">"7"</span>, <span class="string">"8"</span>, <span class="string">"9"</span>, <span class="string">"10"</span>, <span class="string">"J"</span>, <span class="string">"Q"</span>, <span class="string">"K"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 生成整副牌</span></span>
                    <span class="code-line"><span class="variable">deck</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="variable">rank</span>, <span class="variable">suit</span><span class="bracket">)</span> <span class="keyword">for</span> <span class="variable">suit</span> <span class="keyword">in</span> <span class="variable">SUITS</span> <span class="keyword">for</span> <span class="variable">rank</span> <span class="keyword">in</span> <span class="variable">RANKS</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 显示前10张牌</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">"前10张牌:"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">rank</span>, <span class="variable">suit</span> <span class="keyword">in</span> <span class="variable">deck</span><span class="bracket">[</span>:<span class="number">10</span><span class="bracket">]</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"</span><span class="bracket">{</span><span class="variable">rank</span><span class="bracket">}</span><span class="string"></span><span class="bracket">{</span><span class="variable">suit</span><span class="bracket">}</span><span class="string">"</span>, <span class="variable">end</span><span class="operator">=</span><span class="string">" "</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"\n总计: </span><span class="bracket">{</span><span class="function">len</span><span class="bracket">(</span><span class="variable">deck</span><span class="bracket">)</span><span class="bracket">}</span><span class="string"> 张牌"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 查找特定牌</span></span>
                    <span class="code-line"><span class="variable">aces</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="variable">rank</span>, <span class="variable">suit</span><span class="bracket">)</span> <span class="keyword">for</span> <span class="variable">rank</span>, <span class="variable">suit</span> <span class="keyword">in</span> <span class="variable">deck</span> <span class="keyword">if</span> <span class="variable">rank</span> <span class="operator">==</span> <span class="string">"A"</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"所有A: </span><span class="bracket">{</span><span class="variable">aces</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>

                <h2>科学计算</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 三维坐标点</span></span>
                    <span class="code-line"><span class="variable">points_3d</span> <span class="operator">=</span> <span class="bracket">(</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span><span class="bracket">)</span>,  <span class="comment"># 原点</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="number">1</span>, <span class="number">0</span>, <span class="number">0</span><span class="bracket">)</span>,  <span class="comment"># X轴</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="number">0</span>, <span class="number">1</span>, <span class="number">0</span><span class="bracket">)</span>,  <span class="comment"># Y轴</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="number">0</span>, <span class="number">0</span>, <span class="number">1</span><span class="bracket">)</span>,  <span class="comment"># Z轴</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="number">1</span>, <span class="number">1</span>, <span class="number">1</span><span class="bracket">)</span>   <span class="comment"># 对角点</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 计算每个点到原点的距离</span></span>
                    <span class="code-line"><span class="variable">distances</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="bracket">(</span><span class="variable">point</span>, <span class="function">round</span><span class="bracket">((</span><span class="variable">x</span><span class="operator">**</span><span class="number">2</span> <span class="operator">+</span> <span class="variable">y</span><span class="operator">**</span><span class="number">2</span> <span class="operator">+</span> <span class="variable">z</span><span class="operator">**</span><span class="number">2</span><span class="bracket">)</span><span class="operator">**</span><span class="number">0.5</span>, <span class="number">2</span><span class="bracket">))</span></span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">point</span> <span class="keyword">in</span> <span class="variable">points_3d</span></span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">x</span>, <span class="variable">y</span>, <span class="variable">z</span> <span class="keyword">in</span> <span class="bracket">[</span><span class="variable">point</span><span class="bracket">]</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">point</span>, <span class="variable">distance</span> <span class="keyword">in</span> <span class="variable">distances</span>:</span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"</span><span class="bracket">{</span><span class="variable">point</span><span class="bracket">}</span><span class="string"> 到原点距离: </span><span class="bracket">{</span><span class="variable">distance</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                </div>
            </div>
        </div>

        <!-- 第26页：性能对比 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">26/30</div>
                <h1><span class="emoji">🏎️</span>性能对比与优化</h1>
                
                <h2>内存使用对比</h2>
                <div class="code-block">
                    <span class="code-line"><span class="keyword">import</span> <span class="variable">sys</span></span>
                    <span class="code-line"><span class="keyword">import</span> <span class="variable">timeit</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 相同数据的不同存储方式</span></span>
                    <span class="code-line"><span class="variable">data_tuple</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span><span class="function">range</span><span class="bracket">(</span><span class="number">1000</span><span class="bracket">))</span></span>
                    <span class="code-line"><span class="variable">data_list</span> <span class="operator">=</span> <span class="function">list</span><span class="bracket">(</span><span class="function">range</span><span class="bracket">(</span><span class="number">1000</span><span class="bracket">))</span></span>
                    <span class="code-line"><span class="variable">data_set</span> <span class="operator">=</span> <span class="function">set</span><span class="bracket">(</span><span class="function">range</span><span class="bracket">(</span><span class="number">1000</span><span class="bracket">))</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 内存使用对比</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"元组内存: </span><span class="bracket">{</span><span class="variable">sys</span>.<span class="function">getsizeof</span><span class="bracket">(</span><span class="variable">data_tuple</span><span class="bracket">)</span><span class="bracket">}</span><span class="string"> 字节"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"列表内存: </span><span class="bracket">{</span><span class="variable">sys</span>.<span class="function">getsizeof</span><span class="bracket">(</span><span class="variable">data_list</span><span class="bracket">)</span><span class="bracket">}</span><span class="string"> 字节"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"集合内存: </span><span class="bracket">{</span><span class="variable">sys</span>.<span class="function">getsizeof</span><span class="bracket">(</span><span class="variable">data_set</span><span class="bracket">)</span><span class="bracket">}</span><span class="string"> 字节"</span><span class="bracket">)</span></span>
                </div>

                <h2>访问速度对比</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 索引访问速度测试</span></span>
                    <span class="code-line"><span class="variable">tuple_access_time</span> <span class="operator">=</span> <span class="variable">timeit</span>.<span class="function">timeit</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="keyword">lambda</span>: <span class="variable">data_tuple</span><span class="bracket">[</span><span class="number">500</span><span class="bracket">]</span>,</span>
                    <span class="code-line">    <span class="variable">number</span><span class="operator">=</span><span class="number">1000000</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="variable">list_access_time</span> <span class="operator">=</span> <span class="variable">timeit</span>.<span class="function">timeit</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="keyword">lambda</span>: <span class="variable">data_list</span><span class="bracket">[</span><span class="number">500</span><span class="bracket">]</span>,</span>
                    <span class="code-line">    <span class="variable">number</span><span class="operator">=</span><span class="number">1000000</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"元组访问时间: </span><span class="bracket">{</span><span class="variable">tuple_access_time</span><span class="bracket">:</span><span class="string">.4f</span><span class="bracket">}</span><span class="string"> 秒"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"列表访问时间: </span><span class="bracket">{</span><span class="variable">list_access_time</span><span class="bracket">:</span><span class="string">.4f</span><span class="bracket">}</span><span class="string"> 秒"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"元组比列表快: </span><span class="bracket">{</span><span class="variable">list_access_time</span> <span class="operator">/</span> <span class="variable">tuple_access_time</span><span class="bracket">:</span><span class="string">.1f</span><span class="bracket">}</span><span class="string"> 倍"</span><span class="bracket">)</span></span>
                </div>

                <h2>创建性能对比</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 不同创建方式的性能</span></span>
                    <span class="code-line"><span class="variable">range_size</span> <span class="operator">=</span> <span class="number">10000</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 方式1：直接转换</span></span>
                    <span class="code-line"><span class="variable">method1_time</span> <span class="operator">=</span> <span class="variable">timeit</span>.<span class="function">timeit</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="keyword">lambda</span>: <span class="function">tuple</span><span class="bracket">(</span><span class="function">range</span><span class="bracket">(</span><span class="variable">range_size</span><span class="bracket">))</span>,</span>
                    <span class="code-line">    <span class="variable">number</span><span class="operator">=</span><span class="number">100</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 方式2：生成式</span></span>
                    <span class="code-line"><span class="variable">method2_time</span> <span class="operator">=</span> <span class="variable">timeit</span>.<span class="function">timeit</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="keyword">lambda</span>: <span class="function">tuple</span><span class="bracket">(</span><span class="variable">x</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="variable">range_size</span><span class="bracket">))</span>,</span>
                    <span class="code-line">    <span class="variable">number</span><span class="operator">=</span><span class="number">100</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 方式3：先列表后转换</span></span>
                    <span class="code-line"><span class="variable">method3_time</span> <span class="operator">=</span> <span class="variable">timeit</span>.<span class="function">timeit</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="keyword">lambda</span>: <span class="function">tuple</span><span class="bracket">(</span><span class="function">list</span><span class="bracket">(</span><span class="function">range</span><span class="bracket">(</span><span class="variable">range_size</span><span class="bracket">)))</span>,</span>
                    <span class="code-line">    <span class="variable">number</span><span class="operator">=</span><span class="number">100</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">"创建性能对比："</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"直接转换: </span><span class="bracket">{</span><span class="variable">method1_time</span><span class="bracket">:</span><span class="string">.4f</span><span class="bracket">}</span><span class="string"> 秒 (最快)"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"生成式: </span><span class="bracket">{</span><span class="variable">method2_time</span><span class="bracket">:</span><span class="string">.4f</span><span class="bracket">}</span><span class="string"> 秒"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="string">f"先列表后转换: </span><span class="bracket">{</span><span class="variable">method3_time</span><span class="bracket">:</span><span class="string">.4f</span><span class="bracket">}</span><span class="string"> 秒 (最慢)"</span><span class="bracket">)</span></span>
                </div>

                <h2>最佳实践建议</h2>
                <div class="best-practice">
                    <h3><span class="emoji">⚡</span>性能优化建议</h3>
                    <ul>
                        <li><strong>优先使用元组</strong>：当数据不需要修改时</li>
                        <li><strong>避免频繁转换</strong>：元组↔列表转换有性能开销</li>
                        <li><strong>合理使用生成式</strong>：简单转换用tuple()，复杂逻辑用生成式</li>
                        <li><strong>预分配大小</strong>：大数据集时考虑内存使用</li>
                        <li><strong>利用不可变性</strong>：作为字典键和集合元素</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第27页：元组与AI编程 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">27/30</div>
                <h1><span class="emoji">🤖</span>元组与AI编程助手</h1>
                
                <h2>AI如何帮助学习元组</h2>
                <div class="best-practice">
                    <h3><span class="emoji">💡</span>学习策略</h3>
                    <ul>
                        <li><strong>概念理解</strong>：让AI解释元组的核心概念和特性</li>
                        <li><strong>代码示例</strong>：请AI提供具体的使用场景和代码</li>
                        <li><strong>对比分析</strong>：让AI比较元组与其他数据结构的差异</li>
                        <li><strong>实践练习</strong>：请AI设计练习题和项目案例</li>
                        <li><strong>错误调试</strong>：让AI帮助分析和修复代码问题</li>
                    </ul>
                </div>

                <h2>有效的提示词示例</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 基础学习提示词</span></span>
                    <span class="code-line"><span class="string">"请详细解释Python元组的特性，并与列表进行对比"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 实践应用提示词</span></span>
                    <span class="code-line"><span class="string">"给我5个元组在实际项目中的应用场景，并提供代码示例"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 问题解决提示词</span></span>
                    <span class="code-line"><span class="string">"我的元组代码出现了TypeError，请帮我分析原因并提供解决方案"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 深入学习提示词</span></span>
                    <span class="code-line"><span class="string">"请解释元组的内存管理机制，以及为什么它比列表更节省内存"</span></span>
                </div>

                <h2>AI辅助编程最佳实践</h2>
                <div class="best-practice">
                    <h3><span class="emoji">🎯</span>高效使用技巧</h3>
                    <ul>
                        <li><strong>具体描述</strong>：提供清晰的问题描述和上下文</li>
                        <li><strong>逐步学习</strong>：从基础概念到高级应用循序渐进</li>
                        <li><strong>实践验证</strong>：运行AI提供的代码并理解其工作原理</li>
                        <li><strong>举一反三</strong>：基于AI的示例创造自己的变体</li>
                        <li><strong>持续对话</strong>：在同一对话中深入探讨相关问题</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第28页：元组高级应用 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">28/30</div>
                <h1><span class="emoji">🚀</span>元组高级应用与技巧</h1>
                
                <h2>元组与函数式编程</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 使用元组进行函数式编程</span></span>
                    <span class="code-line"><span class="keyword">from</span> <span class="variable">functools</span> <span class="keyword">import</span> <span class="variable">reduce</span></span>
                    <span class="code-line"><span class="keyword">from</span> <span class="variable">operator</span> <span class="keyword">import</span> <span class="variable">add</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 元组的函数式操作</span></span>
                    <span class="code-line"><span class="variable">numbers</span> <span class="operator">=</span> <span class="bracket">(</span><span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 映射操作</span></span>
                    <span class="code-line"><span class="variable">squared</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span><span class="function">map</span><span class="bracket">(</span><span class="keyword">lambda</span> <span class="variable">x</span>: <span class="variable">x</span><span class="operator">**</span><span class="number">2</span>, <span class="variable">numbers</span><span class="bracket">))</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">squared</span><span class="bracket">)</span>  <span class="comment"># (1, 4, 9, 16, 25)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 过滤操作</span></span>
                    <span class="code-line"><span class="variable">evens</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span><span class="function">filter</span><span class="bracket">(</span><span class="keyword">lambda</span> <span class="variable">x</span>: <span class="variable">x</span> <span class="operator">%</span> <span class="number">2</span> <span class="operator">==</span> <span class="number">0</span>, <span class="variable">numbers</span><span class="bracket">))</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">evens</span><span class="bracket">)</span>  <span class="comment"># (2, 4)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 归约操作</span></span>
                    <span class="code-line"><span class="variable">total</span> <span class="operator">=</span> <span class="variable">reduce</span><span class="bracket">(</span><span class="variable">add</span>, <span class="variable">numbers</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">total</span><span class="bracket">)</span>  <span class="comment"># 15</span></span>
                </div>

                <h2>元组与数据验证</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 使用元组进行数据验证</span></span>
                    <span class="code-line"><span class="keyword">def</span> <span class="function">validate_user_data</span><span class="bracket">(</span><span class="variable">user_tuple</span><span class="bracket">)</span>:</span>
                    <span class="code-line">    <span class="string">"""验证用户数据元组格式"""</span></span>
                    <span class="code-line">    <span class="keyword">if</span> <span class="function">len</span><span class="bracket">(</span><span class="variable">user_tuple</span><span class="bracket">)</span> <span class="operator">!=</span> <span class="number">4</span>:</span>
                    <span class="code-line">        <span class="keyword">return</span> <span class="boolean">False</span>, <span class="string">"数据长度不正确"</span></span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="variable">name</span>, <span class="variable">age</span>, <span class="variable">email</span>, <span class="variable">phone</span> <span class="operator">=</span> <span class="variable">user_tuple</span></span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="comment"># 验证规则</span></span>
                    <span class="code-line">    <span class="keyword">if</span> <span class="operator">not</span> <span class="function">isinstance</span><span class="bracket">(</span><span class="variable">name</span>, <span class="function">str</span><span class="bracket">)</span> <span class="operator">or</span> <span class="function">len</span><span class="bracket">(</span><span class="variable">name</span><span class="bracket">)</span> <span class="operator"><</span> <span class="number">2</span>:</span>
                    <span class="code-line">        <span class="keyword">return</span> <span class="boolean">False</span>, <span class="string">"姓名格式错误"</span></span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="keyword">if</span> <span class="operator">not</span> <span class="function">isinstance</span><span class="bracket">(</span><span class="variable">age</span>, <span class="function">int</span><span class="bracket">)</span> <span class="operator">or</span> <span class="variable">age</span> <span class="operator"><</span> <span class="number">0</span> <span class="operator">or</span> <span class="variable">age</span> <span class="operator">></span> <span class="number">150</span>:</span>
                    <span class="code-line">        <span class="keyword">return</span> <span class="boolean">False</span>, <span class="string">"年龄格式错误"</span></span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="keyword">if</span> <span class="string">"@"</span> <span class="operator">not</span> <span class="keyword">in</span> <span class="variable">email</span>:</span>
                    <span class="code-line">        <span class="keyword">return</span> <span class="boolean">False</span>, <span class="string">"邮箱格式错误"</span></span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="keyword">return</span> <span class="boolean">True</span>, <span class="string">"验证通过"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 测试数据验证</span></span>
                    <span class="code-line"><span class="variable">user1</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"张三"</span>, <span class="number">25</span>, <span class="string">"<EMAIL>"</span>, <span class="string">"13800138000"</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="variable">user2</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"李四"</span>, <span class="number">-5</span>, <span class="string">"invalid-email"</span>, <span class="string">"phone"</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="function">validate_user_data</span><span class="bracket">(</span><span class="variable">user1</span><span class="bracket">))</span>  <span class="comment"># (True, '验证通过')</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="function">validate_user_data</span><span class="bracket">(</span><span class="variable">user2</span><span class="bracket">))</span>  <span class="comment"># (False, '年龄格式错误')</span></span>
                </div>

                <h2>元组与缓存机制</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 利用元组的不可变性实现缓存</span></span>
                    <span class="code-line"><span class="keyword">from</span> <span class="variable">functools</span> <span class="keyword">import</span> <span class="variable">lru_cache</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="decorator">@lru_cache</span><span class="bracket">(</span><span class="variable">maxsize</span><span class="operator">=</span><span class="number">128</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="keyword">def</span> <span class="function">expensive_calculation</span><span class="bracket">(</span><span class="variable">data_tuple</span><span class="bracket">)</span>:</span>
                    <span class="code-line">    <span class="string">"""昂贵的计算函数，使用元组作为缓存键"""</span></span>
                    <span class="code-line">    <span class="function">print</span><span class="bracket">(</span><span class="string">f"正在计算: </span><span class="bracket">{</span><span class="variable">data_tuple</span><span class="bracket">}</span><span class="string">"</span><span class="bracket">)</span></span>
                    <span class="code-line">    <span class="keyword">return</span> <span class="function">sum</span><span class="bracket">(</span><span class="variable">x</span><span class="operator">**</span><span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="variable">data_tuple</span><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 第一次调用会执行计算</span></span>
                    <span class="code-line"><span class="variable">result1</span> <span class="operator">=</span> <span class="function">expensive_calculation</span><span class="bracket">(</span><span class="bracket">(</span><span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span><span class="bracket">))</span></span>
                    <span class="code-line"><span class="comment"># 第二次调用会使用缓存</span></span>
                    <span class="code-line"><span class="variable">result2</span> <span class="operator">=</span> <span class="function">expensive_calculation</span><span class="bracket">(</span><span class="bracket">(</span><span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span><span class="bracket">))</span></span>
                </div>
            </div>
        </div>

        <!-- 第29页：实战项目案例 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">29/30</div>
                <h1><span class="emoji">🛠️</span>实战项目案例</h1>
                
                <h2>项目1：学生成绩管理系统</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 使用元组存储学生信息</span></span>
                    <span class="code-line"><span class="keyword">class</span> <span class="function">StudentGradeSystem</span>:</span>
                    <span class="code-line">    <span class="keyword">def</span> <span class="function">__init__</span><span class="bracket">(</span><span class="variable">self</span><span class="bracket">)</span>:</span>
                    <span class="code-line">        <span class="comment"># 学生信息：(学号, 姓名, 班级, 成绩元组)</span></span>
                    <span class="code-line">        <span class="variable">self</span>.<span class="variable">students</span> <span class="operator">=</span> <span class="bracket">[</span></span>
                    <span class="code-line">            <span class="bracket">(</span><span class="string">"2023001"</span>, <span class="string">"张三"</span>, <span class="string">"计算机1班"</span>, <span class="bracket">(</span><span class="number">85</span>, <span class="number">92</span>, <span class="number">78</span>, <span class="number">88</span><span class="bracket">))</span>,</span>
                    <span class="code-line">            <span class="bracket">(</span><span class="string">"2023002"</span>, <span class="string">"李四"</span>, <span class="string">"计算机1班"</span>, <span class="bracket">(</span><span class="number">90</span>, <span class="number">87</span>, <span class="number">95</span>, <span class="number">92</span><span class="bracket">))</span>,</span>
                    <span class="code-line">            <span class="bracket">(</span><span class="string">"2023003"</span>, <span class="string">"王五"</span>, <span class="string">"计算机2班"</span>, <span class="bracket">(</span><span class="number">76</span>, <span class="number">82</span>, <span class="number">79</span>, <span class="number">85</span><span class="bracket">))</span></span>
                    <span class="code-line">        <span class="bracket">]</span></span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="keyword">def</span> <span class="function">calculate_average</span><span class="bracket">(</span><span class="variable">self</span>, <span class="variable">student_id</span><span class="bracket">)</span>:</span>
                    <span class="code-line">        <span class="string">"""计算学生平均成绩"""</span></span>
                    <span class="code-line">        <span class="keyword">for</span> <span class="variable">sid</span>, <span class="variable">name</span>, <span class="variable">class_name</span>, <span class="variable">grades</span> <span class="keyword">in</span> <span class="variable">self</span>.<span class="variable">students</span>:</span>
                    <span class="code-line">            <span class="keyword">if</span> <span class="variable">sid</span> <span class="operator">==</span> <span class="variable">student_id</span>:</span>
                    <span class="code-line">                <span class="variable">average</span> <span class="operator">=</span> <span class="function">sum</span><span class="bracket">(</span><span class="variable">grades</span><span class="bracket">)</span> <span class="operator">/</span> <span class="function">len</span><span class="bracket">(</span><span class="variable">grades</span><span class="bracket">)</span></span>
                    <span class="code-line">                <span class="keyword">return</span> <span class="variable">name</span>, <span class="variable">average</span></span>
                    <span class="code-line">        <span class="keyword">return</span> <span class="boolean">None</span></span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="keyword">def</span> <span class="function">get_class_statistics</span><span class="bracket">(</span><span class="variable">self</span>, <span class="variable">class_name</span><span class="bracket">)</span>:</span>
                    <span class="code-line">        <span class="string">"""获取班级统计信息"""</span></span>
                    <span class="code-line">        <span class="variable">class_students</span> <span class="operator">=</span> <span class="bracket">[</span></span>
                    <span class="code-line">            <span class="bracket">(</span><span class="variable">name</span>, <span class="variable">grades</span><span class="bracket">)</span> <span class="keyword">for</span> <span class="variable">sid</span>, <span class="variable">name</span>, <span class="variable">cls</span>, <span class="variable">grades</span> <span class="keyword">in</span> <span class="variable">self</span>.<span class="variable">students</span></span>
                    <span class="code-line">            <span class="keyword">if</span> <span class="variable">cls</span> <span class="operator">==</span> <span class="variable">class_name</span></span>
                    <span class="code-line">        <span class="bracket">]</span></span>
                    <span class="code-line">        </span>
                    <span class="code-line">        <span class="variable">averages</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">            <span class="function">sum</span><span class="bracket">(</span><span class="variable">grades</span><span class="bracket">)</span> <span class="operator">/</span> <span class="function">len</span><span class="bracket">(</span><span class="variable">grades</span><span class="bracket">)</span> </span>
                    <span class="code-line">            <span class="keyword">for</span> <span class="variable">name</span>, <span class="variable">grades</span> <span class="keyword">in</span> <span class="variable">class_students</span></span>
                    <span class="code-line">        <span class="bracket">)</span></span>
                    <span class="code-line">        </span>
                    <span class="code-line">        <span class="keyword">return</span> <span class="bracket">{</span></span>
                    <span class="code-line">            <span class="string">'class_name'</span>: <span class="variable">class_name</span>,</span>
                    <span class="code-line">            <span class="string">'student_count'</span>: <span class="function">len</span><span class="bracket">(</span><span class="variable">class_students</span><span class="bracket">)</span>,</span>
                    <span class="code-line">            <span class="string">'class_average'</span>: <span class="function">sum</span><span class="bracket">(</span><span class="variable">averages</span><span class="bracket">)</span> <span class="operator">/</span> <span class="function">len</span><span class="bracket">(</span><span class="variable">averages</span><span class="bracket">)</span>,</span>
                    <span class="code-line">            <span class="string">'highest_average'</span>: <span class="function">max</span><span class="bracket">(</span><span class="variable">averages</span><span class="bracket">)</span>,</span>
                    <span class="code-line">            <span class="string">'lowest_average'</span>: <span class="function">min</span><span class="bracket">(</span><span class="variable">averages</span><span class="bracket">)</span></span>
                    <span class="code-line">        <span class="bracket">}</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 使用示例</span></span>
                    <span class="code-line"><span class="variable">system</span> <span class="operator">=</span> <span class="function">StudentGradeSystem</span><span class="bracket">()</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">system</span>.<span class="function">calculate_average</span><span class="bracket">(</span><span class="string">"2023001"</span><span class="bracket">))</span></span>
                    <span class="code-line"><span class="function">print</span><span class="bracket">(</span><span class="variable">system</span>.<span class="function">get_class_statistics</span><span class="bracket">(</span><span class="string">"计算机1班"</span><span class="bracket">))</span></span>
                </div>

                <h2>项目2：配置管理系统</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 使用元组存储不可变配置</span></span>
                    <span class="code-line"><span class="keyword">class</span> <span class="function">ConfigManager</span>:</span>
                    <span class="code-line">    <span class="comment"># 数据库配置 (host, port, database, user)</span></span>
                    <span class="code-line">    <span class="variable">DB_CONFIG</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"localhost"</span>, <span class="number">5432</span>, <span class="string">"myapp"</span>, <span class="string">"admin"</span><span class="bracket">)</span></span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="comment"># API端点配置 (base_url, version, timeout)</span></span>
                    <span class="code-line">    <span class="variable">API_CONFIG</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"https://api.example.com"</span>, <span class="string">"v1"</span>, <span class="number">30</span><span class="bracket">)</span></span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="comment"># 缓存配置 (redis_host, redis_port, ttl)</span></span>
                    <span class="code-line">    <span class="variable">CACHE_CONFIG</span> <span class="operator">=</span> <span class="bracket">(</span><span class="string">"redis://localhost"</span>, <span class="number">6379</span>, <span class="number">3600</span><span class="bracket">)</span></span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="decorator">@classmethod</span></span>
                    <span class="code-line">    <span class="keyword">def</span> <span class="function">get_db_connection_string</span><span class="bracket">(</span><span class="variable">cls</span><span class="bracket">)</span>:</span>
                    <span class="code-line">        <span class="variable">host</span>, <span class="variable">port</span>, <span class="variable">database</span>, <span class="variable">user</span> <span class="operator">=</span> <span class="variable">cls</span>.<span class="variable">DB_CONFIG</span></span>
                    <span class="code-line">        <span class="keyword">return</span> <span class="string">f"postgresql://</span><span class="bracket">{</span><span class="variable">user</span><span class="bracket">}</span><span class="string">@</span><span class="bracket">{</span><span class="variable">host</span><span class="bracket">}</span><span class="string">:</span><span class="bracket">{</span><span class="variable">port</span><span class="bracket">}</span><span class="string">/</span><span class="bracket">{</span><span class="variable">database</span><span class="bracket">}</span><span class="string">"</span></span>
                    <span class="code-line">    </span>
                    <span class="code-line">    <span class="decorator">@classmethod</span></span>
                    <span class="code-line">    <span class="keyword">def</span> <span class="function">get_api_endpoint</span><span class="bracket">(</span><span class="variable">cls</span>, <span class="variable">path</span><span class="bracket">)</span>:</span>
                    <span class="code-line">        <span class="variable">base_url</span>, <span class="variable">version</span>, <span class="variable">timeout</span> <span class="operator">=</span> <span class="variable">cls</span>.<span class="variable">API_CONFIG</span></span>
                    <span class="code-line">        <span class="keyword">return</span> <span class="string">f"</span><span class="bracket">{</span><span class="variable">base_url</span><span class="bracket">}</span><span class="string">/</span><span class="bracket">{</span><span class="variable">version</span><span class="bracket">}</span><span class="string">/</span><span class="bracket">{</span><span class="variable">path</span><span class="bracket">}</span><span class="string">"</span>, <span class="variable">timeout</span></span>
                </div>

                <h2>项目要点总结</h2>
                <div class="best-practice">
                    <h3><span class="emoji">🎯</span>关键设计原则</h3>
                    <ul>
                        <li><strong>数据完整性</strong>：使用元组保证关键数据不被意外修改</li>
                        <li><strong>性能优化</strong>：利用元组的内存效率和访问速度</li>
                        <li><strong>代码可读性</strong>：通过元组解包提高代码的可读性</li>
                        <li><strong>类型安全</strong>：元组的固定结构提供了隐式的类型检查</li>
                        <li><strong>函数式编程</strong>：元组支持函数式编程范式</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第30页：课程总结 -->
        <div class="slide">
            <div class="slide-content">
                <div class="slide-number">30/30</div>
                <h1><span class="emoji">🎓</span>课程总结与展望</h1>
                
                <h2>知识回顾</h2>
                <div class="best-practice">
                    <h3><span class="emoji">📚</span>核心概念</h3>
                    <ul>
                        <li><strong>元组基础</strong>：不可变的有序数据集合</li>
                        <li><strong>创建与操作</strong>：多种创建方式和基本操作方法</li>
                        <li><strong>元组解包</strong>：优雅的数据提取和多重赋值</li>
                        <li><strong>生成式语法</strong>：高效创建复杂元组的方法</li>
                        <li><strong>实际应用</strong>：在真实项目中的使用场景</li>
                    </ul>
                </div>

                <h2>技能掌握程度自测</h2>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 自测题目：你能理解并写出以下代码吗？</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 1. 复杂元组生成式</span></span>
                    <span class="code-line"><span class="variable">matrix_tuple</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="function">tuple</span><span class="bracket">(</span><span class="variable">i</span> <span class="operator">*</span> <span class="variable">j</span> <span class="keyword">for</span> <span class="variable">j</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="number">1</span>, <span class="number">4</span><span class="bracket">))</span> </span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span><span class="bracket">(</span><span class="number">1</span>, <span class="number">4</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 2. 函数式编程应用</span></span>
                    <span class="code-line"><span class="variable">processed_data</span> <span class="operator">=</span> <span class="function">tuple</span><span class="bracket">(</span></span>
                    <span class="code-line">    <span class="function">map</span><span class="bracket">(</span><span class="keyword">lambda</span> <span class="variable">row</span>: <span class="function">sum</span><span class="bracket">(</span><span class="variable">row</span><span class="bracket">)</span>, <span class="variable">matrix_tuple</span><span class="bracket">)</span></span>
                    <span class="code-line"><span class="bracket">)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 3. 高级解包技巧</span></span>
                    <span class="code-line"><span class="variable">first</span>, <span class="operator">*</span><span class="variable">middle</span>, <span class="variable">last</span> <span class="operator">=</span> <span class="variable">processed_data</span></span>
                </div>

                <h2>下一步学习建议</h2>
                <div class="best-practice">
                    <h3><span class="emoji">🚀</span>进阶方向</h3>
                    <ul>
                        <li><strong>命名元组深入</strong>：collections.namedtuple的高级用法</li>
                        <li><strong>数据类</strong>：学习dataclass作为元组的现代替代</li>
                        <li><strong>类型提示</strong>：使用typing模块为元组添加类型注解</li>
                        <li><strong>性能优化</strong>：深入理解元组的内存模型和优化技巧</li>
                        <li><strong>函数式编程</strong>：结合itertools等模块的高级应用</li>
                    </ul>
                </div>

                <h2>实践建议</h2>
                <div class="best-practice">
                    <h3><span class="emoji">💪</span>持续提升</h3>
                    <ul>
                        <li><strong>日常练习</strong>：在项目中主动使用元组替代列表</li>
                        <li><strong>代码重构</strong>：将现有代码中的可变数据结构改为元组</li>
                        <li><strong>性能测试</strong>：对比不同数据结构的性能差异</li>
                        <li><strong>开源贡献</strong>：参与开源项目，学习最佳实践</li>
                        <li><strong>技术分享</strong>：向他人讲解元组知识，加深理解</li>
                    </ul>
                </div>

                <div class="title-slide" style="text-align: center; margin-top: 40px;">
                    <h2><span class="emoji">🎉</span>恭喜完成Python元组学习！</h2>
                    <p style="font-size: 1.2em; color: #666; margin-top: 20px;">继续探索Python的精彩世界吧！</p>
                </div>
            </div>
        </div>

    </div>

    <!-- 导航控件 -->
    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">上一页</button>
        <button class="nav-btn" onclick="nextSlide()">下一页</button>
    </div>

    <!-- 页面指示器 -->
    <div class="page-indicator">
        <span id="current-page">1</span> / 30
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        function showSlide(index) {
            // 隐藏所有幻灯片
            slides.forEach(slide => {
                slide.classList.remove('active');
            });

            // 显示当前幻灯片
            if (index >= 0 && index < totalSlides) {
                slides[index].classList.add('active');
                currentSlide = index;
                document.getElementById('current-page').textContent = index + 1;
            }
        }

        function nextSlide() {
            if (currentSlide < totalSlides - 1) {
                showSlide(currentSlide + 1);
            }
        }

        function previousSlide() {
            if (currentSlide > 0) {
                showSlide(currentSlide - 1);
            }
        }

        // 键盘导航
        document.addEventListener('keydown', function(event) {
            if (event.key === 'ArrowRight' || event.key === ' ') {
                nextSlide();
            } else if (event.key === 'ArrowLeft') {
                previousSlide();
            }
        });

        // 代码动画效果
        function animateCodeLines() {
            const codeLines = document.querySelectorAll('.code-line');
            codeLines.forEach((line, index) => {
                line.style.animationDelay = `${0.1 + index * 0.02}s`;
            });
        }

        // 页面加载完成后执行动画
        document.addEventListener('DOMContentLoaded', function() {
            animateCodeLines();
        });
    </script>
</body>
</html>