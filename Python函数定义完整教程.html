<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python函数定义完整教程</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow-x: hidden;
        }

        .presentation {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100vh;
            padding: 40px;
            display: none;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            text-align: left;
            background: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow-y: auto;
            animation: slideIn 0.8s ease-in-out;
        }

        .slide.active {
            display: flex;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .emoji {
            font-size: 2em;
            display: inline-block;
            animation: bounce 2s infinite;
            margin-right: 15px;
        }

        h1 {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 30px;
            animation: fadeInUp 1s ease-out;
            text-align: left;
        }

        h2 {
            font-size: 2em;
            color: #34495e;
            margin: 25px 0 15px 0;
            animation: fadeInUp 1.2s ease-out;
            text-align: left;
        }

        h3 {
            font-size: 1.5em;
            color: #7f8c8d;
            margin: 20px 0 10px 0;
            animation: fadeInUp 1.4s ease-out;
            text-align: left;
        }

        p, li {
            font-size: 1.2em;
            line-height: 1.8;
            margin-bottom: 15px;
            animation: fadeInUp 1.6s ease-out;
            text-align: left;
        }

        ul {
            margin-left: 20px;
            text-align: left;
        }

        .code-container {
            background: #1e1e1e;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
            overflow-y: auto;
            animation: fadeInUp 1.8s ease-out;
            width: 100%;
            min-height: 200px;
            max-height: 400px;
        }

        .code {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            color: #d4d4d4;
            font-size: 1.1em;
            line-height: 1.6;
            white-space: pre;
            text-align: left;
        }

        .keyword {
            color: #569cd6;
        }

        .string {
            color: #ce9178;
        }

        .comment {
            color: #6a9955;
        }

        .function {
            color: #dcdcaa;
        }

        .number {
            color: #b5cea8;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
        }

        .nav-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 15px 20px;
            margin: 0 5px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: #2980b9;
            transform: scale(1.1);
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 1em;
        }

        .ai-prompt {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            animation: fadeInUp 2s ease-out;
        }

        .highlight {
            background: #f39c12;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            animation: fadeInUp 2.2s ease-out;
        }

        .example-box {
            border-left: 4px solid #3498db;
            background: #ecf0f1;
            padding: 15px;
            margin: 15px 0;
            animation: fadeInUp 2.4s ease-out;
        }
    </style>
</head>
<body>
    <div class="presentation">
        <div class="slide-counter">
            <span id="current-slide">1</span> / <span id="total-slides">30</span>
        </div>

        <!-- 第1页：标题页 -->
        <div class="slide active">
            <span class="emoji">🐍</span>
            <h1>Python函数定义完整教程</h1>
            <h2>📚 学习内容概览</h2>
            <ul>
                <li>🎯 函数的基本概念与定义</li>
                <li>📝 函数参数的各种类型</li>
                <li>🔄 函数的返回值与作用域</li>
                <li>🚀 高级函数特性</li>
                <li>🤖 AI辅助编程学习方法</li>
            </ul>
            <div class="ai-prompt">
                <h3>🤖 AI学习提示词模板</h3>
                <p>"请详细解释Python函数的定义语法，包括参数类型、返回值、作用域等概念，并提供实际代码示例"</p>
            </div>
        </div>

        <!-- 第2页：什么是函数 -->
        <div class="slide">
            <span class="emoji">🔧</span>
            <h1>什么是函数？</h1>
            <h2>📖 函数的定义</h2>
            <p>函数是一段可重复使用的代码块，用于执行特定的任务。它接收输入（参数），处理数据，并可能返回结果。</p>
            
            <h3>🎯 函数的作用</h3>
            <ul>
                <li>代码重用：避免重复编写相同的代码</li>
                <li>模块化：将复杂问题分解为小的、可管理的部分</li>
                <li>可读性：使代码更易理解和维护</li>
                <li>调试：更容易定位和修复错误</li>
            </ul>

            <div class="example-box">
                <h3>💡 生活中的函数类比</h3>
                <p>就像咖啡机一样：输入咖啡豆和水，经过处理，输出一杯咖啡。</p>
            </div>

            <div class="ai-prompt">
                <h3>🤖 AI学习提示词</h3>
                <p>"用生活中的例子来解释编程中函数的概念，并说明函数在程序设计中的重要性"</p>
            </div>
        </div>

        <!-- 第3页：函数的基本语法 -->
        <div class="slide">
            <span class="emoji">📝</span>
            <h1>函数的基本语法</h1>
            <h2>🔤 Python函数定义语法</h2>
            
            <div class="code-container">
                <div class="code"><span class="keyword">def</span> <span class="function">函数名</span>(<span class="keyword">参数列表</span>):
    <span class="comment"># 函数体</span>
    <span class="comment"># 执行的代码</span>
    <span class="keyword">return</span> <span class="keyword">返回值</span>  <span class="comment"># 可选</span></div>
            </div>

            <h3>🔍 语法要素解析</h3>
            <ul>
                <li><span class="highlight">def</span>：定义函数的关键字</li>
                <li><span class="highlight">函数名</span>：遵循变量命名规则</li>
                <li><span class="highlight">参数列表</span>：函数接收的输入，可以为空</li>
                <li><span class="highlight">冒号(:)</span>：语法要求，不可省略</li>
                <li><span class="highlight">缩进</span>：函数体必须缩进</li>
                <li><span class="highlight">return</span>：返回结果，可选</li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 AI学习提示词</h3>
                <p>"详细解释Python函数定义的语法结构，包括每个组成部分的作用和注意事项"</p>
            </div>
        </div>

        <!-- 第4页：第一个函数示例 -->
        <div class="slide">
            <span class="emoji">🎉</span>
            <h1>第一个函数示例</h1>
            <h2>👋 简单的问候函数</h2>
            
            <div class="code-container">
                <div class="code"><span class="keyword">def</span> <span class="function">say_hello</span>():
    <span class="string">"""这是一个简单的问候函数"""</span>
    <span class="keyword">print</span>(<span class="string">"Hello, World!"</span>)
    <span class="keyword">print</span>(<span class="string">"欢迎学习Python函数！"</span>)

<span class="comment"># 调用函数</span>
<span class="function">say_hello</span>()</div>
            </div>

            <h3>📋 输出结果</h3>
            <div class="example-box">
                <p>Hello, World!</p>
                <p>欢迎学习Python函数！</p>
            </div>

            <h3>🔍 代码解析</h3>
            <ul>
                <li>函数名：<code>say_hello</code></li>
                <li>参数：无参数（空括号）</li>
                <li>功能：打印两行问候语</li>
                <li>调用：<code>say_hello()</code></li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 AI学习提示词</h3>
                <p>"创建一个简单的Python函数示例，解释函数的定义和调用过程"</p>
            </div>
        </div>

        <!-- 第5页：函数参数基础 -->
        <div class="slide">
            <span class="emoji">📥</span>
            <h1>函数参数基础</h1>
            <h2>🎯 什么是参数？</h2>
            <p>参数是函数接收的输入值，使函数能够处理不同的数据并产生相应的结果。</p>
            
            <h3>📝 带参数的函数示例</h3>
            <div class="code-container">
                <div class="code"><span class="keyword">def</span> <span class="function">greet_person</span>(<span class="keyword">name</span>):
    <span class="string">"""问候指定的人"""</span>
    <span class="keyword">print</span>(<span class="string">f"你好，{name}！"</span>)
    <span class="keyword">print</span>(<span class="string">f"很高兴见到你，{name}！"</span>)

<span class="comment"># 调用函数并传递参数</span>
<span class="function">greet_person</span>(<span class="string">"小明"</span>)
<span class="function">greet_person</span>(<span class="string">"小红"</span>)</div>
            </div>

            <h3>📋 输出结果</h3>
            <div class="example-box">
                <p>你好，小明！</p>
                <p>很高兴见到你，小明！</p>
                <p>你好，小红！</p>
                <p>很高兴见到你，小红！</p>
            </div>

            <div class="ai-prompt">
                <h3>🤖 AI学习提示词</h3>
                <p>"解释Python函数参数的概念，并提供带参数的函数定义和调用示例"</p>
            </div>
        </div>

        <!-- 第6页：多个参数 -->
        <div class="slide">
            <span class="emoji">🔢</span>
            <h1>多个参数的函数</h1>
            <h2>➕ 数学运算函数</h2>
            
            <div class="code-container">
                <div class="code"><span class="keyword">def</span> <span class="function">add_numbers</span>(<span class="keyword">a</span>, <span class="keyword">b</span>):
    <span class="string">"""计算两个数的和"""</span>
    <span class="keyword">result</span> = <span class="keyword">a</span> + <span class="keyword">b</span>
    <span class="keyword">print</span>(<span class="string">f"{a} + {b} = {result}"</span>)
    <span class="keyword">return</span> <span class="keyword">result</span>

<span class="keyword">def</span> <span class="function">calculate_area</span>(<span class="keyword">length</span>, <span class="keyword">width</span>):
    <span class="string">"""计算矩形面积"""</span>
    <span class="keyword">area</span> = <span class="keyword">length</span> * <span class="keyword">width</span>
    <span class="keyword">print</span>(<span class="string">f"长{length}，宽{width}的矩形面积是：{area}"</span>)
    <span class="keyword">return</span> <span class="keyword">area</span>

<span class="comment"># 函数调用</span>
<span class="keyword">sum_result</span> = <span class="function">add_numbers</span>(<span class="number">10</span>, <span class="number">20</span>)
<span class="keyword">area_result</span> = <span class="function">calculate_area</span>(<span class="number">5</span>, <span class="number">8</span>)</div>
            </div>

            <h3>🔍 参数传递规则</h3>
            <ul>
                <li>参数按位置顺序传递</li>
                <li>参数数量必须匹配</li>
                <li>参数类型要合适</li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 AI学习提示词</h3>
                <p>"演示Python函数如何接收多个参数，并解释参数传递的规则和注意事项"</p>
            </div>
        </div>

        <!-- 第7页：返回值 -->
        <div class="slide">
            <span class="emoji">↩️</span>
            <h1>函数的返回值</h1>
            <h2>🎁 return语句的作用</h2>
            <p>return语句用于从函数中返回值，并结束函数的执行。</p>
            
            <div class="code-container">
                <div class="code"><span class="keyword">def</span> <span class="function">multiply</span>(<span class="keyword">x</span>, <span class="keyword">y</span>):
    <span class="string">"""计算两个数的乘积"""</span>
    <span class="keyword">result</span> = <span class="keyword">x</span> * <span class="keyword">y</span>
    <span class="keyword">return</span> <span class="keyword">result</span>  <span class="comment"># 返回计算结果</span>

<span class="keyword">def</span> <span class="function">get_user_info</span>(<span class="keyword">name</span>, <span class="keyword">age</span>):
    <span class="string">"""返回用户信息字符串"""</span>
    <span class="keyword">info</span> = <span class="string">f"姓名：{name}，年龄：{age}岁"</span>
    <span class="keyword">return</span> <span class="keyword">info</span>

<span class="comment"># 使用返回值</span>
<span class="keyword">product</span> = <span class="function">multiply</span>(<span class="number">6</span>, <span class="number">7</span>)
<span class="keyword">print</span>(<span class="string">f"乘积是：{product}"</span>)

<span class="keyword">user_data</span> = <span class="function">get_user_info</span>(<span class="string">"张三"</span>, <span class="number">25</span>)
<span class="keyword">print</span>(<span class="keyword">user_data</span>)</div>
            </div>

            <h3>📝 返回值特点</h3>
            <ul>
                <li>可以返回任何类型的数据</li>
                <li>没有return语句时返回None</li>
                <li>return后的代码不会执行</li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 AI学习提示词</h3>
                <p>"详细解释Python函数的return语句，包括返回值的类型、用法和注意事项"</p>
            </div>
        </div>

        <!-- 第8页：默认参数 -->
        <div class="slide">
            <span class="emoji">⚙️</span>
            <h1>默认参数</h1>
            <h2>🔧 为参数设置默认值</h2>
            <p>默认参数允许在调用函数时省略某些参数，使用预设的默认值。</p>
            
            <div class="code-container">
                <div class="code"><span class="keyword">def</span> <span class="function">introduce_person</span>(<span class="keyword">name</span>, <span class="keyword">age</span>=<span class="number">18</span>, <span class="keyword">city</span>=<span class="string">"北京"</span>):
    <span class="string">"""介绍一个人的信息"""</span>
    <span class="keyword">print</span>(<span class="string">f"大家好，我是{name}"</span>)
    <span class="keyword">print</span>(<span class="string">f"我今年{age}岁"</span>)
    <span class="keyword">print</span>(<span class="string">f"我来自{city}"</span>)
    <span class="keyword">print</span>(<span class="string">"-" * 20</span>)

<span class="comment"># 不同的调用方式</span>
<span class="function">introduce_person</span>(<span class="string">"小明"</span>)  <span class="comment"># 使用默认年龄和城市</span>
<span class="function">introduce_person</span>(<span class="string">"小红"</span>, <span class="number">22</span>)  <span class="comment"># 指定年龄，使用默认城市</span>
<span class="function">introduce_person</span>(<span class="string">"小李"</span>, <span class="number">30</span>, <span class="string">"上海"</span>)  <span class="comment"># 指定所有参数</span></div>
            </div>

            <h3>📋 输出结果</h3>
            <div class="example-box">
                <p>大家好，我是小明<br>我今年18岁<br>我来自北京</p>
                <p>大家好，我是小红<br>我今年22岁<br>我来自北京</p>
                <p>大家好，我是小李<br>我今年30岁<br>我来自上海</p>
            </div>

            <div class="ai-prompt">
                <h3>🤖 AI学习提示词</h3>
                <p>"解释Python函数默认参数的概念和用法，提供实际应用示例"</p>
            </div>
        </div>

        <!-- 第9页：关键字参数 -->
        <div class="slide">
            <span class="emoji">🏷️</span>
            <h1>关键字参数</h1>
            <h2>🎯 按名称传递参数</h2>
            <p>关键字参数允许按参数名称传递值，不受位置限制。</p>
            
            <div class="code-container">
                <div class="code"><span class="keyword">def</span> <span class="function">create_profile</span>(<span class="keyword">name</span>, <span class="keyword">age</span>, <span class="keyword">profession</span>, <span class="keyword">hobby</span>):
    <span class="string">"""创建个人档案"""</span>
    <span class="keyword">print</span>(<span class="string">f"=== {name}的个人档案 ==="</span>)
    <span class="keyword">print</span>(<span class="string">f"年龄：{age}岁"</span>)
    <span class="keyword">print</span>(<span class="string">f"职业：{profession}"</span>)
    <span class="keyword">print</span>(<span class="string">f"爱好：{hobby}"</span>)

<span class="comment"># 位置参数调用</span>
<span class="function">create_profile</span>(<span class="string">"王五"</span>, <span class="number">28</span>, <span class="string">"程序员"</span>, <span class="string">"编程"</span>)

<span class="comment"># 关键字参数调用（可以改变顺序）</span>
<span class="function">create_profile</span>(
    <span class="keyword">hobby</span>=<span class="string">"阅读"</span>,
    <span class="keyword">name</span>=<span class="string">"赵六"</span>,
    <span class="keyword">profession</span>=<span class="string">"教师"</span>,
    <span class="keyword">age</span>=<span class="number">35</span>
)

<span class="comment"># 混合使用位置参数和关键字参数</span>
<span class="function">create_profile</span>(<span class="string">"孙七"</span>, <span class="number">24</span>, <span class="keyword">hobby</span>=<span class="string">"音乐"</span>, <span class="keyword">profession</span>=<span class="string">"设计师"</span>)</div>
            </div>

            <h3>✅ 关键字参数优势</h3>
            <ul>
                <li>提高代码可读性</li>
                <li>参数顺序灵活</li>
                <li>减少传参错误</li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 AI学习提示词</h3>
                <p>"演示Python关键字参数的使用方法，解释其优势和应用场景"</p>
            </div>
        </div>

        <!-- 第10页：可变参数 *args -->
        <div class="slide">
            <span class="emoji">📦</span>
            <h1>可变参数 *args</h1>
            <h2>🔄 接收任意数量的位置参数</h2>
            <p>*args允许函数接收任意数量的位置参数，参数被收集到一个元组中。</p>
            
            <div class="code-container">
                <div class="code"><span class="keyword">def</span> <span class="function">calculate_sum</span>(*<span class="keyword">numbers</span>):
    <span class="string">"""计算任意数量数字的和"""</span>
    <span class="keyword">print</span>(<span class="string">f"接收到的参数：{numbers}"</span>)
    <span class="keyword">print</span>(<span class="string">f"参数类型：{type(numbers)}"</span>)
    
    <span class="keyword">total</span> = <span class="number">0</span>
    <span class="keyword">for</span> <span class="keyword">num</span> <span class="keyword">in</span> <span class="keyword">numbers</span>:
        <span class="keyword">total</span> += <span class="keyword">num</span>
    
    <span class="keyword">print</span>(<span class="string">f"总和：{total}"</span>)
    <span class="keyword">return</span> <span class="keyword">total</span>

<span class="comment"># 不同数量的参数调用</span>
<span class="function">calculate_sum</span>(<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>)
<span class="keyword">print</span>(<span class="string">"-" * 30</span>)

<span class="function">calculate_sum</span>(<span class="number">10</span>, <span class="number">20</span>, <span class="number">30</span>, <span class="number">40</span>, <span class="number">50</span>)
<span class="keyword">print</span>(<span class="string">"-" * 30</span>)

<span class="function">calculate_sum</span>(<span class="number">100</span>)  <span class="comment"># 单个参数也可以</span></div>
            </div>

            <h3>🔍 *args特点</h3>
            <ul>
                <li>参数被收集到元组中</li>
                <li>可以接收0个或多个参数</li>
                <li>必须放在普通参数之后</li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 AI学习提示词</h3>
                <p>"详细解释Python中*args的用法，包括其工作原理和实际应用场景"</p>
            </div>
        </div>

        <!-- 第11页：可变关键字参数 **kwargs -->
        <div class="slide">
            <span class="emoji">🗂️</span>
            <h1>可变关键字参数 **kwargs</h1>
            <h2>🏷️ 接收任意数量的关键字参数</h2>
            <p>**kwargs允许函数接收任意数量的关键字参数，参数被收集到一个字典中。</p>
            
            <div class="code-container">
                <div class="code"><span class="keyword">def</span> <span class="function">create_student_info</span>(**<span class="keyword">student_data</span>):
    <span class="string">"""创建学生信息"""</span>
    <span class="keyword">print</span>(<span class="string">f"接收到的数据：{student_data}"</span>)
    <span class="keyword">print</span>(<span class="string">f"数据类型：{type(student_data)}"</span>)
    <span class="keyword">print</span>(<span class="string">"=== 学生信息 ==="</span>)
    
    <span class="keyword">for</span> <span class="keyword">key</span>, <span class="keyword">value</span> <span class="keyword">in</span> <span class="keyword">student_data</span>.<span class="function">items</span>():
        <span class="keyword">print</span>(<span class="string">f"{key}：{value}"</span>)

<span class="comment"># 传递不同的关键字参数</span>
<span class="function">create_student_info</span>(
    <span class="keyword">name</span>=<span class="string">"张三"</span>,
    <span class="keyword">age</span>=<span class="number">20</span>,
    <span class="keyword">major</span>=<span class="string">"计算机科学"</span>,
    <span class="keyword">grade</span>=<span class="string">"大二"</span>
)

<span class="keyword">print</span>(<span class="string">"-" * 30</span>)

<span class="function">create_student_info</span>(
    <span class="keyword">name</span>=<span class="string">"李四"</span>,
    <span class="keyword">hobby</span>=<span class="string">"篮球"</span>,
    <span class="keyword">hometown</span>=<span class="string">"广州"</span>
)</div>
            </div>

            <h3>🔍 **kwargs特点</h3>
            <ul>
                <li>参数被收集到字典中</li>
                <li>键是参数名，值是参数值</li>
                <li>必须放在所有其他参数之后</li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 AI学习提示词</h3>
                <p>"解释Python中**kwargs的概念和用法，并提供实际的代码示例"</p>
            </div>
        </div>

        <!-- 第12页：参数组合使用 -->
        <div class="slide">
            <span class="emoji">🎭</span>
            <h1>参数类型组合使用</h1>
            <h2>🔗 混合使用各种参数类型</h2>
            <p>在一个函数中可以同时使用多种参数类型，但必须遵循特定的顺序。</p>
            
            <div class="code-container">
                <div class="code"><span class="keyword">def</span> <span class="function">complex_function</span>(<span class="keyword">required_param</span>, <span class="keyword">default_param</span>=<span class="string">"默认值"</span>, *<span class="keyword">args</span>, **<span class="keyword">kwargs</span>):
    <span class="string">"""演示参数类型的组合使用"""</span>
    <span class="keyword">print</span>(<span class="string">f"必需参数：{required_param}"</span>)
    <span class="keyword">print</span>(<span class="string">f"默认参数：{default_param}"</span>)
    <span class="keyword">print</span>(<span class="string">f"可变位置参数：{args}"</span>)
    <span class="keyword">print</span>(<span class="string">f"可变关键字参数：{kwargs}"</span>)
    <span class="keyword">print</span>(<span class="string">"-" * 40</span>)

<span class="comment"># 不同的调用方式</span>
<span class="function">complex_function</span>(<span class="string">"必需的值"</span>)

<span class="function">complex_function</span>(<span class="string">"必需的值"</span>, <span class="string">"自定义默认值"</span>)

<span class="function">complex_function</span>(<span class="string">"必需的值"</span>, <span class="string">"自定义默认值"</span>, <span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>)

<span class="function">complex_function</span>(
    <span class="string">"必需的值"</span>,
    <span class="string">"自定义默认值"</span>,
    <span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>,
    <span class="keyword">name</span>=<span class="string">"张三"</span>,
    <span class="keyword">age</span>=<span class="number">25</span>
)</div>
            </div>

            <h3>📋 参数顺序规则</h3>
            <ol>
                <li>普通参数（必需参数）</li>
                <li>默认参数</li>
                <li>*args（可变位置参数）</li>
                <li>**kwargs（可变关键字参数）</li>
            </ol>

            <div class="ai-prompt">
                <h3>🤖 AI学习提示词</h3>
                <p>"演示Python函数中不同参数类型的组合使用，解释参数顺序规则"</p>
            </div>
        </div>

        <!-- 第13页：函数作用域 -->
        <div class="slide">
            <span class="emoji">🌐</span>
            <h1>函数作用域</h1>
            <h2>🔍 变量的可见性范围</h2>
            <p>作用域决定了变量在程序中的可见性和访问范围。</p>
            
            <div class="code-container">
                <div class="code"><span class="comment"># 全局变量</span>
<span class="keyword">global_var</span> = <span class="string">"我是全局变量"</span>

<span class="keyword">def</span> <span class="function">scope_demo</span>():
    <span class="comment"># 局部变量</span>
    <span class="keyword">local_var</span> = <span class="string">"我是局部变量"</span>
    
    <span class="keyword">print</span>(<span class="string">f"函数内部访问全局变量：{global_var}"</span>)
    <span class="keyword">print</span>(<span class="string">f"函数内部访问局部变量：{local_var}"</span>)

<span class="keyword">def</span> <span class="function">modify_global</span>():
    <span class="keyword">global</span> <span class="keyword">global_var</span>  <span class="comment"># 声明要修改全局变量</span>
    <span class="keyword">global_var</span> = <span class="string">"全局变量被修改了"</span>
    <span class="keyword">print</span>(<span class="string">f"修改后的全局变量：{global_var}"</span>)

<span class="comment"># 函数调用</span>
<span class="keyword">print</span>(<span class="string">f"调用函数前的全局变量：{global_var}"</span>)
<span class="function">scope_demo</span>()
<span class="function">modify_global</span>()
<span class="keyword">print</span>(<span class="string">f"函数调用后的全局变量：{global_var}"</span>)

<span class="comment"># 尝试访问局部变量会报错</span>
<span class="comment"># print(local_var)  # NameError: name 'local_var' is not defined</span></div>
            </div>

            <h3>🔍 作用域类型</h3>
            <ul>
                <li><span class="highlight">全局作用域</span>：整个程序可访问</li>
                <li><span class="highlight">局部作用域</span>：仅函数内部可访问</li>
                <li><span class="highlight">global关键字</span>：在函数内修改全局变量</li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 AI学习提示词</h3>
                <p>"详细解释Python函数作用域的概念，包括全局变量和局部变量的区别"</p>
            </div>
        </div>

        <!-- 第14页：lambda函数 -->
        <div class="slide">
            <span class="emoji">⚡</span>
            <h1>Lambda函数（匿名函数）</h1>
            <h2>🚀 简洁的函数定义方式</h2>
            <p>Lambda函数是一种创建小型匿名函数的简洁方式，通常用于简单的操作。</p>
            
            <div class="code-container">
                <div class="code"><span class="comment"># 普通函数定义</span>
<span class="keyword">def</span> <span class="function">square</span>(<span class="keyword">x</span>):
    <span class="keyword">return</span> <span class="keyword">x</span> ** <span class="number">2</span>

<span class="comment"># 等价的lambda函数</span>
<span class="keyword">square_lambda</span> = <span class="keyword">lambda</span> <span class="keyword">x</span>: <span class="keyword">x</span> ** <span class="number">2</span>

<span class="comment"># 使用示例</span>
<span class="keyword">print</span>(<span class="string">f"普通函数：{square(5)}"</span>)
<span class="keyword">print</span>(<span class="string">f"Lambda函数：{square_lambda(5)}"</span>)

<span class="comment"># 多参数lambda函数</span>
<span class="keyword">add</span> = <span class="keyword">lambda</span> <span class="keyword">x</span>, <span class="keyword">y</span>: <span class="keyword">x</span> + <span class="keyword">y</span>
<span class="keyword">print</span>(<span class="string">f"两数相加：{add(10, 20)}"</span>)

<span class="comment"># 在高阶函数中使用lambda</span>
<span class="keyword">numbers</span> = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>]
<span class="keyword">squared_numbers</span> = <span class="function">list</span>(<span class="function">map</span>(<span class="keyword">lambda</span> <span class="keyword">x</span>: <span class="keyword">x</span> ** <span class="number">2</span>, <span class="keyword">numbers</span>))
<span class="keyword">print</span>(<span class="string">f"平方后的数字：{squared_numbers}"</span>)

<span class="keyword">even_numbers</span> = <span class="function">list</span>(<span class="function">filter</span>(<span class="keyword">lambda</span> <span class="keyword">x</span>: <span class="keyword">x</span> % <span class="number">2</span> == <span class="number">0</span>, <span class="keyword">numbers</span>))
<span class="keyword">print</span>(<span class="string">f"偶数：{even_numbers}"</span>)</div>
            </div>

            <h3>✨ Lambda函数特点</h3>
            <ul>
                <li>语法简洁，适合简单操作</li>
                <li>只能包含表达式，不能有语句</li>
                <li>常与map、filter、sort等函数配合使用</li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 AI学习提示词</h3>
                <p>"解释Python lambda函数的概念和用法，对比普通函数定义的区别"</p>
            </div>
        </div>

        <!-- 第15页：递归函数 -->
        <div class="slide">
            <span class="emoji">🔄</span>
            <h1>递归函数</h1>
            <h2>🌀 函数调用自身</h2>
            <p>递归是函数调用自身的编程技术，适用于解决可以分解为相似子问题的问题。</p>
            
            <div class="code-container">
                <div class="code"><span class="keyword">def</span> <span class="function">factorial</span>(<span class="keyword">n</span>):
    <span class="string">"""计算阶乘的递归函数"""</span>
    <span class="comment"># 基础情况（递归终止条件）</span>
    <span class="keyword">if</span> <span class="keyword">n</span> == <span class="number">0</span> <span class="keyword">or</span> <span class="keyword">n</span> == <span class="number">1</span>:
        <span class="keyword">print</span>(<span class="string">f"基础情况：{n}! = 1"</span>)
        <span class="keyword">return</span> <span class="number">1</span>
    <span class="keyword">else</span>:
        <span class="comment"># 递归情况</span>
        <span class="keyword">print</span>(<span class="string">f"计算：{n}! = {n} × {n-1}!"</span>)
        <span class="keyword">result</span> = <span class="keyword">n</span> * <span class="function">factorial</span>(<span class="keyword">n</span> - <span class="number">1</span>)
        <span class="keyword">print</span>(<span class="string">f"返回：{n}! = {result}"</span>)
        <span class="keyword">return</span> <span class="keyword">result</span>

<span class="keyword">def</span> <span class="function">fibonacci</span>(<span class="keyword">n</span>):
    <span class="string">"""计算斐波那契数列的递归函数"""</span>
    <span class="keyword">if</span> <span class="keyword">n</span> <= <span class="number">1</span>:
        <span class="keyword">return</span> <span class="keyword">n</span>
    <span class="keyword">else</span>:
        <span class="keyword">return</span> <span class="function">fibonacci</span>(<span class="keyword">n</span> - <span class="number">1</span>) + <span class="function">fibonacci</span>(<span class="keyword">n</span> - <span class="number">2</span>)

<span class="comment"># 使用递归函数</span>
<span class="keyword">print</span>(<span class="string">"=== 计算5的阶乘 ==="</span>)
<span class="keyword">result</span> = <span class="function">factorial</span>(<span class="number">5</span>)
<span class="keyword">print</span>(<span class="string">f"最终结果：5! = {result}"</span>)

<span class="keyword">print</span>(<span class="string">"\n=== 斐波那契数列前10项 ==="</span>)
<span class="keyword">for</span> <span class="keyword">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">10</span>):
    <span class="keyword">print</span>(<span class="string">f"F({i}) = {fibonacci(i)}"</span>)</div>
            </div>

            <h3>🔑 递归的关键要素</h3>
            <ul>
                <li><span class="highlight">基础情况</span>：递归终止条件</li>
                <li><span class="highlight">递归情况</span>：函数调用自身</li>
                <li><span class="highlight">问题规模缩小</span>：每次递归问题变小</li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 AI学习提示词</h3>
                <p>"详细解释Python递归函数的概念，提供阶乘和斐波那契数列的递归实现"</p>
            </div>
        </div>

        <!-- 第16页：高阶函数 -->
        <div class="slide">
            <span class="emoji">🎯</span>
            <h1>高阶函数</h1>
            <h2>🔧 函数作为参数或返回值</h2>
            <p>高阶函数是接受函数作为参数或返回函数的函数，提供了强大的抽象能力。</p>
            
            <div class="code-container">
                <div class="code"><span class="keyword">def</span> <span class="function">apply_operation</span>(<span class="keyword">numbers</span>, <span class="keyword">operation</span>):
    <span class="string">"""对数字列表应用指定操作"""</span>
    <span class="keyword">result</span> = []
    <span class="keyword">for</span> <span class="keyword">num</span> <span class="keyword">in</span> <span class="keyword">numbers</span>:
        <span class="keyword">result</span>.<span class="function">append</span>(<span class="function">operation</span>(<span class="keyword">num</span>))
    <span class="keyword">return</span> <span class="keyword">result</span>

<span class="keyword">def</span> <span class="function">double</span>(<span class="keyword">x</span>):
    <span class="string">"""数字翻倍"""</span>
    <span class="keyword">return</span> <span class="keyword">x</span> * <span class="number">2</span>

<span class="keyword">def</span> <span class="function">square</span>(<span class="keyword">x</span>):
    <span class="string">"""数字平方"""</span>
    <span class="keyword">return</span> <span class="keyword">x</span> ** <span class="number">2</span>

<span class="keyword">def</span> <span class="function">create_multiplier</span>(<span class="keyword">factor</span>):
    <span class="string">"""创建乘法器函数（返回函数的函数）"""</span>
    <span class="keyword">def</span> <span class="function">multiplier</span>(<span class="keyword">x</span>):
        <span class="keyword">return</span> <span class="keyword">x</span> * <span class="keyword">factor</span>
    <span class="keyword">return</span> <span class="function">multiplier</span>

<span class="comment"># 使用高阶函数</span>
<span class="keyword">numbers</span> = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>]

<span class="keyword">doubled</span> = <span class="function">apply_operation</span>(<span class="keyword">numbers</span>, <span class="function">double</span>)
<span class="keyword">print</span>(<span class="string">f"翻倍结果：{doubled}"</span>)

<span class="keyword">squared</span> = <span class="function">apply_operation</span>(<span class="keyword">numbers</span>, <span class="function">square</span>)
<span class="keyword">print</span>(<span class="string">f"平方结果：{squared}"</span>)

<span class="comment"># 使用返回函数的函数</span>
<span class="keyword">times_3</span> = <span class="function">create_multiplier</span>(<span class="number">3</span>)
<span class="keyword">times_10</span> = <span class="function">create_multiplier</span>(<span class="number">10</span>)

<span class="keyword">print</span>(<span class="string">f"5 × 3 = {times_3(5)}"</span>)
<span class="keyword">print</span>(<span class="string">f"5 × 10 = {times_10(5)}"</span>)</div>
            </div>

            <h3>🌟 高阶函数的应用</h3>
            <ul>
                <li>map()、filter()、reduce()等内置函数</li>
                <li>装饰器模式</li>
                <li>函数式编程</li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 AI学习提示词</h3>
                <p>"解释Python高阶函数的概念，演示函数作为参数和返回值的用法"</p>
            </div>
        </div>

        <!-- 第17页：装饰器基础 -->
        <div class="slide">
            <span class="emoji">🎨</span>
            <h1>装饰器基础</h1>
            <h2>✨ 增强函数功能的优雅方式</h2>
            <p>装饰器是一种设计模式，允许在不修改原函数代码的情况下增加额外功能。</p>
            
            <div class="code-container">
                <div class="code"><span class="keyword">import</span> <span class="keyword">time</span>

<span class="keyword">def</span> <span class="function">timer_decorator</span>(<span class="keyword">func</span>):
    <span class="string">"""计时装饰器"""</span>
    <span class="keyword">def</span> <span class="function">wrapper</span>(*<span class="keyword">args</span>, **<span class="keyword">kwargs</span>):
        <span class="keyword">start_time</span> = <span class="keyword">time</span>.<span class="function">time</span>()
        <span class="keyword">print</span>(<span class="string">f"开始执行函数：{func.__name__}"</span>)
        
        <span class="keyword">result</span> = <span class="function">func</span>(*<span class="keyword">args</span>, **<span class="keyword">kwargs</span>)
        
        <span class="keyword">end_time</span> = <span class="keyword">time</span>.<span class="function">time</span>()
        <span class="keyword">execution_time</span> = <span class="keyword">end_time</span> - <span class="keyword">start_time</span>
        <span class="keyword">print</span>(<span class="string">f"函数 {func.__name__} 执行时间：{execution_time:.4f}秒"</span>)
        
        <span class="keyword">return</span> <span class="keyword">result</span>
    <span class="keyword">return</span> <span class="function">wrapper</span>

<span class="keyword">def</span> <span class="function">log_decorator</span>(<span class="keyword">func</span>):
    <span class="string">"""日志装饰器"""</span>
    <span class="keyword">def</span> <span class="function">wrapper</span>(*<span class="keyword">args</span>, **<span class="keyword">kwargs</span>):
        <span class="keyword">print</span>(<span class="string">f"📝 调用函数：{func.__name__}"</span>)
        <span class="keyword">print</span>(<span class="string">f"📥 参数：args={args}, kwargs={kwargs}"</span>)
        
        <span class="keyword">result</span> = <span class="function">func</span>(*<span class="keyword">args</span>, **<span class="keyword">kwargs</span>)
        
        <span class="keyword">print</span>(<span class="string">f"📤 返回值：{result}"</span>)
        <span class="keyword">return</span> <span class="keyword">result</span>
    <span class="keyword">return</span> <span class="function">wrapper</span>

<span class="comment"># 使用装饰器</span>
<span class="keyword">@timer_decorator</span>
<span class="keyword">@log_decorator</span>
<span class="keyword">def</span> <span class="function">calculate_sum</span>(<span class="keyword">n</span>):
    <span class="string">"""计算1到n的和"""</span>
    <span class="keyword">total</span> = <span class="function">sum</span>(<span class="function">range</span>(<span class="number">1</span>, <span class="keyword">n</span> + <span class="number">1</span>))
    <span class="keyword">return</span> <span class="keyword">total</span>

<span class="comment"># 调用被装饰的函数</span>
<span class="keyword">result</span> = <span class="function">calculate_sum</span>(<span class="number">1000</span>)</div>
            </div>

            <h3>🔧 装饰器工作原理</h3>
            <ul>
                <li>装饰器本质上是高阶函数</li>
                <li>@语法是语法糖，简化装饰器使用</li>
                <li>可以叠加多个装饰器</li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 AI学习提示词</h3>
                <p>"详细解释Python装饰器的概念和工作原理，提供实际的装饰器示例"</p>
            </div>
        </div>

        <!-- 第18页：函数注解 -->
        <div class="slide">
            <span class="emoji">📋</span>
            <h1>函数注解</h1>
            <h2>🏷️ 类型提示和文档化</h2>
            <p>函数注解提供了一种为函数参数和返回值添加类型提示的方法，提高代码可读性。</p>
            
            <div class="code-container">
                <div class="code"><span class="keyword">def</span> <span class="function">calculate_bmi</span>(<span class="keyword">weight</span>: <span class="keyword">float</span>, <span class="keyword">height</span>: <span class="keyword">float</span>) -> <span class="keyword">float</span>:
    <span class="string">"""计算BMI指数
    
    Args:
        weight: 体重（公斤）
        height: 身高（米）
    
    Returns:
        BMI指数
    """</span>
    <span class="keyword">bmi</span> = <span class="keyword">weight</span> / (<span class="keyword">height</span> ** <span class="number">2</span>)
    <span class="keyword">return</span> <span class="function">round</span>(<span class="keyword">bmi</span>, <span class="number">2</span>)

<span class="keyword">def</span> <span class="function">greet_user</span>(<span class="keyword">name</span>: <span class="keyword">str</span>, <span class="keyword">age</span>: <span class="keyword">int</span>, <span class="keyword">is_student</span>: <span class="keyword">bool</span> = <span class="keyword">False</span>) -> <span class="keyword">str</span>:
    <span class="string">"""生成用户问候语"""</span>
    <span class="keyword">status</span> = <span class="string">"学生"</span> <span class="keyword">if</span> <span class="keyword">is_student</span> <span class="keyword">else</span> <span class="string">"非学生"</span>
    <span class="keyword">return</span> <span class="string">f"你好，{name}！你今年{age}岁，身份：{status}"</span>

<span class="keyword">from</span> <span class="keyword">typing</span> <span class="keyword">import</span> <span class="keyword">List</span>, <span class="keyword">Dict</span>, <span class="keyword">Optional</span>

<span class="keyword">def</span> <span class="function">process_scores</span>(<span class="keyword">scores</span>: <span class="keyword">List</span>[<span class="keyword">int</span>]) -> <span class="keyword">Dict</span>[<span class="keyword">str</span>, <span class="keyword">float</span>]:
    <span class="string">"""处理分数列表，返回统计信息"""</span>
    <span class="keyword">return</span> {
        <span class="string">"平均分"</span>: <span class="function">sum</span>(<span class="keyword">scores</span>) / <span class="function">len</span>(<span class="keyword">scores</span>),
        <span class="string">"最高分"</span>: <span class="function">max</span>(<span class="keyword">scores</span>),
        <span class="string">"最低分"</span>: <span class="function">min</span>(<span class="keyword">scores</span>)
    }

<span class="comment"># 使用带注解的函数</span>
<span class="keyword">bmi_result</span> = <span class="function">calculate_bmi</span>(<span class="number">70.0</span>, <span class="number">1.75</span>)
<span class="keyword">print</span>(<span class="string">f"BMI: {bmi_result}"</span>)

<span class="keyword">greeting</span> = <span class="function">greet_user</span>(<span class="string">"小明"</span>, <span class="number">20</span>, <span class="keyword">True</span>)
<span class="keyword">print</span>(<span class="keyword">greeting</span>)</div>
            </div>

            <h3>✨ 注解的优势</h3>
            <ul>
                <li>提高代码可读性</li>
                <li>IDE支持更好的代码提示</li>
                <li>便于静态类型检查</li>
                <li>自动生成文档</li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 AI学习提示词</h3>
                <p>"解释Python函数注解的概念和用法，包括类型提示的最佳实践"</p>
            </div>
        </div>

        <!-- 第19页：AI辅助编程入门 -->
        <div class="slide">
            <span class="emoji">🤖</span>
            <h1>AI辅助编程入门</h1>
            <h2>🚀 利用AI提升编程效率</h2>
            <p>AI编程助手可以帮助我们更快地学习和编写Python函数，提供代码建议、错误修复和最佳实践指导。</p>
            
            <h3>🛠️ 主流AI编程工具</h3>
            <ul>
                <li><span class="highlight">GitHub Copilot</span>：代码自动补全</li>
                <li><span class="highlight">ChatGPT</span>：代码解释和生成</li>
                <li><span class="highlight">Claude</span>：代码分析和优化</li>
                <li><span class="highlight">Cursor</span>：AI驱动的代码编辑器</li>
            </ul>

            <div class="example-box">
                <h3>💡 AI辅助学习的优势</h3>
                <ul>
                    <li>即时获得代码解释</li>
                    <li>快速生成示例代码</li>
                    <li>发现代码中的问题</li>
                    <li>学习最佳编程实践</li>
                </ul>
            </div>

            <div class="ai-prompt">
                <h3>🤖 AI学习提示词模板</h3>
                <p>"我是Python初学者，请帮我解释这个函数的工作原理，并提供一个简单的使用示例"</p>
            </div>
        </div>

        <!-- 第20页：AI提示词技巧 -->
        <div class="slide">
            <span class="emoji">💬</span>
            <h1>AI提示词编写技巧</h1>
            <h2>📝 如何与AI有效沟通</h2>
            <p>好的提示词能帮助AI更准确地理解你的需求，提供更有用的回答。</p>
            
            <h3>✅ 有效提示词的特征</h3>
            <ul>
                <li><span class="highlight">具体明确</span>：说明具体需求和场景</li>
                <li><span class="highlight">包含上下文</span>：提供相关背景信息</li>
                <li><span class="highlight">指定格式</span>：说明期望的输出格式</li>
                <li><span class="highlight">举例说明</span>：提供期望结果的示例</li>
            </ul>

            <div class="code-container">
                <div class="code"><span class="comment"># 不好的提示词</span>
<span class="string">"帮我写个函数"</span>

<span class="comment"># 好的提示词</span>
<span class="string">"请帮我写一个Python函数，用于计算列表中所有偶数的平均值。
函数应该：
1. 接收一个整数列表作为参数
2. 过滤出所有偶数
3. 计算平均值并返回
4. 如果没有偶数，返回0
5. 包含详细的注释和类型提示

请提供完整的函数定义和使用示例。"</span></div>
            </div>

            <div class="ai-prompt">
                <h3>🤖 函数学习专用提示词</h3>
                <p>"请详细解释这个Python函数的每一行代码，包括参数、返回值、算法逻辑，并提供3个不同的使用示例"</p>
            </div>
        </div>

        <!-- 第21页：AI代码审查 -->
        <div class="slide">
            <span class="emoji">🔍</span>
            <h1>AI代码审查与优化</h1>
            <h2>🛠️ 让AI帮你改进代码</h2>
            <p>AI可以帮助审查代码质量，发现潜在问题，并提供优化建议。</p>
            
            <div class="code-container">
                <div class="code"><span class="comment"># 需要优化的代码示例</span>
<span class="keyword">def</span> <span class="function">bad_function</span>(<span class="keyword">data</span>):
    <span class="keyword">result</span> = []
    <span class="keyword">for</span> <span class="keyword">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="function">len</span>(<span class="keyword">data</span>)):
        <span class="keyword">if</span> <span class="keyword">data</span>[<span class="keyword">i</span>] % <span class="number">2</span> == <span class="number">0</span>:
            <span class="keyword">result</span>.<span class="function">append</span>(<span class="keyword">data</span>[<span class="keyword">i</span>] * <span class="number">2</span>)
    <span class="keyword">return</span> <span class="keyword">result</span>

<span class="comment"># AI优化后的代码</span>
<span class="keyword">def</span> <span class="function">optimized_function</span>(<span class="keyword">numbers</span>: <span class="keyword">List</span>[<span class="keyword">int</span>]) -> <span class="keyword">List</span>[<span class="keyword">int</span>]:
    <span class="string">"""返回列表中所有偶数的两倍
    
    Args:
        numbers: 整数列表
    
    Returns:
        包含所有偶数两倍的列表
    """</span>
    <span class="keyword">return</span> [<span class="keyword">num</span> * <span class="number">2</span> <span class="keyword">for</span> <span class="keyword">num</span> <span class="keyword">in</span> <span class="keyword">numbers</span> <span class="keyword">if</span> <span class="keyword">num</span> % <span class="number">2</span> == <span class="number">0</span>]</div>
            </div>

            <h3>🎯 AI代码审查要点</h3>
            <ul>
                <li>代码可读性和命名规范</li>
                <li>性能优化建议</li>
                <li>错误处理和边界情况</li>
                <li>代码风格和最佳实践</li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 代码审查提示词</h3>
                <p>"请审查这段Python代码，指出可能的问题和改进建议，包括性能、可读性、错误处理等方面"</p>
            </div>
        </div>

        <!-- 第22页：AI学习路径规划 -->
        <div class="slide">
            <span class="emoji">🗺️</span>
            <h1>AI辅助学习路径规划</h1>
            <h2>📚 制定个性化学习计划</h2>
            <p>利用AI制定适合自己的Python函数学习路径，循序渐进地掌握各种概念。</p>
            
            <h3>🎯 学习阶段划分</h3>
            <div class="example-box">
                <h4>📖 初级阶段（1-2周）</h4>
                <ul>
                    <li>函数基本语法和调用</li>
                    <li>参数传递和返回值</li>
                    <li>简单的实际应用</li>
                </ul>
                
                <h4>🚀 中级阶段（2-3周）</h4>
                <ul>
                    <li>默认参数和关键字参数</li>
                    <li>*args和**kwargs</li>
                    <li>作用域和闭包</li>
                </ul>
                
                <h4>⭐ 高级阶段（3-4周）</h4>
                <ul>
                    <li>装饰器和高阶函数</li>
                    <li>递归和函数式编程</li>
                    <li>类型注解和最佳实践</li>
                </ul>
            </div>

            <div class="ai-prompt">
                <h3>🤖 学习规划提示词</h3>
                <p>"我是Python初学者，请为我制定一个4周的函数学习计划，包括每天的学习内容、练习题目和项目实践"</p>
            </div>
        </div>

        <!-- 第23页：实战项目建议 -->
        <div class="slide">
            <span class="emoji">🛠️</span>
            <h1>函数实战项目建议</h1>
            <h2>💼 通过项目巩固知识</h2>
            <p>通过实际项目来应用所学的函数知识，加深理解并提升编程能力。</p>
            
            <h3>🎮 项目难度分级</h3>
            <div class="example-box">
                <h4>🟢 初级项目</h4>
                <ul>
                    <li>计算器程序（加减乘除函数）</li>
                    <li>温度转换器（摄氏度华氏度互转）</li>
                    <li>简单的文本处理工具</li>
                </ul>
                
                <h4>🟡 中级项目</h4>
                <ul>
                    <li>学生成绩管理系统</li>
                    <li>简单的银行账户模拟</li>
                    <li>文件批量处理工具</li>
                </ul>
                
                <h4>🔴 高级项目</h4>
                <ul>
                    <li>数据分析工具包</li>
                    <li>Web API接口设计</li>
                    <li>自动化测试框架</li>
                </ul>
            </div>

            <div class="ai-prompt">
                <h3>🤖 项目指导提示词</h3>
                <p>"请为我设计一个Python函数练习项目，要求使用到默认参数、*args、**kwargs等特性，并提供完整的实现思路"</p>
            </div>
        </div>

        <!-- 第24页：函数性能优化 -->
        <div class="slide">
            <span class="emoji">⚡</span>
            <h1>函数性能优化</h1>
            <h2>🚀 提升函数执行效率</h2>
            <p>了解如何优化函数性能，编写更高效的Python代码。</p>
            
            <div class="code-container">
                <div class="code"><span class="keyword">import</span> <span class="keyword">time</span>
<span class="keyword">from</span> <span class="keyword">functools</span> <span class="keyword">import</span> <span class="keyword">lru_cache</span>

<span class="comment"># 未优化的递归函数</span>
<span class="keyword">def</span> <span class="function">fibonacci_slow</span>(<span class="keyword">n</span>):
    <span class="keyword">if</span> <span class="keyword">n</span> <= <span class="number">1</span>:
        <span class="keyword">return</span> <span class="keyword">n</span>
    <span class="keyword">return</span> <span class="function">fibonacci_slow</span>(<span class="keyword">n</span>-<span class="number">1</span>) + <span class="function">fibonacci_slow</span>(<span class="keyword">n</span>-<span class="number">2</span>)

<span class="comment"># 使用缓存优化的递归函数</span>
<span class="keyword">@lru_cache</span>(<span class="keyword">maxsize</span>=<span class="keyword">None</span>)
<span class="keyword">def</span> <span class="function">fibonacci_fast</span>(<span class="keyword">n</span>):
    <span class="keyword">if</span> <span class="keyword">n</span> <= <span class="number">1</span>:
        <span class="keyword">return</span> <span class="keyword">n</span>
    <span class="keyword">return</span> <span class="function">fibonacci_fast</span>(<span class="keyword">n</span>-<span class="number">1</span>) + <span class="function">fibonacci_fast</span>(<span class="keyword">n</span>-<span class="number">2</span>)

<span class="comment"># 性能测试</span>
<span class="keyword">def</span> <span class="function">performance_test</span>():
    <span class="keyword">n</span> = <span class="number">35</span>
    
    <span class="keyword">start</span> = <span class="keyword">time</span>.<span class="function">time</span>()
    <span class="keyword">result1</span> = <span class="function">fibonacci_slow</span>(<span class="keyword">n</span>)
    <span class="keyword">time1</span> = <span class="keyword">time</span>.<span class="function">time</span>() - <span class="keyword">start</span>
    
    <span class="keyword">start</span> = <span class="keyword">time</span>.<span class="function">time</span>()
    <span class="keyword">result2</span> = <span class="function">fibonacci_fast</span>(<span class="keyword">n</span>)
    <span class="keyword">time2</span> = <span class="keyword">time</span>.<span class="function">time</span>() - <span class="keyword">start</span>
    
    <span class="keyword">print</span>(<span class="string">f"未优化版本：{time1:.4f}秒"</span>)
    <span class="keyword">print</span>(<span class="string">f"优化版本：{time2:.4f}秒"</span>)
    <span class="keyword">print</span>(<span class="string">f"性能提升：{time1/time2:.0f}倍"</span>)</div>
            </div>

            <h3>🎯 性能优化技巧</h3>
            <ul>
                <li><span class="highlight">使用缓存</span>：避免重复计算</li>
                <li><span class="highlight">选择合适的数据结构</span>：列表vs字典vs集合</li>
                <li><span class="highlight">避免不必要的循环</span>：使用内置函数</li>
                <li><span class="highlight">生成器表达式</span>：节省内存</li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 性能优化提示词</h3>
                <p>"请分析这个Python函数的性能瓶颈，并提供具体的优化建议和改进后的代码"</p>
            </div>
        </div>

        <!-- 第25页：函数测试 -->
        <div class="slide">
            <span class="emoji">🧪</span>
            <h1>函数测试</h1>
            <h2>✅ 确保函数正确性</h2>
            <p>编写测试用例来验证函数的正确性，是良好编程实践的重要组成部分。</p>
            
            <div class="code-container">
                <div class="code"><span class="keyword">def</span> <span class="function">divide_numbers</span>(<span class="keyword">a</span>: <span class="keyword">float</span>, <span class="keyword">b</span>: <span class="keyword">float</span>) -> <span class="keyword">float</span>:
    <span class="string">"""安全的除法函数"""</span>
    <span class="keyword">if</span> <span class="keyword">b</span> == <span class="number">0</span>:
        <span class="keyword">raise</span> <span class="keyword">ValueError</span>(<span class="string">"除数不能为零"</span>)
    <span class="keyword">return</span> <span class="keyword">a</span> / <span class="keyword">b</span>

<span class="comment"># 简单的测试函数</span>
<span class="keyword">def</span> <span class="function">test_divide_numbers</span>():
    <span class="string">"""测试除法函数"""</span>
    <span class="comment"># 正常情况测试</span>
    <span class="keyword">assert</span> <span class="function">divide_numbers</span>(<span class="number">10</span>, <span class="number">2</span>) == <span class="number">5.0</span>
    <span class="keyword">assert</span> <span class="function">divide_numbers</span>(<span class="number">7</span>, <span class="number">3</span>) == <span class="number">7</span>/<span class="number">3</span>
    
    <span class="comment"># 边界情况测试</span>
    <span class="keyword">assert</span> <span class="function">divide_numbers</span>(<span class="number">0</span>, <span class="number">5</span>) == <span class="number">0.0</span>
    
    <span class="comment"># 异常情况测试</span>
    <span class="keyword">try</span>:
        <span class="function">divide_numbers</span>(<span class="number">10</span>, <span class="number">0</span>)
        <span class="keyword">assert</span> <span class="keyword">False</span>, <span class="string">"应该抛出异常"</span>
    <span class="keyword">except</span> <span class="keyword">ValueError</span>:
        <span class="keyword">pass</span>  <span class="comment"># 预期的异常</span>
    
    <span class="keyword">print</span>(<span class="string">"✅ 所有测试通过！"</span>)

<span class="comment"># 使用unittest模块</span>
<span class="keyword">import</span> <span class="keyword">unittest</span>

<span class="keyword">class</span> <span class="function">TestDivideNumbers</span>(<span class="keyword">unittest</span>.<span class="keyword">TestCase</span>):
    <span class="keyword">def</span> <span class="function">test_normal_division</span>(<span class="keyword">self</span>):
        <span class="keyword">self</span>.<span class="function">assertEqual</span>(<span class="function">divide_numbers</span>(<span class="number">10</span>, <span class="number">2</span>), <span class="number">5.0</span>)
    
    <span class="keyword">def</span> <span class="function">test_zero_division</span>(<span class="keyword">self</span>):
        <span class="keyword">with</span> <span class="keyword">self</span>.<span class="function">assertRaises</span>(<span class="keyword">ValueError</span>):
            <span class="function">divide_numbers</span>(<span class="number">10</span>, <span class="number">0</span>)

<span class="comment"># 运行测试</span>
<span class="function">test_divide_numbers</span>()</div>
            </div>

            <h3>🔍 测试类型</h3>
            <ul>
                <li><span class="highlight">单元测试</span>：测试单个函数</li>
                <li><span class="highlight">边界测试</span>：测试极端情况</li>
                <li><span class="highlight">异常测试</span>：测试错误处理</li>
                <li><span class="highlight">性能测试</span>：测试执行效率</li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 测试编写提示词</h3>
                <p>"请为这个Python函数编写完整的测试用例，包括正常情况、边界情况和异常情况的测试"</p>
            </div>
        </div>

        <!-- 第26页：函数文档化 -->
        <div class="slide">
            <span class="emoji">📚</span>
            <h1>函数文档化</h1>
            <h2>📝 编写清晰的函数文档</h2>
            <p>良好的文档是代码可维护性的关键，帮助其他开发者理解和使用你的函数。</p>
            
            <div class="code-container">
                <div class="code"><span class="keyword">def</span> <span class="function">calculate_compound_interest</span>(
    <span class="keyword">principal</span>: <span class="keyword">float</span>,
    <span class="keyword">rate</span>: <span class="keyword">float</span>,
    <span class="keyword">time</span>: <span class="keyword">float</span>,
    <span class="keyword">compound_frequency</span>: <span class="keyword">int</span> = <span class="number">1</span>
) -> <span class="keyword">float</span>:
    <span class="string">"""计算复利
    
    这个函数使用复利公式计算投资的最终价值：
    A = P(1 + r/n)^(nt)
    
    Args:
        principal (float): 本金金额
        rate (float): 年利率（小数形式，如0.05表示5%）
        time (float): 投资时间（年）
        compound_frequency (int, optional): 每年复利次数。默认为1（年复利）
    
    Returns:
        float: 投资的最终价值
    
    Raises:
        ValueError: 当参数为负数时抛出异常
    
    Examples:
        >>> calculate_compound_interest(1000, 0.05, 10)
        1628.89
        
        >>> calculate_compound_interest(1000, 0.05, 10, 12)
        1643.62
    
    Note:
        利率应该以小数形式输入，例如5%应该输入为0.05
    """</span>
    <span class="keyword">if</span> <span class="keyword">principal</span> < <span class="number">0</span> <span class="keyword">or</span> <span class="keyword">rate</span> < <span class="number">0</span> <span class="keyword">or</span> <span class="keyword">time</span> < <span class="number">0</span>:
        <span class="keyword">raise</span> <span class="keyword">ValueError</span>(<span class="string">"所有参数必须为非负数"</span>)
    
    <span class="keyword">amount</span> = <span class="keyword">principal</span> * (<span class="number">1</span> + <span class="keyword">rate</span> / <span class="keyword">compound_frequency</span>) ** (<span class="keyword">compound_frequency</span> * <span class="keyword">time</span>)
    <span class="keyword">return</span> <span class="function">round</span>(<span class="keyword">amount</span>, <span class="number">2</span>)</div>
            </div>

            <h3>📋 文档字符串组成部分</h3>
            <ul>
                <li><span class="highlight">简短描述</span>：一句话说明函数功能</li>
                <li><span class="highlight">详细说明</span>：函数的工作原理</li>
                <li><span class="highlight">参数说明</span>：每个参数的类型和含义</li>
                <li><span class="highlight">返回值</span>：返回值的类型和含义</li>
                <li><span class="highlight">异常</span>：可能抛出的异常</li>
                <li><span class="highlight">示例</span>：使用示例</li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 文档编写提示词</h3>
                <p>"请为这个Python函数编写完整的文档字符串，包括参数说明、返回值、异常处理和使用示例"</p>
            </div>
        </div>

        <!-- 第27页：常见错误与调试 -->
        <div class="slide">
            <span class="emoji">🐛</span>
            <h1>常见错误与调试</h1>
            <h2>🔧 识别和修复函数错误</h2>
            <p>了解Python函数中的常见错误类型，学会有效的调试技巧。</p>
            
            <div class="code-container">
                <div class="code"><span class="comment"># 常见错误1：参数数量不匹配</span>
<span class="keyword">def</span> <span class="function">greet</span>(<span class="keyword">name</span>, <span class="keyword">age</span>):
    <span class="keyword">return</span> <span class="string">f"Hello {name}, you are {age} years old"</span>

<span class="comment"># greet("Alice")  # TypeError: 缺少参数</span>

<span class="comment"># 常见错误2：变量作用域问题</span>
<span class="keyword">x</span> = <span class="number">10</span>
<span class="keyword">def</span> <span class="function">modify_x</span>():
    <span class="keyword">x</span> = <span class="number">20</span>  <span class="comment"># 这是局部变量，不会影响全局x</span>
    <span class="keyword">print</span>(<span class="string">f"函数内x: {x}"</span>)

<span class="function">modify_x</span>()
<span class="keyword">print</span>(<span class="string">f"全局x: {x}"</span>)  <span class="comment"># 仍然是10</span>

<span class="comment"># 常见错误3：可变默认参数</span>
<span class="keyword">def</span> <span class="function">bad_append</span>(<span class="keyword">item</span>, <span class="keyword">target_list</span>=[]):  <span class="comment"># 危险！</span>
    <span class="keyword">target_list</span>.<span class="function">append</span>(<span class="keyword">item</span>)
    <span class="keyword">return</span> <span class="keyword">target_list</span>

<span class="comment"># 正确的写法</span>
<span class="keyword">def</span> <span class="function">good_append</span>(<span class="keyword">item</span>, <span class="keyword">target_list</span>=<span class="keyword">None</span>):
    <span class="keyword">if</span> <span class="keyword">target_list</span> <span class="keyword">is</span> <span class="keyword">None</span>:
        <span class="keyword">target_list</span> = []
    <span class="keyword">target_list</span>.<span class="function">append</span>(<span class="keyword">item</span>)
    <span class="keyword">return</span> <span class="keyword">target_list</span>

<span class="comment"># 调试技巧：使用print调试</span>
<span class="keyword">def</span> <span class="function">debug_function</span>(<span class="keyword">numbers</span>):
    <span class="keyword">print</span>(<span class="string">f"输入参数: {numbers}"</span>)
    <span class="keyword">result</span> = []
    <span class="keyword">for</span> <span class="keyword">i</span>, <span class="keyword">num</span> <span class="keyword">in</span> <span class="function">enumerate</span>(<span class="keyword">numbers</span>):
        <span class="keyword">print</span>(<span class="string">f"处理第{i}个元素: {num}"</span>)
        <span class="keyword">if</span> <span class="keyword">num</span> > <span class="number">0</span>:
            <span class="keyword">result</span>.<span class="function">append</span>(<span class="keyword">num</span> * <span class="number">2</span>)
    <span class="keyword">print</span>(<span class="string">f"最终结果: {result}"</span>)
    <span class="keyword">return</span> <span class="keyword">result</span></div>
            </div>

            <h3>🚨 常见错误类型</h3>
            <ul>
                <li><span class="highlight">TypeError</span>：参数类型或数量错误</li>
                <li><span class="highlight">NameError</span>：变量名未定义</li>
                <li><span class="highlight">IndentationError</span>：缩进错误</li>
                <li><span class="highlight">UnboundLocalError</span>：局部变量引用错误</li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 调试帮助提示词</h3>
                <p>"这个Python函数出现了错误，请帮我分析错误原因并提供修复建议"</p>
            </div>
        </div>

        <!-- 第28页：最佳实践总结 -->
        <div class="slide">
            <span class="emoji">⭐</span>
            <h1>函数编写最佳实践</h1>
            <h2>🏆 成为更好的Python程序员</h2>
            <p>遵循这些最佳实践，编写出高质量、可维护的Python函数。</p>
            
            <h3>📝 命名规范</h3>
            <ul>
                <li>使用小写字母和下划线：<code>calculate_total</code></li>
                <li>函数名应该是动词：<code>get_user_info</code></li>
                <li>避免缩写：<code>calculate_average</code> 而不是 <code>calc_avg</code></li>
            </ul>

            <h3>🎯 函数设计原则</h3>
            <ul>
                <li><span class="highlight">单一职责</span>：一个函数只做一件事</li>
                <li><span class="highlight">参数合理</span>：避免过多参数（建议不超过5个）</li>
                <li><span class="highlight">返回一致</span>：总是返回相同类型的值</li>
                <li><span class="highlight">避免副作用</span>：尽量不修改全局状态</li>
            </ul>

            <div class="example-box">
                <h3>✅ 好的函数示例</h3>
                <div class="code-container">
                    <div class="code"><span class="keyword">def</span> <span class="function">calculate_tax</span>(<span class="keyword">income</span>: <span class="keyword">float</span>, <span class="keyword">tax_rate</span>: <span class="keyword">float</span>) -> <span class="keyword">float</span>:
    <span class="string">"""计算税额
    
    Args:
        income: 收入金额
        tax_rate: 税率（0-1之间的小数）
    
    Returns:
        应缴税额
    """</span>
    <span class="keyword">if</span> <span class="keyword">income</span> < <span class="number">0</span> <span class="keyword">or</span> <span class="keyword">tax_rate</span> < <span class="number">0</span> <span class="keyword">or</span> <span class="keyword">tax_rate</span> > <span class="number">1</span>:
        <span class="keyword">raise</span> <span class="keyword">ValueError</span>(<span class="string">"参数值无效"</span>)
    
    <span class="keyword">return</span> <span class="keyword">income</span> * <span class="keyword">tax_rate</span></div>
                </div>
            </div>

            <div class="ai-prompt">
                <h3>🤖 代码审查提示词</h3>
                <p>"请审查这个Python函数是否遵循了最佳实践，包括命名、文档、错误处理等方面"</p>
            </div>
        </div>

        <!-- 第29页：进阶学习资源 -->
        <div class="slide">
            <span class="emoji">📖</span>
            <h1>进阶学习资源</h1>
            <h2>🚀 继续提升Python技能</h2>
            <p>推荐一些优质的学习资源，帮助你深入掌握Python函数和编程技巧。</p>
            
            <h3>📚 推荐书籍</h3>
            <ul>
                <li><span class="highlight">《Python编程：从入门到实践》</span> - Eric Matthes</li>
                <li><span class="highlight">《流畅的Python》</span> - Luciano Ramalho</li>
                <li><span class="highlight">《Python Tricks》</span> - Dan Bader</li>
                <li><span class="highlight">《Effective Python》</span> - Brett Slatkin</li>
            </ul>

            <h3>🌐 在线资源</h3>
            <ul>
                <li><span class="highlight">Python官方文档</span>：docs.python.org</li>
                <li><span class="highlight">Real Python</span>：realpython.com</li>
                <li><span class="highlight">Python.org教程</span>：python.org/doc/</li>
                <li><span class="highlight">LeetCode</span>：练习算法和数据结构</li>
            </ul>

            <h3>🎯 实践平台</h3>
            <ul>
                <li><span class="highlight">GitHub</span>：参与开源项目</li>
                <li><span class="highlight">Kaggle</span>：数据科学竞赛</li>
                <li><span class="highlight">HackerRank</span>：编程挑战</li>
                <li><span class="highlight">Codewars</span>：代码练习</li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 学习规划提示词</h3>
                <p>"基于我目前的Python函数知识水平，请为我推荐下一步的学习内容和具体的学习计划"</p>
            </div>
        </div>

        <!-- 第30页：总结 -->
        <div class="slide">
            <span class="emoji">🎉</span>
            <h1>课程总结</h1>
            <h2>🏁 Python函数学习之旅完成！</h2>
            <p>恭喜你完成了Python函数的完整学习！让我们回顾一下所学的重要内容。</p>
            
            <h3>📋 知识点回顾</h3>
            <div class="example-box">
                <h4>🔧 基础概念</h4>
                <ul>
                    <li>函数定义语法和调用方法</li>
                    <li>参数传递和返回值</li>
                    <li>作用域和变量生命周期</li>
                </ul>
                
                <h4>⚙️ 高级特性</h4>
                <ul>
                    <li>默认参数、*args、**kwargs</li>
                    <li>Lambda函数和高阶函数</li>
                    <li>装饰器和递归</li>
                    <li>函数注解和类型提示</li>
                </ul>
                
                <h4>🤖 AI辅助编程</h4>
                <ul>
                    <li>AI工具的使用方法</li>
                    <li>提示词编写技巧</li>
                    <li>代码审查和优化</li>
                </ul>
            </div>

            <h3>🎯 下一步行动</h3>
            <ul>
                <li>🔨 <span class="highlight">多练习</span>：通过项目巩固所学知识</li>
                <li>📖 <span class="highlight">深入学习</span>：探索更高级的Python特性</li>
                <li>🤝 <span class="highlight">参与社区</span>：加入Python开发者社区</li>
                <li>🚀 <span class="highlight">持续改进</span>：不断优化代码质量</li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 继续学习提示词</h3>
                <p>"我已经掌握了Python函数的基础知识，请为我推荐下一个学习主题，比如面向对象编程或数据结构"</p>
            </div>

            <div style="text-align: center; margin-top: 40px;">
                <h2>🌟 感谢学习！祝你编程愉快！🌟</h2>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">← 上一页</button>
        <button class="nav-btn" onclick="nextSlide()">下一页 →</button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        
        document.getElementById('total-slides').textContent = totalSlides;
        
        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            document.getElementById('current-slide').textContent = currentSlide + 1;
        }
        
        function nextSlide() {
            showSlide(currentSlide + 1);
        }
        
        function previousSlide() {
            showSlide(currentSlide - 1);
        }
        
        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                previousSlide();
            }
        });
        
        // 触摸滑动支持
        let startX = 0;
        let endX = 0;
        
        document.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
        });
        
        document.addEventListener('touchend', function(e) {
            endX = e.changedTouches[0].clientX;
            handleSwipe();
        });
        
        function handleSwipe() {
            const threshold = 50;
            const diff = startX - endX;
            
            if (Math.abs(diff) > threshold) {
                if (diff > 0) {
                    nextSlide();
                } else {
                    previousSlide();
                }
            }
        }
        
        // 自动播放功能（可选）
        let autoPlay = false;
        let autoPlayInterval;
        
        function toggleAutoPlay() {
            if (autoPlay) {
                clearInterval(autoPlayInterval);
                autoPlay = false;
            } else {
                autoPlayInterval = setInterval(nextSlide, 5000);
                autoPlay = true;
            }
        }
        
        // 双击切换自动播放
        document.addEventListener('dblclick', toggleAutoPlay);
    </script>
</body>
</html>