<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python变量与数据类型教学PPT</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
            height: 100vh;
        }

        .ppt-container {
            position: relative;
            width: 100%;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slide {
            position: absolute;
            width: 90%;
            max-width: 1200px;
            height: 85vh;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            display: none;
            opacity: 0;
            transform: translateX(100px);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .slide:first-child {
            display: block;
            opacity: 1;
            transform: translateX(0);
        }

        .slide.active {
            display: block !important;
            opacity: 1 !important;
            transform: translateX(0) !important;
        }

        .slide-content {
            padding: 40px;
            height: 100%;
            overflow-y: auto;
            text-align: left;
        }

        .slide-content::-webkit-scrollbar {
            width: 8px;
        }

        .slide-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .slide-content::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        .slide-content::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        h1 {
            font-size: 3em;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            animation: titleSlideIn 1s ease-out;
        }

        h2 {
            font-size: 2.2em;
            color: #34495e;
            margin-bottom: 25px;
            text-align: left;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }

        h3 {
            font-size: 1.6em;
            color: #2980b9;
            margin: 20px 0 15px 0;
            text-align: left;
            animation: fadeInUp 0.8s ease-out 0.4s both;
        }

        p, li {
            font-size: 1.2em;
            line-height: 1.8;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: left;
            animation: fadeInUp 0.8s ease-out 0.6s both;
        }

        ul {
            padding-left: 30px;
            margin-bottom: 20px;
        }

        li {
            margin-bottom: 10px;
        }

        .emoji {
            font-size: 1.5em;
            margin-right: 10px;
            animation: bounce 2s infinite;
        }

        .code-block {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 1.1em;
            line-height: 1.6;
            text-align: left;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow-x: auto;
            animation: codeSlideIn 1s ease-out 0.8s both;
        }

        .code-block::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 30px;
            background: #323233;
            border-radius: 12px 12px 0 0;
        }

        .code-block::after {
            content: '● ● ●';
            position: absolute;
            top: 8px;
            left: 15px;
            color: #ff6b6b;
            font-size: 12px;
        }

        .code-line {
            display: block;
            margin-top: 35px;
            animation: typewriter 0.05s ease-out forwards;
            opacity: 0;
        }

        .keyword { color: #569cd6; }
        .string { color: #ce9178; }
        .number { color: #b5cea8; }
        .comment { color: #6a9955; }
        .function { color: #dcdcaa; }
        .variable { color: #9cdcfe; }

        .highlight-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: left;
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
            animation: highlightPulse 2s ease-in-out infinite alternate;
        }

        .tips-box {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: left;
            border-left: 5px solid #0066cc;
            animation: fadeInLeft 1s ease-out 1s both;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            color: #2c3e50;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .page-indicator {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: rgba(52, 73, 94, 0.9);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 1001;
        }

        .slide-number {
            position: absolute;
            top: 20px;
            right: 30px;
            background: rgba(52, 73, 94, 0.8);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
        }

        @keyframes titleSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes codeSlideIn {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        @keyframes highlightPulse {
            from {
                box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
            }
            to {
                box-shadow: 0 12px 35px rgba(240, 147, 251, 0.6);
            }
        }

        @keyframes typewriter {
            to {
                opacity: 1;
            }
        }

        .prompt-template {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            border-left: 4px solid #4299e1;
            font-family: 'Consolas', monospace;
            animation: fadeInUp 0.8s ease-out 1.2s both;
        }

        .ai-tool {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: left;
            animation: fadeInUp 0.8s ease-out 0.6s both;
        }
    </style>
</head>
<body>
    <div class="ppt-container">
        <!-- 第1页：标题页 -->
        <div class="slide">
            <div class="slide-number">1/30</div>
            <div class="slide-content">
                <h1><span class="emoji">🐍</span>Python变量与数据类型</h1>
                <div style="text-align: center; margin-top: 80px;">
                    <h2><span class="emoji">📚</span>完整教学指南</h2>
                    <p style="font-size: 1.5em; color: #7f8c8d; margin-top: 40px;">从基础概念到AI辅助编程</p>
                    <p style="font-size: 1.2em; color: #95a5a6; margin-top: 30px;">掌握Python数据处理的核心技能</p>
                </div>
            </div>
        </div>

        <!-- 第2页：目录 -->
        <div class="slide">
            <div class="slide-number">2/30</div>
            <div class="slide-content">
                <h2><span class="emoji">📋</span>课程大纲</h2>
                <ul style="font-size: 1.3em; line-height: 2.2;">
                    <li><span class="emoji">🔤</span>Python变量基础概念</li>
                    <li><span class="emoji">🔢</span>数字类型 (int, float, complex)</li>
                    <li><span class="emoji">📝</span>字符串类型处理</li>
                    <li><span class="emoji">✅</span>布尔类型应用</li>
                    <li><span class="emoji">📝</span>列表类型操作</li>
                    <li><span class="emoji">📦</span>元组类型特性</li>
                    <li><span class="emoji">🗂️</span>字典类型应用</li>
                    <li><span class="emoji">🎯</span>集合类型运算</li>
                    <li><span class="emoji">🤖</span>AI辅助编程工具</li>
                    <li><span class="emoji">💡</span>提示词模板与技巧</li>
                </ul>
            </div>
        </div>

        <!-- 第3页：Python变量基础 -->
        <div class="slide">
            <div class="slide-number">3/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🔤</span>Python变量基础</h2>
                <h3>什么是变量？</h3>
                <p>变量是存储数据值的容器。在Python中，变量不需要声明，赋值即创建。</p>
                
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 创建变量</span></span>
                    <span class="code-line"><span class="variable">name</span> = <span class="string">"Python"</span></span>
                    <span class="code-line"><span class="variable">age</span> = <span class="number">30</span></span>
                    <span class="code-line"><span class="variable">is_awesome</span> = <span class="keyword">True</span></span>
                </div>

                <div class="highlight-box">
                    <p><strong>核心特点：</strong></p>
                    <ul>
                        <li>动态类型 - 无需声明类型</li>
                        <li>区分大小写 - name 和 Name 是不同变量</li>
                        <li>命名规则 - 字母、数字、下划线组合</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第4页：变量命名规则 -->
        <div class="slide">
            <div class="slide-number">4/30</div>
            <div class="slide-content">
                <h2><span class="emoji">📝</span>变量命名规则</h2>
                
                <h3>合法的变量名</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">user_name</span> = <span class="string">"张三"</span></span>
                    <span class="code-line"><span class="variable">age1</span> = <span class="number">25</span></span>
                    <span class="code-line"><span class="variable">_private_var</span> = <span class="string">"隐私"</span></span>
                    <span class="code-line"><span class="variable">MAX_SIZE</span> = <span class="number">100</span></span>
                </div>

                <h3>非法的变量名</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 以下命名会报错</span></span>
                    <span class="code-line"><span class="comment"># 1name = "错误"     # 不能以数字开头</span></span>
                    <span class="code-line"><span class="comment"># my-var = "错误"   # 不能包含连字符</span></span>
                    <span class="code-line"><span class="comment"># class = "错误"    # 不能使用保留字</span></span>
                </div>

                <div class="tips-box">
                    <p><strong>📌 命名建议：</strong></p>
                    <ul>
                        <li>使用描述性名称：user_age 比 a 更好</li>
                        <li>常量使用大写：MAX_USERS = 1000</li>
                        <li>私有变量以下划线开头：_internal_data</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第5页：变量赋值与重新赋值 -->
        <div class="slide">
            <div class="slide-number">5/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🔄</span>变量赋值与重新赋值</h2>
                
                <h3>基本赋值</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">x</span> = <span class="number">10</span>          <span class="comment"># 整数赋值</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">x</span>)       <span class="comment"># 输出: 10</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="variable">x</span> = <span class="string">"Hello"</span>     <span class="comment"># 重新赋值为字符串</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">x</span>)       <span class="comment"># 输出: Hello</span></span>
                </div>

                <h3>多重赋值</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 同时给多个变量赋相同值</span></span>
                    <span class="code-line"><span class="variable">a</span> = <span class="variable">b</span> = <span class="variable">c</span> = <span class="number">100</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 同时给多个变量赋不同值</span></span>
                    <span class="code-line"><span class="variable">name</span>, <span class="variable">age</span>, <span class="variable">city</span> = <span class="string">"李四"</span>, <span class="number">28</span>, <span class="string">"北京"</span></span>
                </div>

                <div class="highlight-box">
                    <p><strong>重要概念：</strong>Python中变量是对象的引用，而不是内存位置！</p>
                </div>
            </div>
        </div>

        <!-- 第6页：整数类型 -->
        <div class="slide">
            <div class="slide-number">6/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🔢</span>整数类型 (int)</h2>
                
                <h3>基本特性</h3>
                <p>Python的整数类型可以存储任意大小的整数，不受位数限制。</p>
                
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 不同进制的整数</span></span>
                    <span class="code-line"><span class="variable">decimal_num</span> = <span class="number">42</span>         <span class="comment"># 十进制</span></span>
                    <span class="code-line"><span class="variable">binary_num</span> = <span class="number">0b101010</span>    <span class="comment"># 二进制</span></span>
                    <span class="code-line"><span class="variable">octal_num</span> = <span class="number">0o52</span>        <span class="comment"># 八进制</span></span>
                    <span class="code-line"><span class="variable">hex_num</span> = <span class="number">0x2A</span>         <span class="comment"># 十六进制</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 大整数</span></span>
                    <span class="code-line"><span class="variable">big_number</span> = <span class="number">123456789012345678901234567890</span></span>
                </div>

                <h3>常用操作</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">a</span> = <span class="number">10</span></span>
                    <span class="code-line"><span class="variable">b</span> = <span class="number">3</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">a</span> + <span class="variable">b</span>)     <span class="comment"># 加法: 13</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">a</span> - <span class="variable">b</span>)     <span class="comment"># 减法: 7</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">a</span> * <span class="variable">b</span>)     <span class="comment"># 乘法: 30</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">a</span> // <span class="variable">b</span>)    <span class="comment"># 整除: 3</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">a</span> % <span class="variable">b</span>)     <span class="comment"># 取余: 1</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">a</span> ** <span class="variable">b</span>)    <span class="comment"># 幂运算: 1000</span></span>
                </div>
            </div>
        </div>

        <!-- 第7页：浮点数类型 -->
        <div class="slide">
            <div class="slide-number">7/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🎯</span>浮点数类型 (float)</h2>
                
                <h3>浮点数的表示</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 小数形式</span></span>
                    <span class="code-line"><span class="variable">pi</span> = <span class="number">3.14159</span></span>
                    <span class="code-line"><span class="variable">price</span> = <span class="number">99.99</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 科学计数法</span></span>
                    <span class="code-line"><span class="variable">light_speed</span> = <span class="number">3e8</span>      <span class="comment"># 3 × 10^8</span></span>
                    <span class="code-line"><span class="variable">tiny_number</span> = <span class="number">1.5e-10</span>  <span class="comment"># 1.5 × 10^-10</span></span>
                </div>

                <h3>浮点数运算</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">x</span> = <span class="number">10.5</span></span>
                    <span class="code-line"><span class="variable">y</span> = <span class="number">2.3</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">x</span> + <span class="variable">y</span>)           <span class="comment"># 12.8</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">x</span> / <span class="variable">y</span>)           <span class="comment"># 4.565217391304348</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">round</span>(<span class="variable">x</span> / <span class="variable">y</span>, <span class="number">2</span>)) <span class="comment"># 4.57 (保留2位小数)</span></span>
                </div>

                <div class="highlight-box">
                    <p><strong>⚠️ 注意：</strong>浮点数运算可能存在精度问题</p>
                    <div class="code-block" style="margin-top: 15px;">
                        <span class="code-line"><span class="function">print</span>(<span class="number">0.1</span> + <span class="number">0.2</span>)  <span class="comment"># 0.30000000000000004</span></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第8页：复数类型 -->
        <div class="slide">
            <div class="slide-number">8/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🔬</span>复数类型 (complex)</h2>
                
                <h3>复数的表示</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 复数创建</span></span>
                    <span class="code-line"><span class="variable">z1</span> = <span class="number">3</span> + <span class="number">4j</span>        <span class="comment"># 标准形式</span></span>
                    <span class="code-line"><span class="variable">z2</span> = <span class="function">complex</span>(<span class="number">2</span>, <span class="number">-3</span>) <span class="comment"># 使用complex()函数</span></span>
                    <span class="code-line"><span class="variable">z3</span> = <span class="number">5j</span>            <span class="comment"># 纯虚数</span></span>
                </div>

                <h3>复数操作</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">z</span> = <span class="number">3</span> + <span class="number">4j</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">z</span>.<span class="function">real</span>)      <span class="comment"># 实部: 3.0</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">z</span>.<span class="function">imag</span>)      <span class="comment"># 虚部: 4.0</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">abs</span>(<span class="variable">z</span>))      <span class="comment"># 模长: 5.0</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">z</span>.<span class="function">conjugate</span>()) <span class="comment"># 共轭: (3-4j)</span></span>
                </div>

                <h3>复数运算</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">a</span> = <span class="number">1</span> + <span class="number">2j</span></span>
                    <span class="code-line"><span class="variable">b</span> = <span class="number">2</span> - <span class="number">1j</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">a</span> + <span class="variable">b</span>)  <span class="comment"># (3+1j)</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">a</span> * <span class="variable">b</span>)  <span class="comment"># (4+3j)</span></span>
                </div>

                <div class="tips-box">
                    <p><strong>🎓 应用场景：</strong>科学计算、信号处理、量子计算等领域</p>
                </div>
            </div>
        </div>

        <!-- 第9页：数字类型转换 -->
        <div class="slide">
            <div class="slide-number">9/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🔄</span>数字类型转换</h2>
                
                <h3>类型转换函数</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 转换为整数</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">int</span>(<span class="number">3.14</span>))     <span class="comment"># 3</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">int</span>(<span class="string">"42"</span>))     <span class="comment"># 42</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 转换为浮点数</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">float</span>(<span class="number">10</span>))     <span class="comment"># 10.0</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">float</span>(<span class="string">"3.5"</span>))  <span class="comment"># 3.5</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 转换为复数</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">complex</span>(<span class="number">5</span>))   <span class="comment"># (5+0j)</span></span>
                </div>

                <h3>类型检查</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">num</span> = <span class="number">42</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">type</span>(<span class="variable">num</span>))          <span class="comment"># &lt;class 'int'&gt;</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">isinstance</span>(<span class="variable">num</span>, <span class="keyword">int</span>)) <span class="comment"># True</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 检查是否为数字类型</span></span>
                    <span class="code-line"><span class="keyword">import</span> numbers</span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">isinstance</span>(<span class="variable">num</span>, numbers.Number)) <span class="comment"># True</span></span>
                </div>

                <div class="highlight-box">
                    <p><strong>💡 实用技巧：</strong></p>
                    <ul>
                        <li>int() 截断小数部分，不是四舍五入</li>
                        <li>使用 round() 函数进行四舍五入</li>
                        <li>转换前检查数据有效性，避免异常</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第10页：字符串基础 -->
        <div class="slide">
            <div class="slide-number">10/30</div>
            <div class="slide-content">
                <h2><span class="emoji">📝</span>字符串类型基础</h2>
                
                <h3>字符串创建</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 单引号和双引号</span></span>
                    <span class="code-line"><span class="variable">name1</span> = <span class="string">'Python'</span></span>
                    <span class="code-line"><span class="variable">name2</span> = <span class="string">"Python"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 三引号多行字符串</span></span>
                    <span class="code-line"><span class="variable">text</span> = <span class="string">"""</span></span>
                    <span class="code-line"><span class="string">这是一个</span></span>
                    <span class="code-line"><span class="string">多行字符串</span></span>
                    <span class="code-line"><span class="string">"""</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 原始字符串</span></span>
                    <span class="code-line"><span class="variable">path</span> = <span class="string">r"C:\Users\<USER>\file.txt"</span></span>
                </div>

                <h3>字符串拼接</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">first_name</span> = <span class="string">"张"</span></span>
                    <span class="code-line"><span class="variable">last_name</span> = <span class="string">"三"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 使用 + 号拼接</span></span>
                    <span class="code-line"><span class="variable">full_name</span> = <span class="variable">first_name</span> + <span class="variable">last_name</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 使用 join() 方法</span></span>
                    <span class="code-line"><span class="variable">result</span> = <span class="string">""</span>.<span class="function">join</span>([<span class="variable">first_name</span>, <span class="variable">last_name</span>])</span>
                </div>

                <div class="tips-box">
                    <p><strong>📌 字符串特性：</strong></p>
                    <ul>
                        <li>不可变 (immutable) - 创建后无法修改</li>
                        <li>有序 - 支持索引和切片操作</li>
                        <li>可迭代 - 可以遍历每个字符</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第11页：字符串格式化 -->
        <div class="slide">
            <div class="slide-number">11/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🎨</span>字符串格式化</h2>
                
                <h3>f-string格式化 (推荐)</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">name</span> = <span class="string">"李明"</span></span>
                    <span class="code-line"><span class="variable">age</span> = <span class="number">25</span></span>
                    <span class="code-line"><span class="variable">score</span> = <span class="number">87.5</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 基本格式化</span></span>
                    <span class="code-line"><span class="variable">info</span> = <span class="string">f"姓名: {name}, 年龄: {age}"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 表达式和格式控制</span></span>
                    <span class="code-line"><span class="variable">result</span> = <span class="string">f"{name}的分数是{score:.1f}分"</span></span>
                    <span class="code-line"><span class="variable">birth_year</span> = <span class="string">f"出生年份: {2024 - age}"</span></span>
                </div>

                <h3>format()方法</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 位置参数</span></span>
                    <span class="code-line"><span class="variable">text1</span> = <span class="string">"我叫{}, 今年{}岁"</span>.<span class="function">format</span>(<span class="variable">name</span>, <span class="variable">age</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 关键字参数</span></span>
                    <span class="code-line"><span class="variable">text2</span> = <span class="string">"我叫{n}, 今年{a}岁"</span>.<span class="function">format</span>(n=<span class="variable">name</span>, a=<span class="variable">age</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 数字格式化</span></span>
                    <span class="code-line"><span class="variable">price</span> = <span class="number">1234.567</span></span>
                    <span class="code-line"><span class="variable">formatted</span> = <span class="string">"价格: ¥{:.2f}"</span>.<span class="function">format</span>(<span class="variable">price</span>)</span>
                </div>

                <div class="highlight-box">
                    <p><strong>🚀 格式化选项：</strong></p>
                    <ul>
                        <li>{:.2f} - 保留2位小数</li>
                        <li>{:>10} - 右对齐，宽度10</li>
                        <li>{:0>5} - 用0填充到5位</li>
                        <li>{:%} - 百分比格式</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第12页：字符串方法 -->
        <div class="slide">
            <div class="slide-number">12/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🛠️</span>字符串常用方法</h2>
                
                <h3>大小写转换</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">text</span> = <span class="string">"Hello Python World"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">text</span>.<span class="function">upper</span>())     <span class="comment"># HELLO PYTHON WORLD</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">text</span>.<span class="function">lower</span>())     <span class="comment"># hello python world</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">text</span>.<span class="function">title</span>())     <span class="comment"># Hello Python World</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">text</span>.<span class="function">capitalize</span>()) <span class="comment"># Hello python world</span></span>
                </div>

                <h3>查找和替换</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">sentence</span> = <span class="string">"Python是最好的编程语言"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">sentence</span>.<span class="function">find</span>(<span class="string">"Python"</span>))    <span class="comment"># 0</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">sentence</span>.<span class="function">count</span>(<span class="string">"最"</span>))       <span class="comment"># 1</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">sentence</span>.<span class="function">replace</span>(<span class="string">"最好"</span>, <span class="string">"很棒"</span>))</span>
                </div>

                <h3>分割和连接</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">data</span> = <span class="string">"苹果,香蕉,橙子,葡萄"</span></span>
                    <span class="code-line"><span class="variable">fruits</span> = <span class="variable">data</span>.<span class="function">split</span>(<span class="string">","</span>)         <span class="comment"># 分割成列表</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">fruits</span>)                   <span class="comment"># ['苹果', '香蕉', '橙子', '葡萄']</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="variable">result</span> = <span class="string">" | "</span>.<span class="function">join</span>(<span class="variable">fruits</span>)     <span class="comment"># 重新连接</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">result</span>)                   <span class="comment"># 苹果 | 香蕉 | 橙子 | 葡萄</span></span>
                </div>

                <div class="tips-box">
                    <p><strong>💡 实用方法：</strong>strip(), startswith(), endswith(), isdigit(), isalpha()</p>
                </div>
            </div>
        </div>

        <!-- 第13页：布尔类型 -->
        <div class="slide">
            <div class="slide-number">13/30</div>
            <div class="slide-content">
                <h2><span class="emoji">✅</span>布尔类型 (bool)</h2>
                
                <h3>布尔值基础</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 布尔值只有两个: True 和 False</span></span>
                    <span class="code-line"><span class="variable">is_student</span> = <span class="keyword">True</span></span>
                    <span class="code-line"><span class="variable">is_graduated</span> = <span class="keyword">False</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 比较运算产生布尔值</span></span>
                    <span class="code-line"><span class="variable">age</span> = <span class="number">18</span></span>
                    <span class="code-line"><span class="variable">is_adult</span> = <span class="variable">age</span> >= <span class="number">18</span>  <span class="comment"># True</span></span>
                    <span class="code-line"><span class="variable">is_teenager</span> = <span class="number">13</span> <= <span class="variable">age</span> < <span class="number">20</span>  <span class="comment"># True</span></span>
                </div>

                <h3>逻辑运算</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">a</span> = <span class="keyword">True</span></span>
                    <span class="code-line"><span class="variable">b</span> = <span class="keyword">False</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">a</span> <span class="keyword">and</span> <span class="variable">b</span>)    <span class="comment"># False (与运算)</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">a</span> <span class="keyword">or</span> <span class="variable">b</span>)     <span class="comment"># True  (或运算)</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="keyword">not</span> <span class="variable">a</span>)       <span class="comment"># False (非运算)</span></span>
                </div>

                <h3>真值测试</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 这些值被认为是 False</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">bool</span>(<span class="number">0</span>))        <span class="comment"># False</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">bool</span>(<span class="string">""</span>))       <span class="comment"># False (空字符串)</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">bool</span>([]))       <span class="comment"># False (空列表)</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">bool</span>(<span class="keyword">None</span>))     <span class="comment"># False</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 其他值通常是 True</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">bool</span>(<span class="number">42</span>))       <span class="comment"># True</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">bool</span>(<span class="string">"Hello"</span>))  <span class="comment"># True</span></span>
                </div>

                <div class="highlight-box">
                    <p><strong>🎯 实际应用：</strong>条件判断、循环控制、数据验证</p>
                </div>
            </div>
        </div>

        <!-- 第14页：列表基础 -->
        <div class="slide">
            <div class="slide-number">14/30</div>
            <div class="slide-content">
                <h2><span class="emoji">📝</span>列表类型基础</h2>
                
                <h3>列表创建和特性</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 创建列表</span></span>
                    <span class="code-line"><span class="variable">numbers</span> = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>]</span>
                    <span class="code-line"><span class="variable">names</span> = [<span class="string">"张三"</span>, <span class="string">"李四"</span>, <span class="string">"王五"</span>]</span>
                    <span class="code-line"><span class="variable">mixed</span> = [<span class="number">1</span>, <span class="string">"hello"</span>, <span class="number">3.14</span>, <span class="keyword">True</span>]  <span class="comment"># 混合类型</span></span>
                    <span class="code-line"><span class="variable">empty</span> = []                     <span class="comment"># 空列表</span></span>
                </div>

                <h3>索引和切片</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">fruits</span> = [<span class="string">"苹果"</span>, <span class="string">"香蕉"</span>, <span class="string">"橙子"</span>, <span class="string">"葡萄"</span>, <span class="string">"西瓜"</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 正向索引</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">fruits</span>[<span class="number">0</span>])     <span class="comment"># 苹果 (第一个)</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">fruits</span>[<span class="number">2</span>])     <span class="comment"># 橙子 (第三个)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 负向索引</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">fruits</span>[-<span class="number">1</span>])    <span class="comment"># 西瓜 (最后一个)</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">fruits</span>[-<span class="number">2</span>])    <span class="comment"># 葡萄 (倒数第二个)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 切片操作</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">fruits</span>[<span class="number">1</span>:<span class="number">4</span>])   <span class="comment"># ['香蕉', '橙子', '葡萄']</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">fruits</span>[:<span class="number">3</span>])    <span class="comment"># ['苹果', '香蕉', '橙子']</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">fruits</span>[<span class="number">2</span>:])    <span class="comment"># ['橙子', '葡萄', '西瓜']</span></span>
                </div>

                <div class="tips-box">
                    <p><strong>📌 列表特点：</strong></p>
                    <ul>
                        <li>有序 - 元素有固定位置</li>
                        <li>可变 - 可以修改、添加、删除元素</li>
                        <li>可重复 - 允许相同元素存在</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第15页：列表操作方法 -->
        <div class="slide">
            <div class="slide-number">15/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🔧</span>列表操作方法</h2>
                
                <h3>添加元素</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">shopping_list</span> = [<span class="string">"苹果"</span>, <span class="string">"牛奶"</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 在末尾添加单个元素</span></span>
                    <span class="code-line"><span class="variable">shopping_list</span>.<span class="function">append</span>(<span class="string">"面包"</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 在指定位置插入</span></span>
                    <span class="code-line"><span class="variable">shopping_list</span>.<span class="function">insert</span>(<span class="number">1</span>, <span class="string">"鸡蛋"</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 扩展列表（添加多个元素）</span></span>
                    <span class="code-line"><span class="variable">shopping_list</span>.<span class="function">extend</span>([<span class="string">"香蕉"</span>, <span class="string">"橙子"</span>])</span>
                </div>

                <h3>删除元素</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">numbers</span> = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">2</span>, <span class="number">4</span>, <span class="number">5</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 删除指定值（第一个匹配项）</span></span>
                    <span class="code-line"><span class="variable">numbers</span>.<span class="function">remove</span>(<span class="number">2</span>)         <span class="comment"># [1, 3, 2, 4, 5]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 删除指定索引的元素</span></span>
                    <span class="code-line"><span class="variable">deleted</span> = <span class="variable">numbers</span>.<span class="function">pop</span>(<span class="number">1</span>)    <span class="comment"># 删除索引1的元素并返回</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 删除最后一个元素</span></span>
                    <span class="code-line"><span class="variable">last</span> = <span class="variable">numbers</span>.<span class="function">pop</span>()        <span class="comment"># 删除并返回最后一个</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 清空列表</span></span>
                    <span class="code-line"><span class="variable">numbers</span>.<span class="function">clear</span>()</span>
                </div>

                <h3>查找和统计</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">grades</span> = [<span class="number">85</span>, <span class="number">92</span>, <span class="number">78</span>, <span class="number">92</span>, <span class="number">88</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">grades</span>.<span class="function">index</span>(<span class="number">92</span>))    <span class="comment"># 1 (第一个92的索引)</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">grades</span>.<span class="function">count</span>(<span class="number">92</span>))    <span class="comment"># 2 (92出现的次数)</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">len</span>(<span class="variable">grades</span>))        <span class="comment"># 5 (列表长度)</span></span>
                </div>
            </div>
        </div>

        <!-- 第16页：列表排序和反转 -->
        <div class="slide">
            <div class="slide-number">16/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🔄</span>列表排序和反转</h2>
                
                <h3>原地排序和反转</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">scores</span> = [<span class="number">85</span>, <span class="number">92</span>, <span class="number">78</span>, <span class="number">96</span>, <span class="number">81</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 升序排序（修改原列表）</span></span>
                    <span class="code-line"><span class="variable">scores</span>.<span class="function">sort</span>()</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">scores</span>)  <span class="comment"># [78, 81, 85, 92, 96]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 降序排序</span></span>
                    <span class="code-line"><span class="variable">scores</span>.<span class="function">sort</span>(reverse=<span class="keyword">True</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">scores</span>)  <span class="comment"># [96, 92, 85, 81, 78]</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 反转列表</span></span>
                    <span class="code-line"><span class="variable">scores</span>.<span class="function">reverse</span>()</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">scores</span>)  <span class="comment"># [78, 81, 85, 92, 96]</span></span>
                </div>

                <h3>返回新列表的排序</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">original</span> = [<span class="number">3</span>, <span class="number">1</span>, <span class="number">4</span>, <span class="number">1</span>, <span class="number">5</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 保持原列表不变</span></span>
                    <span class="code-line"><span class="variable">sorted_list</span> = <span class="function">sorted</span>(<span class="variable">original</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">original</span>)    <span class="comment"># [3, 1, 4, 1, 5] (未改变)</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">sorted_list</span>) <span class="comment"># [1, 1, 3, 4, 5] (新列表)</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 反转后的新列表</span></span>
                    <span class="code-line"><span class="variable">reversed_list</span> = <span class="function">list</span>(<span class="function">reversed</span>(<span class="variable">original</span>))</span>
                </div>

                <h3>自定义排序</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">students</span> = [<span class="string">"Alice"</span>, <span class="string">"Bob"</span>, <span class="string">"Charlie"</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 按字符串长度排序</span></span>
                    <span class="code-line"><span class="variable">students</span>.<span class="function">sort</span>(key=<span class="function">len</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">students</span>)  <span class="comment"># ['Bob', 'Alice', 'Charlie']</span></span>
                </div>

                <div class="highlight-box">
                    <p><strong>⚡ 性能提示：</strong>sort() 比 sorted() 更快，但会修改原列表</p>
                </div>
            </div>
        </div>

        <!-- 第17页：元组基础 -->
        <div class="slide">
            <div class="slide-number">17/30</div>
            <div class="slide-content">
                <h2><span class="emoji">📦</span>元组类型基础</h2>
                
                <h3>元组创建和特性</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 创建元组</span></span>
                    <span class="code-line"><span class="variable">coordinates</span> = (<span class="number">10</span>, <span class="number">20</span>)           <span class="comment"># 坐标点</span></span>
                    <span class="code-line"><span class="variable">colors</span> = (<span class="string">"红"</span>, <span class="string">"绿"</span>, <span class="string">"蓝"</span>)         <span class="comment"># RGB颜色</span></span>
                    <span class="code-line"><span class="variable">single</span> = (<span class="number">42</span>,)                  <span class="comment"># 单元素元组（注意逗号）</span></span>
                    <span class="code-line"><span class="variable">empty</span> = ()                      <span class="comment"># 空元组</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 不使用括号也可以（不推荐）</span></span>
                    <span class="code-line"><span class="variable">point</span> = <span class="number">3</span>, <span class="number">4</span>                   <span class="comment"># 自动识别为元组</span></span>
                </div>

                <h3>元组访问和操作</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">person_info</span> = (<span class="string">"张三"</span>, <span class="number">25</span>, <span class="string">"工程师"</span>, <span class="string">"北京"</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 索引访问</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">person_info</span>[<span class="number">0</span>])   <span class="comment"># 张三</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">person_info</span>[-<span class="number">1</span>])  <span class="comment"># 北京</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 切片操作</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">person_info</span>[<span class="number">1</span>:<span class="number">3</span>]) <span class="comment"># (25, '工程师')</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 遍历元组</span></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">item</span> <span class="keyword">in</span> <span class="variable">person_info</span>:</span>
                    <span class="code-line">    <span class="function">print</span>(<span class="variable">item</span>)</span>
                </div>

                <h3>元组解包</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">student</span> = (<span class="string">"李明"</span>, <span class="number">20</span>, <span class="number">85.5</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 解包赋值</span></span>
                    <span class="code-line"><span class="variable">name</span>, <span class="variable">age</span>, <span class="variable">score</span> = <span class="variable">student</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">f"学生{name}, 年龄{age}, 分数{score}"</span>)</span>
                </div>

                <div class="highlight-box">
                    <p><strong>🔒 核心特点：</strong></p>
                    <ul>
                        <li>不可变 - 创建后无法修改</li>
                        <li>有序 - 支持索引和切片</li>
                        <li>可作为字典键 - 因为不可变</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第18页：元组应用场景 -->
        <div class="slide">
            <div class="slide-number">18/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🎯</span>元组应用场景</h2>
                
                <h3>函数返回多个值</h3>
                <div class="code-block">
                    <span class="code-line"><span class="keyword">def</span> <span class="function">get_name_age</span>():</span>
                    <span class="code-line">    <span class="variable">name</span> = <span class="string">"王小明"</span></span>
                    <span class="code-line">    <span class="variable">age</span> = <span class="number">23</span></span>
                    <span class="code-line">    <span class="keyword">return</span> <span class="variable">name</span>, <span class="variable">age</span>  <span class="comment"># 返回元组</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 接收返回值</span></span>
                    <span class="code-line"><span class="variable">result</span> = <span class="function">get_name_age</span>()      <span class="comment"># 接收为元组</span></span>
                    <span class="code-line"><span class="variable">name</span>, <span class="variable">age</span> = <span class="function">get_name_age</span>()    <span class="comment"># 直接解包</span></span>
                </div>

                <h3>坐标和配置数据</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 二维坐标</span></span>
                    <span class="code-line"><span class="variable">start_point</span> = (<span class="number">0</span>, <span class="number">0</span>)</span>
                    <span class="code-line"><span class="variable">end_point</span> = (<span class="number">100</span>, <span class="number">50</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># RGB颜色值</span></span>
                    <span class="code-line"><span class="variable">red</span> = (<span class="number">255</span>, <span class="number">0</span>, <span class="number">0</span>)</span>
                    <span class="code-line"><span class="variable">green</span> = (<span class="number">0</span>, <span class="number">255</span>, <span class="number">0</span>)</span>
                    <span class="code-line"><span class="variable">blue</span> = (<span class="number">0</span>, <span class="number">0</span>, <span class="number">255</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 数据库记录</span></span>
                    <span class="code-line"><span class="variable">user_record</span> = (<span class="number">1001</span>, <span class="string">"张三"</span>, <span class="string">"<EMAIL>"</span>, <span class="string">"2024-01-15"</span>)</span>
                </div>

                <h3>元组方法</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">numbers</span> = (<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">2</span>, <span class="number">4</span>, <span class="number">2</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">numbers</span>.<span class="function">count</span>(<span class="number">2</span>))    <span class="comment"># 3 (元素2出现3次)</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">numbers</span>.<span class="function">index</span>(<span class="number">3</span>))    <span class="comment"># 2 (元素3的索引)</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="function">len</span>(<span class="variable">numbers</span>))        <span class="comment"># 6 (元组长度)</span></span>
                </div>

                <div class="tips-box">
                    <p><strong>🏗️ 使用建议：</strong></p>
                    <ul>
                        <li>固定格式的数据用元组</li>
                        <li>需要修改的数据用列表</li>
                        <li>作为字典键时选择元组</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第19页：字典基础 -->
        <div class="slide">
            <div class="slide-number">19/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🗂️</span>字典类型基础</h2>
                
                <h3>字典创建和特性</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 创建字典</span></span>
                    <span class="code-line"><span class="variable">student</span> = {</span>
                    <span class="code-line">    <span class="string">"姓名"</span>: <span class="string">"李明"</span>,</span>
                    <span class="code-line">    <span class="string">"年龄"</span>: <span class="number">20</span>,</span>
                    <span class="code-line">    <span class="string">"专业"</span>: <span class="string">"计算机科学"</span>,</span>
                    <span class="code-line">    <span class="string">"成绩"</span>: [<span class="number">85</span>, <span class="number">92</span>, <span class="number">78</span>]</span>
                    <span class="code-line">}</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 空字典</span></span>
                    <span class="code-line"><span class="variable">empty_dict</span> = {}</span>
                    <span class="code-line"><span class="variable">another_empty</span> = <span class="function">dict</span>()</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 使用dict()函数</span></span>
                    <span class="code-line"><span class="variable">person</span> = <span class="function">dict</span>(姓名=<span class="string">"张三"</span>, 年龄=<span class="number">25</span>)</span>
                </div>

                <h3>字典访问和修改</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 访问值</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">student</span>[<span class="string">"姓名"</span>])        <span class="comment"># 李明</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">student</span>.<span class="function">get</span>(<span class="string">"年龄"</span>))    <span class="comment"># 20</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">student</span>.<span class="function">get</span>(<span class="string">"身高"</span>, <span class="string">"未知"</span>)) <span class="comment"># 未知（默认值）</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 修改值</span></span>
                    <span class="code-line"><span class="variable">student</span>[<span class="string">"年龄"</span>] = <span class="number">21</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 添加新键值对</span></span>
                    <span class="code-line"><span class="variable">student</span>[<span class="string">"学号"</span>] = <span class="string">"2024001"</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 删除键值对</span></span>
                    <span class="code-line"><span class="keyword">del</span> <span class="variable">student</span>[<span class="string">"成绩"</span>]</span>
                </div>

                <div class="highlight-box">
                    <p><strong>🔑 字典特点：</strong></p>
                    <ul>
                        <li>无序 (Python 3.7+保持插入顺序)</li>
                        <li>可变 - 可以修改、添加、删除</li>
                        <li>键唯一 - 相同键会覆盖</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第20页：字典操作方法 -->
        <div class="slide">
            <div class="slide-number">20/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🛠️</span>字典操作方法</h2>
                
                <h3>获取键、值和项</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">scores</span> = {<span class="string">"语文"</span>: <span class="number">85</span>, <span class="string">"数学"</span>: <span class="number">92</span>, <span class="string">"英语"</span>: <span class="number">78</span>}</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 获取所有键</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">scores</span>.<span class="function">keys</span>())    <span class="comment"># dict_keys(['语文', '数学', '英语'])</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 获取所有值</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">scores</span>.<span class="function">values</span>())  <span class="comment"># dict_values([85, 92, 78])</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 获取所有键值对</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">scores</span>.<span class="function">items</span>())   <span class="comment"># dict_items([('语文', 85), ...])</span></span>
                </div>

                <h3>字典遍历</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 遍历键</span></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">subject</span> <span class="keyword">in</span> <span class="variable">scores</span>:</span>
                    <span class="code-line">    <span class="function">print</span>(<span class="string">f"科目: {subject}"</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 遍历值</span></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">score</span> <span class="keyword">in</span> <span class="variable">scores</span>.<span class="function">values</span>():</span>
                    <span class="code-line">    <span class="function">print</span>(<span class="string">f"分数: {score}"</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 遍历键值对</span></span>
                    <span class="code-line"><span class="keyword">for</span> <span class="variable">subject</span>, <span class="variable">score</span> <span class="keyword">in</span> <span class="variable">scores</span>.<span class="function">items</span>():</span>
                    <span class="code-line">    <span class="function">print</span>(<span class="string">f"{subject}: {score}分"</span>)</span>
                </div>

                <h3>字典更新和合并</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">basic_info</span> = {<span class="string">"姓名"</span>: <span class="string">"王五"</span>, <span class="string">"年龄"</span>: <span class="number">22</span>}</span>
                    <span class="code-line"><span class="variable">contact_info</span> = {<span class="string">"邮箱"</span>: <span class="string">"<EMAIL>"</span>, <span class="string">"电话"</span>: <span class="string">"123456"</span>}</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 更新字典</span></span>
                    <span class="code-line"><span class="variable">basic_info</span>.<span class="function">update</span>(<span class="variable">contact_info</span>)</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">basic_info</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># Python 3.9+ 合并运算符</span></span>
                    <span class="code-line"><span class="variable">merged</span> = <span class="variable">basic_info</span> | <span class="variable">contact_info</span></span>
                </div>

                <div class="tips-box">
                    <p><strong>🎯 实用技巧：</strong>使用 get() 方法避免 KeyError 异常</p>
                </div>
            </div>
        </div>

        <!-- 第21页：字典推导式 -->
        <div class="slide">
            <div class="slide-number">21/30</div>
            <div class="slide-content">
                <h2><span class="emoji">⚡</span>字典推导式</h2>
                
                <h3>基本语法</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 基本字典推导式</span></span>
                    <span class="code-line"><span class="variable">squares</span> = {<span class="variable">x</span>: <span class="variable">x</span>**<span class="number">2</span> <span class="keyword">for</span> <span class="variable">x</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1</span>, <span class="number">6</span>)}</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">squares</span>)  <span class="comment"># {1: 1, 2: 4, 3: 9, 4: 16, 5: 25}</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 从列表创建字典</span></span>
                    <span class="code-line"><span class="variable">words</span> = [<span class="string">"apple"</span>, <span class="string">"banana"</span>, <span class="string">"cherry"</span>]</span>
                    <span class="code-line"><span class="variable">word_lengths</span> = {<span class="variable">word</span>: <span class="function">len</span>(<span class="variable">word</span>) <span class="keyword">for</span> <span class="variable">word</span> <span class="keyword">in</span> <span class="variable">words</span>}</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">word_lengths</span>)  <span class="comment"># {'apple': 5, 'banana': 6, 'cherry': 6}</span></span>
                </div>

                <h3>条件过滤</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">students</span> = [</span>
                    <span class="code-line">    (<span class="string">"张三"</span>, <span class="number">85</span>), (<span class="string">"李四"</span>, <span class="number">92</span>), (<span class="string">"王五"</span>, <span class="number">76</span>), (<span class="string">"赵六"</span>, <span class="number">88</span>)</span>
                    <span class="code-line">]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 只包含及格学生</span></span>
                    <span class="code-line"><span class="variable">passed_students</span> = {</span>
                    <span class="code-line">    <span class="variable">name</span>: <span class="variable">score</span> <span class="keyword">for</span> <span class="variable">name</span>, <span class="variable">score</span> <span class="keyword">in</span> <span class="variable">students</span> <span class="keyword">if</span> <span class="variable">score</span> >= <span class="number">80</span></span>
                    <span class="code-line">}</span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">passed_students</span>)  <span class="comment"># {'张三': 85, '李四': 92, '赵六': 88}</span></span>
                </div>

                <h3>嵌套字典推导式</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 创建乘法表</span></span>
                    <span class="code-line"><span class="variable">multiplication_table</span> = {</span>
                    <span class="code-line">    <span class="variable">i</span>: {<span class="variable">j</span>: <span class="variable">i</span> * <span class="variable">j</span> <span class="keyword">for</span> <span class="variable">j</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1</span>, <span class="number">4</span>)} </span>
                    <span class="code-line">    <span class="keyword">for</span> <span class="variable">i</span> <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1</span>, <span class="number">4</span>)</span>
                    <span class="code-line">}</span>
                    <span class="code-line"><span class="comment"># {1: {1: 1, 2: 2, 3: 3}, 2: {1: 2, 2: 4, 3: 6}, 3: {1: 3, 2: 6, 3: 9}}</span></span>
                </div>

                <div class="highlight-box">
                    <p><strong>💡 推导式优势：</strong>简洁、高效、可读性强</p>
                </div>
            </div>
        </div>

        <!-- 第22页：集合基础 -->
        <div class="slide">
            <div class="slide-number">22/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🎯</span>集合类型基础</h2>
                
                <h3>集合创建和特性</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 创建集合</span></span>
                    <span class="code-line"><span class="variable">fruits</span> = {<span class="string">"苹果"</span>, <span class="string">"香蕉"</span>, <span class="string">"橙子"</span>}</span>
                    <span class="code-line"><span class="variable">numbers</span> = {<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">2</span>, <span class="number">1</span>}      <span class="comment"># 自动去重: {1, 2, 3}</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 从列表创建集合</span></span>
                    <span class="code-line"><span class="variable">list_data</span> = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">3</span>, <span class="number">4</span>]</span>
                    <span class="code-line"><span class="variable">unique_numbers</span> = <span class="function">set</span>(<span class="variable">list_data</span>)    <span class="comment"># {1, 2, 3, 4}</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 空集合</span></span>
                    <span class="code-line"><span class="variable">empty_set</span> = <span class="function">set</span>()                <span class="comment"># 注意: {} 是空字典</span></span>
                </div>

                <h3>集合操作</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">colors</span> = {<span class="string">"红色"</span>, <span class="string">"绿色"</span>, <span class="string">"蓝色"</span>}</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 添加元素</span></span>
                    <span class="code-line"><span class="variable">colors</span>.<span class="function">add</span>(<span class="string">"黄色"</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 删除元素</span></span>
                    <span class="code-line"><span class="variable">colors</span>.<span class="function">remove</span>(<span class="string">"红色"</span>)     <span class="comment"># 不存在会报错</span></span>
                    <span class="code-line"><span class="variable">colors</span>.<span class="function">discard</span>(<span class="string">"紫色"</span>)    <span class="comment"># 不存在不会报错</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 检查成员</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">"绿色"</span> <span class="keyword">in</span> <span class="variable">colors</span>)     <span class="comment"># True</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 清空集合</span></span>
                    <span class="code-line"><span class="variable">colors</span>.<span class="function">clear</span>()</span>
                </div>

                <div class="highlight-box">
                    <p><strong>🔑 集合特点：</strong></p>
                    <ul>
                        <li>无序 - 元素没有固定位置</li>
                        <li>唯一 - 自动去除重复元素</li>
                        <li>可变 - 可以添加和删除元素</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第23页：集合运算 -->
        <div class="slide">
            <div class="slide-number">23/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🧮</span>集合运算</h2>
                
                <h3>基本集合运算</h3>
                <div class="code-block">
                    <span class="code-line"><span class="variable">set_a</span> = {<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>}</span>
                    <span class="code-line"><span class="variable">set_b</span> = {<span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>}</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 并集 (所有元素)</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">set_a</span> | <span class="variable">set_b</span>)         <span class="comment"># {1, 2, 3, 4, 5, 6}</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">set_a</span>.<span class="function">union</span>(<span class="variable">set_b</span>))    <span class="comment"># 同上</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 交集 (共同元素)</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">set_a</span> & <span class="variable">set_b</span>)         <span class="comment"># {3, 4}</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">set_a</span>.<span class="function">intersection</span>(<span class="variable">set_b</span>)) <span class="comment"># 同上</span></span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 差集 (A中有但B中没有)</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">set_a</span> - <span class="variable">set_b</span>)         <span class="comment"># {1, 2}</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="variable">set_a</span>.<span class="function">difference</span>(<span class="variable">set_b</span>)) <span class="comment"># 同上</span></span>
                </div>

                <h3>实际应用示例</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 学生选课情况</span></span>
                    <span class="code-line"><span class="variable">python_students</span> = {<span class="string">"张三"</span>, <span class="string">"李四"</span>, <span class="string">"王五"</span>, <span class="string">"赵六"</span>}</span>
                    <span class="code-line"><span class="variable">java_students</span> = {<span class="string">"李四"</span>, <span class="string">"王五"</span>, <span class="string">"钱七"</span>, <span class="string">"孙八"</span>}</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 同时选择两门课的学生</span></span>
                    <span class="code-line"><span class="variable">both_courses</span> = <span class="variable">python_students</span> & <span class="variable">java_students</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">f"同时选择Python和Java: {both_courses}"</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 只选择Python的学生</span></span>
                    <span class="code-line"><span class="variable">only_python</span> = <span class="variable">python_students</span> - <span class="variable">java_students</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">f"只选择Python: {only_python}"</span>)</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 至少选择一门课的学生</span></span>
                    <span class="code-line"><span class="variable">all_students</span> = <span class="variable">python_students</span> | <span class="variable">java_students</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">f"至少选择一门: {all_students}"</span>)</span>
                </div>

                <div class="tips-box">
                    <p><strong>🎯 应用场景：</strong>数据去重、关系分析、权限管理</p>
                </div>
            </div>
        </div>

        <!-- 第24页：AI辅助编程工具介绍 -->
        <div class="slide">
            <div class="slide-number">24/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🤖</span>AI辅助编程工具</h2>
                
                <h3>主流AI编程助手</h3>
                <div class="ai-tool">
                    <h4><span class="emoji">⚡</span>GitHub Copilot</h4>
                    <ul>
                        <li>IDE集成插件，实时代码建议</li>
                        <li>支持多种编程语言</li>
                        <li>上下文感知的智能补全</li>
                    </ul>
                </div>

                <div class="ai-tool">
                    <h4><span class="emoji">💬</span>ChatGPT / Claude</h4>
                    <ul>
                        <li>对话式编程助手</li>
                        <li>代码解释、调试、优化</li>
                        <li>学习概念和最佳实践</li>
                    </ul>
                </div>

                <div class="ai-tool">
                    <h4><span class="emoji">🔧</span>Cursor / CodeWhisperer</h4>
                    <ul>
                        <li>智能代码编辑器</li>
                        <li>项目级别的代码理解</li>
                        <li>自动化重构和优化</li>
                    </ul>
                </div>

                <div class="highlight-box">
                    <p><strong>🚀 学习Python变量的AI工作流：</strong></p>
                    <p>理论学习 → AI解释 → 代码实践 → AI调试 → 深入理解</p>
                </div>
            </div>
        </div>

        <!-- 第25页：AI学习Python变量的方法 -->
        <div class="slide">
            <div class="slide-number">25/30</div>
            <div class="slide-content">
                <h2><span class="emoji">📚</span>AI学习Python变量的方法</h2>
                
                <h3>概念理解阶段</h3>
                <div class="tips-box">
                    <p><strong>💡 学习策略：</strong></p>
                    <ul>
                        <li>向AI提问基础概念："什么是Python变量？"</li>
                        <li>要求对比解释："变量和常量的区别"</li>
                        <li>索要实际例子："给我5个变量使用的例子"</li>
                    </ul>
                </div>

                <h3>代码实践阶段</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># AI生成的练习代码</span></span>
                    <span class="code-line"><span class="variable">student_name</span> = <span class="string">"小明"</span></span>
                    <span class="code-line"><span class="variable">student_age</span> = <span class="number">18</span></span>
                    <span class="code-line"><span class="variable">grades</span> = [<span class="number">85</span>, <span class="number">92</span>, <span class="number">78</span>]</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 让AI解释每行代码的作用</span></span>
                    <span class="code-line"><span class="function">print</span>(<span class="string">f"学生{student_name}的平均分是{sum(grades)/len(grades)}"</span>)</span>
                </div>

                <h3>错误调试阶段</h3>
                <div class="ai-tool">
                    <h4><span class="emoji">🐛</span>AI调试工作流</h4>
                    <ul>
                        <li>复制错误信息给AI</li>
                        <li>AI分析错误原因</li>
                        <li>获得修复建议</li>
                        <li>理解错误避免方法</li>
                    </ul>
                </div>

                <div class="highlight-box">
                    <p><strong>🎯 高效学习公式：</strong>AI问答 + 实际编程 + 错误调试 = 快速掌握</p>
                </div>
            </div>
        </div>

        <!-- 第26页：提示词模板 - 基础学习 -->
        <div class="slide">
            <div class="slide-number">26/30</div>
            <div class="slide-content">
                <h2><span class="emoji">💡</span>提示词模板 - 基础学习</h2>
                
                <h3>概念理解模板</h3>
                <div class="prompt-template">
                    请详细解释Python中的[数据类型名称]：
                    1. 基本定义和特点
                    2. 常用操作方法
                    3. 实际应用场景
                    4. 与其他数据类型的区别
                    5. 给出3个简单易懂的代码示例
                    请用初学者容易理解的语言说明。
                </div>

                <h3>代码生成模板</h3>
                <div class="prompt-template">
                    请为我生成关于[具体主题]的Python代码：
                    要求：
                    - 包含详细的中文注释
                    - 代码要循序渐进，从简单到复杂
                    - 每段代码后解释其功能
                    - 提供可能的运行结果
                    - 如果有陷阱或注意事项，请特别说明
                </div>

                <h3>错误分析模板</h3>
                <div class="prompt-template">
                    我的Python代码出现了错误：
                    
                    [贴入你的代码]
                    
                    错误信息：[贴入错误信息]
                    
                    请帮我：
                    1. 解释错误的具体原因
                    2. 提供修正后的代码
                    3. 说明如何避免类似错误
                    4. 相关的最佳实践建议
                </div>
            </div>
        </div>

        <!-- 第27页：提示词模板 - 深入学习 -->
        <div class="slide">
            <div class="slide-number">27/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🎯</span>提示词模板 - 深入学习</h2>
                
                <h3>对比分析模板</h3>
                <div class="prompt-template">
                    请详细对比Python中的[A数据类型]和[B数据类型]：
                    
                    对比维度：
                    - 性能效率
                    - 内存占用
                    - 使用场景
                    - 操作方法差异
                    - 代码示例对比
                    
                    最后给出选择建议：什么情况下用哪一个？
                </div>

                <h3>实战项目模板</h3>
                <div class="prompt-template">
                    设计一个使用[特定数据类型]的实际项目：
                    
                    项目要求：
                    - 解决一个实际问题
                    - 充分利用该数据类型的特性
                    - 包含用户交互
                    - 代码结构清晰
                    - 有完整的测试用例
                    
                    请提供完整的代码实现和说明文档。
                </div>

                <h3>性能优化模板</h3>
                <div class="prompt-template">
                    请分析并优化这段Python代码的性能：
                    
                    [贴入代码]
                    
                    分析角度：
                    1. 时间复杂度分析
                    2. 空间复杂度分析  
                    3. 数据结构选择是否合理
                    4. 提供优化后的代码
                    5. 解释优化的原理和效果
                </div>
            </div>
        </div>

        <!-- 第28页：AI辅助学习最佳实践 -->
        <div class="slide">
            <div class="slide-number">28/30</div>
            <div class="slide-content">
                <h2><span class="emoji">⭐</span>AI辅助学习最佳实践</h2>
                
                <h3>高效提问技巧</h3>
                <div class="tips-box">
                    <p><strong>🎯 SMART提问法：</strong></p>
                    <ul>
                        <li><strong>S</strong>pecific - 具体明确的问题</li>
                        <li><strong>M</strong>easurable - 可衡量的学习目标</li>
                        <li><strong>A</strong>chievable - 符合当前水平</li>
                        <li><strong>R</strong>elevant - 与学习目标相关</li>
                        <li><strong>T</strong>ime-bound - 有明确的时间框架</li>
                    </ul>
                </div>

                <h3>学习进度管理</h3>
                <div class="highlight-box">
                    <p><strong>📈 分阶段学习路径：</strong></p>
                    <ol>
                        <li><strong>基础理解</strong> - 掌握概念和语法</li>
                        <li><strong>实践应用</strong> - 编写简单程序</li>
                        <li><strong>问题解决</strong> - 调试和优化代码</li>
                        <li><strong>项目实战</strong> - 完成完整项目</li>
                        <li><strong>深度钻研</strong> - 研究高级特性</li>
                    </ol>
                </div>

                <h3>注意事项</h3>
                <div class="ai-tool">
                    <h4><span class="emoji">⚠️</span>使用AI的注意点</h4>
                    <ul>
                        <li>验证AI提供的代码，不要盲目信任</li>
                        <li>理解代码逻辑，而非仅仅复制粘贴</li>
                        <li>多角度提问，获得全面理解</li>
                        <li>结合官方文档，确保准确性</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第29页：实践练习建议 -->
        <div class="slide">
            <div class="slide-number">29/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🏃</span>实践练习建议</h2>
                
                <h3>每日练习计划</h3>
                <div class="highlight-box">
                    <h4><strong>第1-3天：变量基础</strong></h4>
                    <ul>
                        <li>练习变量命名和赋值</li>
                        <li>尝试不同数据类型的转换</li>
                        <li>用AI生成练习题并解答</li>
                    </ul>
                </div>

                <div class="highlight-box">
                    <h4><strong>第4-7天：容器类型</strong></h4>
                    <ul>
                        <li>列表、元组、字典、集合的综合运用</li>
                        <li>实现简单的数据处理程序</li>
                        <li>向AI询问性能优化建议</li>
                    </ul>
                </div>

                <h3>项目实践方向</h3>
                <div class="code-block">
                    <span class="code-line"><span class="comment"># 项目示例：学生成绩管理系统</span></span>
                    <span class="code-line"><span class="variable">students</span> = {</span>
                    <span class="code-line">    <span class="string">"张三"</span>: {<span class="string">"数学"</span>: <span class="number">85</span>, <span class="string">"英语"</span>: <span class="number">92</span>},</span>
                    <span class="code-line">    <span class="string">"李四"</span>: {<span class="string">"数学"</span>: <span class="number">78</span>, <span class="string">"英语"</span>: <span class="number">88</span>}</span>
                    <span class="code-line">}</span>
                    <span class="code-line"></span>
                    <span class="code-line"><span class="comment"># 向AI询问：如何计算平均分？如何找出最高分？</span></span>
                </div>

                <div class="tips-box">
                    <p><strong>🎯 进阶挑战：</strong>用所学数据类型实现通讯录、图书管理、购物车等项目</p>
                </div>
            </div>
        </div>

        <!-- 第30页：总结 -->
        <div class="slide">
            <div class="slide-number">30/30</div>
            <div class="slide-content">
                <h2><span class="emoji">🎉</span>课程总结</h2>
                
                <h3>知识回顾</h3>
                <div class="highlight-box">
                    <p><strong>📚 已掌握的内容：</strong></p>
                    <ul>
                        <li>✅ Python变量的创建和命名规则</li>
                        <li>✅ 数字类型：int、float、complex</li>
                        <li>✅ 字符串类型及其强大的处理方法</li>
                        <li>✅ 布尔类型和逻辑运算</li>
                        <li>✅ 容器类型：列表、元组、字典、集合</li>
                        <li>✅ AI辅助编程工具的使用技巧</li>
                    </ul>
                </div>

                <h3>下一步学习方向</h3>
                <div class="ai-tool">
                    <h4><span class="emoji">🚀</span>继续深入</h4>
                    <ul>
                        <li>函数和模块的使用</li>
                        <li>面向对象编程</li>
                        <li>文件操作和异常处理</li>
                        <li>第三方库的学习和使用</li>
                    </ul>
                </div>

                <div style="text-align: center; margin-top: 50px;">
                    <h3><span class="emoji">💪</span>坚持练习，持续进步！</h3>
                    <p style="font-size: 1.3em; color: #7f8c8d;">Remember: Practice makes perfect!</p>
                    <p style="font-size: 1.1em; color: #95a5a6; margin-top: 20px;">利用AI工具，让学习变得更高效、更有趣！</p>
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">◀ 上一页</button>
        <button class="nav-btn" onclick="nextSlide()">下一页 ▶</button>
    </div>

    <div class="page-indicator" id="pageIndicator">
        1 / 30
    </div>

    <script>
        let currentSlide = 0;
        let slides = [];
        let totalSlides = 0;

        // 初始化函数
        function initializeSlides() {
            slides = document.querySelectorAll('.slide');
            totalSlides = slides.length;
            
            // 确保第一张幻灯片显示
            if (slides.length > 0) {
                slides[0].classList.add('active');
                slides[0].style.display = 'block';
                slides[0].style.opacity = '1';
                slides[0].style.transform = 'translateX(0)';
            }
            
            console.log(`总共${totalSlides}张幻灯片已初始化`);
            updatePageIndicator();
        }

        function showSlide(n) {
            if (slides.length === 0) return;
            
            // 隐藏当前幻灯片
            slides[currentSlide].classList.remove('active');
            slides[currentSlide].style.display = 'none';
            slides[currentSlide].style.opacity = '0';
            slides[currentSlide].style.transform = 'translateX(100px)';
            
            // 计算新的幻灯片索引
            currentSlide = (n + totalSlides) % totalSlides;
            
            // 显示新幻灯片
            setTimeout(() => {
                slides[currentSlide].style.display = 'block';
                slides[currentSlide].classList.add('active');
                slides[currentSlide].style.opacity = '1';
                slides[currentSlide].style.transform = 'translateX(0)';
                
                // 重新触发代码动画
                const codeLines = slides[currentSlide].querySelectorAll('.code-line');
                codeLines.forEach((line, index) => {
                    line.style.animation = 'none';
                    line.offsetHeight; // 强制重绘
                    line.style.animation = `typewriter 0.05s ease-out ${index * 0.1}s forwards`;
                });
                
                // 更新页面指示器
                updatePageIndicator();
                
                console.log(`切换到第${currentSlide + 1}张幻灯片`);
            }, 50);
        }

        function nextSlide() {
            showSlide(currentSlide + 1);
        }

        function previousSlide() {
            showSlide(currentSlide - 1);
        }

        function updatePageIndicator() {
            const indicator = document.getElementById('pageIndicator');
            if (indicator) {
                indicator.textContent = `${currentSlide + 1} / ${totalSlides}`;
            }
        }

        // 键盘控制
        document.addEventListener('keydown', function(e) {
            e.preventDefault();
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                previousSlide();
            } else if (e.key >= '1' && e.key <= '9') {
                // 数字键直接跳转
                const slideNumber = parseInt(e.key) - 1;
                if (slideNumber < totalSlides) {
                    showSlide(slideNumber);
                }
            }
        });

        // 触摸控制（移动端）
        let startX = 0;
        document.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
        });

        document.addEventListener('touchend', function(e) {
            const endX = e.changedTouches[0].clientX;
            const diff = startX - endX;
            
            if (Math.abs(diff) > 50) { // 滑动距离大于50px
                if (diff > 0) {
                    nextSlide(); // 向左滑动，下一页
                } else {
                    previousSlide(); // 向右滑动，上一页
                }
            }
        });

        // 鼠标滚轮控制
        document.addEventListener('wheel', function(e) {
            e.preventDefault();
            if (e.deltaY > 0) {
                nextSlide();
            } else {
                previousSlide();
            }
        }, { passive: false });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSlides();
            
            // 初始化代码行动画
            document.querySelectorAll('.code-line').forEach((line, index) => {
                line.style.animationDelay = `${index * 0.1}s`;
            });
            
            console.log('PPT初始化完成，可以使用以下方式控制：');
            console.log('- 左右方向键或空格键');
            console.log('- 点击导航按钮');
            console.log('- 数字键1-9直接跳转');
            console.log('- 鼠标滚轮');
            console.log('- 触摸滑动（移动端）');
        });
    </script>
</body>
</html>