<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python正则表达式完整教程</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
            height: 100vh;
        }

        .presentation {
            width: 100%;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100vh;
            position: absolute;
            top: 0;
            left: 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            padding: 40px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: #888 #f1f1f1;
        }

        .slide::-webkit-scrollbar {
            width: 8px;
        }

        .slide::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        .slide::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }

        .slide::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .slide.active {
            opacity: 1;
            transform: translateX(0);
        }

        .slide.prev {
            transform: translateX(-100%);
        }

        h1 {
            font-size: 3em;
            color: #fff;
            margin-bottom: 30px;
            text-align: left;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: slideInLeft 1s ease-out;
        }

        h2 {
            font-size: 2.5em;
            color: #fff;
            margin-bottom: 25px;
            text-align: left;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: slideInLeft 1s ease-out 0.2s both;
        }

        h3 {
            font-size: 2em;
            color: #fff;
            margin-bottom: 20px;
            text-align: left;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
            animation: slideInLeft 1s ease-out 0.4s both;
        }

        .content {
            width: 100%;
            text-align: left;
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .emoji {
            font-size: 2em;
            margin-right: 15px;
            animation: bounce 2s infinite;
        }

        .text-content {
            color: #fff;
            font-size: 1.3em;
            line-height: 1.8;
            margin-bottom: 20px;
            text-align: left;
        }

        .code-block {
            background: #1e1e1e;
            border: 2px solid #333;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 1.1em;
            color: #f8f8f2;
            text-align: left;
            overflow-x: auto;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            animation: slideInUp 1s ease-out 0.8s both;
            white-space: pre;
            tab-size: 4;
        }

        .code-line {
            display: block;
            padding: 2px 0;
            border-left: 3px solid transparent;
            padding-left: 10px;
            animation: typewriter 0.5s ease-out;
            white-space: pre;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }

        .code-line:hover {
            background: rgba(255,255,255,0.1);
            border-left-color: #4CAF50;
        }

        .keyword { color: #569cd6; }
        .string { color: #ce9178; }
        .comment { color: #6a9955; }
        .function { color: #dcdcaa; }
        .number { color: #b5cea8; }
        .operator { color: #d4d4d4; }

        .list-item {
            color: #fff;
            font-size: 1.2em;
            margin: 10px 0;
            padding-left: 20px;
            text-align: left;
            animation: slideInRight 0.8s ease-out;
        }

        .list-item:before {
            content: "▶";
            color: #4CAF50;
            margin-right: 10px;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: #fff;
            padding: 12px 20px;
            margin: 0 5px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .slide-counter {
            position: fixed;
            bottom: 30px;
            left: 30px;
            color: #fff;
            font-size: 1.1em;
            background: rgba(0,0,0,0.3);
            padding: 10px 15px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }

        .highlight-box {
            background: rgba(255,255,255,0.1);
            border-left: 4px solid #4CAF50;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            backdrop-filter: blur(10px);
        }

        .ai-prompt {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: 2px solid #fff;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #fff;
            font-style: italic;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        @keyframes slideInLeft {
            from { transform: translateX(-100px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideInRight {
            from { transform: translateX(100px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideInUp {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @keyframes fadeInUp {
            from { transform: translateY(30px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        @keyframes typewriter {
            from { width: 0; }
            to { width: 100%; }
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 4px;
            background: #4CAF50;
            transition: width 0.3s ease;
            z-index: 1001;
        }
    </style>
</head>
<body>
    <div class="progress-bar" id="progressBar"></div>
    <div class="presentation" id="presentation">
        <!-- 第1页：标题页 -->
        <div class="slide active">
            <h1><span class="emoji">🐍</span>Python正则表达式完整教程</h1>
            <div class="content">
                <div class="text-content">
                    <span class="emoji">📚</span>深入学习Python正则表达式的强大功能
                </div>
                <div class="text-content">
                    <span class="emoji">🎯</span>掌握元字符、模式匹配和实际应用
                </div>
                <div class="text-content">
                    <span class="emoji">🤖</span>学会使用AI辅助编程工具提升学习效率
                </div>
                <div class="highlight-box">
                    <strong>课程目标：</strong><br>
                    • 理解正则表达式的基本概念和语法<br>
                    • 掌握常用元字符和特殊字符<br>
                    • 学会使用Python的re模块<br>
                    • 实践复杂的模式匹配场景<br>
                    • 运用AI工具辅助学习和开发
                </div>
            </div>
        </div>

        <!-- 第2页：什么是正则表达式 -->
        <div class="slide">
            <h2><span class="emoji">🔍</span>什么是正则表达式？</h2>
            <div class="content">
                <div class="text-content">
                    正则表达式（Regular Expression，简称regex或regexp）是一种强大的文本处理工具，用于描述、匹配和操作字符串模式。
                </div>
                <div class="highlight-box">
                    <strong>核心特点：</strong><br>
                    • 模式匹配：查找符合特定规则的文本<br>
                    • 文本替换：批量修改符合条件的内容<br>
                    • 数据验证：检查输入格式是否正确<br>
                    • 文本提取：从复杂文本中提取有用信息
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 简单示例：匹配邮箱地址</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">pattern</span> = <span class="string">r'\w+@\w+\.\w+'</span></div>
                    <div class="code-line"><span class="function">text</span> = <span class="string">"联系我：<EMAIL>"</span></div>
                    <div class="code-line"><span class="function">result</span> = <span class="function">re</span>.<span class="function">search</span>(<span class="function">pattern</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="function">result</span>.<span class="function">group</span>())  <span class="comment"># 输出: <EMAIL></span></div>
                </div>
            </div>
        </div>

        <!-- 第3页：正则表达式的应用场景 -->
        <div class="slide">
            <h2><span class="emoji">🎯</span>正则表达式的应用场景</h2>
            <div class="content">
                <div class="list-item"><span class="emoji">📧</span>邮箱地址验证和提取</div>
                <div class="list-item"><span class="emoji">📱</span>手机号码格式检查</div>
                <div class="list-item"><span class="emoji">🌐</span>URL链接匹配和解析</div>
                <div class="list-item"><span class="emoji">💳</span>身份证号码验证</div>
                <div class="list-item"><span class="emoji">📄</span>日志文件分析和处理</div>
                <div class="list-item"><span class="emoji">🔄</span>文本批量替换和清理</div>
                <div class="list-item"><span class="emoji">📊</span>数据提取和格式化</div>
                <div class="list-item"><span class="emoji">🔒</span>密码强度验证</div>
                
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 实际应用示例：提取文本中的所有数字</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">text</span> = <span class="string">"今年销售额达到1000万，比去年增长25%"</span></div>
                    <div class="code-line"><span class="function">numbers</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'\d+'</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="function">numbers</span>)  <span class="comment"># 输出: ['1000', '25']</span></div>
                </div>
            </div>
        </div>

        <!-- 第4页：基础元字符 - 字符类 -->
        <div class="slide">
            <h2><span class="emoji">🔤</span>基础元字符 - 字符类</h2>
            <div class="content">
                <div class="text-content">
                    字符类用于匹配特定类型的字符，是正则表达式的基础构建块。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 常用字符类</span></div>
                    <div class="code-line"><span class="string">\d</span>  <span class="comment"># 匹配任意数字 [0-9]</span></div>
                    <div class="code-line"><span class="string">\D</span>  <span class="comment"># 匹配任意非数字</span></div>
                    <div class="code-line"><span class="string">\w</span>  <span class="comment"># 匹配字母、数字、下划线 [a-zA-Z0-9_]</span></div>
                    <div class="code-line"><span class="string">\W</span>  <span class="comment"># 匹配非字母数字下划线</span></div>
                    <div class="code-line"><span class="string">\s</span>  <span class="comment"># 匹配空白字符（空格、制表符、换行符）</span></div>
                    <div class="code-line"><span class="string">\S</span>  <span class="comment"># 匹配非空白字符</span></div>
                    <div class="code-line"><span class="string">.</span>   <span class="comment"># 匹配除换行符外的任意字符</span></div>
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 实践示例</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">text</span> = <span class="string">"用户ID: user123, 密码: pass@word"</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 提取数字</span></div>
                    <div class="code-line"><span class="function">digits</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'\d+'</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"数字: {digits}"</span>)  <span class="comment"># ['123']</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 提取单词字符</span></div>
                    <div class="code-line"><span class="function">words</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'\w+'</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"单词: {words}"</span>)  <span class="comment"># ['用户ID', 'user123', '密码', 'pass', 'word']</span></div>
                </div>
            </div>
        </div>

        <!-- 第5页：量词元字符 -->
        <div class="slide">
            <h2><span class="emoji">🔢</span>量词元字符</h2>
            <div class="content">
                <div class="text-content">
                    量词用于指定前面的字符或组应该匹配多少次。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 基础量词</span></div>
                    <div class="code-line"><span class="string">*</span>      <span class="comment"># 匹配0次或多次</span></div>
                    <div class="code-line"><span class="string">+</span>      <span class="comment"># 匹配1次或多次</span></div>
                    <div class="code-line"><span class="string">?</span>      <span class="comment"># 匹配0次或1次</span></div>
                    <div class="code-line"><span class="string">{n}</span>    <span class="comment"># 精确匹配n次</span></div>
                    <div class="code-line"><span class="string">{n,}</span>   <span class="comment"># 匹配n次或更多</span></div>
                    <div class="code-line"><span class="string">{n,m}</span>  <span class="comment"># 匹配n到m次</span></div>
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 量词应用示例</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">text</span> = <span class="string">"电话: 138-1234-5678, 座机: 010-12345678"</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 匹配手机号（11位数字）</span></div>
                    <div class="code-line"><span class="function">mobile</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'\d{3}-\d{4}-\d{4}'</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"手机号: {mobile}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 匹配任意长度的数字</span></div>
                    <div class="code-line"><span class="function">all_numbers</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'\d+'</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"所有数字: {all_numbers}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 匹配可选的区号</span></div>
                    <div class="code-line"><span class="function">phones</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'\d{3,4}-?\d{4,8}'</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"电话号码: {phones}"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第6页：位置元字符 -->
        <div class="slide">
            <h2><span class="emoji">📍</span>位置元字符</h2>
            <div class="content">
                <div class="text-content">
                    位置元字符用于指定匹配的位置，而不是匹配具体的字符。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 位置锚点</span></div>
                    <div class="code-line"><span class="string">^</span>      <span class="comment"># 行的开始</span></div>
                    <div class="code-line"><span class="string">$</span>      <span class="comment"># 行的结束</span></div>
                    <div class="code-line"><span class="string">\b</span>     <span class="comment"># 单词边界</span></div>
                    <div class="code-line"><span class="string">\B</span>     <span class="comment"># 非单词边界</span></div>
                    <div class="code-line"><span class="string">\A</span>     <span class="comment"># 字符串开始</span></div>
                    <div class="code-line"><span class="string">\Z</span>     <span class="comment"># 字符串结束</span></div>
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 位置匹配示例</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">text</span> = <span class="string">"""Python编程语言
学习Python很有趣
Python应用广泛"""</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 匹配行首的Python</span></div>
                    <div class="code-line"><span class="function">start_python</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'^Python'</span>, <span class="function">text</span>, <span class="function">re</span>.<span class="function">MULTILINE</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"行首Python: {start_python}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 匹配完整单词Python</span></div>
                    <div class="code-line"><span class="function">word_python</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'\bPython\b'</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"完整单词Python: {word_python}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 验证邮箱格式（完整匹配）</span></div>
                    <div class="code-line"><span class="function">email</span> = <span class="string">"<EMAIL>"</span></div>
                    <div class="code-line"><span class="function">is_valid</span> = <span class="function">re</span>.<span class="function">match</span>(<span class="string">r'^\w+@\w+\.\w+$'</span>, <span class="function">email</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"邮箱有效: {bool(is_valid)}"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第7页：字符集和范围 -->
        <div class="slide">
            <h2><span class="emoji">🎨</span>字符集和范围</h2>
            <div class="content">
                <div class="text-content">
                    字符集允许匹配一组特定的字符，提供更精确的匹配控制。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 字符集语法</span></div>
                    <div class="code-line"><span class="string">[abc]</span>     <span class="comment"># 匹配a、b或c中的任意一个</span></div>
                    <div class="code-line"><span class="string">[a-z]</span>     <span class="comment"># 匹配小写字母a到z</span></div>
                    <div class="code-line"><span class="string">[A-Z]</span>     <span class="comment"># 匹配大写字母A到Z</span></div>
                    <div class="code-line"><span class="string">[0-9]</span>     <span class="comment"># 匹配数字0到9</span></div>
                    <div class="code-line"><span class="string">[^abc]</span>    <span class="comment"># 匹配除a、b、c之外的任意字符</span></div>
                    <div class="code-line"><span class="string">[a-zA-Z0-9]</span> <span class="comment"># 匹配字母和数字</span></div>
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 字符集应用示例</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">text</span> = <span class="string">"产品编号: A123, B456, C789, X999"</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 匹配A或B开头的产品编号</span></div>
                    <div class="code-line"><span class="function">ab_products</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'[AB]\d{3}'</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"A或B系列: {ab_products}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 匹配非数字字符</span></div>
                    <div class="code-line"><span class="function">non_digits</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'[^0-9]+'</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"非数字部分: {non_digits}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 匹配中文字符</span></div>
                    <div class="code-line"><span class="function">chinese_text</span> = <span class="string">"Hello世界123"</span></div>
                    <div class="code-line"><span class="function">chinese</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'[\u4e00-\u9fff]+'</span>, <span class="function">chinese_text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"中文字符: {chinese}"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第8页：分组和捕获 -->
        <div class="slide">
            <h2><span class="emoji">🎯</span>分组和捕获</h2>
            <div class="content">
                <div class="text-content">
                    分组允许将正则表达式的一部分作为一个单元处理，并可以捕获匹配的内容。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 分组语法</span></div>
                    <div class="code-line"><span class="string">(pattern)</span>    <span class="comment"># 捕获分组</span></div>
                    <div class="code-line"><span class="string">(?:pattern)</span>  <span class="comment"># 非捕获分组</span></div>
                    <div class="code-line"><span class="string">(?P&lt;name&gt;pattern)</span> <span class="comment"># 命名分组</span></div>
                    <div class="code-line"><span class="string">\1, \2</span>      <span class="comment"># 反向引用</span></div>
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 分组捕获示例</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">text</span> = <span class="string">"姓名: 张三, 电话: 138-1234-5678"</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 使用分组提取信息</span></div>
                    <div class="code-line"><span class="function">pattern</span> = <span class="string">r'姓名: (\w+), 电话: (\d{3}-\d{4}-\d{4})'</span></div>
                    <div class="code-line"><span class="function">match</span> = <span class="function">re</span>.<span class="function">search</span>(<span class="function">pattern</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">if</span> <span class="function">match</span>:</div>
                    <div class="code-line">    <span class="function">name</span> = <span class="function">match</span>.<span class="function">group</span>(<span class="number">1</span>)</div>
                    <div class="code-line">    <span class="function">phone</span> = <span class="function">match</span>.<span class="function">group</span>(<span class="number">2</span>)</div>
                    <div class="code-line">    <span class="keyword">print</span>(<span class="string">f"姓名: {name}, 电话: {phone}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 命名分组示例</span></div>
                    <div class="code-line"><span class="function">named_pattern</span> = <span class="string">r'姓名: (?P&lt;name&gt;\w+), 电话: (?P&lt;phone&gt;\d{3}-\d{4}-\d{4})'</span></div>
                    <div class="code-line"><span class="function">named_match</span> = <span class="function">re</span>.<span class="function">search</span>(<span class="function">named_pattern</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">if</span> <span class="function">named_match</span>:</div>
                    <div class="code-line">    <span class="keyword">print</span>(<span class="function">named_match</span>.<span class="function">groupdict</span>())</div>
                </div>
            </div>
        </div>

        <!-- 第9页：贪婪与非贪婪匹配 -->
        <div class="slide">
            <h2><span class="emoji">🍽️</span>贪婪与非贪婪匹配</h2>
            <div class="content">
                <div class="text-content">
                    量词默认是贪婪的，会尽可能多地匹配字符。非贪婪匹配则尽可能少地匹配。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 贪婪与非贪婪量词</span></div>
                    <div class="code-line"><span class="string">*</span>      <span class="comment"># 贪婪：匹配尽可能多的字符</span></div>
                    <div class="code-line"><span class="string">*?</span>     <span class="comment"># 非贪婪：匹配尽可能少的字符</span></div>
                    <div class="code-line"><span class="string">+</span>      <span class="comment"># 贪婪</span></div>
                    <div class="code-line"><span class="string">+?</span>     <span class="comment"># 非贪婪</span></div>
                    <div class="code-line"><span class="string">?</span>      <span class="comment"># 贪婪</span></div>
                    <div class="code-line"><span class="string">??</span>     <span class="comment"># 非贪婪</span></div>
                    <div class="code-line"><span class="string">{n,m}</span>  <span class="comment"># 贪婪</span></div>
                    <div class="code-line"><span class="string">{n,m}?</span> <span class="comment"># 非贪婪</span></div>
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 贪婪与非贪婪对比</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">html</span> = <span class="string">"&lt;div&gt;内容1&lt;/div&gt;&lt;div&gt;内容2&lt;/div&gt;"</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 贪婪匹配：会匹配整个字符串</span></div>
                    <div class="code-line"><span class="function">greedy</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'&lt;div&gt;.*&lt;/div&gt;'</span>, <span class="function">html</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"贪婪匹配: {greedy}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 非贪婪匹配：分别匹配每个div</span></div>
                    <div class="code-line"><span class="function">non_greedy</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'&lt;div&gt;.*?&lt;/div&gt;'</span>, <span class="function">html</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"非贪婪匹配: {non_greedy}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 提取div标签内容</span></div>
                    <div class="code-line"><span class="function">content</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'&lt;div&gt;(.*?)&lt;/div&gt;'</span>, <span class="function">html</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"标签内容: {content}"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第10页：转义字符 -->
        <div class="slide">
            <h2><span class="emoji">🔄</span>转义字符</h2>
            <div class="content">
                <div class="text-content">
                    当需要匹配正则表达式中的特殊字符时，需要使用转义字符。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 需要转义的特殊字符</span></div>
                    <div class="code-line"><span class="string">. ^ $ * + ? { } [ ] \ | ( )</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 转义方法</span></div>
                    <div class="code-line"><span class="string">\.</span>     <span class="comment"># 匹配字面意思的点号</span></div>
                    <div class="code-line"><span class="string">\^</span>     <span class="comment"># 匹配字面意思的^</span></div>
                    <div class="code-line"><span class="string">\$</span>     <span class="comment"># 匹配字面意思的$</span></div>
                    <div class="code-line"><span class="string">\\</span>     <span class="comment"># 匹配反斜杠</span></div>
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 转义字符应用示例</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">text</span> = <span class="string">"价格: $19.99, 网址: www.example.com"</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 匹配美元符号和价格</span></div>
                    <div class="code-line"><span class="function">price</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'\$\d+\.\d{2}'</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"价格: {price}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 匹配网址中的点号</span></div>
                    <div class="code-line"><span class="function">domain</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'\w+\.\w+\.\w+'</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"域名: {domain}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 使用re.escape()自动转义</span></div>
                    <div class="code-line"><span class="function">special_chars</span> = <span class="string">"$19.99"</span></div>
                    <div class="code-line"><span class="function">escaped</span> = <span class="function">re</span>.<span class="function">escape</span>(<span class="function">special_chars</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"转义后: {escaped}"</span>)</div>
                    <div class="code-line"><span class="function">found</span> = <span class="function">re</span>.<span class="function">search</span>(<span class="function">escaped</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"找到: {bool(found)}"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第11页：re模块基础函数 -->
        <div class="slide">
            <h2><span class="emoji">🔧</span>re模块基础函数</h2>
            <div class="content">
                <div class="text-content">
                    Python的re模块提供了丰富的正则表达式处理函数。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 主要函数概览</span></div>
                    <div class="code-line"><span class="function">re.search()</span>    <span class="comment"># 搜索第一个匹配</span></div>
                    <div class="code-line"><span class="function">re.match()</span>     <span class="comment"># 从字符串开头匹配</span></div>
                    <div class="code-line"><span class="function">re.findall()</span>   <span class="comment"># 找到所有匹配</span></div>
                    <div class="code-line"><span class="function">re.finditer()</span>  <span class="comment"># 返回匹配对象迭代器</span></div>
                    <div class="code-line"><span class="function">re.sub()</span>       <span class="comment"># 替换匹配的内容</span></div>
                    <div class="code-line"><span class="function">re.split()</span>     <span class="comment"># 按模式分割字符串</span></div>
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 基础函数使用示例</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">text</span> = <span class="string">"Python编程很有趣，学习Python让人快乐"</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># search: 找到第一个匹配</span></div>
                    <div class="code-line"><span class="function">first_match</span> = <span class="function">re</span>.<span class="function">search</span>(<span class="string">r'Python'</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"第一个匹配: {first_match.group() if first_match else 'None'}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># findall: 找到所有匹配</span></div>
                    <div class="code-line"><span class="function">all_matches</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'Python'</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"所有匹配: {all_matches}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># sub: 替换匹配内容</span></div>
                    <div class="code-line"><span class="function">replaced</span> = <span class="function">re</span>.<span class="function">sub</span>(<span class="string">r'Python'</span>, <span class="string">r'Java'</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"替换后: {replaced}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># split: 按模式分割</span></div>
                    <div class="code-line"><span class="function">parts</span> = <span class="function">re</span>.<span class="function">split</span>(<span class="string">r'[，。]'</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"分割结果: {parts}"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第12页：re.search()详解 -->
        <div class="slide">
            <h2><span class="emoji">🔍</span>re.search()详解</h2>
            <div class="content">
                <div class="text-content">
                    re.search()在字符串中搜索第一个匹配的模式，返回Match对象或None。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># re.search()语法</span></div>
                    <div class="code-line"><span class="function">re.search</span>(<span class="function">pattern</span>, <span class="function">string</span>, <span class="function">flags</span>=<span class="number">0</span>)</div>
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># search()详细示例</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">text</span> = <span class="string">"联系方式: 邮箱 <EMAIL>, 电话 138-1234-5678"</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 搜索邮箱地址</span></div>
                    <div class="code-line"><span class="function">email_pattern</span> = <span class="string">r'([\w.-]+)@([\w.-]+)\.([a-zA-Z]{2,})'</span></div>
                    <div class="code-line"><span class="function">email_match</span> = <span class="function">re</span>.<span class="function">search</span>(<span class="function">email_pattern</span>, <span class="function">text</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="keyword">if</span> <span class="function">email_match</span>:</div>
                    <div class="code-line">    <span class="keyword">print</span>(<span class="string">f"完整邮箱: {email_match.group()}"</span>)</div>
                    <div class="code-line">    <span class="keyword">print</span>(<span class="string">f"用户名: {email_match.group(1)}"</span>)</div>
                    <div class="code-line">    <span class="keyword">print</span>(<span class="string">f"域名: {email_match.group(2)}"</span>)</div>
                    <div class="code-line">    <span class="keyword">print</span>(<span class="string">f"顶级域: {email_match.group(3)}"</span>)</div>
                    <div class="code-line">    <span class="keyword">print</span>(<span class="string">f"匹配位置: {email_match.span()}"</span>)</div>
                    <div class="code-line">    <span class="keyword">print</span>(<span class="string">f"开始位置: {email_match.start()}"</span>)</div>
                    <div class="code-line">    <span class="keyword">print</span>(<span class="string">f"结束位置: {email_match.end()}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 搜索不存在的模式</span></div>
                    <div class="code-line"><span class="function">no_match</span> = <span class="function">re</span>.<span class="function">search</span>(<span class="string">r'\d{4}-\d{2}-\d{2}'</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"日期匹配: {no_match}"</span>)  <span class="comment"># None</span></div>
                </div>
            </div>
        </div>

        <!-- 第13页：re.match()与re.fullmatch() -->
        <div class="slide">
            <h2><span class="emoji">🎯</span>re.match()与re.fullmatch()</h2>
            <div class="content">
                <div class="text-content">
                    match()从字符串开头匹配，fullmatch()要求整个字符串都匹配模式。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 函数对比</span></div>
                    <div class="code-line"><span class="function">re.search()</span>     <span class="comment"># 在任意位置搜索</span></div>
                    <div class="code-line"><span class="function">re.match()</span>      <span class="comment"># 从开头匹配</span></div>
                    <div class="code-line"><span class="function">re.fullmatch()</span>  <span class="comment"># 完整匹配整个字符串</span></div>
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 三种匹配方式对比</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">text</span> = <span class="string">"Python编程语言"</span></div>
                    <div class="code-line"><span class="function">pattern</span> = <span class="string">r'Python'</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># search: 在任意位置查找</span></div>
                    <div class="code-line"><span class="function">search_result</span> = <span class="function">re</span>.<span class="function">search</span>(<span class="function">pattern</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"search结果: {bool(search_result)}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># match: 从开头匹配</span></div>
                    <div class="code-line"><span class="function">match_result</span> = <span class="function">re</span>.<span class="function">match</span>(<span class="function">pattern</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"match结果: {bool(match_result)}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># fullmatch: 完整匹配</span></div>
                    <div class="code-line"><span class="function">fullmatch_result</span> = <span class="function">re</span>.<span class="function">fullmatch</span>(<span class="function">pattern</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"fullmatch结果: {bool(fullmatch_result)}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 验证邮箱格式示例</span></div>
                    <div class="code-line"><span class="function">email</span> = <span class="string">"<EMAIL>"</span></div>
                    <div class="code-line"><span class="function">email_pattern</span> = <span class="string">r'^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}$'</span></div>
                    <div class="code-line"><span class="function">is_valid_email</span> = <span class="function">re</span>.<span class="function">fullmatch</span>(<span class="function">email_pattern</span>, <span class="function">email</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"邮箱格式正确: {bool(is_valid_email)}"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第14页：re.findall()与re.finditer() -->
        <div class="slide">
            <h2><span class="emoji">📋</span>re.findall()与re.finditer()</h2>
            <div class="content">
                <div class="text-content">
                    findall()返回所有匹配的字符串列表，finditer()返回匹配对象的迭代器。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># findall与finditer对比</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">text</span> = <span class="string">"订单号: ORD001, ORD002, ORD003"</span></div>
                    <div class="code-line"><span class="function">pattern</span> = <span class="string">r'ORD\d{3}'</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># findall: 返回字符串列表</span></div>
                    <div class="code-line"><span class="function">all_orders</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="function">pattern</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"所有订单号: {all_orders}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># finditer: 返回匹配对象迭代器</span></div>
                    <div class="code-line"><span class="function">order_matches</span> = <span class="function">re</span>.<span class="function">finditer</span>(<span class="function">pattern</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">for</span> <span class="function">i</span>, <span class="function">match</span> <span class="keyword">in</span> <span class="function">enumerate</span>(<span class="function">order_matches</span>, <span class="number">1</span>):</div>
                    <div class="code-line">    <span class="keyword">print</span>(<span class="string">f"第{i}个匹配: {match.group()} 位置: {match.span()}"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第15页：re.sub()替换函数 -->
        <div class="slide">
            <h2><span class="emoji">🔄</span>re.sub()替换函数</h2>
            <div class="content">
                <div class="text-content">
                    re.sub()用于替换字符串中匹配模式的部分，支持简单替换和复杂的回调函数替换。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># sub()基本语法</span></div>
                    <div class="code-line"><span class="function">re.sub</span>(<span class="function">pattern</span>, <span class="function">repl</span>, <span class="function">string</span>, <span class="function">count</span>=<span class="number">0</span>, <span class="function">flags</span>=<span class="number">0</span>)</div>
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 替换示例</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">text</span> = <span class="string">"今天是2024年1月15日，明天是2024年1月16日"</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 简单替换</span></div>
                    <div class="code-line"><span class="function">replaced</span> = <span class="function">re</span>.<span class="function">sub</span>(<span class="string">r'2024'</span>, <span class="string">r'2025'</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"年份替换: {replaced}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 使用分组进行替换</span></div>
                    <div class="code-line"><span class="function">phone_text</span> = <span class="string">"联系电话: 138-1234-5678"</span></div>
                    <div class="code-line"><span class="function">formatted</span> = <span class="function">re</span>.<span class="function">sub</span>(<span class="string">r'(\d{3})-(\d{4})-(\d{4})'</span>, <span class="string">r'(\1) \2-\3'</span>, <span class="function">phone_text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"格式化电话: {formatted}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 使用函数进行替换</span></div>
                    <div class="code-line"><span class="keyword">def</span> <span class="function">upper_match</span>(<span class="function">match</span>):</div>
                    <div class="code-line">    <span class="keyword">return</span> <span class="function">match</span>.<span class="function">group</span>().<span class="function">upper</span>()</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">text2</span> = <span class="string">"学习python和java编程"</span></div>
                    <div class="code-line"><span class="function">upper_result</span> = <span class="function">re</span>.<span class="function">sub</span>(<span class="string">r'\b[a-z]+\b'</span>, <span class="function">upper_match</span>, <span class="function">text2</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"大写转换: {upper_result}"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第16页：re.split()分割函数 -->
        <div class="slide">
            <h2><span class="emoji">✂️</span>re.split()分割函数</h2>
            <div class="content">
                <div class="text-content">
                    re.split()使用正则表达式模式来分割字符串，比普通的split()更灵活。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># split()基本语法</span></div>
                    <div class="code-line"><span class="function">re.split</span>(<span class="function">pattern</span>, <span class="function">string</span>, <span class="function">maxsplit</span>=<span class="number">0</span>, <span class="function">flags</span>=<span class="number">0</span>)</div>
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 分割示例</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">text</span> = <span class="string">"苹果,香蕉;橙子 葡萄\t西瓜\n草莓"</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 按多种分隔符分割</span></div>
                    <div class="code-line"><span class="function">fruits</span> = <span class="function">re</span>.<span class="function">split</span>(<span class="string">r'[,;\s]+'</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"水果列表: {fruits}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 保留分隔符</span></div>
                    <div class="code-line"><span class="function">text2</span> = <span class="string">"Hello,World;Python"</span></div>
                    <div class="code-line"><span class="function">parts_with_sep</span> = <span class="function">re</span>.<span class="function">split</span>(<span class="string">r'([,;])'</span>, <span class="function">text2</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"保留分隔符: {parts_with_sep}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 限制分割次数</span></div>
                    <div class="code-line"><span class="function">text3</span> = <span class="string">"a-b-c-d-e"</span></div>
                    <div class="code-line"><span class="function">limited_split</span> = <span class="function">re</span>.<span class="function">split</span>(<span class="string">r'-'</span>, <span class="function">text3</span>, <span class="function">maxsplit</span>=<span class="number">2</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"限制分割: {limited_split}"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第17页：编译正则表达式 -->
        <div class="slide">
            <h2><span class="emoji">⚙️</span>编译正则表达式</h2>
            <div class="content">
                <div class="text-content">
                    使用re.compile()预编译正则表达式可以提高重复使用时的性能。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 编译正则表达式</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 编译模式对象</span></div>
                    <div class="code-line"><span class="function">email_pattern</span> = <span class="function">re</span>.<span class="function">compile</span>(<span class="string">r'[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}'</span>)</div>
                    <div class="code-line"><span class="function">phone_pattern</span> = <span class="function">re</span>.<span class="function">compile</span>(<span class="string">r'\d{3}-\d{4}-\d{4}'</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">text</span> = <span class="string">"联系方式: <EMAIL>, 138-1234-5678"</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 使用编译后的模式</span></div>
                    <div class="code-line"><span class="function">email_match</span> = <span class="function">email_pattern</span>.<span class="function">search</span>(<span class="function">text</span>)</div>
                    <div class="code-line"><span class="function">phone_match</span> = <span class="function">phone_pattern</span>.<span class="function">search</span>(<span class="function">text</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"邮箱: {email_match.group() if email_match else 'None'}"</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"电话: {phone_match.group() if phone_match else 'None'}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 编译的优势：重复使用时性能更好</span></div>
                    <div class="code-line"><span class="function">texts</span> = [<span class="string">"<EMAIL>"</span>, <span class="string">"<EMAIL>"</span>, <span class="string">"<EMAIL>"</span>]</div>
                    <div class="code-line"><span class="keyword">for</span> <span class="function">t</span> <span class="keyword">in</span> <span class="function">texts</span>:</div>
                    <div class="code-line">    <span class="keyword">if</span> <span class="function">email_pattern</span>.<span class="function">match</span>(<span class="function">t</span>):</div>
                    <div class="code-line">        <span class="keyword">print</span>(<span class="string">f"有效邮箱: {t}"</span>)</div>
                    <div class="code-line">    <span class="keyword">else</span>:</div>
                    <div class="code-line">        <span class="keyword">print</span>(<span class="string">f"无效邮箱: {t}"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第18页：正则表达式标志 -->
        <div class="slide">
            <h2><span class="emoji">🚩</span>正则表达式标志</h2>
            <div class="content">
                <div class="text-content">
                    标志（flags）用于修改正则表达式的匹配行为。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 常用标志</span></div>
                    <div class="code-line"><span class="function">re.IGNORECASE</span> <span class="comment"># 忽略大小写</span></div>
                    <div class="code-line"><span class="function">re.MULTILINE</span>  <span class="comment"># 多行模式</span></div>
                    <div class="code-line"><span class="function">re.DOTALL</span>     <span class="comment"># 点号匹配所有字符包括换行符</span></div>
                    <div class="code-line"><span class="function">re.VERBOSE</span>    <span class="comment"># 详细模式，允许注释</span></div>
                    <div class="code-line"><span class="function">re.ASCII</span>      <span class="comment"># ASCII模式</span></div>
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 标志使用示例</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">text</span> = <span class="string">"""Python编程
JAVA开发
javascript前端"""</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 忽略大小写匹配</span></div>
                    <div class="code-line"><span class="function">case_insensitive</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'java'</span>, <span class="function">text</span>, <span class="function">re</span>.<span class="function">IGNORECASE</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"忽略大小写: {case_insensitive}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 多行模式</span></div>
                    <div class="code-line"><span class="function">multiline_match</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'^\w+'</span>, <span class="function">text</span>, <span class="function">re</span>.<span class="function">MULTILINE</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"每行开头的单词: {multiline_match}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 组合多个标志</span></div>
                    <div class="code-line"><span class="function">combined_flags</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'^java'</span>, <span class="function">text</span>, <span class="function">re</span>.<span class="function">IGNORECASE</span> | <span class="function">re</span>.<span class="function">MULTILINE</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"组合标志: {combined_flags}"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第19页：实际应用案例 - 数据验证 -->
        <div class="slide">
            <h2><span class="emoji">✅</span>实际应用案例 - 数据验证</h2>
            <div class="content">
                <div class="text-content">
                    使用正则表达式进行常见的数据格式验证。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 数据验证函数集合</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="keyword">def</span> <span class="function">validate_email</span>(<span class="function">email</span>):</div>
                    <div class="code-line">    <span class="string">"""验证邮箱格式"""</span></div>
                    <div class="code-line">    <span class="function">pattern</span> = <span class="string">r'^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}$'</span></div>
                    <div class="code-line">    <span class="keyword">return</span> <span class="function">bool</span>(<span class="function">re</span>.<span class="function">match</span>(<span class="function">pattern</span>, <span class="function">email</span>))</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="keyword">def</span> <span class="function">validate_phone</span>(<span class="function">phone</span>):</div>
                    <div class="code-line">    <span class="string">"""验证手机号格式"""</span></div>
                    <div class="code-line">    <span class="function">pattern</span> = <span class="string">r'^1[3-9]\d{9}$'</span></div>
                    <div class="code-line">    <span class="keyword">return</span> <span class="function">bool</span>(<span class="function">re</span>.<span class="function">match</span>(<span class="function">pattern</span>, <span class="function">phone</span>))</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="keyword">def</span> <span class="function">validate_id_card</span>(<span class="function">id_card</span>):</div>
                    <div class="code-line">    <span class="string">"""验证身份证号格式"""</span></div>
                    <div class="code-line">    <span class="function">pattern</span> = <span class="string">r'^\d{17}[\dXx]$'</span></div>
                    <div class="code-line">    <span class="keyword">return</span> <span class="function">bool</span>(<span class="function">re</span>.<span class="function">match</span>(<span class="function">pattern</span>, <span class="function">id_card</span>))</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 测试验证函数</span></div>
                    <div class="code-line"><span class="function">test_data</span> = {</div>
                    <div class="code-line">    <span class="string">'email'</span>: <span class="string">'<EMAIL>'</span>,</div>
                    <div class="code-line">    <span class="string">'phone'</span>: <span class="string">'13812345678'</span>,</div>
                    <div class="code-line">    <span class="string">'id_card'</span>: <span class="string">'110101199001011234'</span></div>
                    <div class="code-line">}</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"邮箱验证: {validate_email(test_data['email'])}"</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"手机验证: {validate_phone(test_data['phone'])}"</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"身份证验证: {validate_id_card(test_data['id_card'])}"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第20页：实际应用案例 - 文本处理 -->
        <div class="slide">
            <h2><span class="emoji">📝</span>实际应用案例 - 文本处理</h2>
            <div class="content">
                <div class="text-content">
                    使用正则表达式进行复杂的文本提取和处理任务。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 文本处理示例</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">log_text</span> = <span class="string">"""2024-01-15 10:30:25 [INFO] 用户登录成功 IP:*************
2024-01-15 10:31:02 [ERROR] 数据库连接失败 IP:*************
2024-01-15 10:32:15 [WARNING] 内存使用率过高 IP:*************"""</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 提取日志信息</span></div>
                    <div class="code-line"><span class="function">log_pattern</span> = <span class="string">r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \[(\w+)\] (.+) IP:(\d+\.\d+\.\d+\.\d+)'</span></div>
                    <div class="code-line"><span class="function">log_matches</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="function">log_pattern</span>, <span class="function">log_text</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="keyword">for</span> <span class="function">match</span> <span class="keyword">in</span> <span class="function">log_matches</span>:</div>
                    <div class="code-line">    <span class="function">timestamp</span>, <span class="function">level</span>, <span class="function">message</span>, <span class="function">ip</span> = <span class="function">match</span></div>
                    <div class="code-line">    <span class="keyword">print</span>(<span class="string">f"时间: {timestamp}, 级别: {level}, IP: {ip}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 清理HTML标签</span></div>
                    <div class="code-line"><span class="function">html_text</span> = <span class="string">"&lt;p&gt;这是一个&lt;strong&gt;重要&lt;/strong&gt;的&lt;em&gt;消息&lt;/em&gt;&lt;/p&gt;"</span></div>
                    <div class="code-line"><span class="function">clean_text</span> = <span class="function">re</span>.<span class="function">sub</span>(<span class="string">r'&lt;[^&gt;]+&gt;'</span>, <span class="string">''</span>, <span class="function">html_text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"清理后: {clean_text}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 提取URL链接</span></div>
                    <div class="code-line"><span class="function">text_with_urls</span> = <span class="string">"访问 https://www.python.org 和 http://example.com 获取更多信息"</span></div>
                    <div class="code-line"><span class="function">urls</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'https?://[\w.-]+(?:/[\w.-]*)*'</span>, <span class="function">text_with_urls</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"找到的URL: {urls}"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第21页：AI辅助编程 - 学习正则表达式 -->
        <div class="slide">
            <h2><span class="emoji">🤖</span>AI辅助编程 - 学习正则表达式</h2>
            <div class="content">
                <div class="text-content">
                    利用AI工具可以大大提高学习正则表达式的效率和理解深度。
                </div>
                <div class="text-content">
                    <strong>AI辅助学习的优势：</strong><br>
                    • 即时解答疑问，提供详细解释<br>
                    • 生成大量练习案例和测试数据<br>
                    • 提供多种解决方案和最佳实践<br>
                    • 帮助调试和优化正则表达式<br>
                    • 解释复杂模式的工作原理
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># AI可以帮助的场景示例</span></div>
                    <div class="code-line"><span class="comment"># 1. 解释复杂的正则表达式</span></div>
                    <div class="code-line"><span class="function">complex_pattern</span> = <span class="string">r'(?P&lt;year&gt;\d{4})-(?P&lt;month&gt;\d{2})-(?P&lt;day&gt;\d{2})T(?P&lt;hour&gt;\d{2}):(?P&lt;minute&gt;\d{2}):(?P&lt;second&gt;\d{2})'</span></div>
                    <div class="code-line"><span class="comment"># AI可以解释：这是一个匹配ISO 8601日期时间格式的正则表达式</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 2. 生成测试用例</span></div>
                    <div class="code-line"><span class="function">test_cases</span> = [</div>
                    <div class="code-line">    <span class="string">'2024-01-15T10:30:25'</span>,  <span class="comment"># 有效</span></div>
                    <div class="code-line">    <span class="string">'2024-1-15T10:30:25'</span>,   <span class="comment"># 无效（月份格式）</span></div>
                    <div class="code-line">    <span class="string">'2024-01-15 10:30:25'</span>,  <span class="comment"># 无效（缺少T）</span></div>
                    <div class="code-line">]</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 3. 优化建议</span></div>
                    <div class="code-line"><span class="comment"># AI可以建议使用更严格的验证或提供替代方案</span></div>
                </div>
            </div>
        </div>

        <!-- 第22页：AI提示词模板 - 基础学习 -->
        <div class="slide">
            <h2><span class="emoji">💡</span>AI提示词模板 - 基础学习</h2>
            <div class="content">
                <div class="text-content">
                    使用这些提示词模板与AI进行高效的正则表达式学习对话。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 模板1：解释正则表达式</span></div>
                    <div class="code-line"><span class="string">"""</span></div>
                    <div class="code-line"><span class="string">请详细解释这个正则表达式的含义：[你的正则表达式]</span></div>
                    <div class="code-line"><span class="string">包括：</span></div>
                    <div class="code-line"><span class="string">1. 每个部分的作用</span></div>
                    <div class="code-line"><span class="string">2. 匹配的具体规则</span></div>
                    <div class="code-line"><span class="string">3. 提供3个匹配和3个不匹配的示例</span></div>
                    <div class="code-line"><span class="string">"""</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 模板2：生成正则表达式</span></div>
                    <div class="code-line"><span class="string">"""</span></div>
                    <div class="code-line"><span class="string">我需要一个正则表达式来匹配：[描述你的需求]</span></div>
                    <div class="code-line"><span class="string">要求：</span></div>
                    <div class="code-line"><span class="string">1. 提供完整的正则表达式</span></div>
                    <div class="code-line"><span class="string">2. 解释每个部分的作用</span></div>
                    <div class="code-line"><span class="string">3. 提供Python代码示例</span></div>
                    <div class="code-line"><span class="string">4. 包含测试用例</span></div>
                    <div class="code-line"><span class="string">"""</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 模板3：调试正则表达式</span></div>
                    <div class="code-line"><span class="string">"""</span></div>
                    <div class="code-line"><span class="string">我的正则表达式：[你的表达式]</span></div>
                    <div class="code-line"><span class="string">测试文本：[你的测试文本]</span></div>
                    <div class="code-line"><span class="string">期望结果：[期望匹配的内容]</span></div>
                    <div class="code-line"><span class="string">实际结果：[实际匹配的内容]</span></div>
                    <div class="code-line"><span class="string">请帮我找出问题并提供修正方案。</span></div>
                    <div class="code-line"><span class="string">"""</span></div>
                </div>
            </div>
        </div>

        <!-- 第24页：正则表达式高级技巧 -->
        <div class="slide">
            <h2><span class="emoji">⚡</span>正则表达式高级技巧</h2>
            <div class="content">
                <div class="text-content">
                    掌握这些高级技巧可以让你的正则表达式更加强大和高效。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 1. 前瞻和后顾断言</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 正向前瞻 (?=...)</span></div>
                    <div class="code-line"><span class="function">text</span> = <span class="string">"password123 password abc123"</span></div>
                    <div class="code-line"><span class="function">strong_passwords</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'\b\w*(?=.*\d)\w*\b'</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"包含数字的单词: {strong_passwords}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 负向前瞻 (?!...)</span></div>
                    <div class="code-line"><span class="function">no_digits</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'\b(?!.*\d)\w+\b'</span>, <span class="function">text</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"不包含数字的单词: {no_digits}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 2. 条件匹配</span></div>
                    <div class="code-line"><span class="function">conditional_pattern</span> = <span class="string">r'(\d+)(?:(\.)\d+)?'</span></div>
                    <div class="code-line"><span class="function">numbers</span> = <span class="string">"123 45.67 89"</span></div>
                    <div class="code-line"><span class="function">matches</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="function">conditional_pattern</span>, <span class="function">numbers</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"数字匹配: {matches}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 3. 递归匹配（Python不直接支持，但可以用其他方法）</span></div>
                    <div class="code-line"><span class="function">nested_brackets</span> = <span class="string">"((()))(()())"</span></div>
                    <div class="code-line"><span class="comment"># 使用平衡组的替代方法</span></div>
                    <div class="code-line"><span class="function">balanced</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'\([^()]*\)'</span>, <span class="function">nested_brackets</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"平衡括号: {balanced}"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第25页：性能优化和最佳实践 -->
        <div class="slide">
            <h2><span class="emoji">🏆</span>性能优化和最佳实践</h2>
            <div class="content">
                <div class="text-content">
                    编写高效、可维护的正则表达式的关键原则。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 1. 编译正则表达式以提高性能</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">time</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="keyword">def</span> <span class="function">validate_emails</span>(<span class="function">email_list</span>):</div>
                    <div class="code-line">    <span class="string">"""验证邮箱列表的函数"""</span></div>
                    <div class="code-line">    <span class="function">pattern</span> = <span class="function">re</span>.<span class="function">compile</span>(<span class="string">r'^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}$'</span>)</div>
                    <div class="code-line">    <span class="function">valid_emails</span> = []</div>
                    <div class="code-line">    </div>
                    <div class="code-line">    <span class="keyword">for</span> <span class="function">email</span> <span class="keyword">in</span> <span class="function">email_list</span>:</div>
                    <div class="code-line">        <span class="keyword">if</span> <span class="function">pattern</span>.<span class="function">match</span>(<span class="function">email</span>):</div>
                    <div class="code-line">            <span class="function">valid_emails</span>.<span class="function">append</span>(<span class="function">email</span>)</div>
                    <div class="code-line">            <span class="keyword">print</span>(<span class="string">f"✓ 有效邮箱: {email}"</span>)</div>
                    <div class="code-line">        <span class="keyword">else</span>:</div>
                    <div class="code-line">            <span class="keyword">print</span>(<span class="string">f"✗ 无效邮箱: {email}"</span>)</div>
                    <div class="code-line">    </div>
                    <div class="code-line">    <span class="keyword">return</span> <span class="function">valid_emails</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 测试函数</span></div>
                    <div class="code-line"><span class="function">emails</span> = [<span class="string">"<EMAIL>"</span>, <span class="string">"invalid-email"</span>, <span class="string">"<EMAIL>"</span>]</div>
                    <div class="code-line"><span class="function">result</span> = <span class="function">validate_emails</span>(<span class="function">emails</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 不推荐：每次都重新编译</span></div>
                    <div class="code-line"><span class="keyword">def</span> <span class="function">slow_search</span>(<span class="function">text_list</span>):</div>
                    <div class="code-line">    <span class="function">results</span> = []</div>
                    <div class="code-line">    <span class="keyword">for</span> <span class="function">text</span> <span class="keyword">in</span> <span class="function">text_list</span>:</div>
                    <div class="code-line">        <span class="keyword">if</span> <span class="function">re</span>.<span class="function">search</span>(<span class="string">r'\b\w+@\w+\.\w+\b'</span>, <span class="function">text</span>):</div>
                    <div class="code-line">            <span class="function">results</span>.<span class="function">append</span>(<span class="function">text</span>)</div>
                    <div class="code-line">    <span class="keyword">return</span> <span class="function">results</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 推荐：预编译正则表达式</span></div>
                    <div class="code-line"><span class="function">EMAIL_PATTERN</span> = <span class="function">re</span>.<span class="function">compile</span>(<span class="string">r'\b\w+@\w+\.\w+\b'</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="keyword">def</span> <span class="function">fast_search</span>(<span class="function">text_list</span>):</div>
                    <div class="code-line">    <span class="function">results</span> = []</div>
                    <div class="code-line">    <span class="keyword">for</span> <span class="function">text</span> <span class="keyword">in</span> <span class="function">text_list</span>:</div>
                    <div class="code-line">        <span class="keyword">if</span> <span class="function">EMAIL_PATTERN</span>.<span class="function">search</span>(<span class="function">text</span>):</div>
                    <div class="code-line">            <span class="function">results</span>.<span class="function">append</span>(<span class="function">text</span>)</div>
                    <div class="code-line">    <span class="keyword">return</span> <span class="function">results</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 2. 避免回溯陷阱</span></div>
                    <div class="code-line"><span class="comment"># 危险：可能导致灾难性回溯</span></div>
                    <div class="code-line"><span class="comment"># bad_pattern = r'(a+)+b'</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 安全：使用原子组或占有量词</span></div>
                    <div class="code-line"><span class="function">safe_pattern</span> = <span class="string">r'a+b'</span></div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">"使用简化的模式避免回溯问题"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第26页：常见错误和陷阱 -->
        <div class="slide">
            <h2><span class="emoji">⚠️</span>常见错误和陷阱</h2>
            <div class="content">
                <div class="text-content">
                    了解这些常见错误可以帮你避免在实际开发中踩坑。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 1. 忘记转义特殊字符</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 错误：没有转义点号</span></div>
                    <div class="code-line"><span class="function">wrong_pattern</span> = <span class="string">r'file.txt'</span>  <span class="comment"># 会匹配 "fileXtxt"</span></div>
                    <div class="code-line"><span class="comment"># 正确：转义点号</span></div>
                    <div class="code-line"><span class="function">correct_pattern</span> = <span class="string">r'file\.txt'</span>  <span class="comment"># 只匹配 "file.txt"</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">test_text</span> = <span class="string">"file.txt fileXtxt"</span></div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"错误模式匹配: {re.findall(wrong_pattern, test_text)}"</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"正确模式匹配: {re.findall(correct_pattern, test_text)}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 2. 贪婪匹配的陷阱</span></div>
                    <div class="code-line"><span class="function">html</span> = <span class="string">"&lt;div&gt;内容1&lt;/div&gt;&lt;div&gt;内容2&lt;/div&gt;"</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 贪婪匹配：会匹配整个字符串</span></div>
                    <div class="code-line"><span class="function">greedy</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'&lt;div&gt;.*&lt;/div&gt;'</span>, <span class="function">html</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"贪婪匹配: {greedy}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 非贪婪匹配：匹配最短的</span></div>
                    <div class="code-line"><span class="function">non_greedy</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="string">r'&lt;div&gt;.*?&lt;/div&gt;'</span>, <span class="function">html</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"非贪婪匹配: {non_greedy}"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 3. 字符集的常见错误</span></div>
                    <div class="code-line"><span class="comment"># 错误：在字符集中使用 \d</span></div>
                    <div class="code-line"><span class="function">wrong_charset</span> = <span class="string">r'[\d-]'</span>  <span class="comment"># 可能不是你想要的</span></div>
                    <div class="code-line"><span class="comment"># 正确：明确指定范围</span></div>
                    <div class="code-line"><span class="function">correct_charset</span> = <span class="string">r'[0-9-]'</span>  <span class="comment"># 数字和连字符</span></div>
                </div>
            </div>
        </div>

        <!-- 第27页：正则表达式工具和资源 -->
        <div class="slide">
            <h2><span class="emoji">🛠️</span>正则表达式工具和资源</h2>
            <div class="content">
                <div class="text-content">
                    推荐的工具和资源，帮助你更好地学习和使用正则表达式。
                </div>
                <div class="text-content">
                    <strong>在线测试工具：</strong><br>
                    • regex101.com - 功能强大的在线正则测试器<br>
                    • regexr.com - 可视化正则表达式学习工具<br>
                    • regexpal.com - 简洁的在线测试工具<br><br>
                    
                    <strong>Python正则表达式库：</strong><br>
                    • re - Python标准库<br>
                    • regex - 增强版正则表达式库<br>
                    • pyparsing - 复杂解析的替代方案<br><br>
                    
                    <strong>学习资源：</strong><br>
                    • Python官方文档 - re模块详细说明<br>
                    • 《精通正则表达式》- 经典书籍<br>
                    • MDN正则表达式指南 - Web开发参考
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 使用regex库的示例（需要安装：pip install regex）</span></div>
                    <div class="code-line"><span class="comment"># import regex as re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># regex库支持更多高级功能</span></div>
                    <div class="code-line"><span class="comment"># 如递归匹配、更好的Unicode支持等</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 调试正则表达式的技巧</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 使用详细模式编写可读的正则表达式</span></div>
                    <div class="code-line"><span class="function">verbose_pattern</span> = <span class="function">re</span>.<span class="function">compile</span>(<span class="string">r"""</span></div>
                    <div class="code-line"><span class="string">    ^                    # 行开始</span></div>
                    <div class="code-line"><span class="string">    [a-zA-Z0-9._%+-]+    # 用户名部分</span></div>
                    <div class="code-line"><span class="string">    @                    # @ 符号</span></div>
                    <div class="code-line"><span class="string">    [a-zA-Z0-9.-]+       # 域名部分</span></div>
                    <div class="code-line"><span class="string">    \.                   # 点号</span></div>
                    <div class="code-line"><span class="string">    [a-zA-Z]{2,}         # 顶级域名</span></div>
                    <div class="code-line"><span class="string">    $                    # 行结束</span></div>
                    <div class="code-line"><span class="string">"""</span>, <span class="function">re</span>.<span class="function">VERBOSE</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">email</span> = <span class="string">"<EMAIL>"</span></div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"邮箱验证: {bool(verbose_pattern.match(email))}"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第28页：实战项目 - 日志分析器 -->
        <div class="slide">
            <h2><span class="emoji">📊</span>实战项目 - 日志分析器</h2>
            <div class="content">
                <div class="text-content">
                    综合运用正则表达式知识，构建一个完整的日志分析工具。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 日志分析器实现</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"><span class="keyword">from</span> <span class="function">collections</span> <span class="keyword">import</span> <span class="function">Counter</span></div>
                    <div class="code-line"><span class="keyword">from</span> <span class="function">datetime</span> <span class="keyword">import</span> <span class="function">datetime</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="keyword">class</span> <span class="function">LogAnalyzer</span>:</div>
                    <div class="code-line">    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="function">self</span>):</div>
                    <div class="code-line">        <span class="comment"># 编译常用的正则表达式模式</span></div>
                    <div class="code-line">        <span class="function">self</span>.<span class="function">log_pattern</span> = <span class="function">re</span>.<span class="function">compile</span>(<span class="string">r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \[(\w+)\] (.+)'</span>)</div>
                    <div class="code-line">        <span class="function">self</span>.<span class="function">ip_pattern</span> = <span class="function">re</span>.<span class="function">compile</span>(<span class="string">r'\b(?:\d{1,3}\.){3}\d{1,3}\b'</span>)</div>
                    <div class="code-line">        <span class="function">self</span>.<span class="function">error_pattern</span> = <span class="function">re</span>.<span class="function">compile</span>(<span class="string">r'\b(error|exception|failed|timeout)\b'</span>, <span class="function">re</span>.<span class="function">IGNORECASE</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line">    <span class="keyword">def</span> <span class="function">parse_log_line</span>(<span class="function">self</span>, <span class="function">line</span>):</div>
                    <div class="code-line">        <span class="string">"""解析单行日志"""</span></div>
                    <div class="code-line">        <span class="function">match</span> = <span class="function">self</span>.<span class="function">log_pattern</span>.<span class="function">match</span>(<span class="function">line</span>)</div>
                    <div class="code-line">        <span class="keyword">if</span> <span class="function">match</span>:</div>
                    <div class="code-line">            <span class="function">timestamp</span>, <span class="function">level</span>, <span class="function">message</span> = <span class="function">match</span>.<span class="function">groups</span>()</div>
                    <div class="code-line">            <span class="keyword">return</span> {</div>
                    <div class="code-line">                <span class="string">'timestamp'</span>: <span class="function">timestamp</span>,</div>
                    <div class="code-line">                <span class="string">'level'</span>: <span class="function">level</span>,</div>
                    <div class="code-line">                <span class="string">'message'</span>: <span class="function">message</span>,</div>
                    <div class="code-line">                <span class="string">'ips'</span>: <span class="function">self</span>.<span class="function">ip_pattern</span>.<span class="function">findall</span>(<span class="function">message</span>),</div>
                    <div class="code-line">                <span class="string">'has_error'</span>: <span class="function">bool</span>(<span class="function">self</span>.<span class="function">error_pattern</span>.<span class="function">search</span>(<span class="function">message</span>))</div>
                    <div class="code-line">            }</div>
                    <div class="code-line">        <span class="keyword">return</span> <span class="function">None</span></div>
                    <div class="code-line"></div>
                    <div class="code-line">    <span class="keyword">def</span> <span class="function">analyze_logs</span>(<span class="function">self</span>, <span class="function">log_lines</span>):</div>
                    <div class="code-line">        <span class="string">"""分析日志统计信息"""</span></div>
                    <div class="code-line">        <span class="function">level_counts</span> = <span class="function">Counter</span>()</div>
                    <div class="code-line">        <span class="function">ip_counts</span> = <span class="function">Counter</span>()</div>
                    <div class="code-line">        <span class="function">error_count</span> = <span class="number">0</span></div>
                    <div class="code-line"></div>
                    <div class="code-line">        <span class="keyword">for</span> <span class="function">line</span> <span class="keyword">in</span> <span class="function">log_lines</span>:</div>
                    <div class="code-line">            <span class="function">parsed</span> = <span class="function">self</span>.<span class="function">parse_log_line</span>(<span class="function">line</span>)</div>
                    <div class="code-line">            <span class="keyword">if</span> <span class="function">parsed</span>:</div>
                    <div class="code-line">                <span class="function">level_counts</span>[<span class="function">parsed</span>[<span class="string">'level'</span>]] += <span class="number">1</span></div>
                    <div class="code-line">                <span class="function">ip_counts</span>.<span class="function">update</span>(<span class="function">parsed</span>[<span class="string">'ips'</span>])</div>
                    <div class="code-line">                <span class="keyword">if</span> <span class="function">parsed</span>[<span class="string">'has_error'</span>]:</div>
                    <div class="code-line">                    <span class="function">error_count</span> += <span class="number">1</span></div>
                    <div class="code-line"></div>
                    <div class="code-line">        <span class="keyword">return</span> {</div>
                    <div class="code-line">            <span class="string">'level_distribution'</span>: <span class="function">dict</span>(<span class="function">level_counts</span>),</div>
                    <div class="code-line">            <span class="string">'top_ips'</span>: <span class="function">ip_counts</span>.<span class="function">most_common</span>(<span class="number">5</span>),</div>
                    <div class="code-line">            <span class="string">'error_count'</span>: <span class="function">error_count</span></div>
                    <div class="code-line">        }</div>
                </div>
            </div>
        </div>

        <!-- 第29页：学习路径和进阶方向 -->
        <div class="slide">
            <h2><span class="emoji">🎯</span>学习路径和进阶方向</h2>
            <div class="content">
                <div class="text-content">
                    为不同水平的学习者提供系统的学习路径和进阶建议。
                </div>
                <div class="text-content">
                    <strong>初级阶段（1-2周）：</strong><br>
                    • 掌握基本元字符和量词<br>
                    • 学会使用字符集和分组<br>
                    • 熟练使用re.search()、re.match()、re.findall()<br>
                    • 练习简单的文本匹配和提取<br><br>
                    
                    <strong>中级阶段（2-4周）：</strong><br>
                    • 掌握前瞻后顾断言<br>
                    • 学会使用命名分组和反向引用<br>
                    • 理解贪婪与非贪婪匹配<br>
                    • 熟练使用re.sub()进行文本替换<br>
                    • 学会编译正则表达式优化性能<br><br>
                    
                    <strong>高级阶段（持续学习）：</strong><br>
                    • 掌握复杂的模式设计<br>
                    • 学会性能优化和避免回溯陷阱<br>
                    • 了解不同编程语言的正则差异<br>
                    • 能够设计复杂的文本处理系统
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 练习项目建议</span></div>
                    <div class="code-line"><span class="comment"># 1. 初级项目</span></div>
                    <div class="code-line"><span class="comment">#    - 邮箱验证器</span></div>
                    <div class="code-line"><span class="comment">#    - 电话号码格式化工具</span></div>
                    <div class="code-line"><span class="comment">#    - 简单的数据清洗脚本</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 2. 中级项目</span></div>
                    <div class="code-line"><span class="comment">#    - 日志分析工具</span></div>
                    <div class="code-line"><span class="comment">#    - 网页内容提取器</span></div>
                    <div class="code-line"><span class="comment">#    - 配置文件解析器</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 3. 高级项目</span></div>
                    <div class="code-line"><span class="comment">#    - 简单的编程语言词法分析器</span></div>
                    <div class="code-line"><span class="comment">#    - 复杂文档格式转换工具</span></div>
                    <div class="code-line"><span class="comment">#    - 智能文本处理系统</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 持续学习建议</span></div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">"记住：正则表达式是工具，不是目的"</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">"在实际项目中应用才能真正掌握"</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">"保持练习，关注性能和可维护性"</span>)</div>
                </div>
            </div>
        </div>

        <!-- 第30页：总结 -->
        <div class="slide">
            <h2><span class="emoji">🎉</span>总结</h2>
            <div class="content">
                <div class="text-content">
                    恭喜你完成了Python正则表达式的学习之旅！
                </div>
                <div class="text-content">
                    <strong>我们学习了：</strong><br>
                    • 正则表达式的基础概念和语法<br>
                    • Python re模块的核心函数<br>
                    • 实际应用场景和最佳实践<br>
                    • AI辅助学习的方法和技巧<br>
                    • 性能优化和常见陷阱<br><br>
                    
                    <strong>关键要点：</strong><br>
                    • 正则表达式是强大的文本处理工具<br>
                    • 合理使用可以大大提高开发效率<br>
                    • 注意性能和可读性的平衡<br>
                    • 结合AI工具可以加速学习过程<br><br>
                    
                    <strong>下一步：</strong><br>
                    • 在实际项目中应用所学知识<br>
                    • 持续练习和优化技能<br>
                    • 关注新的工具和最佳实践<br>
                    • 与社区分享经验和心得
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 最后的寄语</span></div>
                    <div class="code-line"><span class="keyword">import</span> <span class="function">re</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="function">message</span> = <span class="string">"学习正则表达式，让文本处理变得简单高效！"</span></div>
                    <div class="code-line"><span class="function">pattern</span> = <span class="string">r'(\w+)'</span></div>
                    <div class="code-line"><span class="function">words</span> = <span class="function">re</span>.<span class="function">findall</span>(<span class="function">pattern</span>, <span class="function">message</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">"感谢你的学习！"</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">f"提取的关键词: {words}"</span>)</div>
                    <div class="code-line"><span class="keyword">print</span>(<span class="string">"继续探索，持续进步！🚀"</span>)</div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 正则表达式：让复杂的文本处理变得简单</span></div>
                    <div class="code-line"><span class="comment"># AI辅助：让学习过程更加高效</span></div>
                    <div class="code-line"><span class="comment"># 实践应用：让知识转化为能力</span></div>
                </div>
            </div>
        </div>

        <!-- JavaScript结束标签 -->
        </div>
        
        <div class="navigation">
            <button id="prevBtn" onclick="changeSlide(-1)">‹ 上一页</button>
            <span id="slideInfo">1 / 30</span>
            <button id="nextBtn" onclick="changeSlide(1)">下一页 ›</button>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            
            document.getElementById('slideInfo').textContent = `${currentSlide + 1} / ${totalSlides}`;
            
            // 更新导航按钮状态
            document.getElementById('prevBtn').disabled = currentSlide === 0;
            document.getElementById('nextBtn').disabled = currentSlide === totalSlides - 1;
            
            // 触发动画
            const activeSlide = slides[currentSlide];
            const codeLines = activeSlide.querySelectorAll('.code-line');
            
            codeLines.forEach((line, index) => {
                line.style.animationDelay = `${index * 0.1}s`;
                line.classList.remove('animate');
                setTimeout(() => {
                    line.classList.add('animate');
                }, 50);
            });
        }

        function changeSlide(direction) {
            showSlide(currentSlide + direction);
        }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') {
                changeSlide(-1);
            } else if (e.key === 'ArrowRight') {
                changeSlide(1);
            }
        });

        // 初始化
        showSlide(0);
    </script>
</body>
</html>

        <!-- 第23页：AI提示词模板 - 进阶应用 -->
        <div class="slide">
            <h2><span class="emoji">🚀</span>AI提示词模板 - 进阶应用</h2>
            <div class="content">
                <div class="text-content">
                    针对复杂场景和性能优化的AI提示词模板。
                </div>
                <div class="code-block">
                    <div class="code-line"><span class="comment"># 模板4：性能优化</span></div>
                    <div class="code-line"><span class="string">"""</span></div>
                    <div class="code-line"><span class="string">请帮我优化这个正则表达式的性能：[你的表达式]</span></div>
                    <div class="code-line"><span class="string">使用场景：[描述使用场景，如大文件处理、实时匹配等]</span></div>
                    <div class="code-line"><span class="string">请提供：</span></div>
                    <div class="code-line"><span class="string">1. 优化后的表达式</span></div>
                    <div class="code-line"><span class="string">2. 性能改进说明</span></div>
                    <div class="code-line"><span class="string">3. 替代方案（如果有）</span></div>
                    <div class="code-line"><span class="string">"""</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 模板5：复杂数据提取</span></div>
                    <div class="code-line"><span class="string">"""</span></div>
                    <div class="code-line"><span class="string">我需要从以下格式的文本中提取数据：</span></div>
                    <div class="code-line"><span class="string">[提供示例文本]</span></div>
                    <div class="code-line"><span class="string">需要提取的字段：[列出所有字段]</span></div>
                    <div class="code-line"><span class="string">请提供：</span></div>
                    <div class="code-line"><span class="string">1. 完整的正则表达式（使用命名分组）</span></div>
                    <div class="code-line"><span class="string">2. Python代码实现</span></div>
                    <div class="code-line"><span class="string">3. 错误处理机制</span></div>
                    <div class="code-line"><span class="string">"""</span></div>
                    <div class="code-line"></div>
                    <div class="code-line"><span class="comment"># 模板6：学习路径规划</span></div>
                    <div class="code-line"><span class="string">"""</span></div>
                    <div class="code-line"><span class="string">我的正则表达式水平：[初级/中级/高级]</span></div>
                    <div class="code-line"><span class="string">学习目标：[描述你想达到的目标]</span></div>
                    <div class="code-line"><span class="string">请为我制定一个学习计划，包括：</span></div>
                    <div class="code-line"><span class="string">1. 学习顺序和重点</span></div>
                    <div class="code-line"><span class="string">2. 练习项目建议</span></div>
                    <div class="code-line"><span class="string">3. 常见陷阱和注意事项</span></div>
                    <div class="code-line"><span class="string">"""</span></div>
                </div>
            </div>
        </div>