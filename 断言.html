<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python异常处理与断言完整教程</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow-x: hidden;
        }

        .presentation {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100vh;
            padding: 60px;
            display: none;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            text-align: left;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            margin: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
            scroll-behavior: smooth;
        }

        .slide.active {
            display: flex;
            animation: slideIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide h1 {
            font-size: 3.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: left;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .slide h2 {
            font-size: 2.5rem;
            font-weight: 600;
            color: #34495e;
            margin-bottom: 25px;
            text-align: left;
            animation: fadeInUp 1s ease-out 0.5s both;
        }

        .slide h3 {
            font-size: 1.8rem;
            font-weight: 500;
            color: #7f8c8d;
            margin-bottom: 20px;
            text-align: left;
            animation: fadeInUp 1s ease-out 0.7s both;
        }

        .slide p, .slide li {
            font-size: 1.3rem;
            line-height: 1.8;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: left;
            animation: fadeInUp 1s ease-out 0.9s both;
        }

        .slide ul {
            margin-left: 30px;
            margin-bottom: 25px;
        }

        .emoji {
            font-size: 2.5rem;
            margin-right: 15px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .code-container {
            background: #1e1e1e;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            width: 100%;
            min-height: 200px;
            overflow-x: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: slideInLeft 1s ease-out 1.1s both;
        }

        .code {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 1.1rem;
            line-height: 1.6;
            color: #f8f8f2;
            text-align: left;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Python语法高亮 */
        .keyword { color: #66d9ef; font-weight: bold; }
        .string { color: #e6db74; }
        .comment { color: #75715e; font-style: italic; }
        .number { color: #ae81ff; }
        .function { color: #a6e22e; }
        .builtin { color: #f92672; }
        .exception { color: #f92672; font-weight: bold; }

        .navigation {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50px;
            padding: 15px 25px;
            margin: 0 10px;
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            color: #2c3e50;
            backdrop-filter: blur(10px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .highlight-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
            }
        }

        .ai-prompt {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            font-style: italic;
            animation: slideInRight 1s ease-out 1.3s both;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
            z-index: 1001;
        }

        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-left: 4px solid #fdcb6e;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }

        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-left: 4px solid #17a2b8;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }

        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-left: 4px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }

        .error-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-left: 4px solid #dc3545;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .comparison-table th {
            background: #667eea;
            color: white;
            font-weight: 600;
        }

        .comparison-table tr:hover {
            background: #f5f5f5;
        }
    </style>
</head>
<body>
    <div class="progress-bar" id="progressBar"></div>
    <div class="slide-counter" id="slideCounter">1 / 30</div>
    
    <div class="presentation">
        <!-- 第1页：标题页 -->
        <div class="slide active">
            <h1><span class="emoji">🐍</span>Python异常处理与断言</h1>
            <h2>完整教程与AI辅助学习指南</h2>
            <div class="highlight-box">
                <h3>🎯 学习目标</h3>
                <ul>
                    <li>掌握Python异常处理机制</li>
                    <li>理解断言的使用场景</li>
                    <li>学会使用AI工具辅助编程</li>
                    <li>提升代码质量和调试能力</li>
                    <li>构建健壮的Python应用程序</li>
                </ul>
            </div>
            <div class="info-box">
                <p><strong>💡 提示：</strong>本教程采用交互式设计，支持键盘导航（←→键）和触摸滑动。</p>
            </div>
        </div>

        <!-- 第2页：什么是异常 -->
        <div class="slide">
            <h1><span class="emoji">⚠️</span>什么是异常？</h1>
            <p>异常是程序执行过程中发生的错误事件，它会中断程序的正常执行流程。</p>
            
            <h3>🔍 异常的特点：</h3>
            <ul>
                <li><strong>可预测性</strong>：某些异常可以预见并处理</li>
                <li><strong>传播性</strong>：异常会沿着调用栈向上传播</li>
                <li><strong>类型化</strong>：不同类型的错误有不同的异常类</li>
                <li><strong>信息性</strong>：异常包含错误的详细信息</li>
            </ul>

            <h3>🔍 常见异常类型：</h3>
            <div class="two-column">
                <div>
                    <ul>
                        <li><strong>SyntaxError</strong>：语法错误</li>
                        <li><strong>NameError</strong>：名称错误</li>
                        <li><strong>TypeError</strong>：类型错误</li>
                        <li><strong>ValueError</strong>：值错误</li>
                    </ul>
                </div>
                <div>
                    <ul>
                        <li><strong>IndexError</strong>：索引错误</li>
                        <li><strong>KeyError</strong>：键错误</li>
                        <li><strong>FileNotFoundError</strong>：文件未找到</li>
                        <li><strong>ZeroDivisionError</strong>：除零错误</li>
                    </ul>
                </div>
            </div>

            <div class="code-container">
                <div class="code">
<span class="comment"># 常见异常示例</span>

<span class="comment"># NameError - 使用未定义的变量</span>
<span class="keyword">print</span>(undefined_variable)  <span class="comment"># NameError: name 'undefined_variable' is not defined</span>

<span class="comment"># TypeError - 类型不匹配</span>
result = <span class="string">"hello"</span> + <span class="number">5</span>  <span class="comment"># TypeError: can only concatenate str (not "int") to str</span>

<span class="comment"># IndexError - 索引超出范围</span>
my_list = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>]
<span class="keyword">print</span>(my_list[<span class="number">10</span>])  <span class="comment"># IndexError: list index out of range</span>

<span class="comment"># ValueError - 值错误</span>
number = <span class="builtin">int</span>(<span class="string">"abc"</span>)  <span class="comment"># ValueError: invalid literal for int() with base 10: 'abc'</span>
                </div>
            </div>
        </div>

        <!-- 第3页：异常层次结构 -->
        <div class="slide">
            <h1><span class="emoji">🏗️</span>Python异常层次结构</h1>
            <p>Python中的所有异常都继承自BaseException类，形成了完整的异常层次结构。</p>

            <div class="code-container">
                <div class="code">
<span class="comment"># Python异常层次结构（简化版）</span>
BaseException
 +-- SystemExit
 +-- KeyboardInterrupt
 +-- GeneratorExit
 +-- Exception
      +-- StopIteration
      +-- StopAsyncIteration
      +-- ArithmeticError
      |    +-- FloatingPointError
      |    +-- OverflowError
      |    +-- ZeroDivisionError
      +-- AssertionError
      +-- AttributeError
      +-- BufferError
      +-- EOFError
      +-- ImportError
      |    +-- ModuleNotFoundError
      +-- LookupError
      |    +-- IndexError
      |    +-- KeyError
      +-- MemoryError
      +-- NameError
      |    +-- UnboundLocalError
      +-- OSError
      |    +-- BlockingIOError
      |    +-- ChildProcessError
      |    +-- ConnectionError
      |    +-- FileExistsError
      |    +-- FileNotFoundError
      |    +-- InterruptedError
      |    +-- IsADirectoryError
      |    +-- NotADirectoryError
      |    +-- PermissionError
      |    +-- ProcessLookupError
      |    +-- TimeoutError
      +-- ReferenceError
      +-- RuntimeError
      |    +-- NotImplementedError
      |    +-- RecursionError
      +-- SyntaxError
      |    +-- IndentationError
      |         +-- TabError
      +-- SystemError
      +-- TypeError
      +-- ValueError
      |    +-- UnicodeError
      |         +-- UnicodeDecodeError
      |         +-- UnicodeEncodeError
      |         +-- UnicodeTranslateError
      +-- Warning
           +-- DeprecationWarning
           +-- PendingDeprecationWarning
           +-- RuntimeWarning
           +-- SyntaxWarning
           +-- UserWarning
           +-- FutureWarning
           +-- ImportWarning
           +-- UnicodeWarning
           +-- BytesWarning
           +-- ResourceWarning
                </div>
            </div>

            <div class="info-box">
                <p><strong>💡 重要提示：</strong>通常我们捕获Exception及其子类，而不是BaseException，因为BaseException包含了系统级异常如KeyboardInterrupt。</p>
            </div>
        </div>

        <!-- 第4页：try-except基础语法 -->
        <div class="slide">
            <h1><span class="emoji">🛡️</span>try-except基础语法</h1>
            <p>使用try-except语句可以捕获和处理异常，防止程序崩溃。</p>

            <h3>📝 基本语法结构：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">try</span>:
    <span class="comment"># 可能出现异常的代码</span>
    risky_code()
<span class="keyword">except</span> <span class="exception">ExceptionType</span>:
    <span class="comment"># 处理异常的代码</span>
    handle_exception()
                </div>
            </div>

            <h3>🌟 实际应用示例：</h3>
            <div class="code-container">
                <div class="code">
<span class="comment"># 示例1：处理除零错误</span>
<span class="keyword">def</span> <span class="function">safe_divide</span>(a, b):
    <span class="keyword">try</span>:
        result = a / b
        <span class="keyword">print</span>(<span class="string">f"结果: {result}"</span>)
        <span class="keyword">return</span> result
    <span class="keyword">except</span> <span class="exception">ZeroDivisionError</span>:
        <span class="keyword">print</span>(<span class="string">"错误：不能除以零！"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>

<span class="comment"># 测试</span>
safe_divide(<span class="number">10</span>, <span class="number">2</span>)   <span class="comment"># 正常执行</span>
safe_divide(<span class="number">10</span>, <span class="number">0</span>)   <span class="comment"># 捕获异常</span>

<span class="comment"># 示例2：处理类型转换错误</span>
<span class="keyword">def</span> <span class="function">get_integer_input</span>(prompt):
    <span class="keyword">try</span>:
        user_input = <span class="builtin">input</span>(prompt)
        number = <span class="builtin">int</span>(user_input)
        <span class="keyword">print</span>(<span class="string">f"您输入的数字是: {number}"</span>)
        <span class="keyword">return</span> number
    <span class="keyword">except</span> <span class="exception">ValueError</span>:
        <span class="keyword">print</span>(<span class="string">"错误：请输入有效的数字！"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>

<span class="comment"># 使用示例</span>
<span class="comment"># number = get_integer_input("请输入一个数字: ")</span>
                </div>
            </div>

            <div class="success-box">
                <p><strong>✅ 最佳实践：</strong>始终捕获具体的异常类型，而不是使用裸露的except语句。</p>
            </div>
        </div>

        <!-- 第5页：捕获异常信息 -->
        <div class="slide">
            <h1><span class="emoji">📋</span>捕获异常信息</h1>
            <p>使用as关键字可以捕获异常对象，获取详细的错误信息。</p>

            <h3>🔍 获取异常详细信息：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">def</span> <span class="function">detailed_error_handling</span>():
    <span class="keyword">try</span>:
        <span class="comment"># 故意制造不同类型的异常</span>
        numbers = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>]
        index = <span class="builtin">int</span>(<span class="builtin">input</span>(<span class="string">"输入索引: "</span>))
        result = numbers[index] / <span class="number">0</span>
        
    <span class="keyword">except</span> <span class="exception">ValueError</span> <span class="keyword">as</span> e:
        <span class="keyword">print</span>(<span class="string">f"值错误: {e}"</span>)
        <span class="keyword">print</span>(<span class="string">f"异常类型: {type(e).__name__}"</span>)
        <span class="keyword">print</span>(<span class="string">f"异常参数: {e.args}"</span>)
        
    <span class="keyword">except</span> <span class="exception">IndexError</span> <span class="keyword">as</span> e:
        <span class="keyword">print</span>(<span class="string">f"索引错误: {e}"</span>)
        <span class="keyword">print</span>(<span class="string">f"列表长度: {len(numbers)}"</span>)
        <span class="keyword">print</span>(<span class="string">f"有效索引范围: 0 到 {len(numbers)-1}"</span>)
        
    <span class="keyword">except</span> <span class="exception">ZeroDivisionError</span> <span class="keyword">as</span> e:
        <span class="keyword">print</span>(<span class="string">f"除零错误: {e}"</span>)
        <span class="keyword">print</span>(<span class="string">"提示: 除数不能为零"</span>)

<span class="comment"># 异常对象的常用属性</span>
<span class="keyword">def</span> <span class="function">explore_exception_attributes</span>():
    <span class="keyword">try</span>:
        result = <span class="number">10</span> / <span class="number">0</span>
    <span class="keyword">except</span> <span class="exception">ZeroDivisionError</span> <span class="keyword">as</span> e:
        <span class="keyword">print</span>(<span class="string">f"异常消息: {str(e)}"</span>)
        <span class="keyword">print</span>(<span class="string">f"异常类型: {type(e)}"</span>)
        <span class="keyword">print</span>(<span class="string">f"异常参数: {e.args}"</span>)
        <span class="keyword">print</span>(<span class="string">f"异常模块: {e.__class__.__module__}"</span>)
        <span class="keyword">print</span>(<span class="string">f"异常文档: {e.__doc__}"</span>)

<span class="comment"># 运行示例</span>
explore_exception_attributes()
                </div>
            </div>

            <div class="info-box">
                <p><strong>💡 技巧：</strong>异常对象包含丰富的信息，合理利用这些信息可以提供更好的用户体验和调试信息。</p>
            </div>
        </div>

        <!-- 第6页：多重异常处理 -->
        <div class="slide">
            <h1><span class="emoji">🎯</span>多重异常处理</h1>
            <p>一个try块可以捕获多种不同类型的异常，提供更精确的错误处理。</p>

            <h3>🔧 多个except子句：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">def</span> <span class="function">comprehensive_file_processor</span>(filename):
    <span class="keyword">try</span>:
        <span class="keyword">with</span> <span class="builtin">open</span>(filename, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> f:
            content = f.read()
            data = json.loads(content)
            result = data[<span class="string">'value'</span>] * <span class="number">2</span>
            <span class="keyword">return</span> result
            
    <span class="keyword">except</span> <span class="exception">FileNotFoundError</span> <span class="keyword">as</span> e:
        <span class="keyword">print</span>(<span class="string">f"文件未找到: {filename}"</span>)
        <span class="keyword">print</span>(<span class="string">f"请检查文件路径是否正确"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
        
    <span class="keyword">except</span> <span class="exception">PermissionError</span> <span class="keyword">as</span> e:
        <span class="keyword">print</span>(<span class="string">f"权限错误: 无法读取文件 {filename}"</span>)
        <span class="keyword">print</span>(<span class="string">f"请检查文件权限设置"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
        
    <span class="keyword">except</span> json.<span class="exception">JSONDecodeError</span> <span class="keyword">as</span> e:
        <span class="keyword">print</span>(<span class="string">f"JSON解析错误: {e}"</span>)
        <span class="keyword">print</span>(<span class="string">f"错误位置: 行 {e.lineno}, 列 {e.colno}"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
        
    <span class="keyword">except</span> <span class="exception">KeyError</span> <span class="keyword">as</span> e:
        <span class="keyword">print</span>(<span class="string">f"缺少必需的键: {e}"</span>)
        <span class="keyword">print</span>(<span class="string">f"请确保JSON包含 'value' 字段"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
        
    <span class="keyword">except</span> <span class="exception">TypeError</span> <span class="keyword">as</span> e:
        <span class="keyword">print</span>(<span class="string">f"类型错误: {e}"</span>)
        <span class="keyword">print</span>(<span class="string">f"'value' 字段必须是数字类型"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
</div>
            </div>

            <h3>🎪 一次捕获多种异常：</h3>
            <div class="code-container">
                <div class="code">
<span class="comment"># 方法1：使用元组捕获多种异常</span>
<span class="keyword">def</span> <span class="function">handle_multiple_exceptions_v1</span>():
    <span class="keyword">try</span>:
        data = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>]
        index = <span class="builtin">int</span>(<span class="builtin">input</span>(<span class="string">"输入索引: "</span>))
        <span class="keyword">print</span>(data[index])
    ```html
    <span class="keyword">except</span> (<span class="exception">ValueError</span>, <span class="exception">IndexError</span>) <span class="keyword">as</span> e:
        <span class="keyword">print</span>(<span class="string">f"输入错误: {e}"</span>)
        <span class="keyword">print</span>(<span class="string">f"异常类型: {type(e).__name__}"</span>)

<span class="comment"># 方法2：使用Exception基类捕获所有异常</span>
<span class="keyword">def</span> <span class="function">handle_multiple_exceptions_v2</span>():
    <span class="keyword">try</span>:
        <span class="comment"># 危险操作</span>
        risky_operation()
    <span class="keyword">except</span> <span class="exception">Exception</span> <span class="keyword">as</span> e:
        <span class="keyword">print</span>(<span class="string">f"发生异常: {type(e).__name__}: {e}"</span>)
        <span class="comment"># 记录日志或进行通用处理</span>
        log_error(e)

<span class="comment"># 方法3：异常处理的优先级</span>
<span class="keyword">def</span> <span class="function">exception_priority_demo</span>():
    <span class="keyword">try</span>:
        numbers = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>]
        index = <span class="builtin">int</span>(<span class="builtin">input</span>(<span class="string">"输入索引: "</span>))
        result = numbers[index] / <span class="number">0</span>
    <span class="keyword">except</span> <span class="exception">ValueError</span>:
        <span class="keyword">print</span>(<span class="string">"处理值错误"</span>)
    <span class="keyword">except</span> <span class="exception">IndexError</span>:
        <span class="keyword">print</span>(<span class="string">"处理索引错误"</span>)
    <span class="keyword">except</span> <span class="exception">ZeroDivisionError</span>:
        <span class="keyword">print</span>(<span class="string">"处理除零错误"</span>)
    <span class="keyword">except</span> <span class="exception">Exception</span> <span class="keyword">as</span> e:
        <span class="keyword">print</span>(<span class="string">f"处理其他异常: {e}"</span>)
                </div>
            </div>

            <div class="warning-box">
                <p><strong>⚠️ 注意：</strong>except子句的顺序很重要！更具体的异常应该放在前面，更通用的异常放在后面。</p>
            </div>
        </div>

        <!-- 第7页：else和finally子句 -->
        <div class="slide">
            <h1><span class="emoji">🔄</span>else和finally子句</h1>
            <p>else和finally子句为异常处理提供了更多的控制选项。</p>

            <h3>✅ else子句 - 无异常时执行：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">def</span> <span class="function">process_user_input</span>():
    <span class="keyword">try</span>:
        number = <span class="builtin">int</span>(<span class="builtin">input</span>(<span class="string">"请输入一个数字: "</span>))
    <span class="keyword">except</span> <span class="exception">ValueError</span>:
        <span class="keyword">print</span>(<span class="string">"输入的不是有效数字"</span>)
    <span class="keyword">else</span>:
        <span class="comment"># 只有在没有异常时才执行</span>
        <span class="keyword">print</span>(<span class="string">f"您输入的数字是: {number}"</span>)
        <span class="keyword">print</span>(<span class="string">f"数字的平方是: {number ** 2}"</span>)
        <span class="keyword">print</span>(<span class="string">f"数字的立方是: {number ** 3}"</span>)
        
        <span class="comment"># 进行只有在成功时才需要的操作</span>
        save_to_database(number)
        send_notification(<span class="string">f"成功处理数字: {number}"</span>)

<span class="keyword">def</span> <span class="function">save_to_database</span>(value):
    <span class="keyword">print</span>(<span class="string">f"保存到数据库: {value}"</span>)

<span class="keyword">def</span> <span class="function">send_notification</span>(message):
    <span class="keyword">print</span>(<span class="string">f"发送通知: {message}"</span>)
                </div>
            </div>

            <h3>🔒 finally子句 - 总是执行：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">def</span> <span class="function">advanced_file_processor</span>(filename):
    file_handle = <span class="keyword">None</span>
    connection = <span class="keyword">None</span>
    
    <span class="keyword">try</span>:
        <span class="keyword">print</span>(<span class="string">f"开始处理文件: {filename}"</span>)
        file_handle = <span class="builtin">open</span>(filename, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>)
        connection = connect_to_database()
        
        content = file_handle.read()
        process_content(content, connection)
        
    <span class="keyword">except</span> <span class="exception">FileNotFoundError</span>:
        <span class="keyword">print</span>(<span class="string">f"文件 {filename} 不存在"</span>)
        <span class="keyword">return</span> <span class="keyword">False</span>
        
    <span class="keyword">except</span> <span class="exception">IOError</span> <span class="keyword">as</span> e:
        <span class="keyword">print</span>(<span class="string">f"文件读取错误: {e}"</span>)
        <span class="keyword">return</span> <span class="keyword">False</span>
        
    <span class="keyword">except</span> <span class="exception">DatabaseError</span> <span class="keyword">as</span> e:
        <span class="keyword">print</span>(<span class="string">f"数据库错误: {e}"</span>)
        <span class="keyword">return</span> <span class="keyword">False</span>
        
    <span class="keyword">else</span>:
        <span class="keyword">print</span>(<span class="string">"文件处理成功完成"</span>)
        <span class="keyword">return</span> <span class="keyword">True</span>
        
    <span class="keyword">finally</span>:
        <span class="comment"># 无论是否发生异常都会执行清理工作</span>
        <span class="keyword">print</span>(<span class="string">"执行清理工作..."</span>)
        
        <span class="keyword">if</span> file_handle:
            file_handle.close()
            <span class="keyword">print</span>(<span class="string">"文件已关闭"</span>)
            
        <span class="keyword">if</span> connection:
            connection.close()
            <span class="keyword">print</span>(<span class="string">"数据库连接已关闭"</span>)
            
        <span class="keyword">print</span>(<span class="string">"清理工作完成"</span>)

<span class="comment"># 辅助函数</span>
<span class="keyword">def</span> <span class="function">connect_to_database</span>():
    <span class="keyword">print</span>(<span class="string">"连接到数据库"</span>)
    <span class="keyword">return</span> <span class="string">"database_connection"</span>

<span class="keyword">def</span> <span class="function">process_content</span>(content, connection):
    <span class="keyword">print</span>(<span class="string">f"处理内容，长度: {len(content)}"</span>)
                </div>
            </div>

            <div class="info-box">
                <p><strong>💡 执行顺序：</strong>try → except (如果有异常) → else (如果无异常) → finally (总是执行)</p>
            </div>
        </div>

        <!-- 第8页：抛出异常 -->
        <div class="slide">
            <h1><span class="emoji">🚀</span>主动抛出异常</h1>
            <p>使用raise语句可以主动抛出异常，用于参数验证和错误控制。</p>

            <h3>⚡ raise语句的不同用法：</h3>
            <div class="code-container">
                <div class="code">
<span class="comment"># 1. 抛出新异常</span>
<span class="keyword">def</span> <span class="function">validate_age</span>(age):
    <span class="keyword">if</span> <span class="keyword">not</span> <span class="builtin">isinstance</span>(age, <span class="builtin">int</span>):
        <span class="keyword">raise</span> <span class="exception">TypeError</span>(<span class="string">f"年龄必须是整数，得到 {type(age).__name__}"</span>)
    
    <span class="keyword">if</span> age < <span class="number">0</span>:
        <span class="keyword">raise</span> <span class="exception">ValueError</span>(<span class="string">f"年龄不能为负数，得到 {age}"</span>)
    
    <span class="keyword">if</span> age > <span class="number">150</span>:
        <span class="keyword">raise</span> <span class="exception">ValueError</span>(<span class="string">f"年龄不能超过150岁，得到 {age}"</span>)
    
    <span class="keyword">return</span> <span class="keyword">True</span>

<span class="comment"># 2. 重新抛出当前异常</span>
<span class="keyword">def</span> <span class="function">process_with_logging</span>(data):
    <span class="keyword">try</span>:
        result = complex_calculation(data)
        <span class="keyword">return</span> result
    <span class="keyword">except</span> <span class="exception">Exception</span> <span class="keyword">as</span> e:
        <span class="comment"># 记录错误日志</span>
        <span class="keyword">import</span> logging
        logging.error(<span class="string">f"计算失败: {e}"</span>)
        
        <span class="comment"># 重新抛出异常，让上层处理</span>
        <span class="keyword">raise</span>

<span class="comment"># 3. 抛出异常但不提供实例</span>
<span class="keyword">def</span> <span class="function">check_permission</span>(user, action):
    <span class="keyword">if</span> <span class="keyword">not</span> user.is_authenticated:
        <span class="keyword">raise</span> <span class="exception">PermissionError</span>  <span class="comment"># 不提供消息</span>
    
    <span class="keyword">if</span> <span class="keyword">not</span> user.has_permission(action):
        <span class="keyword">raise</span> <span class="exception">PermissionError</span>(<span class="string">f"用户 {user.name} 没有权限执行 {action}"</span>)

<span class="comment"># 4. 条件性抛出异常</span>
<span class="keyword">def</span> <span class="function">divide_with_validation</span>(a, b, allow_zero_result=<span class="keyword">False</span>):
    <span class="keyword">if</span> b == <span class="number">0</span>:
        <span class="keyword">raise</span> <span class="exception">ZeroDivisionError</span>(<span class="string">"除数不能为零"</span>)
    
    result = a / b
    
    <span class="keyword">if</span> result == <span class="number">0</span> <span class="keyword">and</span> <span class="keyword">not</span> allow_zero_result:
        <span class="keyword">raise</span> <span class="exception">ValueError</span>(<span class="string">"结果为零，但不允许零结果"</span>)
    
    <span class="keyword">return</span> result
                </div>
            </div>

            <h3>🔄 异常链 - 保留原始异常信息：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">def</span> <span class="function">parse_config_file</span>(filename):
    <span class="keyword">try</span>:
        <span class="keyword">with</span> <span class="builtin">open</span>(filename, <span class="string">'r'</span>) <span class="keyword">as</span> f:
            <span class="keyword">import</span> json
            config = json.load(f)
            <span class="keyword">return</span> config
    <span class="keyword">except</span> <span class="exception">FileNotFoundError</span> <span class="keyword">as</span> e:
        <span class="keyword">raise</span> <span class="exception">ConfigError</span>(<span class="string">f"配置文件 {filename} 不存在"</span>) <span class="keyword">from</span> e
    <span class="keyword">except</span> json.<span class="exception">JSONDecodeError</span> <span class="keyword">as</span> e:
        <span class="keyword">raise</span> <span class="exception">ConfigError</span>(<span class="string">f"配置文件格式错误: {e.msg}"</span>) <span class="keyword">from</span> e

<span class="comment"># 自定义异常类</span>
<span class="keyword">class</span> <span class="function">ConfigError</span>(<span class="exception">Exception</span>):
    <span class="string">"""配置相关的异常"""</span>
    <span class="keyword">pass</span>

<span class="comment"># 使用示例</span>
<span class="keyword">try</span>:
    config = parse_config_file(<span class="string">"nonexistent.json"</span>)
<span class="keyword">except</span> <span class="function">ConfigError</span> <span class="keyword">as</span> e:
    <span class="keyword">print</span>(<span class="string">f"配置错误: {e}"</span>)
    <span class="keyword">print</span>(<span class="string">f"原始异常: {e.__cause__}"</span>)
                </div>
            </div>
        </div>

        <!-- 第9页：自定义异常类 -->
        <div class="slide">
            <h1><span class="emoji">🎨</span>自定义异常类</h1>
            <p>创建自定义异常类可以更精确地表达特定的错误情况，提高代码的可读性和可维护性。</p>

            <h3>🏗️ 基本自定义异常：</h3>
            <div class="code-container">
                <div class="code">
<span class="comment"># 简单的自定义异常</span>
<span class="keyword">class</span> <span class="function">CustomError</span>(<span class="exception">Exception</span>):
    <span class="string">"""自定义异常基类"""</span>
    <span class="keyword">pass</span>

<span class="comment"># 带有额外信息的自定义异常</span>
<span class="keyword">class</span> <span class="function">ValidationError</span>(<span class="function">CustomError</span>):
    <span class="string">"""数据验证异常"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="builtin">self</span>, message, field=<span class="keyword">None</span>, value=<span class="keyword">None</span>, error_code=<span class="keyword">None</span>):
        <span class="builtin">super</span>().__init__(message)
        <span class="builtin">self</span>.message = message
        <span class="builtin">self</span>.field = field
        <span class="builtin">self</span>.value = value
        <span class="builtin">self</span>.error_code = error_code
    
    <span class="keyword">def</span> <span class="function">__str__</span>(<span class="builtin">self</span>):
        base_msg = <span class="builtin">self</span>.message
        <span class="keyword">if</span> <span class="builtin">self</span>.field:
            base_msg += <span class="string">f" (字段: {<span class="builtin">self</span>.field})"</span>
        <span class="keyword">if</span> <span class="builtin">self</span>.value <span class="keyword">is</span> <span class="keyword">not</span> <span class="keyword">None</span>:
            base_msg += <span class="string">f" (值: {<span class="builtin">self</span>.value})"</span>
        <span class="keyword">if</span> <span class="builtin">self</span>.error_code:
            base_msg += <span class="string">f" [错误代码: {<span class="builtin">self</span>.error_code}]"</span>
        <span class="keyword">return</span> base_msg

<span class="comment"># 业务逻辑异常</span>
<span class="keyword">class</span> <span class="function">BusinessLogicError</span>(<span class="function">CustomError</span>):
    <span class="string">"""业务逻辑异常"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="builtin">self</span>, message, operation=<span class="keyword">None</span>, details=<span class="keyword">None</span>):
        <span class="builtin">super</span>().__init__(message)
        <span class="builtin">self</span>.operation = operation
        <span class="builtin">self</span>.details = details <span class="keyword">or</span> {}
    
    <span class="keyword">def</span> <span class="function">add_detail</span>(<span class="builtin">self</span>, key, value):
        <span class="string">"""添加详细信息"""</span>
        <span class="builtin">self</span>.details[key] = value
    
    <span class="keyword">def</span> <span class="function">get_summary</span>(<span class="builtin">self</span>):
        <span class="string">"""获取异常摘要"""</span>
        summary = {
            <span class="string">'message'</span>: <span class="builtin">str</span>(<span class="builtin">self</span>),
            <span class="string">'operation'</span>: <span class="builtin">self</span>.operation,
            <span class="string">'details'</span>: <span class="builtin">self</span>.details
        }
        <span class="keyword">return</span> summary
                </div>
            </div>

            <h3>🎯 实际应用示例：</h3>
            <div class="code-container">
                <div class="code">
<span class="comment"># 用户管理系统的自定义异常</span>
<span class="keyword">class</span> <span class="function">UserError</span>(<span class="exception">Exception</span>):
    <span class="string">"""用户相关异常基类"""</span>
    <span class="keyword">pass</span>

<span class="keyword">class</span> <span class="function">UserNotFoundError</span>(<span class="function">UserError</span>):
    <span class="string">"""用户不存在异常"""</span>
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="builtin">self</span>, user_id):
        <span class="builtin">self</span>.user_id = user_id
        <span class="builtin">super</span>().__init__(<span class="string">f"用户 {user_id} 不存在"</span>)

<span class="keyword">class</span> <span class="function">UserAlreadyExistsError</span>(<span class="function">UserError</span>):
    <span class="string">"""用户已存在异常"""</span>
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="builtin">self</span>, username, email=<span class="keyword">None</span>):
        <span class="builtin">self</span>.username = username
        <span class="builtin">self</span>.email = email
        message = <span class="string">f"用户名 '{username}' 已存在"</span>
        <span class="keyword">if</span> email:
            message += <span class="string">f" (邮箱: {email})"</span>
        <span class="builtin">super</span>().__init__(message)

<span class="keyword">class</span> <span class="function">InvalidCredentialsError</span>(<span class="function">UserError</span>):
    <span class="string">"""无效凭证异常"""</span>
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="builtin">self</span>, username, attempts=<span class="number">1</span>):
        <span class="builtin">self</span>.username = username
        <span class="builtin">self</span>.attempts = attempts
        <span class="builtin">super</span>().__init__(<span class="string">f"用户 '{username}' 凭证无效 (尝试次数: {attempts})"</span>)

<span class="comment"># 使用自定义异常的用户管理类</span>
<span class="keyword">class</span> <span class="function">UserManager</span>:
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="builtin">self</span>):
        <span class="builtin">self</span>.users = {}
    
    <span class="keyword">def</span> <span class="function">create_user</span>(<span class="builtin">self</span>, username, email, password):
        <span class="keyword">if</span> username <span class="keyword">in</span> <span class="builtin">self</span>.users:
            <span class="keyword">raise</span> <span class="function">UserAlreadyExistsError</span>(username, email)
        
        <span class="comment"># 验证邮箱格式</span>
        <span class="keyword">if</span> <span class="string">'@'</span> <span class="keyword">not</span> <span class="keyword">in</span> email:
            <span class="keyword">raise</span> <span class="function">ValidationError</span>(
                <span class="string">"邮箱格式不正确"</span>, 
                field=<span class="string">"email"</span>, 
                value=email, 
                error_code=<span class="string">"INVALID_EMAIL"</span>
            )
        
        <span class="builtin">self</span>.users[username] = {
            <span class="string">'email'</span>: email,
            <span class="string">'password'</span>: password
        }
        <span class="keyword">return</span> <span class="keyword">True</span>
    
    <span class="keyword">def</span> <span class="function">authenticate</span>(<span class="builtin">self</span>, username, password):
        <span class="keyword">if</span> username <span class="keyword">not</span> <span class="keyword">in</span> <span class="builtin">self</span>.users:
            <span class="keyword">raise</span> <span class="function">UserNotFoundError</span>(username)
        
        <span class="keyword">if</span> <span class="builtin">self</span>.users[username][<span class="string">'password'</span>] != password:
            <span class="keyword">raise</span> <span class="function">InvalidCredentialsError</span>(username)
        
        <span class="keyword">return</span> <span class="keyword">True</span>
                </div>
            </div>
        </div>

        <!-- 第10页：异常链和上下文 -->
        <div class="slide">
            <h1><span class="emoji">🔗</span>异常链和上下文</h1>
            <p>Python 3提供了异常链机制，可以保留原始异常信息，提供更完整的错误追踪。</p>

            <h3>⛓️ 使用from关键字创建异常链：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">def</span> <span class="function">load_user_config</span>(user_id):
    <span class="keyword">try</span>:
        config_file = <span class="string">f"config/user_{user_id}.json"</span>
        <span class="keyword">with</span> <span class="builtin">open</span>(config_file, <span class="string">'r'</span>) <span class="keyword">as</span> f:
            <span class="keyword">import</span> json
            config = json.load(f)
            <span class="keyword">return</span> validate_config(config)
            
    <span class="keyword">except</span> <span class="exception">FileNotFoundError</span> <span class="keyword">as</span> e:
        <span class="comment"># 将底层异常转换为业务异常</span>
        <span class="keyword">raise</span> <span class="function">UserConfigError</span>(<span class="string">f"用户 {user_id} 的配置文件不存在"</span>) <span class="keyword">from</span> e
        
    <span class="keyword">except</span> json.<span class="exception">JSONDecodeError</span> <span class="keyword">as</span> e:
        <span class="keyword">raise</span> <span class="function">UserConfigError</span>(
            <span class="string">f"用户 {user_id} 的配置文件格式错误: {e.msg}"</span>
        ) <span class="keyword">from</span> e
        
    <span class="keyword">except</span> <span class="function">ValidationError</span> <span class="keyword">as</span> e:
        <span class="keyword">raise</span> <span class="function">UserConfigError</span>(
            <span class="string">f"用户 {user_id} 的配置验证失败"</span>
        ) <span class="keyword">from</span> e

<span class="keyword">def</span> <span class="function">validate_config</span>(config):
    <span class="keyword">if</span> <span class="string">'theme'</span> <span class="keyword">not</span> <span class="keyword">in</span> config:
        <span class="keyword">raise</span> <span class="function">ValidationError</span>(<span class="string">"缺少必需的 'theme' 配置"</span>)
    <span class="keyword">return</span> config

<span class="keyword">class</span> <span class="function">UserConfigError</span>(<span class="exception">Exception</span>):
    <span class="string">"""用户配置异常"""</span>
    <span class="keyword">pass</span>

<span class="comment"># 演示异常链</span>
<span class="keyword">def</span> <span class="function">demo_exception_chain</span>():
    <span class="keyword">try</span>:
        config = load_user_config(<span class="number">123</span>)
    <span class="keyword">except</span> <span class="function">UserConfigError</span> <span class="keyword">as</span> e:
        <span class="keyword">print</span>(<span class="string">f"业务异常: {e}"</span>)
        <span class="keyword">print</span>(<span class="string">f"原始异常: {e.__cause__}"</span>)
        <span class="keyword">print</span>(<span class="string">f"异常类型: {type(e.__cause__).__name__}"</span>)
        
        <span class="comment"># 打印完整的异常链</span>
        <span class="keyword">import</span> traceback
        traceback.print_exc()
                </div>
            </div>

            <h3>🔍 获取详细的异常上下文信息：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">import</span> sys
<span class="keyword">import</span> traceback
<span class="keyword">from</span> datetime <span class="keyword">import</span> datetime

<span class="keyword">def</span> <span class="function">detailed_exception_handler</span>(func):
    <span class="string">"""装饰器：提供详细的异常信息"""</span>
    <span class="keyword">def</span> <span class="function">wrapper</span>(*args, **kwargs):
        <span class="keyword">try</span>:
            <span class="keyword">return</span> func(*args, **kwargs)
        <span class="keyword">except</span> <span class="exception">Exception</span> <span class="keyword">as</span> e:
            <span class="comment"># 获取异常信息</span>
            exc_type, exc_value, exc_traceback = sys.exc_info()
            
            <span class="keyword">print</span>(<span class="string">f"=== 异常详细信息 ==="</span>)
            <span class="keyword">print</span>(<span class="string">f"时间: {datetime.now()}"</span>)
            <span class="keyword">print</span>(<span class="string">f"函数: {func.__name__}"</span>)
            <span class="keyword">print</span>(<span class="string">f"异常类型: {exc_type.__name__}"</span>)
            <span class="keyword">print</span>(<span class="string">f"异常消息: {exc_value}"</span>)
            <span class="keyword">print</span>(<span class="string">f"异常模块: {exc_type.__module__}"</span>)
            
            <span class="comment"># 获取异常发生的位置</span>
            <span class="keyword">if</span> exc_traceback:
                frame = exc_traceback.tb_frame
                <span class="keyword">print</span>(<span class="string">f"文件: {frame.f_code.co_filename}"</span>)
                <span class="keyword">print</span>(<span class="string">f"函数: {frame.f_code.co_name}"</span>)
                <span class="keyword">print</span>(<span class="string">f"行号: {exc_traceback.tb_lineno}"</span>)
                
                <span class="comment"># 获取局部变量</span>
                local_vars = frame.f_locals
                <span class="keyword">print</span>(<span class="string">f"局部变量: {local_vars}"</span>)
            
            <span class="comment"># 打印格式化的堆栈跟踪</span>
            <span class="keyword">print</span>(<span class="string">f"\\n=== 堆栈跟踪 ==="</span>)
            traceback.print_exc()
            
            <span class="comment"># 重新抛出异常</span>
            <span class="keyword">raise</span>
    
    <span class="keyword">return</span> wrapper

<span class="comment"># 使用装饰器的示例</span>
<span class="builtin">@detailed_exception_handler</span>
<span class="keyword">def</span> <span class="function">problematic_function</span>(x, y):
    <span class="keyword">if</span> x < <span class="number">0</span>:
        <span class="keyword">raise</span> <span class="exception">ValueError</span>(<span class="string">"x不能为负数"</span>)
    result = x / y
    <span class="keyword">return</span> result

<span class="comment"># 测试异常链和上下文</span>
<span class="keyword">def</span> <span class="function">test_exception_context</span>():
    <span class="keyword">try</span>:
        problematic_function(-<span class="number">5</span>, <span class="number">0</span>)
    <span class="keyword">except</span> <span class="exception">Exception</span>:
        <span class="keyword">print</span>(<span class="string">"异常已被处理"</span>)
                </div>
            </div>

            <div class="info-box">
                <p><strong>💡 异常链的优势：</strong>通过from关键字，可以保留原始异常信息，同时提供更有意义的业务层异常，便于调试和错误分析。</p>
            </div>
        </div>

        <!-- 第11页：断言基础 -->
        <div class="slide">
            <h1><span class="emoji">✅</span>断言(Assert)基础</h1>
            <p>断言是一种调试工具，用于检查程序中的假设条件是否为真，主要用于开发和测试阶段。</p>

            <h3>🔧 assert语句语法：</h3>
            <div class="code-container">
                <div class="code">
<span class="comment"># 基本语法</span>
<span class="keyword">assert</span> condition, <span class="string">"错误消息"</span>

<span class="comment"># 等价于</span>
<span class="keyword">if</span> <span class="keyword">not</span> condition:
    <span class="keyword">raise</span> <span class="exception">AssertionError</span>(<span class="string">"错误消息"</span>)

<span class="comment"># 不同形式的断言</span>
<span class="keyword">assert</span> <span class="keyword">True</span>                    <span class="comment"># 简单断言</span>
<span class="keyword">assert</span> x > <span class="number">0</span>                   <span class="comment"># 条件断言</span>
<span class="keyword">assert</span> x > <span class="number">0</span>, <span class="string">"x必须为正数"</span>  <span class="comment"># 带消息的断言</span>
                </div>
            </div>

            <h3>🎯 断言的基本使用：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">def</span> <span class="function">calculate_average</span>(numbers):
    <span class="string">"""计算数字列表的平均值"""</span>
    
    <span class="comment"># 前置条件断言</span>
    <span class="keyword">assert</span> numbers, <span class="string">"数字列表不能为空"</span>
    <span class="keyword">assert</span> <span class="builtin">isinstance</span>(numbers, <span class="builtin">list</span>), <span class="string">"输入必须是列表"</span>
    
    <span class="comment"># 验证列表中的每个元素</span>
    <span class="keyword">for</span> i, num <span class="keyword">in</span> <span class="builtin">enumerate</span>(numbers):
        <span class="keyword">assert</span> <span class="builtin">isinstance</span>(num, (<span class="builtin">int</span>, <span class="builtin">float</span>)), \
               <span class="string">f"索引 {i} 处的元素 '{num}' 不是数字"</span>
        <span class="keyword">assert</span> <span class="keyword">not</span> math.isnan(num), <span class="string">f"索引 {i} 处的元素不能是 NaN"</span>
        <span class="keyword">assert</span> <span class="keyword">not</span> math.isinf(num), <span class="string">f"索引 {i} 处的元素不能是无穷大"</span>
    
    result = <span class="builtin">sum</span>(numbers) / <span class="builtin">len</span>(numbers)
    
    <span class="comment"># 后置条件断言</span>
    <span class="keyword">assert</span> <span class="builtin">isinstance</span>(result, (<span class="builtin">int</span>, <span class="builtin">float</span>)), <span class="string">"结果必须是数字"</span>
    <span class="keyword">assert</span> <span class="keyword">not</span> math.isnan(result), <span class="string">"计算结果不能是 NaN"</span>
    
    <span class="keyword">return</span> result

<span class="keyword">import</span> math

<span class="comment"># 测试函数</span>
<span class="keyword">def</span> <span class="function">test_calculate_average</span>():
    <span class="comment"># 正常情况</span>
    <span class="keyword">print</span>(calculate_average([<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>]))  <span class="comment"># 输出: 3.0</span>
    
    <span class="comment"># 测试各种断言错误</span>
    test_cases = [
        ([], <span class="string">"空列表"</span>),
        (<span class="string">"not a list"</span>, <span class="string">"非列表输入"</span>),
        ([<span class="number">1</span>, <span class="number">2</span>, <span class="string">"3"</span>], <span class="string">"包含非数字元素"</span>),
        ([<span class="number">1</span>, <span class="number">2</span>, <span class="builtin">float</span>(<span class="string">'nan'</span>)], <span class="string">"包含NaN"</span>),
        ([<span class="number">1</span>, <span class="number">2</span>, <span class="builtin">float</span>(<span class="string">'inf'</span>)], <span class="string">"包含无穷大"</span>)
    ]
    
    <span class="keyword">for</span> test_input, description <span class="keyword">in</span> test_cases:
        <span class="keyword">try</span>:
            result = calculate_average(test_input)
            <span class="keyword">print</span>(<span class="string">f"{description}: 通过 (结果: {result})"</span>)
        <span class="keyword">except</span> <span class="exception">AssertionError</span> <span class="keyword">as</span> e:
            <span class="keyword">print</span>(<span class="string">f"{description}: 断言失败 - {e}"</span>)

<span class="comment"># 运行测试</span>
test_calculate_average()
                </div>
            </div>

            <div class="warning-box">
                <p><strong>⚠️ 重要提醒：</strong>断言可以通过 <code>python -O</code> 命令禁用，因此不应该用于生产环境的关键验证。</p>
            </div>
        </div>

        <!-- 第12页：断言的使用场景 -->
        <div class="slide">
            <h1><span class="emoji">🎪</span>断言的使用场景</h1>
            <p>断言主要用于开发和调试阶段，帮助发现程序逻辑错误和验证程序状态。</p>

            <h3>🔍 前置条件检查：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">def</span> <span class="function">binary_search</span>(arr, target):
    <span class="string">"""二分查找算法 - 使用断言验证前置条件"""</span>
    
    <span class="comment"># 前置条件：数组必须已排序</span>
    <span class="keyword">assert</span> <span class="builtin">isinstance</span>(arr, <span class="builtin">list</span>), <span class="string">"输入必须是列表"</span>
    <span class="keyword">assert</span> <span class="builtin">len</span>(arr) > <span class="number">0</span>, <span class="string">"数组不能为空"</span>
    <span class="keyword">assert</span> arr == <span class="builtin">sorted</span>(arr), <span class="string">f"数组必须是已排序的，当前: {arr}"</span>
    
    left, right = <span class="number">0</span>, <span class="builtin">len</span>(arr) - <span class="number">1</span>
    
    <span class="keyword">while</span> left <= right:
        <span class="comment"># 循环不变式断言</span>
        <span class="keyword">assert</span> <span class="number">0</span> <= left <= <span class="builtin">len</span>(arr), <span class="string">"left索引越界"</span>
        <span class="keyword">assert</span> -<span class="number">1</span> <= right < <span class="builtin">len</span>(arr), <span class="string">"right索引越界"</span>
        
        mid = (left + right) // <span class="number">2</span>
        <span class="keyword">assert</span> left <= mid <= right, <span class="string">"mid索引计算错误"</span>
        
        <span class="keyword">if</span> arr[mid] == target:
            <span class="keyword">return</span> mid
        <span class="keyword">elif</span> arr[mid] < target:
            left = mid + <span class="number">1</span>
        <span class="keyword">else</span>:
            right = mid - <span class="number">1</span>
    
    <span class="keyword">return</span> -<span class="number">1</span>

<span class="comment"># 测试二分查找</span>
sorted_arr = [<span class="number">1</span>, <span class="number">3</span>, <span class="number">5</span>, <span class="number">7</span>, <span class="number">9</span>, <span class="number">11</span>, <span class="number">13</span>]
<span class="keyword">print</span>(<span class="string">f"查找5的位置: {binary_search(sorted_arr, 5)}"</span>)

<span class="comment"># 这会触发断言错误</span>
<span class="keyword">try</span>:
    unsorted_arr = [<span class="number">3</span>, <span class="number">1</span>, <span class="number">5</span>, <span class="number">7</span>, <span class="number">9</span>]
    binary_search(unsorted_arr, <span class="number">5</span>)
<span class="keyword">except</span> <span class="exception">AssertionError</span> <span class="keyword">as</span> e:
    <span class="keyword">print</span>(<span class="string">f"断言错误: {e}"</span>)
                </div>
            </div>

            <h3>🎯 后置条件和不变式检查：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">def</span> <span class="function">factorial</span>(n):
    <span class="string">"""计算阶乘 - 使用断言验证前后置条件"""</span>
    
    <span class="comment"># 前置条件</span>
    <span class="keyword">assert</span> <span class="builtin">isinstance</span>(n, <span class="builtin">int</span>), <span class="string">f"输入必须是整数，得到 {type(n).__name__}"</span>
    <span class="keyword">assert</span> n >= <span class="number">0</span>, <span class="string">f"输入必须是非负整数，得到 {n}"</span>
    
    <span class="keyword">if</span> n == <span class="number">0</span> <span class="keyword">or</span> n == <span class="number">1</span>:
        result = <span class="number">1</span>
    <span class="keyword">else</span>:
        result = n * factorial(n - <span class="number">1</span>)
    
    <span class="comment"># 后置条件</span>
    <span class="keyword">assert</span> <span class="builtin">isinstance</span>(result, <span class="builtin">int</span>), <span class="string">"结果必须是整数"</span>
    <span class="keyword">assert</span> result > <span class="number">0</span>, <span class="string">"阶乘结果必须是正数"</span>
    <span class="keyword">assert</span> result >= n, <span class="string">f"阶乘结果 {result} 不能小于输入 {n}"</span>
    
    <span class="keyword">return</span> result

<span class="keyword">class</span> <span class="function">BankAccount</span>:
    <span class="string">"""银行账户类 - 使用断言维护对象不变式"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="builtin">self</span>, initial_balance=<span class="number">0</span>):
        <span class="keyword">assert</span> <span class="builtin">isinstance</span>(initial_balance, (<span class="builtin">int</span>, <span class="builtin">float</span>)), \
               <span class="string">"初始余额必须是数字"</span>
        <span class="keyword">assert</span> initial_balance >= <span class="number">0</span>, <span class="string">"初始余额不能为负"</span>
        
        <span class="builtin">self</span>._balance = initial_balance
        <span class="builtin">self</span>._check_invariants()
    
    <span class="keyword">def</span> <span class="function">_check_invariants</span>(<span class="builtin">self</span>):
        <span class="string">"""检查对象不变式"""</span>
        <span class="keyword">assert</span> <span class="builtin">hasattr</span>(<span class="builtin">self</span>, <span class="string">'_balance'</span>), <span class="string">"账户必须有余额属性"</span>
        <span class="keyword">assert</span> <span class="builtin">isinstance</span>(<span class="builtin">self</span>._balance, (<span class="builtin">int</span>, <span class="builtin">float</span>)), \
               <span class="string">"余额必须是数字"</span>
        <span class="keyword">assert</span> <span class="builtin">self</span>._balance >= <span class="number">0</span>, <span class="string">f"余额不能为负: {<span class="builtin">self</span>._balance}"</span>
    
    <span class="keyword">def</span> <span class="function">deposit</span>(<span class="builtin">self</span>, amount):
        <span class="string">"""存款"""</span>
        <span class="keyword">assert</span> <span class="builtin">isinstance</span>(amount, (<span class="builtin">int</span>, <span class="builtin">float</span>)), <span class="string">"存款金额必须是数字"</span>
        <span class="keyword">assert</span> amount > <span class="number">0</span>, <span class="string">f"存款金额必须为正: {amount}"</span>
        
        old_balance = <span class="builtin">self</span>._balance
        <span class="builtin">self</span>._balance += amount
        
        <span class="comment"># 后置条件</span>
        <span class="keyword">assert</span> <span class="builtin">self</span>._balance == old_balance + amount, \
               <span class="string">"余额更新错误"</span>
        <span class="builtin">self</span>._check_invariants()
    
    <span class="keyword">def</span> <span class="function">withdraw</span>(<span class="builtin">self</span>, amount):
        <span class="string">"""取款"""</span>
        <span class="keyword">assert</span> <span class="builtin">isinstance</span>(amount, (<span class="builtin">int</span>, <span class="builtin">float</span>)), <span class="string">"取款金额必须是数字"</span>
        <span class="keyword">assert</span> amount > <span class="number">0</span>, <span class="string">f"取款金额必须为正: {amount}"</span>
        <span class="keyword">assert</span> <span class="builtin">self</span>._balance >= amount, \
               <span class="string">f"余额不足: 当前 {<span class="builtin">self</span>._balance}, 需要 {amount}"</span>
        
        old_balance = <span class="builtin">self</span>._balance
        <span class="builtin">self</span>._balance -= amount
        
        <span class="comment"># 后置条件</span>
        <span class="keyword">assert</span> <span class="builtin">self</span>._balance == old_balance - amount, \
               <span class="string">"余额更新错误"</span>
        <span class="builtin">self</span>._check_invariants()
    
    <span class="keyword">def</span> <span class="function">get_balance</span>(<span class="builtin">self</span>):
        <span class="builtin">self</span>._check_invariants()
        <span class="keyword">return</span> <span class="builtin">self</span>._balance

<span class="comment"># 测试银行账户</span>
account = <span class="function">BankAccount</span>(<span class="number">1000</span>)
account.deposit(<span class="number">500</span>)
<span class="keyword">print</span>(<span class="string">f"余额: {account.get_balance()}"</span>)
account.withdraw(<span class="number">200</span>)
<span class="keyword">print</span>(<span class="string">f"余额: {account.get_balance()}"</span>)
                </div>
            </div>
        </div>

        <!-- 第13页：断言vs异常处理 -->
        <div class="slide">
            <h1><span class="emoji">⚖️</span>断言 vs 异常处理</h1>
            <p>理解断言和异常处理的区别，选择合适的错误处理方式。</p>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>对比项</th>
                        <th>断言 (Assert)</th>
                        <th>异常处理 (Exception)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>使用目的</strong></td>
                        <td>调试和开发阶段的检查</td>
                        <td>处理运行时可能出现的错误</td>
                    </tr>
                    <tr>
                        <td><strong>可禁用性</strong></td>
                        <td>可通过 -O 参数禁用</td>
                        <td>不能禁用</td>
                    </tr>
                    <tr>
                        <td><strong>错误类型</strong></td>
                        <td>程序逻辑错误</td>
                        <td>外部因素导致的错误</td>
                    </tr>
                    <tr>
                        <td><strong>恢复性</strong></td>
                        <td>不应被捕获和恢复</td>
                        <td>可以被捕获和恢复</td>
                    </tr>
                    <tr>
                        <td><strong>使用场景</strong></td>
                        <td>验证程序状态和假设</td>
                        <td>处理用户输入、网络、文件等</td>
                    </tr>
                </tbody>
            </table>

            <div class="two-column">
                <div class="success-box">
                    <h3>✅ 何时使用断言：</h3>
                    <ul>
                        <li>检查程序内部逻辑错误</li>
                        <li>验证函数的前置和后置条件</li>
                        <li>调试和开发阶段的检查</li>
                        <li>检查"不应该发生"的情况</li>
                        <li>验证数据结构的不变式</li>
                        <li>单元测试中的状态验证</li>
                    </ul>
                </div>

                <div class="error-box">
                    <h3>🛡️ 何时使用异常处理：</h3>
                    <ul>
                        <li>处理外部输入错误</li>
                        <li>处理系统资源问题</li>
                        <li>处理网络和I/O错误</li>
                        <li>处理用户可能犯的错误</li>
                        <li>处理第三方库的异常</li>
                        <li>生产环境的错误处理</li>
                    </ul>
                </div>
            </div>

            <h3>📝 对比示例：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">def</span> <span class="function">user_input_processor</span>(user_age_str):
    <span class="string">"""处理用户输入 - 使用异常处理"""</span>
    <span class="keyword">try</span>:
        age = <span class="builtin">int</span>(user_age_str)
        <span class="keyword">if</span> age < <span class="number">0</span> <span class="keyword">or</span> age > <span class="number">150</span>:
            <span class="keyword">raise</span> <span class="exception">ValueError</span>(<span class="string">f"年龄必须在0-150之间，得到: {age}"</span>)
        <span class="keyword">return</span> age
    <span class="keyword">except</span> <span class="exception">ValueError</span> <span class="keyword">as</span> e:
        <span class="keyword">print</span>(<span class="string">f"输入错误: {e}"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>

<span class="keyword">def</span> <span class="function">calculate_discount</span>(price, discount_rate):
    <span class="string">"""计算折扣 - 使用断言检查程序逻辑"""</span>
    
    <span class="comment"># 使用断言检查程序内部逻辑</span>
    <span class="keyword">assert</span> <span class="builtin">isinstance</span>(price, (<span class="builtin">int</span>, <span class="builtin">float</span>)), \
           <span class="string">f"价格必须是数字，得到 {type(price).__name__}"</span>
    <span class="keyword">assert</span> price >= <span class="number">0</span>, <span class="string">f"价格不能为负，得到 {price}"</span>
    <span class="keyword">assert</span> <span class="builtin">isinstance</span>(discount_rate, (<span class="builtin">int</span>, <span class="builtin">float</span>)), \
           <span class="string">f"折扣率必须是数字，得到 {type(discount_rate).__name__}"</span>
    <span class="keyword">assert</span> <span class="number">0</span> <= discount_rate <= <span class="number">1</span>, \
           <span class="string">f"折扣率必须在0-1之间，得到 {discount_rate}"</span>
    
    discounted_price = price * (<span class="number">1</span> - discount_rate)
    
    <span class="comment"># 后置条件检查</span>
    <span class="keyword">assert</span> discounted_price <= price, \
           <span class="string">f"折扣后价格 {discounted_price} 不能高于原价 {price}"</span>
    <span class="keyword">assert</span> discounted_price >= <span class="number">0</span>, \
           <span class="string">f"折扣后价格不能为负，得到 {discounted_price}"</span>
    
    <span class="keyword">return</span> discounted_price

<span class="keyword">def</span> <span class="function">comprehensive_example</span>():
    <span class="string">"""综合示例：结合使用断言和异常处理"""</span>
    
    <span class="comment"># 处理用户输入 - 使用异常处理</span>
    user_input = <span class="string">"25"</span>  <span class="comment"># 模拟用户输入</span>
    age = user_input_processor(user_input)
    
    <span class="keyword">if</span> age <span class="keyword">is</span> <span class="keyword">not</span> <span class="keyword">None</span>:
        <span class="keyword">print</span>(<span class="string">f"用户年龄: {age}"</span>)
        
继续输出HTML代码：
        <span class="comment"># 内部计算 - 使用断言</span>
        <span class="keyword">try</span>:
            <span class="comment"># 根据年龄计算折扣率（程序内部逻辑）</span>
            <span class="keyword">if</span> age < <span class="number">18</span>:
                discount = <span class="number">0.1</span>  <span class="comment"># 未成年人10%折扣</span>
            <span class="keyword">elif</span> age >= <span class="number">65</span>:
                discount = <span class="number">0.15</span>  <span class="comment"># 老年人15%折扣</span>
            <span class="keyword">else</span>:
                discount = <span class="number">0.05</span>  <span class="comment"># 成年人5%折扣</span>
            
            <span class="comment"># 使用断言确保折扣率计算正确</span>
            final_price = calculate_discount(<span class="number">100.0</span>, discount)
            <span class="keyword">print</span>(<span class="string">f"原价: 100.0, 折扣率: {discount}, 最终价格: {final_price}"</span>)
            
        <span class="keyword">except</span> <span class="exception">AssertionError</span> <span class="keyword">as</span> e:
            <span class="keyword">print</span>(<span class="string">f"程序逻辑错误: {e}"</span>)
            <span class="comment"># 断言错误通常表示程序bug，需要修复而不是恢复</span>

<span class="comment"># 运行综合示例</span>
comprehensive_example()
                </div>
            </div>

            <div class="warning-box">
                <p><strong>⚠️ 关键原则：</strong>断言用于检查"不应该发生"的情况，异常处理用于处理"可能发生"的情况。</p>
            </div>
        </div>

        <!-- 第14页：最佳实践 -->
        <div class="slide">
            <h1><span class="emoji">🏆</span>异常处理最佳实践</h1>
            <p>遵循最佳实践，编写健壮、可维护的异常处理代码。</p>

            <h3>✨ 核心原则：</h3>
            <div class="best-practices">
                <div class="practice-item">
                    <h4>🎯 1. 具体化异常处理</h4>
                    <div class="code-container">
                        <div class="code">
<span class="comment"># ❌ 不好的做法：捕获所有异常</span>
<span class="keyword">try</span>:
    risky_operation()
<span class="keyword">except</span>:
    <span class="keyword">print</span>(<span class="string">"出错了"</span>)

<span class="comment"># ✅ 好的做法：具体化异常处理</span>
<span class="keyword">try</span>:
    result = process_user_data(user_input)
<span class="keyword">except</span> <span class="exception">ValueError</span> <span class="keyword">as</span> e:
    <span class="keyword">print</span>(<span class="string">f"数据格式错误: {e}"</span>)
    <span class="keyword">return</span> default_result()
<span class="keyword">except</span> <span class="exception">FileNotFoundError</span> <span class="keyword">as</span> e:
    <span class="keyword">print</span>(<span class="string">f"文件不存在: {e}"</span>)
    <span class="keyword">return</span> create_default_file()
<span class="keyword">except</span> <span class="exception">PermissionError</span> <span class="keyword">as</span> e:
    <span class="keyword">print</span>(<span class="string">f"权限不足: {e}"</span>)
    <span class="keyword">return</span> request_permission()
                        </div>
                    </div>
                </div>

                <div class="practice-item">
                    <h4>🔄 2. 合理使用资源管理</h4>
                    <div class="code-container">
                        <div class="code">
<span class="comment"># ❌ 不好的做法：手动管理资源</span>
<span class="keyword">def</span> <span class="function">bad_file_processing</span>(filename):
    f = <span class="builtin">open</span>(filename, <span class="string">'r'</span>)
    <span class="keyword">try</span>:
        content = f.read()
        process_content(content)
    <span class="keyword">except</span> <span class="exception">Exception</span> <span class="keyword">as</span> e:
        <span class="keyword">print</span>(<span class="string">f"处理失败: {e}"</span>)
    <span class="keyword">finally</span>:
        f.close()  <span class="comment"># 容易遗忘或出错</span>

<span class="comment"># ✅ 好的做法：使用上下文管理器</span>
<span class="keyword">def</span> <span class="function">good_file_processing</span>(filename):
    <span class="keyword">try</span>:
        <span class="keyword">with</span> <span class="builtin">open</span>(filename, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> f:
            content = f.read()
            <span class="keyword">return</span> process_content(content)
    <span class="keyword">except</span> <span class="exception">FileNotFoundError</span>:
        <span class="keyword">print</span>(<span class="string">f"文件 {filename} 不存在"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
    <span class="keyword">except</span> <span class="exception">UnicodeDecodeError</span>:
        <span class="keyword">print</span>(<span class="string">f"文件 {filename} 编码错误"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
                        </div>
                    </div>
                </div>

                <div class="practice-item">
                    <h4>📝 3. 提供有意义的错误信息</h4>
                    <div class="code-container">
                        <div class="code">
<span class="keyword">class</span> <span class="function">DataProcessor</span>:
    <span class="keyword">def</span> <span class="function">validate_and_process</span>(<span class="builtin">self</span>, data):
        <span class="keyword">try</span>:
            <span class="comment"># 详细的验证和处理</span>
            validated_data = <span class="builtin">self</span>._validate_data(data)
            processed_data = <span class="builtin">self</span>._process_data(validated_data)
            <span class="keyword">return</span> processed_data
            
        <span class="keyword">except</span> <span class="exception">ValueError</span> <span class="keyword">as</span> e:
            error_msg = <span class="string">f"数据验证失败: {e}"</span>
            <span class="builtin">self</span>._log_error(error_msg, data)
            <span class="keyword">raise</span> <span class="function">DataValidationError</span>(error_msg) <span class="keyword">from</span> e
            
        <span class="keyword">except</span> <span class="exception">KeyError</span> <span class="keyword">as</span> e:
            error_msg = <span class="string">f"缺少必需字段 {e}，数据结构: {list(data.keys()) if isinstance(data, dict) else type(data).__name__}"</span>
            <span class="builtin">self</span>._log_error(error_msg, data)
            <span class="keyword">raise</span> <span class="function">DataStructureError</span>(error_msg) <span class="keyword">from</span> e
    
    <span class="keyword">def</span> <span class="function">_validate_data</span>(<span class="builtin">self</span>, data):
        <span class="keyword">if</span> <span class="keyword">not</span> <span class="builtin">isinstance</span>(data, <span class="builtin">dict</span>):
            <span class="keyword">raise</span> <span class="exception">ValueError</span>(<span class="string">f"期望字典类型，得到 {type(data).__name__}"</span>)
        
        required_fields = [<span class="string">'id'</span>, <span class="string">'name'</span>, <span class="string">'email'</span>]
        <span class="keyword">for</span> field <span class="keyword">in</span> required_fields:
            <span class="keyword">if</span> field <span class="keyword">not</span> <span class="keyword">in</span> data:
                <span class="keyword">raise</span> <span class="exception">KeyError</span>(field)
        
        <span class="keyword">return</span> data
    
    <span class="keyword">def</span> <span class="function">_process_data</span>(<span class="builtin">self</span>, data):
        <span class="comment"># 模拟数据处理</span>
        <span class="keyword">return</span> {<span class="string">'processed'</span>: <span class="keyword">True</span>, **data}
    
    <span class="keyword">def</span> <span class="function">_log_error</span>(<span class="builtin">self</span>, message, data):
        <span class="keyword">import</span> logging
        logging.error(<span class="string">f"{message} | 数据: {data}"</span>)

<span class="comment"># 自定义异常类</span>
<span class="keyword">class</span> <span class="function">DataValidationError</span>(<span class="exception">Exception</span>):
    <span class="keyword">pass</span>

<span class="keyword">class</span> <span class="function">DataStructureError</span>(<span class="exception">Exception</span>):
    <span class="keyword">pass</span>
                        </div>
                    </div>
                </div>

                <div class="practice-item">
                    <h4>🔄 4. 实现重试机制</h4>
                    <div class="code-container">
                        <div class="code">
<span class="keyword">import</span> time
<span class="keyword">import</span> random

<span class="keyword">def</span> <span class="function">retry_with_backoff</span>(max_retries=<span class="number">3</span>, base_delay=<span class="number">1</span>, max_delay=<span class="number">60</span>):
    <span class="string">"""重试装饰器，带有指数退避"""</span>
    <span class="keyword">def</span> <span class="function">decorator</span>(func):
        <span class="keyword">def</span> <span class="function">wrapper</span>(*args, **kwargs):
            <span class="keyword">for</span> attempt <span class="keyword">in</span> <span class="builtin">range</span>(max_retries + <span class="number">1</span>):
                <span class="keyword">try</span>:
                    <span class="keyword">return</span> func(*args, **kwargs)
                    
                <span class="keyword">except</span> (<span class="exception">ConnectionError</span>, <span class="exception">TimeoutError</span>) <span class="keyword">as</span> e:
                    <span class="keyword">if</span> attempt == max_retries:
                        <span class="keyword">print</span>(<span class="string">f"最终失败，已重试 {max_retries} 次: {e}"</span>)
                        <span class="keyword">raise</span>
                    
                    <span class="comment"># 计算退避延迟</span>
                    delay = <span class="builtin">min</span>(base_delay * (<span class="number">2</span> ** attempt), max_delay)
                    jitter = random.uniform(<span class="number">0</span>, <span class="number">0.1</span>) * delay  <span class="comment"># 添加随机抖动</span>
                    total_delay = delay + jitter
                    
                    <span class="keyword">print</span>(<span class="string">f"尝试 {attempt + 1} 失败: {e}"</span>)
                    <span class="keyword">print</span>(<span class="string">f"等待 {total_delay:.2f} 秒后重试..."</span>)
                    time.sleep(total_delay)
                    
                <span class="keyword">except</span> <span class="exception">Exception</span> <span class="keyword">as</span> e:
                    <span class="comment"># 对于其他异常，不重试</span>
                    <span class="keyword">print</span>(<span class="string">f"不可重试的异常: {e}"</span>)
                    <span class="keyword">raise</span>
            
        <span class="keyword">return</span> wrapper
    <span class="keyword">return</span> decorator

<span class="builtin">@retry_with_backoff</span>(max_retries=<span class="number">3</span>, base_delay=<span class="number">1</span>)
<span class="keyword">def</span> <span class="function">unreliable_network_call</span>():
    <span class="string">"""模拟不稳定的网络调用"""</span>
    <span class="keyword">if</span> random.random() < <span class="number">0.7</span>:  <span class="comment"># 70%的概率失败</span>
        <span class="keyword">raise</span> <span class="exception">ConnectionError</span>(<span class="string">"网络连接失败"</span>)
    <span class="keyword">return</span> <span class="string">"网络调用成功"</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第15页：性能考虑和调试技巧 -->
        <div class="slide">
            <h1><span class="emoji">⚡</span>性能考虑和调试技巧</h1>
            <p>了解异常处理的性能影响，掌握调试和日志记录技巧。</p>

            <h3>📊 性能考虑：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">import</span> time
<span class="keyword">import</span> logging

<span class="comment"># 配置日志</span>
logging.basicConfig(
    level=logging.INFO,
    format=<span class="string">'%(asctime)s - %(levelname)s - %(message)s'</span>
)

<span class="keyword">def</span> <span class="function">performance_comparison</span>():
    <span class="string">"""比较不同错误处理方式的性能"""</span>
    
    <span class="comment"># 方法1：使用异常处理</span>
    <span class="keyword">def</span> <span class="function">with_exception</span>(numbers):
        result = []
        <span class="keyword">for</span> num <span class="keyword">in</span> numbers:
            <span class="keyword">try</span>:
                result.append(<span class="number">100</span> / num)
            <span class="keyword">except</span> <span class="exception">ZeroDivisionError</span>:
                result.append(<span class="builtin">float</span>(<span class="string">'inf'</span>))
        <span class="keyword">return</span> result
    
    <span class="comment"># 方法2：使用条件检查</span>
    <span class="keyword">def</span> <span class="function">with_condition</span>(numbers):
        result = []
        <span class="keyword">for</span> num <span class="keyword">in</span> numbers:
            <span class="keyword">if</span> num == <span class="number">0</span>:
                result.append(<span class="builtin">float</span>(<span class="string">'inf'</span>))
            <span class="keyword">else</span>:
                result.append(<span class="number">100</span> / num)
        <span class="keyword">return</span> result
    
    <span class="comment"># 测试数据：大部分是正常值，少数为0</span>
    test_data = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>] * <span class="number">10000</span> + [<span class="number">0</span>] * <span class="number">10</span>
    
    <span class="comment"># 测试异常处理方式</span>
    start_time = time.time()
    result1 = with_exception(test_data)
    exception_time = time.time() - start_time
    
    <span class="comment"># 测试条件检查方式</span>
    start_time = time.time()
    result2 = with_condition(test_data)
    condition_time = time.time() - start_time
    
    <span class="keyword">print</span>(<span class="string">f"异常处理方式: {exception_time:.4f} 秒"</span>)
    <span class="keyword">print</span>(<span class="string">f"条件检查方式: {condition_time:.4f} 秒"</span>)
    <span class="keyword">print</span>(<span class="string">f"性能比率: {exception_time/condition_time:.2f}x"</span>)

<span class="comment"># 运行性能比较</span>
performance_comparison()
                </div>
            </div>

            <h3>🔍 调试和日志记录：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">import</span> traceback
<span class="keyword">import</span> sys
<span class="keyword">from</span> functools <span class="keyword">import</span> wraps

<span class="keyword">class</span> <span class="function">ExceptionLogger</span>:
    <span class="string">"""异常日志记录器"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="builtin">self</span>, logger_name=<span class="string">'exception_logger'</span>):
        <span class="builtin">self</span>.logger = logging.getLogger(logger_name)
        <span class="builtin">self</span>.logger.setLevel(logging.DEBUG)
        
        <span class="comment"># 创建文件处理器</span>
        file_handler = logging.FileHandler(<span class="string">'exceptions.log'</span>)
        file_handler.setLevel(logging.ERROR)
        
        <span class="comment"># 创建控制台处理器</span>
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        <span class="comment"># 设置格式</span>
        formatter = logging.Formatter(
            <span class="string">'%(asctime)s - %(name)s - %(levelname)s - %(message)s'</span>
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        <span class="builtin">self</span>.logger.addHandler(file_handler)
        <span class="builtin">self</span>.logger.addHandler(console_handler)
    
    <span class="keyword">def</span> <span class="function">log_exception</span>(<span class="builtin">self</span>, func_name, exception, context=<span class="keyword">None</span>):
        <span class="string">"""记录异常详细信息"""</span>
        exc_type, exc_value, exc_traceback = sys.exc_info()
        
        error_info = {
            <span class="string">'function'</span>: func_name,
            <span class="string">'exception_type'</span>: type(exception).__name__,
            <span class="string">'exception_message'</span>: <span class="builtin">str</span>(exception),
            <span class="string">'context'</span>: context,
            <span class="string">'traceback'</span>: traceback.format_exc()
        }
        
        <span class="builtin">self</span>.logger.error(<span class="string">f"异常发生: {error_info}"</span>)
        
        <span class="comment"># 如果有异常链，也记录原始异常</span>
        <span class="keyword">if</span> <span class="builtin">hasattr</span>(exception, <span class="string">'__cause__'</span>) <span class="keyword">and</span> exception.__cause__:
            <span class="builtin">self</span>.logger.error(<span class="string">f"原始异常: {exception.__cause__}"</span>)

<span class="comment"># 创建全局异常记录器</span>
exception_logger = <span class="function">ExceptionLogger</span>()

<span class="keyword">def</span> <span class="function">exception_handler</span>(reraise=<span class="keyword">True</span>, log_context=<span class="keyword">True</span>):
    <span class="string">"""异常处理装饰器"""</span>
    <span class="keyword">def</span> <span class="function">decorator</span>(func):
        <span class="builtin">@wraps</span>(func)
        <span class="keyword">def</span> <span class="function">wrapper</span>(*args, **kwargs):
            <span class="keyword">try</span>:
                <span class="keyword">return</span> func(*args, **kwargs)
            <span class="keyword">except</span> <span class="exception">Exception</span> <span class="keyword">as</span> e:
                context = <span class="keyword">None</span>
                <span class="keyword">if</span> log_context:
                    context = {
                        <span class="string">'args'</span>: args,
                        <span class="string">'kwargs'</span>: kwargs,
                        <span class="string">'function_name'</span>: func.__name__
                    }
                
                exception_logger.log_exception(func.__name__, e, context)
                
                <span class="keyword">if</span> reraise:
                    <span class="keyword">raise</span>
                <span class="keyword">else</span>:
                    <span class="keyword">return</span> <span class="keyword">None</span>
        
        <span class="keyword">return</span> wrapper
    <span class="keyword">return</span> decorator

<span class="comment"># 使用示例</span>
<span class="builtin">@exception_handler</span>(reraise=<span class="keyword">False</span>)
<span class="keyword">def</span> <span class="function">risky_calculation</span>(x, y, operation=<span class="string">'divide'</span>):
    <span class="string">"""执行可能出错的计算"""</span>
    <span class="keyword">if</span> operation == <span class="string">'divide'</span>:
        <span class="keyword">return</span> x / y
    <span class="keyword">elif</span> operation == <span class="string">'sqrt'</span>:
        <span class="keyword">import</span> math
        <span class="keyword">return</span> math.sqrt(x)
    <span class="keyword">else</span>:
        <span class="keyword">raise</span> <span class="exception">ValueError</span>(<span class="string">f"不支持的操作: {operation}"</span>)

<span class="comment"># 测试异常记录</span>
<span class="keyword">print</span>(<span class="string">"测试异常记录："</span>)
result1 = risky_calculation(<span class="number">10</span>, <span class="number">0</span>)  <span class="comment"># 除零错误</span>
result2 = risky_calculation(-<span class="number">5</span>, <span class="number">2</span>, <span class="string">'sqrt'</span>)  <span class="comment"># 负数开方错误</span>
result3 = risky_calculation(<span class="number">10</span>, <span class="number">5</span>, <span class="string">'unknown'</span>)  <span class="comment"># 不支持的操作</span>
                </div>
            </div>

            <div class="info-box">
                <p><strong>💡 性能提示：</strong>异常处理比条件检查慢，但在异常情况确实罕见时，"请求原谅而非许可"(EAFP)的方式通常更Pythonic。</p>
            </div>
        </div>

        <!-- 第16页：总结 -->
        <div class="slide">
            <h1><span class="emoji">🎓</span>总结</h1>
            <p>通过本教程，我们全面学习了Python异常处理和断言的核心概念、使用方法和最佳实践。</p>

            <div class="summary-grid">
                <div class="summary-item">
                    <h3>🔧 核心概念</h3>
                    <ul>
                        <li>异常的类型和层次结构</li>
                        <li>try-except-else-finally语句</li>
                        <li>raise语句和异常链</li>
                        <li>自定义异常类</li>
                        <li>断言的使用和场景</li>
                    </ul>
                </div>

                <div class="summary-item">
                    <h3>🎯 最佳实践</h3>
                    <ul>
                        <li>具体化异常处理，避免捕获所有异常</li>
                        <li>使用上下文管理器管理资源</li>
                        <li>提供有意义的错误信息</li>
                        <li>合理使用异常链保留错误上下文</li>
                        <li>区分断言和异常处理的使用场景</li>
                    </ul>
                </div>

                <div class="summary-item">
                    <h3>⚡ 性能和调试</h3>
                    <ul>
                        <li>理解异常处理的性能影响</li>
                        <li>实现重试机制处理临时错误</li>
                        <li>使用日志记录异常详细信息</li>
                        <li>利用traceback模块进行调试</li>
                        <li>创建异常处理装饰器</li>
                    </ul>
                </div>

                <div class="summary-item">
                    <h3>🚀 进阶技巧</h3>
                    <ul>
                        <li>创建异常处理装饰器</li>
                        <li>实现自定义上下文管理器</li>
                        <li>使用异常进行控制流</li>
                        <li>处理异步代码中的异常</li>
                        <li>集成异常处理到应用架构中</li>
                    </ul>
                </div>
            </div>

            <div class="key-takeaways">
                <h3>🔑 关键要点</h3>
                <div class="takeaway-grid">
                    <div class="takeaway-item success-box">
                        <h4>✅ 做什么</h4>
                        <ul>
                            <li>使用具体的异常类型</li>
                            <li>提供清晰的错误消息</li>
                            <li>使用with语句管理资源</li>
                            <li>记录异常日志</li>
                            <li>保持异常处理代码简洁</li>
                        </ul>
                    </div>

                    <div class="takeaway-item error-box">
                        <h4>❌ 不要做什么</h4>
                        <ul>
                            <li>不要捕获所有异常</li>
                            <li>不要忽略异常</li>
                            <li>不要在生产代码中依赖断言</li>
                            <li>不要过度使用异常处理</li>
                            <li>不要在异常处理中执行复杂逻辑</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="final-example">
                <h3>🎪 综合示例：</h3>
                <div class="code-container">
                    <div class="code">
<span class="keyword">class</span> <span class="function">RobustDataProcessor</span>:
    <span class="string">"""健壮的数据处理器 - 综合应用所有最佳实践"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="builtin">self</span>):
        <span class="builtin">self</span>.logger = logging.getLogger(__name__)
        <span class="builtin">self</span>.processed_count = <span class="number">0</span>
        <span class="builtin">self</span>.error_count = <span class="number">0</span>
    
    <span class="keyword">def</span> <span class="function">process_data_batch</span>(<span class="builtin">self</span>, data_list):
        <span class="string">"""批量处理数据"""</span>
        <span class="keyword">assert</span> <span class="builtin">isinstance</span>(data_list, <span class="builtin">list</span>), <span class="string">"输入必须是列表"</span>
        
        results = []
        <span class="keyword">for</span> i, data <span class="keyword">in</span> <span class="builtin">enumerate</span>(data_list):
            <span class="keyword">try</span>:
                result = <span class="builtin">self</span>._process_single_item(data, i)
                results.append(result)
                <span class="builtin">self</span>.processed_count += <span class="number">1</span>
                
            <span class="keyword">except</span> <span class="exception">ValueError</span> <span class="keyword">as</span> e:
                <span class="builtin">self</span>.logger.warning(<span class="string">f"数据验证失败 (索引 {i}): {e}"</span>)
                <span class="builtin">self</span>.error_count += <span class="number">1</span>
                results.append(<span class="keyword">None</span>)
                
            <span class="keyword">except</span> <span class="exception">Exception</span> <span class="keyword">as</span> e:
                <span class="builtin">self</span>.logger.error(<span class="string">f"处理失败 (索引 {i}): {e}"</span>)
                <span class="builtin">self</span>.error_count += <span class="number">1</span>
                results.append(<span class="keyword">None</span>)
        
        <span class="comment"># 后置条件检查</span>
        <span class="keyword">assert</span> <span class="builtin">len</span>(results) == <span class="builtin">len</span>(data_list), <span class="string">"结果数量不匹配"</span>
        
        <span class="builtin">self</span>.logger.info(<span class="string">f"批量处理完成: 成功 {<span class="builtin">self</span>.processed_count}, 失败 {<span class="builtin">self</span>.error_count}"</span>)
        <span class="keyword">return</span> results
    
    <span class="keyword">def</span> <span class="function">_process_single_item</span>(<span class="builtin">self</span>, data, index):
        <span class="string">"""处理单个数据项"""</span>
        <span class="keyword">if</span> <span class="keyword">not</span> <span class="builtin">isinstance</span>(data, <span class="builtin">dict</span>):
            <span class="keyword">raise</span> <span class="exception">ValueError</span>(<span class="string">f"期望字典类型，得到 {type(data).__name__}"</span>)
        
        <span class="keyword">if</span> <span class="string">'value'</span> <span class="keyword">not</span> <span class="keyword">in</span> data:
            <span class="keyword">raise</span> <span class="exception">ValueError</span>(<span class="string">"缺少必需字段 'value'"</span>)
        
        <span class="keyword">try</span>:
            processed_value = <span class="builtin">float</span>(data[<span class="string">'value'</span>]) * <span class="number">2</span>
            <span class="keyword">return</span> {<span class="string">'index'</span>: index, <span class="string">'result'</span>: processed_value}
        <span class="keyword">except</span> (<span class="exception">TypeError</span>, <span class="exception">ValueError</span>) <span class="keyword">as</span> e:
            <span class="keyword">raise</span> <span class="exception">ValueError</span>(<span class="string">f"无法处理值 '{data['value']}': {e}"</span>) <span class="keyword">from</span> e

<span class="comment"># 使用示例</span>
processor = <span class="function">RobustDataProcessor</span>()
test_data = [
    {<span class="string">'value'</span>: <span class="number">10</span>},
    {<span class="string">'value'</span>: <span class="string">'20'</span>},
    {<span class="string">'name'</span>: <span class="string">'invalid'</span>},  <span class="comment"># 缺少value字段</span>
    {<span class="string">'value'</span>: <span class="string">'abc'</span>},     <span class="comment"># 无效值</span>
    {<span class="string">'value'</span>: <span class="number">30.5</span>}
]

results = processor.process_data_batch(test_data)
<span class="keyword">print</span>(<span class="string">f"处理结果: {results}"</span>)
                    </div>
                </div>
            </div>

            <div class="congratulations">
                <h3>🎉 恭喜完成学习！</h3>
                <p>您现在已经掌握了Python异常处理和断言的核心知识。继续实践这些概念，编写更加健壮和可维护的Python代码！</p>
                
                <div class="next-steps">
                    <h4>🚀 下一步建议：</h4>
                    <ul>
                        <li>在实际项目中应用这些异常处理技巧</li>
                        <li>学习异步编程中的异常处理</li>
                        <li>探索第三方库的异常处理模式</li>
                        <li>研究测试驱动开发中的异常测试</li>
                        <li>深入了解Python的内部异常机制</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            
            // 更新进度条
            const progress = ((currentSlide + 1) / totalSlides) * 100;
            document.querySelector('.progress').style.width = progress + '%';
            
            // 更新页码
            document.querySelector('.slide-counter').textContent = 
                `${currentSlide + 1} / ${totalSlides}`;
        }

        function nextSlide() {
            showSlide(currentSlide + 1);
        }

        function prevSlide() {
            showSlide(currentSlide - 1);
        }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                prevSlide();
            }
        });

        // 初始化
        showSlide(0);

        // 添加代码高亮动画
        document.querySelectorAll('.code').forEach(code => {
            code.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.02)';
            });
            
            code.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // 添加平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>


