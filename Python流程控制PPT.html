<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python 流程控制完整教程</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
        }

        .slideshow-container {
            position: relative;
            width: 100%;
            height: 100vh;
        }

        .slide {
            display: none;
            padding: 40px;
            height: 100vh;
            overflow-y: auto;
            animation: slideIn 0.8s ease-in-out;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        .slide.active {
            display: block;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .animated {
            animation: fadeInUp 1s ease-out;
        }

        .bounce {
            animation: bounce 2s infinite;
        }

        h1 {
            font-size: 2.5em;
            color: #2c3e50;
            text-align: left;
            margin-bottom: 30px;
            border-left: 5px solid #3498db;
            padding-left: 20px;
        }

        h2 {
            font-size: 2em;
            color: #34495e;
            text-align: left;
            margin: 25px 0 20px 0;
            border-left: 4px solid #e74c3c;
            padding-left: 15px;
        }

        h3 {
            font-size: 1.5em;
            color: #2980b9;
            text-align: left;
            margin: 20px 0 15px 0;
        }

        p, li {
            font-size: 1.1em;
            line-height: 1.8;
            color: #2c3e50;
            text-align: left;
            margin-bottom: 15px;
        }

        ul {
            margin-left: 30px;
            margin-bottom: 20px;
        }

        .code-container {
            background: #1e1e1e;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            min-height: 200px;
            overflow-x: auto;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }

        .code {
            color: #d4d4d4;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            text-align: left;
            white-space: pre;
        }

        .keyword { color: #569cd6; }
        .string { color: #ce9178; }
        .comment { color: #6a9955; }
        .number { color: #b5cea8; }
        .function { color: #dcdcaa; }
        .operator { color: #d4d4d4; }

        .emoji {
            font-size: 2em;
            margin-right: 15px;
            display: inline-block;
        }

        .navigation {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }

        .nav-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 20px;
            margin: 0 5px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .slide-counter {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 14px;
        }

        .highlight-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .tip-box {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .warning-box {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .ai-prompt {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            font-family: monospace;
        }

        /* 导航控制样式 */
        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            align-items: center;
            gap: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 25px;
            border-radius: 50px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            z-index: 1000;
        }

        .nav-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .nav-btn:active {
            transform: translateY(0);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .slide-counter {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            min-width: 80px;
            text-align: center;
        }

        /* 页面指示器样式 */
        .slide-indicators {
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
        }

        .indicators-container {
            display: flex;
            gap: 8px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 15px;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            max-width: 90vw;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .indicators-container::-webkit-scrollbar {
            display: none;
        }

        .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #bdc3c7;
            cursor: pointer;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .indicator:hover {
            background: #95a5a6;
            transform: scale(1.2);
        }

        .indicator.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transform: scale(1.3);
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .navigation {
                bottom: 20px;
                padding: 12px 20px;
            }

            .nav-btn {
                padding: 10px 16px;
                font-size: 14px;
            }

            .slide-counter {
                font-size: 14px;
                min-width: 70px;
            }

            .slide-indicators {
                bottom: 80px;
            }

            .indicators-container {
                padding: 8px 12px;
                gap: 6px;
            }

            .indicator {
                width: 10px;
                height: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="slideshow-container">
        <!-- 第1页：标题页 -->
        <div class="slide active">
            <div class="animated">
                <h1><span class="emoji bounce">🐍</span>Python 流程控制完整教程</h1>
                <div class="highlight-box">
                    <h2><span class="emoji">🎯</span>学习目标</h2>
                    <ul>
                        <li>掌握条件语句的使用方法</li>
                        <li>理解循环语句的工作原理</li>
                        <li>学会异常处理机制</li>
                        <li>了解AI辅助编程工具的使用</li>
                    </ul>
                </div>
                <div class="tip-box">
                    <p><span class="emoji">💡</span><strong>提示：</strong>本PPT支持竖向滚动，可以查看更多内容。使用左右箭头键或底部按钮切换页面。</p>
                </div>
            </div>
        </div>

        <!-- 第2页：流程控制概述 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">🔄</span>流程控制概述</h1>
                <h2><span class="emoji">📋</span>什么是流程控制？</h2>
                <p>流程控制是编程中控制程序执行顺序的机制，它决定了程序中哪些代码块会被执行，以及执行的顺序。</p>
                
                <h3><span class="emoji">🎭</span>流程控制的三种基本结构</h3>
                <ul>
                    <li><strong>顺序结构：</strong>代码按照从上到下的顺序依次执行</li>
                    <li><strong>选择结构：</strong>根据条件判断选择不同的执行路径</li>
                    <li><strong>循环结构：</strong>重复执行某段代码直到满足退出条件</li>
                </ul>

                <div class="code-container">
                    <div class="code"># 流程控制示例
<span class="comment"># 1. 顺序结构</span>
<span class="function">print</span>(<span class="string">"第一步"</span>)
<span class="function">print</span>(<span class="string">"第二步"</span>)
<span class="function">print</span>(<span class="string">"第三步"</span>)

<span class="comment"># 2. 选择结构</span>
age = <span class="number">18</span>
<span class="keyword">if</span> age >= <span class="number">18</span>:
    <span class="function">print</span>(<span class="string">"成年人"</span>)
<span class="keyword">else</span>:
    <span class="function">print</span>(<span class="string">"未成年人"</span>)

<span class="comment"># 3. 循环结构</span>
<span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">3</span>):
    <span class="function">print</span>(<span class="string">f"循环第{i+1}次"</span>)</div>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习流程控制：</strong>"请解释Python中的流程控制概念，并提供简单的代码示例来说明顺序、选择和循环结构的区别。"</p>
                </div>
            </div>
        </div>

        <!-- 第3页：条件语句 - if语句 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">🤔</span>条件语句 - if语句</h1>
                <h2><span class="emoji">🎯</span>基本语法</h2>
                <p>if语句是最基本的条件判断语句，用于根据条件的真假来决定是否执行某段代码。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># if语句基本语法</span>
<span class="keyword">if</span> 条件表达式:
    <span class="comment"># 条件为True时执行的代码块</span>
    代码块

<span class="comment"># 实际示例</span>
temperature = <span class="number">25</span>
<span class="keyword">if</span> temperature > <span class="number">20</span>:
    <span class="function">print</span>(<span class="string">"天气很温暖"</span>)
    <span class="function">print</span>(<span class="string">"适合外出"</span>)

<span class="comment"># 多个条件判断</span>
score = <span class="number">85</span>
<span class="keyword">if</span> score >= <span class="number">90</span>:
    <span class="function">print</span>(<span class="string">"优秀"</span>)
<span class="keyword">if</span> score >= <span class="number">80</span>:
    <span class="function">print</span>(<span class="string">"良好"</span>)
<span class="keyword">if</span> score >= <span class="number">60</span>:
    <span class="function">print</span>(<span class="string">"及格"</span>)</div>
                </div>

                <div class="tip-box">
                    <h3><span class="emoji">💡</span>重要提示</h3>
                    <ul>
                        <li>条件表达式后面必须有冒号 <code>:</code></li>
                        <li>代码块必须有缩进（通常是4个空格）</li>
                        <li>Python使用缩进来表示代码块的层次结构</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习if语句：</strong>"请详细解释Python中if语句的语法规则，包括缩进的重要性，并提供几个不同场景的实际应用示例。"</p>
                </div>
            </div>
        </div>

        <!-- 第4页：条件语句 - if-else语句 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">⚖️</span>条件语句 - if-else语句</h1>
                <h2><span class="emoji">🔀</span>二选一的决策</h2>
                <p>if-else语句提供了两个执行路径：当条件为True时执行if块，当条件为False时执行else块。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># if-else语句基本语法</span>
<span class="keyword">if</span> 条件表达式:
    <span class="comment"># 条件为True时执行</span>
    代码块1
<span class="keyword">else</span>:
    <span class="comment"># 条件为False时执行</span>
    代码块2

<span class="comment"># 实际示例：判断奇偶数</span>
number = <span class="number">7</span>
<span class="keyword">if</span> number % <span class="number">2</span> == <span class="number">0</span>:
    <span class="function">print</span>(<span class="string">f"{number} 是偶数"</span>)
<span class="keyword">else</span>:
    <span class="function">print</span>(<span class="string">f"{number} 是奇数"</span>)

<span class="comment"># 示例：用户登录验证</span>
username = <span class="string">"admin"</span>
password = <span class="string">"123456"</span>

<span class="keyword">if</span> username == <span class="string">"admin"</span> <span class="keyword">and</span> password == <span class="string">"123456"</span>:
    <span class="function">print</span>(<span class="string">"登录成功！"</span>)
    <span class="function">print</span>(<span class="string">"欢迎进入系统"</span>)
<span class="keyword">else</span>:
    <span class="function">print</span>(<span class="string">"用户名或密码错误！"</span>)
    <span class="function">print</span>(<span class="string">"请重新输入"</span>)</div>
                </div>

                <div class="warning-box">
                    <h3><span class="emoji">⚠️</span>常见错误</h3>
                    <ul>
                        <li>忘记在if和else后面加冒号</li>
                        <li>缩进不一致导致IndentationError</li>
                        <li>在条件判断中使用赋值运算符(=)而不是比较运算符(==)</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习if-else：</strong>"请解释Python中if-else语句的工作原理，并提供一些实际的应用场景，比如用户输入验证、数据分类等。"</p>
                </div>
            </div>
        </div>

        <!-- 第5页：条件语句 - elif语句 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">🎭</span>条件语句 - elif语句</h1>
                <h2><span class="emoji">🌈</span>多重条件判断</h2>
                <p>elif（else if的缩写）用于处理多个条件的情况，可以实现多分支的条件判断。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># elif语句基本语法</span>
<span class="keyword">if</span> 条件1:
    代码块1
<span class="keyword">elif</span> 条件2:
    代码块2
<span class="keyword">elif</span> 条件3:
    代码块3
<span class="keyword">else</span>:
    代码块4

<span class="comment"># 实际示例：成绩等级判断</span>
score = <span class="number">85</span>

<span class="keyword">if</span> score >= <span class="number">90</span>:
    grade = <span class="string">"A"</span>
    <span class="function">print</span>(<span class="string">f"成绩：{score}，等级：{grade}，优秀！"</span>)
<span class="keyword">elif</span> score >= <span class="number">80</span>:
    grade = <span class="string">"B"</span>
    <span class="function">print</span>(<span class="string">f"成绩：{score}，等级：{grade}，良好！"</span>)
<span class="keyword">elif</span> score >= <span class="number">70</span>:
    grade = <span class="string">"C"</span>
    <span class="function">print</span>(<span class="string">f"成绩：{score}，等级：{grade}，中等！"</span>)
<span class="keyword">elif</span> score >= <span class="number">60</span>:
    grade = <span class="string">"D"</span>
    <span class="function">print</span>(<span class="string">f"成绩：{score}，等级：{grade}，及格！"</span>)
<span class="keyword">else</span>:
    grade = <span class="string">"F"</span>
    <span class="function">print</span>(<span class="string">f"成绩：{score}，等级：{grade}，不及格！"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 示例：季节判断</span>
month = <span class="number">8</span>

<span class="keyword">if</span> month <span class="keyword">in</span> [<span class="number">12</span>, <span class="number">1</span>, <span class="number">2</span>]:
    season = <span class="string">"冬季"</span>
    <span class="function">print</span>(<span class="string">f"{month}月是{season}，注意保暖！"</span>)
<span class="keyword">elif</span> month <span class="keyword">in</span> [<span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>]:
    season = <span class="string">"春季"</span>
    <span class="function">print</span>(<span class="string">f"{month}月是{season}，万物复苏！"</span>)
<span class="keyword">elif</span> month <span class="keyword">in</span> [<span class="number">6</span>, <span class="number">7</span>, <span class="number">8</span>]:
    season = <span class="string">"夏季"</span>
    <span class="function">print</span>(<span class="string">f"{month}月是{season}，注意防暑！"</span>)
<span class="keyword">elif</span> month <span class="keyword">in</span> [<span class="number">9</span>, <span class="number">10</span>, <span class="number">11</span>]:
    season = <span class="string">"秋季"</span>
    <span class="function">print</span>(<span class="string">f"{month}月是{season}，秋高气爽！"</span>)
<span class="keyword">else</span>:
    <span class="function">print</span>(<span class="string">"输入的月份无效！"</span>)</div>
                </div>

                <div class="tip-box">
                    <h3><span class="emoji">💡</span>执行顺序</h3>
                    <p>elif语句是按顺序检查的，一旦某个条件为True，就执行对应的代码块，然后跳出整个if-elif-else结构，不会再检查后面的条件。</p>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习elif：</strong>"请解释Python中elif语句的使用场景，并提供一个复杂的多分支判断示例，比如根据不同条件给出不同的处理方案。"</p>
                </div>
            </div>
        </div>

        <!-- 第6页：嵌套条件语句 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">🏗️</span>嵌套条件语句</h1>
                <h2><span class="emoji">🎯</span>条件中的条件</h2>
                <p>嵌套条件语句是指在一个条件语句内部包含另一个条件语句，用于处理更复杂的逻辑判断。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># 嵌套条件语句示例：用户权限检查</span>
username = <span class="string">"admin"</span>
password = <span class="string">"123456"</span>
user_type = <span class="string">"admin"</span>

<span class="keyword">if</span> username == <span class="string">"admin"</span> <span class="keyword">and</span> password == <span class="string">"123456"</span>:
    <span class="function">print</span>(<span class="string">"登录成功！"</span>)
    
    <span class="comment"># 嵌套的条件判断</span>
    <span class="keyword">if</span> user_type == <span class="string">"admin"</span>:
        <span class="function">print</span>(<span class="string">"管理员权限：可以访问所有功能"</span>)
        
        <span class="comment"># 更深层的嵌套</span>
        operation = <span class="string">"delete"</span>
        <span class="keyword">if</span> operation == <span class="string">"delete"</span>:
            <span class="function">print</span>(<span class="string">"警告：您正在执行删除操作！"</span>)
        <span class="keyword">elif</span> operation == <span class="string">"modify"</span>:
            <span class="function">print</span>(<span class="string">"正在修改数据..."</span>)
        <span class="keyword">else</span>:
            <span class="function">print</span>(<span class="string">"执行常规操作"</span>)
            
    <span class="keyword">elif</span> user_type == <span class="string">"user"</span>:
        <span class="function">print</span>(<span class="string">"普通用户权限：只能查看和编辑"</span>)
    <span class="keyword">else</span>:
        <span class="function">print</span>(<span class="string">"访客权限：只能查看"</span>)
        
<span class="keyword">else</span>:
    <span class="function">print</span>(<span class="string">"登录失败！"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 示例：学生成绩综合评定</span>
math_score = <span class="number">85</span>
english_score = <span class="number">78</span>
attendance = <span class="number">95</span>  <span class="comment"># 出勤率</span>

<span class="keyword">if</span> math_score >= <span class="number">80</span>:
    <span class="function">print</span>(<span class="string">"数学成绩优秀"</span>)
    
    <span class="keyword">if</span> english_score >= <span class="number">80</span>:
        <span class="function">print</span>(<span class="string">"英语成绩也很好"</span>)
        
        <span class="keyword">if</span> attendance >= <span class="number">90</span>:
            <span class="function">print</span>(<span class="string">"综合评定：优秀学生！"</span>)
            reward = <span class="string">"奖学金"</span>
        <span class="keyword">else</span>:
            <span class="function">print</span>(<span class="string">"出勤率需要提高"</span>)
            reward = <span class="string">"表扬信"</span>
            
    <span class="keyword">else</span>:
        <span class="function">print</span>(<span class="string">"英语成绩需要加强"</span>)
        reward = <span class="string">"鼓励"</span>
        
<span class="keyword">else</span>:
    <span class="function">print</span>(<span class="string">"数学成绩需要努力"</span>)
    reward = <span class="string">"补习建议"</span>

<span class="function">print</span>(<span class="string">f"获得奖励：{reward}"</span>)</div>
                </div>

                <div class="warning-box">
                    <h3><span class="emoji">⚠️</span>注意事项</h3>
                    <ul>
                        <li>嵌套层次不要太深，一般不超过3-4层</li>
                        <li>注意缩进的一致性，每一层嵌套都要正确缩进</li>
                        <li>复杂的嵌套可以考虑拆分成多个函数</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习嵌套条件：</strong>"请解释Python中嵌套条件语句的概念，并提供一个实际的业务场景示例，说明什么时候需要使用嵌套条件以及如何避免过度嵌套。"</p>
                </div>
            </div>
        </div>

        <!-- 第7页：条件表达式（三元运算符） -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">⚡</span>条件表达式（三元运算符）</h1>
                <h2><span class="emoji">🚀</span>简洁的条件判断</h2>
                <p>条件表达式（也称为三元运算符）提供了一种简洁的方式来进行简单的条件判断和赋值。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># 条件表达式基本语法</span>
结果 = 值1 <span class="keyword">if</span> 条件 <span class="keyword">else</span> 值2

<span class="comment"># 等价的if-else语句</span>
<span class="keyword">if</span> 条件:
    结果 = 值1
<span class="keyword">else</span>:
    结果 = 值2

<span class="comment"># 实际示例：判断奇偶数</span>
number = <span class="number">7</span>
result = <span class="string">"偶数"</span> <span class="keyword">if</span> number % <span class="number">2</span> == <span class="number">0</span> <span class="keyword">else</span> <span class="string">"奇数"</span>
<span class="function">print</span>(<span class="string">f"{number} 是 {result}"</span>)

<span class="comment"># 示例：获取绝对值</span>
x = <span class="number">-5</span>
abs_x = x <span class="keyword">if</span> x >= <span class="number">0</span> <span class="keyword">else</span> -x
<span class="function">print</span>(<span class="string">f"{x} 的绝对值是 {abs_x}"</span>)

<span class="comment"># 示例：设置默认值</span>
name = <span class="string">""</span>
display_name = name <span class="keyword">if</span> name <span class="keyword">else</span> <span class="string">"匿名用户"</span>
<span class="function">print</span>(<span class="string">f"欢迎，{display_name}！"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 更多实际应用示例</span>

<span class="comment"># 1. 年龄分类</span>
age = <span class="number">25</span>
category = <span class="string">"成年人"</span> <span class="keyword">if</span> age >= <span class="number">18</span> <span class="keyword">else</span> <span class="string">"未成年人"</span>
<span class="function">print</span>(<span class="string">f"年龄{age}岁，分类：{category}"</span>)

<span class="comment"># 2. 成绩等级（简化版）</span>
score = <span class="number">85</span>
grade = <span class="string">"及格"</span> <span class="keyword">if</span> score >= <span class="number">60</span> <span class="keyword">else</span> <span class="string">"不及格"</span>
<span class="function">print</span>(<span class="string">f"成绩{score}分，结果：{grade}"</span>)

<span class="comment"># 3. 列表操作</span>
numbers = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>]
max_num = numbers[<span class="number">0</span>] <span class="keyword">if</span> numbers <span class="keyword">else</span> <span class="number">0</span>
<span class="function">print</span>(<span class="string">f"列表的第一个元素：{max_num}"</span>)

<span class="comment"># 4. 嵌套的条件表达式（不推荐过度使用）</span>
temperature = <span class="number">25</span>
weather = <span class="string">"热"</span> <span class="keyword">if</span> temperature > <span class="number">30</span> <span class="keyword">else</span> (<span class="string">"温暖"</span> <span class="keyword">if</span> temperature > <span class="number">20</span> <span class="keyword">else</span> <span class="string">"凉爽"</span>)
<span class="function">print</span>(<span class="string">f"温度{temperature}°C，天气：{weather}"</span>)</div>
                </div>

                <div class="tip-box">
                    <h3><span class="emoji">💡</span>使用建议</h3>
                    <ul>
                        <li>适用于简单的二选一判断</li>
                        <li>可以让代码更简洁，但不要过度使用</li>
                        <li>复杂的逻辑还是应该使用完整的if-else语句</li>
                        <li>避免嵌套太多层的条件表达式</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习条件表达式：</strong>"请解释Python中条件表达式（三元运算符）的语法和使用场景，并提供一些实际的代码示例，说明什么时候使用条件表达式比传统if-else更合适。"</p>
                </div>
            </div>
        </div>

        <!-- 第8页：for循环基础 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">🔄</span>for循环基础</h1>
                <h2><span class="emoji">🎯</span>遍历序列的利器</h2>
                <p>for循环是Python中最常用的循环语句，用于遍历序列（如列表、元组、字符串）或其他可迭代对象。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># for循环基本语法</span>
<span class="keyword">for</span> 变量 <span class="keyword">in</span> 可迭代对象:
    循环体代码

<span class="comment"># 示例1：遍历列表</span>
fruits = [<span class="string">"苹果"</span>, <span class="string">"香蕉"</span>, <span class="string">"橙子"</span>, <span class="string">"葡萄"</span>]
<span class="function">print</span>(<span class="string">"我喜欢的水果："</span>)
<span class="keyword">for</span> fruit <span class="keyword">in</span> fruits:
    <span class="function">print</span>(<span class="string">f"- {fruit}"</span>)

<span class="comment"># 示例2：遍历字符串</span>
word = <span class="string">"Python"</span>
<span class="function">print</span>(<span class="string">f"'{word}'的每个字符："</span>)
<span class="keyword">for</span> char <span class="keyword">in</span> word:
    <span class="function">print</span>(<span class="string">f"字符：{char}"</span>)

<span class="comment"># 示例3：使用range()函数</span>
<span class="function">print</span>(<span class="string">"数字1到5："</span>)
<span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1</span>, <span class="number">6</span>):
    <span class="function">print</span>(<span class="string">f"数字：{i}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># range()函数的不同用法</span>

<span class="comment"># range(stop) - 从0开始到stop-1</span>
<span class="function">print</span>(<span class="string">"range(5):"</span>)
<span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">5</span>):
    <span class="function">print</span>(i, end=<span class="string">" "</span>)
<span class="function">print</span>()  <span class="comment"># 换行</span>

<span class="comment"># range(start, stop) - 从start开始到stop-1</span>
<span class="function">print</span>(<span class="string">"range(2, 8):"</span>)
<span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">2</span>, <span class="number">8</span>):
    <span class="function">print</span>(i, end=<span class="string">" "</span>)
<span class="function">print</span>()

<span class="comment"># range(start, stop, step) - 指定步长</span>
<span class="function">print</span>(<span class="string">"range(0, 10, 2):"</span>)
<span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">0</span>, <span class="number">10</span>, <span class="number">2</span>):
    <span class="function">print</span>(i, end=<span class="string">" "</span>)
<span class="function">print</span>()

<span class="comment"># 倒序遍历</span>
<span class="function">print</span>(<span class="string">"倒序 range(10, 0, -1):"</span>)
<span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">10</span>, <span class="number">0</span>, <span class="number">-1</span>):
    <span class="function">print</span>(i, end=<span class="string">" "</span>)
<span class="function">print</span>()</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 实际应用示例</span>

<span class="comment"># 计算列表元素的总和</span>
numbers = [<span class="number">10</span>, <span class="number">20</span>, <span class="number">30</span>, <span class="number">40</span>, <span class="number">50</span>]
total = <span class="number">0</span>
<span class="keyword">for</span> num <span class="keyword">in</span> numbers:
    total += num
<span class="function">print</span>(<span class="string">f"列表元素总和：{total}"</span>)

<span class="comment"># 打印乘法表</span>
<span class="function">print</span>(<span class="string">"\n5的乘法表："</span>)
<span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1</span>, <span class="number">11</span>):
    result = <span class="number">5</span> * i
    <span class="function">print</span>(<span class="string">f"5 × {i} = {result}"</span>)</div>
                </div>

                <div class="tip-box">
                    <h3><span class="emoji">💡</span>重要概念</h3>
                    <ul>
                        <li><strong>可迭代对象：</strong>列表、元组、字符串、字典、集合等</li>
                        <li><strong>循环变量：</strong>每次循环时自动获取序列中的下一个元素</li>
                        <li><strong>range()：</strong>生成数字序列的内置函数</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习for循环：</strong>"请详细解释Python中for循环的语法和工作原理，包括range()函数的各种用法，并提供一些实际的应用场景示例。"</p>
                </div>
            </div>
        </div>

        <!-- 第9页：for循环进阶 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">🚀</span>for循环进阶</h1>
                <h2><span class="emoji">🎯</span>enumerate和zip函数</h2>
                <p>Python提供了一些强大的内置函数来增强for循环的功能，让遍历操作更加灵活和高效。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># enumerate() - 同时获取索引和值</span>
fruits = [<span class="string">"苹果"</span>, <span class="string">"香蕉"</span>, <span class="string">"橙子"</span>, <span class="string">"葡萄"</span>]

<span class="function">print</span>(<span class="string">"使用enumerate获取索引和值："</span>)
<span class="keyword">for</span> index, fruit <span class="keyword">in</span> <span class="function">enumerate</span>(fruits):
    <span class="function">print</span>(<span class="string">f"{index + 1}. {fruit}"</span>)

<span class="comment"># 指定起始索引</span>
<span class="function">print</span>(<span class="string">"\n从索引10开始："</span>)
<span class="keyword">for</span> index, fruit <span class="keyword">in</span> <span class="function">enumerate</span>(fruits, start=<span class="number">10</span>):
    <span class="function">print</span>(<span class="string">f"索引{index}: {fruit}"</span>)

<span class="comment"># zip() - 同时遍历多个序列</span>
names = [<span class="string">"张三"</span>, <span class="string">"李四"</span>, <span class="string">"王五"</span>]
ages = [<span class="number">25</span>, <span class="number">30</span>, <span class="number">28</span>]
cities = [<span class="string">"北京"</span>, <span class="string">"上海"</span>, <span class="string">"广州"</span>]

<span class="function">print</span>(<span class="string">"\n使用zip同时遍历多个列表："</span>)
<span class="keyword">for</span> name, age, city <span class="keyword">in</span> <span class="function">zip</span>(names, ages, cities):
    <span class="function">print</span>(<span class="string">f"{name}，{age}岁，来自{city}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 遍历字典</span>
student_scores = {
    <span class="string">"张三"</span>: <span class="number">85</span>,
    <span class="string">"李四"</span>: <span class="number">92</span>,
    <span class="string">"王五"</span>: <span class="number">78</span>,
    <span class="string">"赵六"</span>: <span class="number">96</span>
}

<span class="comment"># 遍历键</span>
<span class="function">print</span>(<span class="string">"学生姓名："</span>)
<span class="keyword">for</span> name <span class="keyword">in</span> student_scores:
    <span class="function">print</span>(<span class="string">f"- {name}"</span>)

<span class="comment"># 遍历值</span>
<span class="function">print</span>(<span class="string">"\n学生成绩："</span>)
<span class="keyword">for</span> score <span class="keyword">in</span> student_scores.values():
    <span class="function">print</span>(<span class="string">f"- {score}分"</span>)

<span class="comment"># 遍历键值对</span>
<span class="function">print</span>(<span class="string">"\n学生成绩详情："</span>)
<span class="keyword">for</span> name, score <span class="keyword">in</span> student_scores.items():
    grade = <span class="string">"优秀"</span> <span class="keyword">if</span> score >= <span class="number">90</span> <span class="keyword">else</span> <span class="string">"良好"</span> <span class="keyword">if</span> score >= <span class="number">80</span> <span class="keyword">else</span> <span class="string">"及格"</span>
    <span class="function">print</span>(<span class="string">f"{name}: {score}分 ({grade})"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 列表推导式（List Comprehension）</span>

<span class="comment"># 传统方式创建平方数列表</span>
squares_traditional = []
<span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1</span>, <span class="number">6</span>):
    squares_traditional.append(i ** <span class="number">2</span>)
<span class="function">print</span>(<span class="string">f"传统方式：{squares_traditional}"</span>)

<span class="comment"># 使用列表推导式</span>
squares_comprehension = [i ** <span class="number">2</span> <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1</span>, <span class="number">6</span>)]
<span class="function">print</span>(<span class="string">f"列表推导式：{squares_comprehension}"</span>)

<span class="comment"># 带条件的列表推导式</span>
even_squares = [i ** <span class="number">2</span> <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1</span>, <span class="number">11</span>) <span class="keyword">if</span> i % <span class="number">2</span> == <span class="number">0</span>]
<span class="function">print</span>(<span class="string">f"偶数的平方：{even_squares}"</span>)

<span class="comment"># 字符串处理</span>
words = [<span class="string">"python"</span>, <span class="string">"java"</span>, <span class="string">"javascript"</span>, <span class="string">"go"</span>]
uppercase_words = [word.upper() <span class="keyword">for</span> word <span class="keyword">in</span> words <span class="keyword">if</span> <span class="function">len</span>(word) > <span class="number">4</span>]
<span class="function">print</span>(<span class="string">f"长度大于4的单词（大写）：{uppercase_words}"</span>)</div>
                </div>

                <div class="tip-box">
                    <h3><span class="emoji">💡</span>高级技巧</h3>
                    <ul>
                        <li><strong>enumerate()：</strong>当需要同时获取索引和值时使用</li>
                        <li><strong>zip()：</strong>同时遍历多个序列，长度以最短的为准</li>
                        <li><strong>列表推导式：</strong>简洁地创建新列表，但不要过度复杂化</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习for循环进阶：</strong>"请解释Python中enumerate()和zip()函数的用法，以及列表推导式的语法和应用场景，并提供一些实际的代码示例。"</p>
                </div>
            </div>
        </div>

        <!-- 第10页：while循环 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">🔁</span>while循环</h1>
                <h2><span class="emoji">⏰</span>基于条件的循环</h2>
                <p>while循环在条件为True时重复执行代码块，直到条件变为False。它适用于不知道确切循环次数的情况。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># while循环基本语法</span>
<span class="keyword">while</span> 条件表达式:
    循环体代码
    <span class="comment"># 通常需要更新条件相关的变量</span>

<span class="comment"># 示例1：简单计数</span>
count = <span class="number">1</span>
<span class="function">print</span>(<span class="string">"开始计数："</span>)
<span class="keyword">while</span> count <= <span class="number">5</span>:
    <span class="function">print</span>(<span class="string">f"第{count}次"</span>)
    count += <span class="number">1</span>  <span class="comment"># 重要：更新条件变量</span>
<span class="function">print</span>(<span class="string">"计数结束"</span>)

<span class="comment"># 示例2：累加求和</span>
sum_result = <span class="number">0</span>
num = <span class="number">1</span>
<span class="keyword">while</span> num <= <span class="number">10</span>:
    sum_result += num
    num += <span class="number">1</span>
<span class="function">print</span>(<span class="string">f"1到10的和：{sum_result}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 示例3：用户输入验证</span>
password = <span class="string">""</span>
correct_password = <span class="string">"123456"</span>
attempts = <span class="number">0</span>
max_attempts = <span class="number">3</span>

<span class="keyword">while</span> password != correct_password <span class="keyword">and</span> attempts < max_attempts:
    password = <span class="function">input</span>(<span class="string">f"请输入密码（剩余{max_attempts - attempts}次机会）："</span>)
    attempts += <span class="number">1</span>
    
    <span class="keyword">if</span> password == correct_password:
        <span class="function">print</span>(<span class="string">"密码正确，登录成功！"</span>)
    <span class="keyword">elif</span> attempts < max_attempts:
        <span class="function">print</span>(<span class="string">"密码错误，请重试"</span>)
    <span class="keyword">else</span>:
        <span class="function">print</span>(<span class="string">"密码错误次数过多，账户被锁定"</span>)

<span class="comment"># 示例4：猜数字游戏</span>
<span class="keyword">import</span> random

target = random.randint(<span class="number">1</span>, <span class="number">100</span>)
guess = <span class="number">0</span>
count = <span class="number">0</span>

<span class="function">print</span>(<span class="string">"猜数字游戏开始！我想了一个1-100之间的数字"</span>)

<span class="keyword">while</span> guess != target:
    guess = <span class="function">int</span>(<span class="function">input</span>(<span class="string">"请输入你的猜测："</span>))
    count += <span class="number">1</span>
    
    <span class="keyword">if</span> guess < target:
        <span class="function">print</span>(<span class="string">"太小了！"</span>)
    <span class="keyword">elif</span> guess > target:
        <span class="function">print</span>(<span class="string">"太大了！"</span>)
    <span class="keyword">else</span>:
        <span class="function">print</span>(<span class="string">f"恭喜！你用{count}次猜中了数字{target}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 示例5：处理列表元素</span>
numbers = [<span class="number">1</span>, <span class="number">3</span>, <span class="number">5</span>, <span class="number">7</span>, <span class="number">9</span>, <span class="number">2</span>, <span class="number">4</span>, <span class="number">6</span>, <span class="number">8</span>]
index = <span class="number">0</span>
even_found = <span class="keyword">False</span>

<span class="function">print</span>(<span class="string">"查找第一个偶数："</span>)
<span class="keyword">while</span> index < <span class="function">len</span>(numbers) <span class="keyword">and</span> <span class="keyword">not</span> even_found:
    <span class="keyword">if</span> numbers[index] % <span class="number">2</span> == <span class="number">0</span>:
        <span class="function">print</span>(<span class="string">f"找到第一个偶数：{numbers[index]}，位置：{index}"</span>)
        even_found = <span class="keyword">True</span>
    <span class="keyword">else</span>:
        <span class="function">print</span>(<span class="string">f"检查 {numbers[index]}，不是偶数"</span>)
        index += <span class="number">1</span>

<span class="keyword">if</span> <span class="keyword">not</span> even_found:
    <span class="function">print</span>(<span class="string">"列表中没有偶数"</span>)</div>
                </div>

                <div class="warning-box">
                    <h3><span class="emoji">⚠️</span>注意事项</h3>
                    <ul>
                        <li><strong>避免无限循环：</strong>确保循环条件最终会变为False</li>
                        <li><strong>更新条件变量：</strong>在循环体内必须更新影响条件的变量</li>
                        <li><strong>初始化变量：</strong>在循环前正确初始化所有相关变量</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习while循环：</strong>"请解释Python中while循环的语法和使用场景，特别是如何避免无限循环，并提供一些实际的应用示例，比如用户输入验证、游戏逻辑等。"</p>
                </div>
            </div>
        </div>

        <!-- 第11页：循环控制语句 - break -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">🛑</span>循环控制语句 - break</h1>
                <h2><span class="emoji">🚪</span>提前退出循环</h2>
                <p>break语句用于立即终止循环，跳出当前循环体，继续执行循环后面的代码。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># break在for循环中的使用</span>
numbers = [<span class="number">1</span>, <span class="number">3</span>, <span class="number">5</span>, <span class="number">8</span>, <span class="number">9</span>, <span class="number">11</span>, <span class="number">13</span>]

<span class="function">print</span>(<span class="string">"查找第一个偶数："</span>)
<span class="keyword">for</span> num <span class="keyword">in</span> numbers:
    <span class="function">print</span>(<span class="string">f"检查数字：{num}"</span>)
    <span class="keyword">if</span> num % <span class="number">2</span> == <span class="number">0</span>:
        <span class="function">print</span>(<span class="string">f"找到偶数：{num}"</span>)
        <span class="keyword">break</span>  <span class="comment"># 找到偶数后立即退出循环</span>
    <span class="function">print</span>(<span class="string">f"{num} 是奇数，继续查找"</span>)

<span class="function">print</span>(<span class="string">"循环结束"</span>)

<span class="comment"># break在while循环中的使用</span>
count = <span class="number">0</span>
<span class="function">print</span>(<span class="string">"\n计数到5就停止："</span>)
<span class="keyword">while</span> <span class="keyword">True</span>:  <span class="comment"># 无限循环</span>
    count += <span class="number">1</span>
    <span class="function">print</span>(<span class="string">f"当前计数：{count}"</span>)
    <span class="keyword">if</span> count >= <span class="number">5</span>:
        <span class="function">print</span>(<span class="string">"达到目标，退出循环"</span>)
        <span class="keyword">break</span>

<span class="function">print</span>(<span class="string">"while循环结束"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 实际应用：菜单系统</span>
<span class="keyword">def</span> <span class="function">show_menu</span>():
    <span class="function">print</span>(<span class="string">"\n=== 学生管理系统 ==="</span>)
    <span class="function">print</span>(<span class="string">"1. 添加学生"</span>)
    <span class="function">print</span>(<span class="string">"2. 查看学生"</span>)
    <span class="function">print</span>(<span class="string">"3. 删除学生"</span>)
    <span class="function">print</span>(<span class="string">"4. 退出系统"</span>)

students = []

<span class="keyword">while</span> <span class="keyword">True</span>:
    show_menu()
    choice = <span class="function">input</span>(<span class="string">"请选择操作（1-4）："</span>)
    
    <span class="keyword">if</span> choice == <span class="string">"1"</span>:
        name = <span class="function">input</span>(<span class="string">"请输入学生姓名："</span>)
        students.append(name)
        <span class="function">print</span>(<span class="string">f"学生 {name} 添加成功！"</span>)
    
    <span class="keyword">elif</span> choice == <span class="string">"2"</span>:
        <span class="keyword">if</span> students:
            <span class="function">print</span>(<span class="string">"学生列表："</span>)
            <span class="keyword">for</span> i, student <span class="keyword">in</span> <span class="function">enumerate</span>(students, <span class="number">1</span>):
                <span class="function">print</span>(<span class="string">f"{i}. {student}"</span>)
        <span class="keyword">else</span>:
            <span class="function">print</span>(<span class="string">"暂无学生信息"</span>)
    
    <span class="keyword">elif</span> choice == <span class="string">"3"</span>:
        <span class="keyword">if</span> students:
            name = <span class="function">input</span>(<span class="string">"请输入要删除的学生姓名："</span>)
            <span class="keyword">if</span> name <span class="keyword">in</span> students:
                students.remove(name)
                <span class="function">print</span>(<span class="string">f"学生 {name} 删除成功！"</span>)
            <span class="keyword">else</span>:
                <span class="function">print</span>(<span class="string">"未找到该学生"</span>)
        <span class="keyword">else</span>:
            <span class="function">print</span>(<span class="string">"暂无学生信息"</span>)
    
    <span class="keyword">elif</span> choice == <span class="string">"4"</span>:
        <span class="function">print</span>(<span class="string">"感谢使用，再见！"</span>)
        <span class="keyword">break</span>  <span class="comment"># 退出主循环</span>
    
    <span class="keyword">else</span>:
        <span class="function">print</span>(<span class="string">"无效选择，请重新输入"</span>)</div>
                </div>

                <div class="tip-box">
                    <h3><span class="emoji">💡</span>使用场景</h3>
                    <ul>
                        <li><strong>查找操作：</strong>找到目标后立即停止</li>
                        <li><strong>用户交互：</strong>用户选择退出时终止程序</li>
                        <li><strong>错误处理：</strong>遇到错误条件时提前退出</li>
                        <li><strong>性能优化：</strong>避免不必要的循环继续执行</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习break语句：</strong>"请解释Python中break语句的作用和使用场景，并提供一些实际的应用示例，比如在搜索、菜单系统、游戏循环中的使用。"</p>
                </div>
            </div>
        </div>

        <!-- 第12页：循环控制语句 - continue -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">⏭️</span>循环控制语句 - continue</h1>
                <h2><span class="emoji">🔄</span>跳过当前迭代</h2>
                <p>continue语句用于跳过当前循环迭代的剩余代码，直接进入下一次循环迭代。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># continue在for循环中的使用</span>
numbers = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>, <span class="number">7</span>, <span class="number">8</span>, <span class="number">9</span>, <span class="number">10</span>]

<span class="function">print</span>(<span class="string">"只打印偶数："</span>)
<span class="keyword">for</span> num <span class="keyword">in</span> numbers:
    <span class="keyword">if</span> num % <span class="number">2</span> != <span class="number">0</span>:  <span class="comment"># 如果是奇数</span>
        <span class="keyword">continue</span>  <span class="comment"># 跳过后面的代码，进入下一次循环</span>
    <span class="function">print</span>(<span class="string">f"偶数：{num}"</span>)

<span class="comment"># 等价的写法（不使用continue）</span>
<span class="function">print</span>(<span class="string">"\n不使用continue的等价写法："</span>)
<span class="keyword">for</span> num <span class="keyword">in</span> numbers:
    <span class="keyword">if</span> num % <span class="number">2</span> == <span class="number">0</span>:  <span class="comment"># 如果是偶数</span>
        <span class="function">print</span>(<span class="string">f"偶数：{num}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 实际应用：处理学生成绩</span>
students = [
    {<span class="string">"name"</span>: <span class="string">"张三"</span>, <span class="string">"score"</span>: <span class="number">85</span>, <span class="string">"status"</span>: <span class="string">"active"</span>},
    {<span class="string">"name"</span>: <span class="string">"李四"</span>, <span class="string">"score"</span>: <span class="number">92</span>, <span class="string">"status"</span>: <span class="string">"inactive"</span>},
    {<span class="string">"name"</span>: <span class="string">"王五"</span>, <span class="string">"score"</span>: <span class="number">78</span>, <span class="string">"status"</span>: <span class="string">"active"</span>},
    {<span class="string">"name"</span>: <span class="string">"赵六"</span>, <span class="string">"score"</span>: <span class="number">96</span>, <span class="string">"status"</span>: <span class="string">"active"</span>},
    {<span class="string">"name"</span>: <span class="string">"钱七"</span>, <span class="string">"score"</span>: <span class="number">65</span>, <span class="string">"status"</span>: <span class="string">"inactive"</span>}
]

<span class="function">print</span>(<span class="string">"活跃学生的优秀成绩（>=90分）："</span>)
<span class="keyword">for</span> student <span class="keyword">in</span> students:
    <span class="comment"># 跳过非活跃学生</span>
    <span class="keyword">if</span> student[<span class="string">"status"</span>] != <span class="string">"active"</span>:
        <span class="keyword">continue</span>
    
    <span class="comment"># 跳过成绩不够优秀的学生</span>
    <span class="keyword">if</span> student[<span class="string">"score"</span>] < <span class="number">90</span>:
        <span class="keyword">continue</span>
    
    <span class="function">print</span>(<span class="string">f"{student['name']}: {student['score']}分"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># continue在while循环中的使用</span>
count = <span class="number">0</span>
sum_even = <span class="number">0</span>

<span class="function">print</span>(<span class="string">"计算1到10中偶数的和："</span>)
<span class="keyword">while</span> count < <span class="number">10</span>:
    count += <span class="number">1</span>
    
    <span class="keyword">if</span> count % <span class="number">2</span> != <span class="number">0</span>:  <span class="comment"># 如果是奇数</span>
        <span class="function">print</span>(<span class="string">f"{count} 是奇数，跳过"</span>)
        <span class="keyword">continue</span>
    
    <span class="function">print</span>(<span class="string">f"{count} 是偶数，加入计算"</span>)
    sum_even += count

<span class="function">print</span>(<span class="string">f"1到10中偶数的和：{sum_even}"</span>)

<span class="comment"># 处理用户输入（跳过无效输入）</span>
valid_numbers = []
count = <span class="number">0</span>

<span class="function">print</span>(<span class="string">"\n请输入5个正整数："</span>)
<span class="keyword">while</span> <span class="function">len</span>(valid_numbers) < <span class="number">5</span>:
    count += <span class="number">1</span>
    user_input = <span class="function">input</span>(<span class="string">f"第{count}个数字："</span>)
    
    <span class="comment"># 检查输入是否为数字</span>
    <span class="keyword">if</span> <span class="keyword">not</span> user_input.isdigit():
        <span class="function">print</span>(<span class="string">"输入无效，请输入正整数"</span>)
        <span class="keyword">continue</span>
    
    number = <span class="function">int</span>(user_input)
    
    <span class="comment"># 检查是否为正数</span>
    <span class="keyword">if</span> number <= <span class="number">0</span>:
        <span class="function">print</span>(<span class="string">"请输入正整数"</span>)
        <span class="keyword">continue</span>
    
    valid_numbers.append(number)
    <span class="function">print</span>(<span class="string">f"有效输入：{number}"</span>)

<span class="function">print</span>(<span class="string">f"收集到的数字：{valid_numbers}"</span>)</div>
                </div>

                <div class="tip-box">
                    <h3><span class="emoji">💡</span>continue vs break</h3>
                    <ul>
                        <li><strong>continue：</strong>跳过当前迭代，继续下一次循环</li>
                        <li><strong>break：</strong>完全退出循环</li>
                        <li><strong>使用场景：</strong>continue适用于过滤不符合条件的数据</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习continue语句：</strong>"请解释Python中continue语句的作用，并与break语句进行对比，提供一些实际的应用场景，比如数据过滤、输入验证等。"</p>
                </div>
            </div>
        </div>

        <!-- 第13页：嵌套循环 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">🔄🔄</span>嵌套循环</h1>
                <h2><span class="emoji">🎯</span>循环中的循环</h2>
                <p>嵌套循环是指在一个循环内部包含另一个循环，常用于处理二维数据结构或需要多层遍历的场景。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># 基本的嵌套循环示例</span>
<span class="function">print</span>(<span class="string">"九九乘法表："</span>)
<span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1</span>, <span class="number">10</span>):
    <span class="keyword">for</span> j <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1</span>, i + <span class="number">1</span>):
        result = i * j
        <span class="function">print</span>(<span class="string">f"{j}×{i}={result}"</span>, end=<span class="string">"\t"</span>)
    <span class="function">print</span>()  <span class="comment"># 换行</span>

<span class="comment"># 处理二维列表</span>
matrix = [
    [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>],
    [<span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>],
    [<span class="number">7</span>, <span class="number">8</span>, <span class="number">9</span>]
]

<span class="function">print</span>(<span class="string">"\n矩阵遍历："</span>)
<span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="function">len</span>(matrix)):
    <span class="keyword">for</span> j <span class="keyword">in</span> <span class="function">range</span>(<span class="function">len</span>(matrix[i])):
        <span class="function">print</span>(<span class="string">f"matrix[{i}][{j}] = {matrix[i][j]}"</span>)

<span class="comment"># 更简洁的写法</span>
<span class="function">print</span>(<span class="string">"\n简洁的矩阵遍历："</span>)
<span class="keyword">for</span> row_index, row <span class="keyword">in</span> <span class="function">enumerate</span>(matrix):
    <span class="keyword">for</span> col_index, value <span class="keyword">in</span> <span class="function">enumerate</span>(row):
        <span class="function">print</span>(<span class="string">f"位置({row_index},{col_index}): {value}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 实际应用：学生成绩统计</span>
classes = {
    <span class="string">"一班"</span>: [<span class="number">85</span>, <span class="number">92</span>, <span class="number">78</span>, <span class="number">96</span>, <span class="number">88</span>],
    <span class="string">"二班"</span>: [<span class="number">90</span>, <span class="number">87</span>, <span class="number">93</span>, <span class="number">82</span>, <span class="number">95</span>],
    <span class="string">"三班"</span>: [<span class="number">88</span>, <span class="number">91</span>, <span class="number">85</span>, <span class="number">89</span>, <span class="number">92</span>]
}

<span class="function">print</span>(<span class="string">"各班成绩统计："</span>)
<span class="keyword">for</span> class_name, scores <span class="keyword">in</span> classes.items():
    <span class="function">print</span>(<span class="string">f"\n{class_name}："</span>)
    total = <span class="number">0</span>
    excellent_count = <span class="number">0</span>
    
    <span class="keyword">for</span> i, score <span class="keyword">in</span> <span class="function">enumerate</span>(scores, <span class="number">1</span>):
        total += score
        <span class="keyword">if</span> score >= <span class="number">90</span>:
            excellent_count += <span class="number">1</span>
            <span class="function">print</span>(<span class="string">f"  学生{i}: {score}分 ⭐优秀"</span>)
        <span class="keyword">else</span>:
            <span class="function">print</span>(<span class="string">f"  学生{i}: {score}分"</span>)
    
    average = total / <span class="function">len</span>(scores)
    <span class="function">print</span>(<span class="string">f"  平均分: {average:.1f}"</span>)
    <span class="function">print</span>(<span class="string">f"  优秀人数: {excellent_count}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 嵌套循环中的break和continue</span>
<span class="function">print</span>(<span class="string">"查找矩阵中的特定值："</span>)
matrix = [
    [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>],
    [<span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>],
    [<span class="number">7</span>, <span class="number">8</span>, <span class="number">9</span>]
]

target = <span class="number">5</span>
found = <span class="keyword">False</span>

<span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="function">len</span>(matrix)):
    <span class="keyword">for</span> j <span class="keyword">in</span> <span class="function">range</span>(<span class="function">len</span>(matrix[i])):
        <span class="function">print</span>(<span class="string">f"检查位置({i},{j}): {matrix[i][j]}"</span>)
        <span class="keyword">if</span> matrix[i][j] == target:
            <span class="function">print</span>(<span class="string">f"找到目标值{target}，位置：({i},{j})"</span>)
            found = <span class="keyword">True</span>
            <span class="keyword">break</span>  <span class="comment"># 跳出内层循环</span>
    <span class="keyword">if</span> found:
        <span class="keyword">break</span>  <span class="comment"># 跳出外层循环</span>

<span class="keyword">if</span> <span class="keyword">not</span> found:
    <span class="function">print</span>(<span class="string">f"未找到目标值{target}"</span>)

<span class="comment"># 使用标签的方式（Python不支持，这里展示逻辑）</span>
<span class="comment"># 更好的方式是使用函数</span>
<span class="keyword">def</span> <span class="function">find_in_matrix</span>(matrix, target):
    <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="function">len</span>(matrix)):
        <span class="keyword">for</span> j <span class="keyword">in</span> <span class="function">range</span>(<span class="function">len</span>(matrix[i])):
            <span class="keyword">if</span> matrix[i][j] == target:
                <span class="keyword">return</span> (i, j)  <span class="comment"># 直接返回，退出所有循环</span>
    <span class="keyword">return</span> <span class="keyword">None</span>

result = find_in_matrix(matrix, <span class="number">8</span>)
<span class="keyword">if</span> result:
    <span class="function">print</span>(<span class="string">f"\n使用函数查找：找到8，位置：{result}"</span>)
<span class="keyword">else</span>:
    <span class="function">print</span>(<span class="string">"\n使用函数查找：未找到目标值"</span>)</div>
                </div>

                <div class="warning-box">
                    <h3><span class="emoji">⚠️</span>性能注意事项</h3>
                    <ul>
                        <li>嵌套循环的时间复杂度是各层循环的乘积</li>
                        <li>避免不必要的深层嵌套</li>
                        <li>考虑使用函数来简化复杂的嵌套逻辑</li>
                        <li>在合适的时候使用break来提前退出</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习嵌套循环：</strong>"请解释Python中嵌套循环的概念和应用场景，包括如何在嵌套循环中使用break和continue，并提供一些实际的代码示例，比如矩阵操作、数据统计等。"</p>
                </div>
            </div>
        </div>

        <!-- 第14页：异常处理基础 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">🛡️</span>异常处理基础</h1>
                <h2><span class="emoji">⚠️</span>try-except语句</h2>
                <p>异常处理是程序健壮性的重要保障，让程序能够优雅地处理错误情况而不是直接崩溃。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># 基本的异常处理语法</span>
<span class="keyword">try</span>:
    <span class="comment"># 可能出现异常的代码</span>
    result = <span class="number">10</span> / <span class="number">0</span>
    <span class="function">print</span>(<span class="string">f"结果：{result}"</span>)
<span class="keyword">except</span> <span class="function">ZeroDivisionError</span>:
    <span class="comment"># 处理特定异常</span>
    <span class="function">print</span>(<span class="string">"错误：不能除以零！"</span>)

<span class="comment"># 捕获多种异常</span>
<span class="keyword">try</span>:
    num = <span class="function">int</span>(<span class="function">input</span>(<span class="string">"请输入一个数字："</span>))
    result = <span class="number">100</span> / num
    <span class="function">print</span>(<span class="string">f"100除以{num}等于{result}"</span>)
<span class="keyword">except</span> <span class="function">ValueError</span>:
    <span class="function">print</span>(<span class="string">"错误：输入的不是有效数字！"</span>)
<span class="keyword">except</span> <span class="function">ZeroDivisionError</span>:
    <span class="function">print</span>(<span class="string">"错误：不能除以零！"</span>)

<span class="comment"># 捕获所有异常</span>
<span class="keyword">try</span>:
    <span class="comment"># 一些可能出错的操作</span>
    data = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>]
    <span class="function">print</span>(data[<span class="number">10</span>])  <span class="comment"># 索引超出范围</span>
<span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
    <span class="function">print</span>(<span class="string">f"发生了异常：{e}"</span>)
    <span class="function">print</span>(<span class="string">f"异常类型：{type(e).__name__}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 完整的异常处理结构</span>
<span class="keyword">def</span> <span class="function">safe_divide</span>(a, b):
    <span class="keyword">try</span>:
        result = a / b
        <span class="function">print</span>(<span class="string">f"计算成功：{a} ÷ {b} = {result}"</span>)
        <span class="keyword">return</span> result
    <span class="keyword">except</span> <span class="function">ZeroDivisionError</span>:
        <span class="function">print</span>(<span class="string">"错误：除数不能为零"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
    <span class="keyword">except</span> <span class="function">TypeError</span>:
        <span class="function">print</span>(<span class="string">"错误：参数类型不正确"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
    <span class="keyword">else</span>:
        <span class="comment"># 没有异常时执行</span>
        <span class="function">print</span>(<span class="string">"除法运算完成，没有异常"</span>)
    <span class="keyword">finally</span>:
        <span class="comment"># 无论是否有异常都会执行</span>
        <span class="function">print</span>(<span class="string">"除法运算结束"</span>)

<span class="comment"># 测试函数</span>
<span class="function">print</span>(<span class="string">"=== 测试安全除法函数 ==="</span>)
safe_divide(<span class="number">10</span>, <span class="number">2</span>)   <span class="comment"># 正常情况</span>
<span class="function">print</span>()
safe_divide(<span class="number">10</span>, <span class="number">0</span>)   <span class="comment"># 除零异常</span>
<span class="function">print</span>()
safe_divide(<span class="number">10</span>, <span class="string">"a"</span>)  <span class="comment"># 类型错误</span></div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 实际应用：文件操作异常处理</span>
<span class="keyword">def</span> <span class="function">read_file_safely</span>(filename):
    <span class="keyword">try</span>:
        <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
            content = file.read()
            <span class="function">print</span>(<span class="string">f"文件读取成功，内容长度：{len(content)}字符"</span>)
            <span class="keyword">return</span> content
    <span class="keyword">except</span> <span class="function">FileNotFoundError</span>:
        <span class="function">print</span>(<span class="string">f"错误：文件'{filename}'不存在"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
    <span class="keyword">except</span> <span class="function">PermissionError</span>:
        <span class="function">print</span>(<span class="string">f"错误：没有权限读取文件'{filename}'"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
    <span class="keyword">except</span> <span class="function">UnicodeDecodeError</span>:
        <span class="function">print</span>(<span class="string">f"错误：文件'{filename}'编码格式不正确"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
    <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
        <span class="function">print</span>(<span class="string">f"读取文件时发生未知错误：{e}"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>

<span class="comment"># 测试文件读取</span>
<span class="function">print</span>(<span class="string">"=== 测试文件读取 ==="</span>)
read_file_safely(<span class="string">"存在的文件.txt"</span>)
read_file_safely(<span class="string">"不存在的文件.txt"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 用户输入验证的异常处理</span>
<span class="keyword">def</span> <span class="function">get_valid_age</span>():
    <span class="keyword">while</span> <span class="keyword">True</span>:
        <span class="keyword">try</span>:
            age_input = <span class="function">input</span>(<span class="string">"请输入您的年龄："</span>)
            age = <span class="function">int</span>(age_input)
            
            <span class="keyword">if</span> age < <span class="number">0</span>:
                <span class="keyword">raise</span> <span class="function">ValueError</span>(<span class="string">"年龄不能为负数"</span>)
            <span class="keyword">elif</span> age > <span class="number">150</span>:
                <span class="keyword">raise</span> <span class="function">ValueError</span>(<span class="string">"年龄不能超过150岁"</span>)
            
            <span class="function">print</span>(<span class="string">f"您的年龄是：{age}岁"</span>)
            <span class="keyword">return</span> age
            
        <span class="keyword">except</span> <span class="function">ValueError</span> <span class="keyword">as</span> e:
            <span class="keyword">if</span> <span class="string">"invalid literal"</span> <span class="keyword">in</span> <span class="function">str</span>(e):
                <span class="function">print</span>(<span class="string">"错误：请输入有效的数字！"</span>)
            <span class="keyword">else</span>:
                <span class="function">print</span>(<span class="string">f"错误：{e}"</span>)
        <span class="keyword">except</span> <span class="function">KeyboardInterrupt</span>:
            <span class="function">print</span>(<span class="string">"\n用户取消了输入"</span>)
            <span class="keyword">return</span> <span class="keyword">None</span>
        <span class="keyword">except</span> <span class="function">EOFError</span>:
            <span class="function">print</span>(<span class="string">"\n输入结束"</span>)
            <span class="keyword">return</span> <span class="keyword">None</span>

<span class="comment"># 调用函数（在实际环境中使用）</span>
<span class="comment"># age = get_valid_age()</span></div>
                </div>

                <div class="warning-box">
                    <h3><span class="emoji">💡</span>异常处理最佳实践</h3>
                    <ul>
                        <li>具体捕获：优先捕获具体的异常类型，而不是使用通用的Exception</li>
                        <li>适度使用：不要过度使用异常处理，正常的程序逻辑不应该依赖异常</li>
                        <li>记录日志：在生产环境中，应该记录异常信息用于调试</li>
                        <li>资源清理：使用finally或with语句确保资源得到正确释放</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习异常处理：</strong>"请详细解释Python中的异常处理机制，包括try-except-else-finally的用法，常见的异常类型，以及异常处理的最佳实践。请提供一些实际的代码示例。"</p>
                </div>
            </div>
        </div>

        <!-- 第15页：函数定义和调用 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">🔧</span>函数定义和调用</h1>
                <h2><span class="emoji">📦</span>代码的模块化</h2>
                <p>函数是组织代码的基本单位，可以将复杂的问题分解为更小、更易管理的部分，提高代码的可重用性和可维护性。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># 基本函数定义和调用</span>
<span class="keyword">def</span> <span class="function">greet</span>():
    <span class="string">"""简单的问候函数"""</span>
    <span class="function">print</span>(<span class="string">"Hello, World!"</span>)

<span class="comment"># 调用函数</span>
greet()  <span class="comment"># 输出: Hello, World!</span>

<span class="comment"># 带参数的函数</span>
<span class="keyword">def</span> <span class="function">greet_person</span>(name):
    <span class="string">"""带参数的问候函数"""</span>
    <span class="function">print</span>(<span class="string">f"Hello, {name}!"</span>)

<span class="comment"># 调用带参数的函数</span>
greet_person(<span class="string">"Alice"</span>)  <span class="comment"># 输出: Hello, Alice!</span>
greet_person(<span class="string">"Bob"</span>)    <span class="comment"># 输出: Hello, Bob!</span>

<span class="comment"># 带返回值的函数</span>
<span class="keyword">def</span> <span class="function">add_numbers</span>(a, b):
    <span class="string">"""计算两个数的和"""</span>
    result = a + b
    <span class="keyword">return</span> result

<span class="comment"># 使用返回值</span>
sum_result = add_numbers(<span class="number">5</span>, <span class="number">3</span>)
<span class="function">print</span>(<span class="string">f"5 + 3 = {sum_result}"</span>)  <span class="comment"># 输出: 5 + 3 = 8</span>

<span class="comment"># 直接在表达式中使用函数</span>
total = add_numbers(<span class="number">10</span>, <span class="number">20</span>) + add_numbers(<span class="number">5</span>, <span class="number">15</span>)
<span class="function">print</span>(<span class="string">f"总和: {total}"</span>)  <span class="comment"># 输出: 总和: 50</span></div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 函数参数的不同类型</span>
<span class="comment"># 1. 默认参数</span>
<span class="keyword">def</span> <span class="function">greet_with_title</span>(name, title=<span class="string">"先生/女士"</span>):
    <span class="string">"""带默认参数的问候函数"""</span>
    <span class="function">print</span>(<span class="string">f"您好，{title} {name}!"</span>)

greet_with_title(<span class="string">"张三"</span>)           <span class="comment"># 使用默认title</span>
greet_with_title(<span class="string">"李四"</span>, <span class="string">"博士"</span>)    <span class="comment"># 指定title</span>

<span class="comment"># 2. 关键字参数</span>
<span class="keyword">def</span> <span class="function">create_profile</span>(name, age, city, job):
    <span class="string">"""创建用户档案"""</span>
    profile = <span class="string">f"姓名: {name}, 年龄: {age}, 城市: {city}, 职业: {job}"</span>
    <span class="keyword">return</span> profile

<span class="comment"># 使用位置参数</span>
profile1 = create_profile(<span class="string">"王五"</span>, <span class="number">25</span>, <span class="string">"北京"</span>, <span class="string">"程序员"</span>)
<span class="function">print</span>(profile1)

<span class="comment"># 使用关键字参数（可以改变顺序）</span>
profile2 = create_profile(job=<span class="string">"设计师"</span>, name=<span class="string">"赵六"</span>, city=<span class="string">"上海"</span>, age=<span class="number">28</span>)
<span class="function">print</span>(profile2)

<span class="comment"># 3. 可变参数</span>
<span class="keyword">def</span> <span class="function">calculate_sum</span>(*numbers):
    <span class="string">"""计算任意数量数字的和"""</span>
    total = <span class="number">0</span>
    <span class="keyword">for</span> num <span class="keyword">in</span> numbers:
        total += num
    <span class="keyword">return</span> total

<span class="function">print</span>(<span class="string">f"sum(1, 2, 3) = {calculate_sum(1, 2, 3)}"</span>)
<span class="function">print</span>(<span class="string">f"sum(1, 2, 3, 4, 5) = {calculate_sum(1, 2, 3, 4, 5)}"</span>)

<span class="comment"># 4. 关键字可变参数</span>
<span class="keyword">def</span> <span class="function">create_student</span>(name, **kwargs):
    <span class="string">"""创建学生信息"""</span>
    <span class="function">print</span>(<span class="string">f"学生姓名: {name}"</span>)
    <span class="keyword">for</span> key, value <span class="keyword">in</span> kwargs.items():
        <span class="function">print</span>(<span class="string">f"{key}: {value}"</span>)

create_student(<span class="string">"小明"</span>, age=<span class="number">18</span>, grade=<span class="string">"高三"</span>, hobby=<span class="string">"编程"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 实际应用：数学计算函数库</span>
<span class="keyword">def</span> <span class="function">is_prime</span>(n):
    <span class="string">"""判断一个数是否为质数"""</span>
    <span class="keyword">if</span> n < <span class="number">2</span>:
        <span class="keyword">return</span> <span class="keyword">False</span>
    <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">2</span>, <span class="function">int</span>(n ** <span class="number">0.5</span>) + <span class="number">1</span>):
        <span class="keyword">if</span> n % i == <span class="number">0</span>:
            <span class="keyword">return</span> <span class="keyword">False</span>
    <span class="keyword">return</span> <span class="keyword">True</span>

<span class="keyword">def</span> <span class="function">factorial</span>(n):
    <span class="string">"""计算阶乘"""</span>
    <span class="keyword">if</span> n < <span class="number">0</span>:
        <span class="keyword">raise</span> <span class="function">ValueError</span>(<span class="string">"阶乘不能计算负数"</span>)
    <span class="keyword">if</span> n == <span class="number">0</span> <span class="keyword">or</span> n == <span class="number">1</span>:
        <span class="keyword">return</span> <span class="number">1</span>
    result = <span class="number">1</span>
    <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">2</span>, n + <span class="number">1</span>):
        result *= i
    <span class="keyword">return</span> result

<span class="keyword">def</span> <span class="function">fibonacci</span>(n):
    <span class="string">"""生成斐波那契数列的前n项"""</span>
    <span class="keyword">if</span> n <= <span class="number">0</span>:
        <span class="keyword">return</span> []
    <span class="keyword">elif</span> n == <span class="number">1</span>:
        <span class="keyword">return</span> [<span class="number">0</span>]
    <span class="keyword">elif</span> n == <span class="number">2</span>:
        <span class="keyword">return</span> [<span class="number">0</span>, <span class="number">1</span>]
    
    fib_list = [<span class="number">0</span>, <span class="number">1</span>]
    <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">2</span>, n):
        fib_list.append(fib_list[i-<span class="number">1</span>] + fib_list[i-<span class="number">2</span>])
    <span class="keyword">return</span> fib_list

<span class="comment"># 测试数学函数</span>
<span class="function">print</span>(<span class="string">"=== 数学函数测试 ==="</span>)
<span class="function">print</span>(<span class="string">f"17是质数吗？ {is_prime(17)}"</span>)
<span class="function">print</span>(<span class="string">f"5的阶乘: {factorial(5)}"</span>)
<span class="function">print</span>(<span class="string">f"斐波那契数列前10项: {fibonacci(10)}"</span>)

<span class="comment"># 查找范围内的质数</span>
<span class="keyword">def</span> <span class="function">find_primes_in_range</span>(start, end):
    <span class="string">"""查找指定范围内的所有质数"""</span>
    primes = []
    <span class="keyword">for</span> num <span class="keyword">in</span> <span class="function">range</span>(start, end + <span class="number">1</span>):
        <span class="keyword">if</span> is_prime(num):
            primes.append(num)
    <span class="keyword">return</span> primes

primes_1_to_30 = find_primes_in_range(<span class="number">1</span>, <span class="number">30</span>)
<span class="function">print</span>(<span class="string">f"1到30之间的质数: {primes_1_to_30}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 函数作为参数和返回值</span>
<span class="keyword">def</span> <span class="function">apply_operation</span>(numbers, operation):
    <span class="string">"""对数字列表应用指定操作"""</span>
    result = []
    <span class="keyword">for</span> num <span class="keyword">in</span> numbers:
        result.append(operation(num))
    <span class="keyword">return</span> result

<span class="keyword">def</span> <span class="function">square</span>(x):
    <span class="string">"""计算平方"""</span>
    <span class="keyword">return</span> x ** <span class="number">2</span>

<span class="keyword">def</span> <span class="function">cube</span>(x):
    <span class="string">"""计算立方"""</span>
    <span class="keyword">return</span> x ** <span class="number">3</span>

numbers = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>]
<span class="function">print</span>(<span class="string">f"原数列: {numbers}"</span>)
<span class="function">print</span>(<span class="string">f"平方: {apply_operation(numbers, square)}"</span>)
<span class="function">print</span>(<span class="string">f"立方: {apply_operation(numbers, cube)}"</span>)

<span class="comment"># 返回函数的函数</span>
<span class="keyword">def</span> <span class="function">create_multiplier</span>(factor):
    <span class="string">"""创建一个乘法函数"""</span>
    <span class="keyword">def</span> <span class="function">multiplier</span>(x):
        <span class="keyword">return</span> x * factor
    <span class="keyword">return</span> multiplier

<span class="comment"># 创建不同的乘法函数</span>
double = create_multiplier(<span class="number">2</span>)
triple = create_multiplier(<span class="number">3</span>)

<span class="function">print</span>(<span class="string">f"\n使用动态创建的函数:"</span>)
<span class="function">print</span>(<span class="string">f"5的两倍: {double(5)}"</span>)
<span class="function">print</span>(<span class="string">f"5的三倍: {triple(5)}"</span>)</div>
                </div>

                <div class="warning-box">
                    <h3><span class="emoji">💡</span>函数设计原则</h3>
                    <ul>
                        <li>单一职责：每个函数应该只做一件事情</li>
                        <li>参数合理：避免过多的参数，考虑使用对象或字典</li>
                        <li>命名清晰：函数名应该清楚地表达其功能</li>
                        <li>文档字符串：为函数添加说明文档</li>
                        <li>返回值一致：函数应该有一致的返回值类型</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习函数：</strong>"请详细解释Python中函数的定义和调用，包括参数类型（位置参数、关键字参数、默认参数、可变参数），返回值，以及函数的最佳实践。请提供一些实际的代码示例。"</p>
                </div>
            </div>
        </div>

        <!-- 第16页：递归函数 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">🔄</span>递归函数</h1>
                <h2><span class="emoji">🪆</span>函数调用自身</h2>
                <p>递归是一种强大的编程技术，函数可以调用自身来解决问题。递归特别适合处理具有自相似结构的问题。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># 递归的基本概念</span>
<span class="comment"># 递归函数必须有两个要素：</span>
<span class="comment"># 1. 基础情况（递归终止条件）</span>
<span class="comment"># 2. 递归情况（函数调用自身）</span>

<span class="comment"># 经典例子：计算阶乘</span>
<span class="keyword">def</span> <span class="function">factorial_recursive</span>(n):
    <span class="string">"""使用递归计算阶乘"""</span>
    <span class="comment"># 基础情况</span>
    <span class="keyword">if</span> n == <span class="number">0</span> <span class="keyword">or</span> n == <span class="number">1</span>:
        <span class="keyword">return</span> <span class="number">1</span>
    <span class="comment"># 递归情况</span>
    <span class="keyword">else</span>:
        <span class="keyword">return</span> n * factorial_recursive(n - <span class="number">1</span>)

<span class="comment"># 测试递归阶乘</span>
<span class="function">print</span>(<span class="string">"=== 递归阶乘 ==="</span>)
<span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">6</span>):
    result = factorial_recursive(i)
    <span class="function">print</span>(<span class="string">f"{i}! = {result}"</span>)

<span class="comment"># 斐波那契数列的递归实现</span>
<span class="keyword">def</span> <span class="function">fibonacci_recursive</span>(n):
    <span class="string">"""使用递归计算斐波那契数列第n项"""</span>
    <span class="comment"># 基础情况</span>
    <span class="keyword">if</span> n <= <span class="number">1</span>:
        <span class="keyword">return</span> n
    <span class="comment"># 递归情况</span>
    <span class="keyword">else</span>:
        <span class="keyword">return</span> fibonacci_recursive(n - <span class="number">1</span>) + fibonacci_recursive(n - <span class="number">2</span>)

<span class="function">print</span>(<span class="string">"\n=== 递归斐波那契 ==="</span>)
<span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">10</span>):
    result = fibonacci_recursive(i)
    <span class="function">print</span>(<span class="string">f"F({i}) = {result}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 递归的实际应用：文件系统遍历</span>
<span class="keyword">import</span> os

<span class="keyword">def</span> <span class="function">list_files_recursive</span>(directory, level=<span class="number">0</span>):
    <span class="string">"""递归列出目录中的所有文件和子目录"""</span>
    <span class="keyword">try</span>:
        items = os.listdir(directory)
        <span class="keyword">for</span> item <span class="keyword">in</span> items:
            item_path = os.path.join(directory, item)
            indent = <span class="string">"  "</span> * level  <span class="comment"># 缩进表示层级</span>
            
            <span class="keyword">if</span> os.path.isdir(item_path):
                <span class="function">print</span>(<span class="string">f"{indent}📁 {item}/"</span>)
                <span class="comment"># 递归调用处理子目录</span>
                list_files_recursive(item_path, level + <span class="number">1</span>)
            <span class="keyword">else</span>:
                <span class="function">print</span>(<span class="string">f"{indent}📄 {item}"</span>)
    <span class="keyword">except</span> <span class="function">PermissionError</span>:
        <span class="function">print</span>(<span class="string">f"{indent}❌ 权限不足"</span>)

<span class="comment"># 使用示例（注释掉避免实际执行）</span>
<span class="comment"># list_files_recursive("/path/to/directory")</span>

<span class="comment"># 递归计算目录大小</span>
<span class="keyword">def</span> <span class="function">calculate_directory_size</span>(directory):
    <span class="string">"""递归计算目录总大小"""</span>
    total_size = <span class="number">0</span>
    <span class="keyword">try</span>:
        <span class="keyword">for</span> item <span class="keyword">in</span> os.listdir(directory):
            item_path = os.path.join(directory, item)
            <span class="keyword">if</span> os.path.isfile(item_path):
                total_size += os.path.getsize(item_path)
            <span class="keyword">elif</span> os.path.isdir(item_path):
                <span class="comment"># 递归计算子目录大小</span>
                total_size += calculate_directory_size(item_path)
    <span class="keyword">except</span> <span class="function">PermissionError</span>:
        <span class="function">print</span>(<span class="string">f"无法访问目录: {directory}"</span>)
    <span class="keyword">return</span> total_size</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 递归解决数学问题</span>
<span class="comment"># 汉诺塔问题</span>
<span class="keyword">def</span> <span class="function">hanoi</span>(n, source, destination, auxiliary):
    <span class="string">"""解决汉诺塔问题"""</span>
    <span class="keyword">if</span> n == <span class="number">1</span>:
        <span class="function">print</span>(<span class="string">f"移动盘子1从 {source} 到 {destination}"</span>)
    <span class="keyword">else</span>:
        <span class="comment"># 将前n-1个盘子移动到辅助柱</span>
        hanoi(n - <span class="number">1</span>, source, auxiliary, destination)
        <span class="comment"># 移动最大的盘子到目标柱</span>
        <span class="function">print</span>(<span class="string">f"移动盘子{n}从 {source} 到 {destination}"</span>)
        <span class="comment"># 将n-1个盘子从辅助柱移动到目标柱</span>
        hanoi(n - <span class="number">1</span>, auxiliary, destination, source)

<span class="function">print</span>(<span class="string">"=== 汉诺塔问题（3个盘子）==="</span>)
hanoi(<span class="number">3</span>, <span class="string">"A"</span>, <span class="string">"C"</span>, <span class="string">"B"</span>)

<span class="comment"># 递归计算最大公约数（欧几里得算法）</span>
<span class="keyword">def</span> <span class="function">gcd</span>(a, b):
    <span class="string">"""使用递归计算最大公约数"""</span>
    <span class="keyword">if</span> b == <span class="number">0</span>:
        <span class="keyword">return</span> a
    <span class="keyword">else</span>:
        <span class="keyword">return</span> gcd(b, a % b)

<span class="function">print</span>(<span class="string">"\n=== 最大公约数 ==="</span>)
<span class="function">print</span>(<span class="string">f"gcd(48, 18) = {gcd(48, 18)}"</span>)
<span class="function">print</span>(<span class="string">f"gcd(100, 25) = {gcd(100, 25)}"</span>)

<span class="comment"># 递归生成排列</span>
<span class="keyword">def</span> <span class="function">permutations</span>(items):
    <span class="string">"""生成列表的所有排列"""</span>
    <span class="keyword">if</span> <span class="function">len</span>(items) <= <span class="number">1</span>:
        <span class="keyword">return</span> [items]
    
    result = []
    <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="function">len</span>(items)):
        current = items[i]
        remaining = items[:i] + items[i+<span class="number">1</span>:]
        <span class="keyword">for</span> perm <span class="keyword">in</span> permutations(remaining):
            result.append([current] + perm)
    <span class="keyword">return</span> result

<span class="function">print</span>(<span class="string">"\n=== 排列生成 ==="</span>)
perms = permutations([<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>])
<span class="keyword">for</span> i, perm <span class="keyword">in</span> <span class="function">enumerate</span>(perms, <span class="number">1</span>):
    <span class="function">print</span>(<span class="string">f"排列{i}: {perm}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 递归优化：记忆化</span>
<span class="comment"># 未优化的递归斐波那契（效率低）</span>
<span class="keyword">def</span> <span class="function">fib_slow</span>(n):
    <span class="keyword">if</span> n <= <span class="number">1</span>:
        <span class="keyword">return</span> n
    <span class="keyword">return</span> fib_slow(n - <span class="number">1</span>) + fib_slow(n - <span class="number">2</span>)

<span class="comment"># 使用记忆化优化的递归斐波那契</span>
<span class="keyword">def</span> <span class="function">fib_memo</span>(n, memo={}):
    <span class="string">"""使用记忆化的斐波那契递归"""</span>
    <span class="keyword">if</span> n <span class="keyword">in</span> memo:
        <span class="keyword">return</span> memo[n]
    
    <span class="keyword">if</span> n <= <span class="number">1</span>:
        memo[n] = n
        <span class="keyword">return</span> n
    
    memo[n] = fib_memo(n - <span class="number">1</span>, memo) + fib_memo(n - <span class="number">2</span>, memo)
    <span class="keyword">return</span> memo[n]

<span class="comment"># 使用装饰器的记忆化</span>
<span class="keyword">from</span> functools <span class="keyword">import</span> lru_cache

<span class="function">@lru_cache</span>(maxsize=<span class="keyword">None</span>)
<span class="keyword">def</span> <span class="function">fib_cached</span>(n):
    <span class="string">"""使用装饰器缓存的斐波那契递归"""</span>
    <span class="keyword">if</span> n <= <span class="number">1</span>:
        <span class="keyword">return</span> n
    <span class="keyword">return</span> fib_cached(n - <span class="number">1</span>) + fib_cached(n - <span class="number">2</span>)

<span class="comment"># 性能比较</span>
<span class="keyword">import</span> time

<span class="keyword">def</span> <span class="function">time_function</span>(func, n):
    <span class="string">"""测量函数执行时间"""</span>
    start = time.time()
    result = func(n)
    end = time.time()
    <span class="keyword">return</span> result, end - start

n = <span class="number">30</span>
<span class="function">print</span>(<span class="string">f"\n=== 斐波那契性能比较 (n={n}) ==="</span>)

result, duration = time_function(fib_memo, n)
<span class="function">print</span>(<span class="string">f"记忆化递归: F({n}) = {result}, 耗时: {duration:.6f}秒"</span>)

result, duration = time_function(fib_cached, n)
<span class="function">print</span>(<span class="string">f"装饰器缓存: F({n}) = {result}, 耗时: {duration:.6f}秒"</span>)</div>
                </div>

                <div class="warning-box">
                    <h3><span class="emoji">⚠️</span>递归注意事项</h3>
                    <ul>
                        <li>必须有明确的终止条件，避免无限递归</li>
                        <li>注意递归深度限制，Python默认限制约1000层</li>
                        <li>某些递归问题可能效率较低，考虑使用记忆化或迭代</li>
                        <li>递归适合处理树形结构、分治算法等问题</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习递归：</strong>"请详细解释Python中的递归概念，包括递归的基本要素、常见应用场景、性能优化方法（如记忆化），以及递归与迭代的对比。请提供一些经典的递归算法示例。"</p>
                </div>
            </div>
        </div>

        <!-- 第17页：Lambda函数 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">λ</span>Lambda函数</h1>
                <h2><span class="emoji">⚡</span>匿名函数的简洁之美</h2>
                <p>Lambda函数是Python中的匿名函数，用于创建简单的、一次性使用的函数。它们特别适合用于函数式编程和高阶函数。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># Lambda函数基础语法</span>
<span class="comment"># lambda 参数: 表达式</span>

<span class="comment"># 基本示例</span>
<span class="comment"># 普通函数</span>
<span class="keyword">def</span> <span class="function">add</span>(x, y):
    <span class="keyword">return</span> x + y

<span class="comment"># 等价的Lambda函数</span>
add_lambda = <span class="keyword">lambda</span> x, y: x + y

<span class="function">print</span>(<span class="string">"=== 基本Lambda函数 ==="</span>)
<span class="function">print</span>(<span class="string">f"普通函数: add(3, 5) = {add(3, 5)}"</span>)
<span class="function">print</span>(<span class="string">f"Lambda函数: add_lambda(3, 5) = {add_lambda(3, 5)}"</span>)

<span class="comment"># 单参数Lambda函数</span>
square = <span class="keyword">lambda</span> x: x ** <span class="number">2</span>
abs_value = <span class="keyword">lambda</span> x: x <span class="keyword">if</span> x >= <span class="number">0</span> <span class="keyword">else</span> -x

<span class="function">print</span>(<span class="string">"\n=== 单参数Lambda ==="</span>)
<span class="function">print</span>(<span class="string">f"平方: square(4) = {square(4)}"</span>)
<span class="function">print</span>(<span class="string">f"绝对值: abs_value(-5) = {abs_value(-5)}"</span>)

<span class="comment"># 无参数Lambda函数</span>
get_pi = <span class="keyword">lambda</span>: <span class="number">3.14159</span>
get_greeting = <span class="keyword">lambda</span>: <span class="string">"Hello, World!"</span>

<span class="function">print</span>(<span class="string">"\n=== 无参数Lambda ==="</span>)
<span class="function">print</span>(<span class="string">f"π值: {get_pi()}"</span>)
<span class="function">print</span>(<span class="string">f"问候: {get_greeting()}"</span>)

<span class="comment"># 多参数Lambda函数</span>
max_of_three = <span class="keyword">lambda</span> a, b, c: <span class="function">max</span>(a, <span class="function">max</span>(b, c))
full_name = <span class="keyword">lambda</span> first, last: <span class="string">f"{first} {last}"</span>

<span class="function">print</span>(<span class="string">"\n=== 多参数Lambda ==="</span>)
<span class="function">print</span>(<span class="string">f"三数最大值: max_of_three(10, 25, 15) = {max_of_three(10, 25, 15)}"</span>)
<span class="function">print</span>(<span class="string">f"全名: full_name('张', '三') = {full_name('张', '三')}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># Lambda与高阶函数的结合</span>
<span class="comment"># 1. 与map()函数结合</span>
numbers = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>]

<span class="comment"># 使用Lambda计算平方</span>
squares = <span class="function">list</span>(<span class="function">map</span>(<span class="keyword">lambda</span> x: x ** <span class="number">2</span>, numbers))
<span class="function">print</span>(<span class="string">"=== map()与Lambda ==="</span>)
<span class="function">print</span>(<span class="string">f"原数字: {numbers}"</span>)
<span class="function">print</span>(<span class="string">f"平方: {squares}"</span>)

<span class="comment"># 字符串处理</span>
names = [<span class="string">"alice"</span>, <span class="string">"bob"</span>, <span class="string">"charlie"</span>]
capitalized = <span class="function">list</span>(<span class="function">map</span>(<span class="keyword">lambda</span> name: name.capitalize(), names))
<span class="function">print</span>(<span class="string">f"\n原名字: {names}"</span>)
<span class="function">print</span>(<span class="string">f"首字母大写: {capitalized}"</span>)

<span class="comment"># 2. 与filter()函数结合</span>
numbers = <span class="function">list</span>(<span class="function">range</span>(<span class="number">1</span>, <span class="number">21</span>))

<span class="comment"># 筛选偶数</span>
evens = <span class="function">list</span>(<span class="function">filter</span>(<span class="keyword">lambda</span> x: x % <span class="number">2</span> == <span class="number">0</span>, numbers))
<span class="comment"># 筛选大于10的数</span>
greater_than_10 = <span class="function">list</span>(<span class="function">filter</span>(<span class="keyword">lambda</span> x: x > <span class="number">10</span>, numbers))

<span class="function">print</span>(<span class="string">"\n=== filter()与Lambda ==="</span>)
<span class="function">print</span>(<span class="string">f"1-20: {numbers}"</span>)
<span class="function">print</span>(<span class="string">f"偶数: {evens}"</span>)
<span class="function">print</span>(<span class="string">f"大于10: {greater_than_10}"</span>)

<span class="comment"># 筛选字符串</span>
words = [<span class="string">"python"</span>, <span class="string">"java"</span>, <span class="string">"c++"</span>, <span class="string">"javascript"</span>, <span class="string">"go"</span>]
long_words = <span class="function">list</span>(<span class="function">filter</span>(<span class="keyword">lambda</span> word: <span class="function">len</span>(word) > <span class="number">4</span>, words))
<span class="function">print</span>(<span class="string">f"\n编程语言: {words}"</span>)
<span class="function">print</span>(<span class="string">f"长度>4: {long_words}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 3. 与sorted()函数结合</span>
students = [
    {<span class="string">"name"</span>: <span class="string">"张三"</span>, <span class="string">"age"</span>: <span class="number">20</span>, <span class="string">"score"</span>: <span class="number">85</span>},
    {<span class="string">"name"</span>: <span class="string">"李四"</span>, <span class="string">"age"</span>: <span class="number">19</span>, <span class="string">"score"</span>: <span class="number">92</span>},
    {<span class="string">"name"</span>: <span class="string">"王五"</span>, <span class="string">"age"</span>: <span class="number">21</span>, <span class="string">"score"</span>: <span class="number">78</span>}
]

<span class="function">print</span>(<span class="string">"=== sorted()与Lambda ==="</span>)
<span class="function">print</span>(<span class="string">"原始数据:"</span>)
<span class="keyword">for</span> student <span class="keyword">in</span> students:
    <span class="function">print</span>(<span class="string">f"  {student['name']}: 年龄{student['age']}, 分数{student['score']}"</span>)

<span class="comment"># 按分数排序</span>
by_score = <span class="function">sorted</span>(students, key=<span class="keyword">lambda</span> s: s[<span class="string">"score"</span>], reverse=<span class="keyword">True</span>)
<span class="function">print</span>(<span class="string">"\n按分数排序（降序）:"</span>)
<span class="keyword">for</span> student <span class="keyword">in</span> by_score:
    <span class="function">print</span>(<span class="string">f"  {student['name']}: {student['score']}分"</span>)

<span class="comment"># 按年龄排序</span>
by_age = <span class="function">sorted</span>(students, key=<span class="keyword">lambda</span> s: s[<span class="string">"age"</span>])
<span class="function">print</span>(<span class="string">"\n按年龄排序（升序）:"</span>)
<span class="keyword">for</span> student <span class="keyword">in</span> by_age:
    <span class="function">print</span>(<span class="string">f"  {student['name']}: {student['age']}岁"</span>)

<span class="comment"># 复杂排序：先按分数，再按年龄</span>
complex_sort = <span class="function">sorted</span>(students, key=<span class="keyword">lambda</span> s: (-s[<span class="string">"score"</span>], s[<span class="string">"age"</span>]))
<span class="function">print</span>(<span class="string">"\n复合排序（分数降序，年龄升序）:"</span>)
<span class="keyword">for</span> student <span class="keyword">in</span> complex_sort:
    <span class="function">print</span>(<span class="string">f"  {student['name']}: {student['score']}分, {student['age']}岁"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 4. 与reduce()函数结合</span>
<span class="keyword">from</span> functools <span class="keyword">import</span> reduce

numbers = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>]

<span class="comment"># 计算乘积</span>
product = reduce(<span class="keyword">lambda</span> x, y: x * y, numbers)
<span class="function">print</span>(<span class="string">"=== reduce()与Lambda ==="</span>)
<span class="function">print</span>(<span class="string">f"数字列表: {numbers}"</span>)
<span class="function">print</span>(<span class="string">f"乘积: {product}"</span>)

<span class="comment"># 找最大值</span>
max_value = reduce(<span class="keyword">lambda</span> x, y: x <span class="keyword">if</span> x > y <span class="keyword">else</span> y, numbers)
<span class="function">print</span>(<span class="string">f"最大值: {max_value}"</span>)

<span class="comment"># 字符串连接</span>
words = [<span class="string">"Python"</span>, <span class="string">"是"</span>, <span class="string">"最好的"</span>, <span class="string">"编程语言"</span>]
sentence = reduce(<span class="keyword">lambda</span> x, y: x + <span class="string">" "</span> + y, words)
<span class="function">print</span>(<span class="string">f"\n单词列表: {words}"</span>)
<span class="function">print</span>(<span class="string">f"连接结果: {sentence}"</span>)

<span class="comment"># 5. Lambda作为函数参数</span>
<span class="keyword">def</span> <span class="function">apply_operation</span>(numbers, operation):
    <span class="string">"""对数字列表应用指定操作"""</span>
    <span class="keyword">return</span> [operation(num) <span class="keyword">for</span> num <span class="keyword">in</span> numbers]

numbers = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>]

<span class="function">print</span>(<span class="string">"\n=== Lambda作为参数 ==="</span>)
<span class="function">print</span>(<span class="string">f"原数字: {numbers}"</span>)

<span class="comment"># 不同的Lambda操作</span>
squared = apply_operation(numbers, <span class="keyword">lambda</span> x: x ** <span class="number">2</span>)
doubled = apply_operation(numbers, <span class="keyword">lambda</span> x: x * <span class="number">2</span>)
negated = apply_operation(numbers, <span class="keyword">lambda</span> x: -x)

<span class="function">print</span>(<span class="string">f"平方: {squared}"</span>)
<span class="function">print</span>(<span class="string">f"翻倍: {doubled}"</span>)
<span class="function">print</span>(<span class="string">f"取负: {negated}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># Lambda的实际应用场景</span>
<span class="comment"># 1. 数据处理和分析</span>
data = [
    {<span class="string">"product"</span>: <span class="string">"笔记本"</span>, <span class="string">"price"</span>: <span class="number">5000</span>, <span class="string">"quantity"</span>: <span class="number">10</span>},
    {<span class="string">"product"</span>: <span class="string">"手机"</span>, <span class="string">"price"</span>: <span class="number">3000</span>, <span class="string">"quantity"</span>: <span class="number">15</span>},
    {<span class="string">"product"</span>: <span class="string">"平板"</span>, <span class="string">"price"</span>: <span class="number">2000</span>, <span class="string">"quantity"</span>: <span class="number">8</span>}
]

<span class="function">print</span>(<span class="string">"=== 数据处理应用 ==="</span>)

<span class="comment"># 计算总价值</span>
total_values = <span class="function">list</span>(<span class="function">map</span>(<span class="keyword">lambda</span> item: item[<span class="string">"price"</span>] * item[<span class="string">"quantity"</span>], data))
<span class="function">print</span>(<span class="string">"各产品总价值:"</span>)
<span class="keyword">for</span> i, item <span class="keyword">in</span> <span class="function">enumerate</span>(data):
    <span class="function">print</span>(<span class="string">f"  {item['product']}: {total_values[i]}元"</span>)

<span class="comment"># 筛选高价值产品</span>
high_value = <span class="function">list</span>(<span class="function">filter</span>(<span class="keyword">lambda</span> item: item[<span class="string">"price"</span>] * item[<span class="string">"quantity"</span>] > <span class="number">30000</span>, data))
<span class="function">print</span>(<span class="string">"\n高价值产品（>30000元）:"</span>)
<span class="keyword">for</span> item <span class="keyword">in</span> high_value:
    <span class="function">print</span>(<span class="string">f"  {item['product']}: {item['price'] * item['quantity']}元"</span>)

<span class="comment"># 2. 事件处理（模拟）</span>
<span class="keyword">class</span> <span class="function">EventHandler</span>:
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>):
        <span class="keyword">self</span>.handlers = {}
    
    <span class="keyword">def</span> <span class="function">register</span>(<span class="keyword">self</span>, event_type, handler):
        <span class="keyword">self</span>.handlers[event_type] = handler
    
    <span class="keyword">def</span> <span class="function">trigger</span>(<span class="keyword">self</span>, event_type, data):
        <span class="keyword">if</span> event_type <span class="keyword">in</span> <span class="keyword">self</span>.handlers:
            <span class="keyword">return</span> <span class="keyword">self</span>.handlers[event_type](data)

event_handler = EventHandler()

<span class="comment"># 使用Lambda注册事件处理器</span>
event_handler.register(<span class="string">"user_login"</span>, <span class="keyword">lambda</span> user: <span class="string">f"欢迎 {user} 登录！"</span>)
event_handler.register(<span class="string">"user_logout"</span>, <span class="keyword">lambda</span> user: <span class="string">f"再见 {user}！"</span>)
event_handler.register(<span class="string">"calculate_tax"</span>, <span class="keyword">lambda</span> amount: amount * <span class="number">0.1</span>)

<span class="function">print</span>(<span class="string">"\n=== 事件处理应用 ==="</span>)
<span class="function">print</span>(event_handler.trigger(<span class="string">"user_login"</span>, <span class="string">"张三"</span>))
<span class="function">print</span>(event_handler.trigger(<span class="string">"user_logout"</span>, <span class="string">"张三"</span>))
<span class="function">print</span>(<span class="string">f"税费计算: {event_handler.trigger('calculate_tax', 1000)}元"</span>)</div>
                </div>

                <div class="warning-box">
                    <h3><span class="emoji">⚠️</span>Lambda使用注意事项</h3>
                    <ul>
                        <li>Lambda只能包含表达式，不能包含语句（如print、赋值等）</li>
                        <li>Lambda函数应该保持简单，复杂逻辑建议使用普通函数</li>
                        <li>Lambda函数没有函数名，调试时可能不够直观</li>
                        <li>过度使用Lambda可能降低代码可读性</li>
                        <li>Lambda函数不能包含注释</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习Lambda函数：</strong>"请详细解释Python中的Lambda函数，包括语法、使用场景、与高阶函数（map、filter、sorted、reduce）的结合使用，以及Lambda函数的优缺点和最佳实践。"</p>
                </div>
            </div>
        </div>

        <!-- 第18页：列表推导式 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">📋</span>列表推导式</h1>
                <h2><span class="emoji">🚀</span>优雅高效的列表生成</h2>
                <p>列表推导式是Python中创建列表的简洁而强大的方法，它将循环和条件判断融合在一个表达式中，代码更加Pythonic。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># 列表推导式基础语法</span>
<span class="comment"># [表达式 for 变量 in 可迭代对象]</span>
<span class="comment"># [表达式 for 变量 in 可迭代对象 if 条件]</span>

<span class="comment"># 基本示例：生成平方数列表</span>
<span class="comment"># 传统方法</span>
squares_traditional = []
<span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">10</span>):
    squares_traditional.append(i ** <span class="number">2</span>)

<span class="comment"># 列表推导式方法</span>
squares_comprehension = [i ** <span class="number">2</span> <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">10</span>)]

<span class="function">print</span>(<span class="string">"=== 基础列表推导式 ==="</span>)
<span class="function">print</span>(<span class="string">f"传统方法: {squares_traditional}"</span>)
<span class="function">print</span>(<span class="string">f"推导式: {squares_comprehension}"</span>)
<span class="function">print</span>(<span class="string">f"结果相同: {squares_traditional == squares_comprehension}"</span>)

<span class="comment"># 带条件的列表推导式</span>
<span class="comment"># 生成偶数平方</span>
even_squares = [i ** <span class="number">2</span> <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">10</span>) <span class="keyword">if</span> i % <span class="number">2</span> == <span class="number">0</span>]
<span class="function">print</span>(<span class="string">f"\n偶数平方: {even_squares}"</span>)

<span class="comment"># 字符串处理</span>
words = [<span class="string">"python"</span>, <span class="string">"java"</span>, <span class="string">"javascript"</span>, <span class="string">"go"</span>, <span class="string">"rust"</span>]
capitalized_words = [word.capitalize() <span class="keyword">for</span> word <span class="keyword">in</span> words]
long_words = [word <span class="keyword">for</span> word <span class="keyword">in</span> words <span class="keyword">if</span> <span class="function">len</span>(word) > <span class="number">4</span>]

<span class="function">print</span>(<span class="string">f"\n原单词: {words}"</span>)
<span class="function">print</span>(<span class="string">f"首字母大写: {capitalized_words}"</span>)
<span class="function">print</span>(<span class="string">f"长单词: {long_words}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 复杂的列表推导式</span>
<span class="comment"># 嵌套循环的列表推导式</span>
<span class="comment"># 生成乘法表</span>
multiplication_table = [[i * j <span class="keyword">for</span> j <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1</span>, <span class="number">6</span>)] <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1</span>, <span class="number">6</span>)]

<span class="function">print</span>(<span class="string">"=== 嵌套列表推导式 ==="</span>)
<span class="function">print</span>(<span class="string">"5x5乘法表:"</span>)
<span class="keyword">for</span> row <span class="keyword">in</span> multiplication_table:
    <span class="function">print</span>(<span class="string">"  "</span> + <span class="string">" "</span>.join(<span class="string">f"{num:2d}"</span> <span class="keyword">for</span> num <span class="keyword">in</span> row))

<span class="comment"># 展平嵌套列表</span>
nested_list = [[<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>], [<span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>], [<span class="number">7</span>, <span class="number">8</span>, <span class="number">9</span>]]
flattened = [item <span class="keyword">for</span> sublist <span class="keyword">in</span> nested_list <span class="keyword">for</span> item <span class="keyword">in</span> sublist]

<span class="function">print</span>(<span class="string">f"\n嵌套列表: {nested_list}"</span>)
<span class="function">print</span>(<span class="string">f"展平后: {flattened}"</span>)

<span class="comment"># 条件表达式在推导式中的使用</span>
numbers = <span class="function">list</span>(<span class="function">range</span>(-<span class="number">5</span>, <span class="number">6</span>))
abs_values = [x <span class="keyword">if</span> x >= <span class="number">0</span> <span class="keyword">else</span> -x <span class="keyword">for</span> x <span class="keyword">in</span> numbers]
classified = [<span class="string">"正数"</span> <span class="keyword">if</span> x > <span class="number">0</span> <span class="keyword">else</span> <span class="string">"负数"</span> <span class="keyword">if</span> x < <span class="number">0</span> <span class="keyword">else</span> <span class="string">"零"</span> <span class="keyword">for</span> x <span class="keyword">in</span> numbers]

<span class="function">print</span>(<span class="string">f"\n原数字: {numbers}"</span>)
<span class="function">print</span>(<span class="string">f"绝对值: {abs_values}"</span>)
<span class="function">print</span>(<span class="string">f"分类: {classified}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 实际应用场景</span>
<span class="comment"># 1. 数据处理和转换</span>
students = [
    {<span class="string">"name"</span>: <span class="string">"张三"</span>, <span class="string">"scores"</span>: [85, 92, 78]},
    {<span class="string">"name"</span>: <span class="string">"李四"</span>, <span class="string">"scores"</span>: [90, 88, 95]},
    {<span class="string">"name"</span>: <span class="string">"王五"</span>, <span class="string">"scores"</span>: [76, 82, 89]}
]

<span class="function">print</span>(<span class="string">"=== 数据处理应用 ==="</span>)

<span class="comment"># 提取学生姓名</span>
names = [student[<span class="string">"name"</span>] <span class="keyword">for</span> student <span class="keyword">in</span> students]
<span class="function">print</span>(<span class="string">f"学生姓名: {names}"</span>)

<span class="comment"># 计算平均分</span>
averages = [<span class="function">sum</span>(student[<span class="string">"scores"</span>]) / <span class="function">len</span>(student[<span class="string">"scores"</span>]) <span class="keyword">for</span> student <span class="keyword">in</span> students]
<span class="function">print</span>(<span class="string">f"平均分: {[round(avg, 1) for avg in averages]}"</span>)

<span class="comment"># 筛选优秀学生（平均分>85）</span>
excellent_students = [student[<span class="string">"name"</span>] <span class="keyword">for</span> student <span class="keyword">in</span> students 
                     <span class="keyword">if</span> <span class="function">sum</span>(student[<span class="string">"scores"</span>]) / <span class="function">len</span>(student[<span class="string">"scores"</span>]) > <span class="number">85</span>]
<span class="function">print</span>(<span class="string">f"优秀学生: {excellent_students}"</span>)

<span class="comment"># 2. 文件处理模拟</span>
file_names = [<span class="string">"document.txt"</span>, <span class="string">"image.jpg"</span>, <span class="string">"script.py"</span>, <span class="string">"data.csv"</span>, <span class="string">"backup.zip"</span>]

<span class="comment"># 提取文件扩展名</span>
extensions = [name.split(<span class="string">"."</span>)[-<span class="number">1</span>] <span class="keyword">for</span> name <span class="keyword">in</span> file_names]
<span class="function">print</span>(<span class="string">f"\n文件扩展名: {extensions}"</span>)

<span class="comment"># 筛选Python文件</span>
python_files = [name <span class="keyword">for</span> name <span class="keyword">in</span> file_names <span class="keyword">if</span> name.endswith(<span class="string">".py"</span>)]
<span class="function">print</span>(<span class="string">f"Python文件: {python_files}"</span>)

<span class="comment"># 生成新文件名（添加前缀）</span>
backup_names = [<span class="string">f"backup_{name}"</span> <span class="keyword">for</span> name <span class="keyword">in</span> file_names]
<span class="function">print</span>(<span class="string">f"备份文件名: {backup_names}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 3. 数学和科学计算</span>
<span class="keyword">import</span> math

<span class="function">print</span>(<span class="string">"=== 数学计算应用 ==="</span>)

<span class="comment"># 生成质数列表</span>
<span class="keyword">def</span> <span class="function">is_prime</span>(n):
    <span class="keyword">if</span> n < <span class="number">2</span>:
        <span class="keyword">return</span> <span class="keyword">False</span>
    <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">2</span>, <span class="function">int</span>(math.sqrt(n)) + <span class="number">1</span>):
        <span class="keyword">if</span> n % i == <span class="number">0</span>:
            <span class="keyword">return</span> <span class="keyword">False</span>
    <span class="keyword">return</span> <span class="keyword">True</span>

primes = [n <span class="keyword">for</span> n <span class="keyword">in</span> <span class="function">range</span>(<span class="number">2</span>, <span class="number">50</span>) <span class="keyword">if</span> is_prime(n)]
<span class="function">print</span>(<span class="string">f"50以内质数: {primes}"</span>)

<span class="comment"># 生成斐波那契数列</span>
fib = [<span class="number">0</span>, <span class="number">1</span>]
[fib.append(fib[-<span class="number">1</span>] + fib[-<span class="number">2</span>]) <span class="keyword">for</span> _ <span class="keyword">in</span> <span class="function">range</span>(<span class="number">8</span>)]  <span class="comment"># 生成前10项</span>
<span class="function">print</span>(<span class="string">f"斐波那契数列: {fib}"</span>)

<span class="comment"># 矩阵操作</span>
matrix = [[<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>], [<span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>], [<span class="number">7</span>, <span class="number">8</span>, <span class="number">9</span>]]

<span class="comment"># 矩阵转置</span>
transposed = [[row[i] <span class="keyword">for</span> row <span class="keyword">in</span> matrix] <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="function">len</span>(matrix[<span class="number">0</span>]))]
<span class="function">print</span>(<span class="string">f"\n原矩阵: {matrix}"</span>)
<span class="function">print</span>(<span class="string">f"转置矩阵: {transposed}"</span>)

<span class="comment"># 提取对角线元素</span>
diagonal = [matrix[i][i] <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="function">len</span>(matrix))]
<span class="function">print</span>(<span class="string">f"对角线元素: {diagonal}"</span>)

<span class="comment"># 4. 字符串和文本处理</span>
text = <span class="string">"Python是一种优秀的编程语言"</span>

<span class="comment"># 提取字符的ASCII值</span>
ascii_values = [<span class="function">ord</span>(char) <span class="keyword">for</span> char <span class="keyword">in</span> text <span class="keyword">if</span> char.isalpha()]
<span class="function">print</span>(<span class="string">f"\n文本: {text}"</span>)
<span class="function">print</span>(<span class="string">f"字母ASCII值: {ascii_values[:10]}..."</span>)  <span class="comment"># 只显示前10个</span>

<span class="comment"># 单词长度统计</span>
sentence = <span class="string">"Python is a powerful programming language"</span>
word_lengths = [<span class="function">len</span>(word) <span class="keyword">for</span> word <span class="keyword">in</span> sentence.split()]
<span class="function">print</span>(<span class="string">f"句子: {sentence}"</span>)
<span class="function">print</span>(<span class="string">f"单词长度: {word_lengths}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 其他推导式类型</span>
<span class="comment"># 1. 集合推导式</span>
numbers = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">4</span>, <span class="number">5</span>]
unique_squares = {x ** <span class="number">2</span> <span class="keyword">for</span> x <span class="keyword">in</span> numbers}

<span class="function">print</span>(<span class="string">"=== 其他推导式类型 ==="</span>)
<span class="function">print</span>(<span class="string">f"原数字: {numbers}"</span>)
<span class="function">print</span>(<span class="string">f"唯一平方数: {unique_squares}"</span>)

<span class="comment"># 2. 字典推导式</span>
words = [<span class="string">"apple"</span>, <span class="string">"banana"</span>, <span class="string">"cherry"</span>]
word_lengths = {word: <span class="function">len</span>(word) <span class="keyword">for</span> word <span class="keyword">in</span> words}
reversed_words = {word: word[::-<span class="number">1</span>] <span class="keyword">for</span> word <span class="keyword">in</span> words}

<span class="function">print</span>(<span class="string">f"\n单词长度字典: {word_lengths}"</span>)
<span class="function">print</span>(<span class="string">f"反转单词字典: {reversed_words}"</span>)

<span class="comment"># 3. 生成器表达式</span>
squares_gen = (x ** <span class="number">2</span> <span class="keyword">for</span> x <span class="keyword">in</span> <span class="function">range</span>(<span class="number">10</span>))
<span class="function">print</span>(<span class="string">f"\n生成器对象: {squares_gen}"</span>)
<span class="function">print</span>(<span class="string">f"生成器内容: {list(squares_gen)}"</span>)

<span class="comment"># 性能比较示例</span>
<span class="keyword">import</span> time

<span class="keyword">def</span> <span class="function">time_comparison</span>():
    <span class="string">"""比较不同方法的性能"""</span>
    n = <span class="number">100000</span>
    
    <span class="comment"># 传统循环</span>
    start = time.time()
    result1 = []
    <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(n):
        <span class="keyword">if</span> i % <span class="number">2</span> == <span class="number">0</span>:
            result1.append(i ** <span class="number">2</span>)
    time1 = time.time() - start
    
    <span class="comment"># 列表推导式</span>
    start = time.time()
    result2 = [i ** <span class="number">2</span> <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(n) <span class="keyword">if</span> i % <span class="number">2</span> == <span class="number">0</span>]
    time2 = time.time() - start
    
    <span class="function">print</span>(<span class="string">f"\n=== 性能比较 (n={n}) ==="</span>)
    <span class="function">print</span>(<span class="string">f"传统循环: {time1:.4f}秒"</span>)
    <span class="function">print</span>(<span class="string">f"列表推导式: {time2:.4f}秒"</span>)
    <span class="function">print</span>(<span class="string">f"推导式更快: {time1/time2:.2f}倍"</span>)
    <span class="function">print</span>(<span class="string">f"结果相同: {result1 == result2}"</span>)

time_comparison()</div>
                </div>

                <div class="warning-box">
                    <h3><span class="emoji">⚠️</span>列表推导式最佳实践</h3>
                    <ul>
                        <li>保持推导式简洁，复杂逻辑建议使用传统循环</li>
                        <li>避免过度嵌套，影响代码可读性</li>
                        <li>大数据量时考虑使用生成器表达式节省内存</li>
                        <li>合理使用条件过滤，提高效率</li>
                        <li>注意推导式中的变量作用域</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习列表推导式：</strong>"请详细解释Python中的列表推导式，包括基础语法、条件过滤、嵌套推导式，以及与集合推导式、字典推导式、生成器表达式的区别。请提供实际应用场景和性能优势的示例。"</p>
                </div>
            </div>
        </div>

        <!-- 第19页：生成器 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">⚡</span>生成器</h1>
                <h2><span class="emoji">🔄</span>内存高效的迭代器</h2>
                <p>生成器是Python中一种特殊的迭代器，它可以按需生成值，而不是一次性创建所有值，这使得它在处理大数据集时非常高效。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># 生成器函数基础</span>
<span class="comment"># 使用yield关键字创建生成器</span>

<span class="keyword">def</span> <span class="function">simple_generator</span>():
    <span class="string">"""简单的生成器函数"""</span>
    <span class="function">print</span>(<span class="string">"开始生成"</span>)
    <span class="keyword">yield</span> <span class="number">1</span>
    <span class="function">print</span>(<span class="string">"生成第一个值"</span>)
    <span class="keyword">yield</span> <span class="number">2</span>
    <span class="function">print</span>(<span class="string">"生成第二个值"</span>)
    <span class="keyword">yield</span> <span class="number">3</span>
    <span class="function">print</span>(<span class="string">"生成完毕"</span>)

<span class="function">print</span>(<span class="string">"=== 生成器基础演示 ==="</span>)

<span class="comment"># 创建生成器对象</span>
gen = simple_generator()
<span class="function">print</span>(<span class="string">f"生成器对象: {gen}"</span>)
<span class="function">print</span>(<span class="string">f"类型: {type(gen)}"</span>)

<span class="comment"># 逐个获取值</span>
<span class="function">print</span>(<span class="string">"\n逐个获取值:"</span>)
<span class="function">print</span>(<span class="string">f"第一次调用: {next(gen)}"</span>)
<span class="function">print</span>(<span class="string">f"第二次调用: {next(gen)}"</span>)
<span class="function">print</span>(<span class="string">f"第三次调用: {next(gen)}"</span>)

<span class="comment"># 使用for循环遍历生成器</span>
<span class="function">print</span>(<span class="string">"\n使用for循环:"</span>)
gen2 = simple_generator()
<span class="keyword">for</span> value <span class="keyword">in</span> gen2:
    <span class="function">print</span>(<span class="string">f"获取到值: {value}"</span>)

<span class="comment"># 数字序列生成器</span>
<span class="keyword">def</span> <span class="function">number_generator</span>(start, end, step=<span class="number">1</span>):
    <span class="string">"""生成数字序列"""</span>
    current = start
    <span class="keyword">while</span> current < end:
        <span class="keyword">yield</span> current
        current += step

<span class="function">print</span>(<span class="string">"\n=== 数字序列生成器 ==="</span>)
numbers = <span class="function">list</span>(number_generator(<span class="number">0</span>, <span class="number">10</span>, <span class="number">2</span>))
<span class="function">print</span>(<span class="string">f"偶数序列: {numbers}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 生成器表达式</span>
<span class="comment"># 类似列表推导式，但使用圆括号</span>

<span class="function">print</span>(<span class="string">"=== 生成器表达式 ==="</span>)

<span class="comment"># 生成器表达式 vs 列表推导式</span>
list_comp = [x ** <span class="number">2</span> <span class="keyword">for</span> x <span class="keyword">in</span> <span class="function">range</span>(<span class="number">10</span>)]  <span class="comment"># 列表推导式</span>
gen_exp = (x ** <span class="number">2</span> <span class="keyword">for</span> x <span class="keyword">in</span> <span class="function">range</span>(<span class="number">10</span>))   <span class="comment"># 生成器表达式</span>

<span class="function">print</span>(<span class="string">f"列表推导式: {list_comp}"</span>)
<span class="function">print</span>(<span class="string">f"生成器表达式: {gen_exp}"</span>)
<span class="function">print</span>(<span class="string">f"生成器内容: {list(gen_exp)}"</span>)

<span class="comment"># 内存使用比较</span>
<span class="keyword">import</span> sys

large_list = [x <span class="keyword">for</span> x <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1000</span>)]
large_gen = (x <span class="keyword">for</span> x <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1000</span>))

<span class="function">print</span>(<span class="string">f"\n内存使用比较:"</span>)
<span class="function">print</span>(<span class="string">f"列表大小: {sys.getsizeof(large_list)} 字节"</span>)
<span class="function">print</span>(<span class="string">f"生成器大小: {sys.getsizeof(large_gen)} 字节"</span>)

<span class="comment"># 带条件的生成器表达式</span>
even_squares = (x ** <span class="number">2</span> <span class="keyword">for</span> x <span class="keyword">in</span> <span class="function">range</span>(<span class="number">20</span>) <span class="keyword">if</span> x % <span class="number">2</span> == <span class="number">0</span>)
<span class="function">print</span>(<span class="string">f"\n偶数平方: {list(even_squares)}"</span>)

<span class="comment"># 嵌套生成器表达式</span>
matrix = [[<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>], [<span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>], [<span class="number">7</span>, <span class="number">8</span>, <span class="number">9</span>]]
flattened_gen = (item <span class="keyword">for</span> row <span class="keyword">in</span> matrix <span class="keyword">for</span> item <span class="keyword">in</span> row)
<span class="function">print</span>(<span class="string">f"展平矩阵: {list(flattened_gen)}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 实际应用：斐波那契数列生成器</span>
<span class="keyword">def</span> <span class="function">fibonacci_generator</span>(limit=<span class="keyword">None</span>):
    <span class="string">"""斐波那契数列生成器"""</span>
    a, b = <span class="number">0</span>, <span class="number">1</span>
    count = <span class="number">0</span>
    
    <span class="keyword">while</span> limit <span class="keyword">is</span> <span class="keyword">None</span> <span class="keyword">or</span> count < limit:
        <span class="keyword">yield</span> a
        a, b = b, a + b
        count += <span class="number">1</span>

<span class="function">print</span>(<span class="string">"=== 斐波那契生成器 ==="</span>)

<span class="comment"># 生成前10个斐波那契数</span>
fib_gen = fibonacci_generator(<span class="number">10</span>)
fib_list = <span class="function">list</span>(fib_gen)
<span class="function">print</span>(<span class="string">f"前10个斐波那契数: {fib_list}"</span>)

<span class="comment"># 找到第一个大于1000的斐波那契数</span>
fib_unlimited = fibonacci_generator()
<span class="keyword">for</span> fib_num <span class="keyword">in</span> fib_unlimited:
    <span class="keyword">if</span> fib_num > <span class="number">1000</span>:
        <span class="function">print</span>(<span class="string">f"第一个大于1000的斐波那契数: {fib_num}"</span>)
        <span class="keyword">break</span>

<span class="comment"># 质数生成器</span>
<span class="keyword">def</span> <span class="function">prime_generator</span>(limit):
    <span class="string">"""质数生成器"""</span>
    <span class="keyword">def</span> <span class="function">is_prime</span>(n):
        <span class="keyword">if</span> n < <span class="number">2</span>:
            <span class="keyword">return</span> <span class="keyword">False</span>
        <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">2</span>, <span class="function">int</span>(n ** <span class="number">0.5</span>) + <span class="number">1</span>):
            <span class="keyword">if</span> n % i == <span class="number">0</span>:
                <span class="keyword">return</span> <span class="keyword">False</span>
        <span class="keyword">return</span> <span class="keyword">True</span>
    
    <span class="keyword">for</span> num <span class="keyword">in</span> <span class="function">range</span>(<span class="number">2</span>, limit + <span class="number">1</span>):
        <span class="keyword">if</span> is_prime(num):
            <span class="keyword">yield</span> num

<span class="function">print</span>(<span class="string">f"\n100以内的质数: {list(prime_generator(100))}"</span>)

<span class="comment"># 文件行读取生成器</span>
<span class="keyword">def</span> <span class="function">file_line_generator</span>(filename):
    <span class="string">"""逐行读取文件的生成器"""</span>
    <span class="keyword">try</span>:
        <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
            <span class="keyword">for</span> line_num, line <span class="keyword">in</span> <span class="function">enumerate</span>(file, <span class="number">1</span>):
                <span class="keyword">yield</span> line_num, line.strip()
    <span class="keyword">except</span> <span class="function">FileNotFoundError</span>:
        <span class="function">print</span>(<span class="string">f"文件 {filename} 不存在"</span>)

<span class="comment"># 模拟文件内容</span>
<span class="function">print</span>(<span class="string">"\n=== 文件读取生成器模拟 ==="</span>)
sample_lines = [<span class="string">"第一行内容"</span>, <span class="string">"第二行内容"</span>, <span class="string">"第三行内容"</span>]

<span class="keyword">def</span> <span class="function">simulate_file_generator</span>(lines):
    <span class="keyword">for</span> line_num, line <span class="keyword">in</span> <span class="function">enumerate</span>(lines, <span class="number">1</span>):
        <span class="keyword">yield</span> line_num, line

<span class="keyword">for</span> line_num, content <span class="keyword">in</span> simulate_file_generator(sample_lines):
    <span class="function">print</span>(<span class="string">f"行 {line_num}: {content}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 生成器的高级特性</span>
<span class="comment"># 1. 生成器的send()方法</span>
<span class="keyword">def</span> <span class="function">echo_generator</span>():
    <span class="string">"""可以接收外部值的生成器"""</span>
    <span class="keyword">while</span> <span class="keyword">True</span>:
        received = <span class="keyword">yield</span>
        <span class="keyword">if</span> received <span class="keyword">is</span> <span class="keyword">not</span> <span class="keyword">None</span>:
            <span class="keyword">yield</span> <span class="string">f"收到: {received}"</span>

<span class="function">print</span>(<span class="string">"=== 生成器高级特性 ==="</span>)

echo_gen = echo_generator()
<span class="function">next</span>(echo_gen)  <span class="comment"># 启动生成器</span>

<span class="function">print</span>(<span class="string">"发送消息给生成器:"</span>)
response1 = echo_gen.send(<span class="string">"Hello"</span>)
<span class="function">print</span>(<span class="string">f"响应: {response1}"</span>)

response2 = echo_gen.send(<span class="string">"World"</span>)
<span class="function">print</span>(<span class="string">f"响应: {response2}"</span>)

<span class="comment"># 2. 累加器生成器</span>
<span class="keyword">def</span> <span class="function">accumulator_generator</span>():
    <span class="string">"""累加器生成器"""</span>
    total = <span class="number">0</span>
    <span class="keyword">while</span> <span class="keyword">True</span>:
        value = <span class="keyword">yield</span> total
        <span class="keyword">if</span> value <span class="keyword">is</span> <span class="keyword">not</span> <span class="keyword">None</span>:
            total += value

<span class="function">print</span>(<span class="string">"\n累加器生成器:"</span>)
acc_gen = accumulator_generator()
<span class="function">print</span>(<span class="string">f"初始值: {next(acc_gen)}"</span>)
<span class="function">print</span>(<span class="string">f"加5后: {acc_gen.send(5)}"</span>)
<span class="function">print</span>(<span class="string">f"加10后: {acc_gen.send(10)}"</span>)
<span class="function">print</span>(<span class="string">f"加3后: {acc_gen.send(3)}"</span>)

<span class="comment"># 3. 生成器的异常处理</span>
<span class="keyword">def</span> <span class="function">safe_generator</span>():
    <span class="string">"""带异常处理的生成器"""</span>
    <span class="keyword">try</span>:
        <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">5</span>):
            <span class="keyword">yield</span> i
    <span class="keyword">except</span> <span class="function">GeneratorExit</span>:
        <span class="function">print</span>(<span class="string">"生成器被关闭"</span>)
    <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
        <span class="function">print</span>(<span class="string">f"生成器发生异常: {e}"</span>)
        <span class="keyword">yield</span> <span class="string">"异常处理"</span>

<span class="function">print</span>(<span class="string">"\n生成器异常处理:"</span>)
safe_gen = safe_generator()
<span class="keyword">for</span> i, value <span class="keyword">in</span> <span class="function">enumerate</span>(safe_gen):
    <span class="function">print</span>(<span class="string">f"值: {value}"</span>)
    <span class="keyword">if</span> i == <span class="number">2</span>:
        safe_gen.close()  <span class="comment"># 关闭生成器</span>
        <span class="keyword">break</span></div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 生成器的实际应用场景</span>
<span class="comment"># 1. 大数据处理</span>
<span class="keyword">def</span> <span class="function">process_large_dataset</span>(data_size):
    <span class="string">"""模拟处理大数据集"""</span>
    <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(data_size):
        <span class="comment"># 模拟数据处理</span>
        processed_data = {
            <span class="string">'id'</span>: i,
            <span class="string">'value'</span>: i ** <span class="number">2</span>,
            <span class="string">'status'</span>: <span class="string">'processed'</span>
        }
        <span class="keyword">yield</span> processed_data

<span class="function">print</span>(<span class="string">"=== 大数据处理应用 ==="</span>)

<span class="comment"># 处理前5条数据</span>
data_processor = process_large_dataset(<span class="number">1000000</span>)  <span class="comment"># 100万条数据</span>
<span class="keyword">for</span> i, data <span class="keyword">in</span> <span class="function">enumerate</span>(data_processor):
    <span class="keyword">if</span> i < <span class="number">5</span>:
        <span class="function">print</span>(<span class="string">f"处理数据 {i+1}: {data}"</span>)
    <span class="keyword">else</span>:
        <span class="keyword">break</span>

<span class="comment"># 2. 批量处理生成器</span>
<span class="keyword">def</span> <span class="function">batch_generator</span>(data, batch_size):
    <span class="string">"""批量处理生成器"""</span>
    <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">0</span>, <span class="function">len</span>(data), batch_size):
        <span class="keyword">yield</span> data[i:i + batch_size]

<span class="function">print</span>(<span class="string">"\n=== 批量处理 ==="</span>)
data = <span class="function">list</span>(<span class="function">range</span>(<span class="number">20</span>))
batch_size = <span class="number">5</span>

<span class="keyword">for</span> batch_num, batch <span class="keyword">in</span> <span class="function">enumerate</span>(batch_generator(data, batch_size), <span class="number">1</span>):
    <span class="function">print</span>(<span class="string">f"批次 {batch_num}: {batch}"</span>)

<span class="comment"># 3. 无限序列生成器</span>
<span class="keyword">def</span> <span class="function">infinite_counter</span>(start=<span class="number">0</span>, step=<span class="number">1</span>):
    <span class="string">"""无限计数器"""</span>
    current = start
    <span class="keyword">while</span> <span class="keyword">True</span>:
        <span class="keyword">yield</span> current
        current += step

<span class="function">print</span>(<span class="string">"\n=== 无限序列 ==="</span>)
counter = infinite_counter(<span class="number">10</span>, <span class="number">3</span>)
<span class="function">print</span>(<span class="string">"前10个值:"</span>)
<span class="keyword">for</span> i, value <span class="keyword">in</span> <span class="function">enumerate</span>(counter):
    <span class="keyword">if</span> i < <span class="number">10</span>:
        <span class="function">print</span>(<span class="string">f"  {value}"</span>)
    <span class="keyword">else</span>:
        <span class="keyword">break</span>

<span class="comment"># 4. 管道式数据处理</span>
<span class="keyword">def</span> <span class="function">filter_even</span>(numbers):
    <span class="string">"""过滤偶数"""</span>
    <span class="keyword">for</span> num <span class="keyword">in</span> numbers:
        <span class="keyword">if</span> num % <span class="number">2</span> == <span class="number">0</span>:
            <span class="keyword">yield</span> num

<span class="keyword">def</span> <span class="function">square_numbers</span>(numbers):
    <span class="string">"""计算平方"""</span>
    <span class="keyword">for</span> num <span class="keyword">in</span> numbers:
        <span class="keyword">yield</span> num ** <span class="number">2</span>

<span class="keyword">def</span> <span class="function">limit_results</span>(numbers, limit):
    <span class="string">"""限制结果数量"""</span>
    count = <span class="number">0</span>
    <span class="keyword">for</span> num <span class="keyword">in</span> numbers:
        <span class="keyword">if</span> count < limit:
            <span class="keyword">yield</span> num
            count += <span class="number">1</span>
        <span class="keyword">else</span>:
            <span class="keyword">break</span>

<span class="function">print</span>(<span class="string">"\n=== 管道式处理 ==="</span>)
<span class="comment"># 创建处理管道</span>
original_data = <span class="function">range</span>(<span class="number">1</span>, <span class="number">21</span>)  <span class="comment"># 1到20</span>
processed = limit_results(
    square_numbers(
        filter_even(original_data)
    ), 
    <span class="number">5</span>
)

result = <span class="function">list</span>(processed)
<span class="function">print</span>(<span class="string">f"原数据: {list(range(1, 21))}"</span>)
<span class="function">print</span>(<span class="string">f"处理结果: {result}"</span>)
<span class="function">print</span>(<span class="string">"处理步骤: 1→20 → 过滤偶数 → 计算平方 → 取前5个"</span>)</div>
                </div>

                <div class="warning-box">
                    <h3><span class="emoji">⚠️</span>生成器使用注意事项</h3>
                    <ul>
                        <li>生成器只能遍历一次，遍历后会耗尽</li>
                        <li>生成器是惰性求值，只在需要时才计算</li>
                        <li>适合处理大数据集，节省内存</li>
                        <li>不支持索引访问和len()函数</li>
                        <li>可以使用next()、send()、close()等方法控制</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习生成器：</strong>"请详细解释Python中的生成器概念，包括生成器函数、生成器表达式、yield关键字的使用，以及生成器在内存优化和大数据处理中的应用。请提供实际的应用场景和最佳实践。"</p>
                </div>
            </div>
        </div>

        <!-- 第20页：装饰器 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">🎭</span>装饰器</h1>
                <h2><span class="emoji">✨</span>优雅的功能增强</h2>
                <p>装饰器是Python中一种强大的设计模式，它允许在不修改原函数代码的情况下，为函数添加新的功能。装饰器本质上是一个返回函数的高阶函数。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># 装饰器基础概念</span>
<span class="comment"># 装饰器是一个接受函数作为参数并返回函数的函数</span>

<span class="keyword">def</span> <span class="function">simple_decorator</span>(func):
    <span class="string">"""简单的装饰器示例"""</span>
    <span class="keyword">def</span> <span class="function">wrapper</span>():
        <span class="function">print</span>(<span class="string">"函数执行前"</span>)
        result = func()
        <span class="function">print</span>(<span class="string">"函数执行后"</span>)
        <span class="keyword">return</span> result
    <span class="keyword">return</span> wrapper

<span class="comment"># 使用装饰器的两种方式</span>
<span class="comment"># 方式1：使用@语法糖</span>
<span class="decorator">@simple_decorator</span>
<span class="keyword">def</span> <span class="function">say_hello</span>():
    <span class="function">print</span>(<span class="string">"Hello, World!"</span>)
    <span class="keyword">return</span> <span class="string">"greeting"</span>

<span class="comment"># 方式2：手动应用装饰器</span>
<span class="keyword">def</span> <span class="function">say_goodbye</span>():
    <span class="function">print</span>(<span class="string">"Goodbye, World!"</span>)
    <span class="keyword">return</span> <span class="string">"farewell"</span>

say_goodbye = simple_decorator(say_goodbye)

<span class="function">print</span>(<span class="string">"=== 基础装饰器演示 ==="</span>)
<span class="function">print</span>(<span class="string">"调用装饰后的函数:"</span>)
result1 = say_hello()
<span class="function">print</span>(<span class="string">f"返回值: {result1}"</span>)

<span class="function">print</span>(<span class="string">"\n手动装饰的函数:"</span>)
result2 = say_goodbye()
<span class="function">print</span>(<span class="string">f"返回值: {result2}"</span>)

<span class="comment"># 带参数的装饰器</span>
<span class="keyword">def</span> <span class="function">args_decorator</span>(func):
    <span class="string">"""处理带参数函数的装饰器"""</span>
    <span class="keyword">def</span> <span class="function">wrapper</span>(*args, **kwargs):
        <span class="function">print</span>(<span class="string">f"调用函数: {func.__name__}"</span>)
        <span class="function">print</span>(<span class="string">f"位置参数: {args}"</span>)
        <span class="function">print</span>(<span class="string">f"关键字参数: {kwargs}"</span>)
        result = func(*args, **kwargs)
        <span class="function">print</span>(<span class="string">f"函数返回: {result}"</span>)
        <span class="keyword">return</span> result
    <span class="keyword">return</span> wrapper

<span class="decorator">@args_decorator</span>
<span class="keyword">def</span> <span class="function">add_numbers</span>(a, b, operation=<span class="string">"add"</span>):
    <span class="keyword">if</span> operation == <span class="string">"add"</span>:
        <span class="keyword">return</span> a + b
    <span class="keyword">elif</span> operation == <span class="string">"multiply"</span>:
        <span class="keyword">return</span> a * b
    <span class="keyword">else</span>:
        <span class="keyword">return</span> <span class="string">"未知操作"</span>

<span class="function">print</span>(<span class="string">"\n=== 带参数的装饰器 ==="</span>)
add_numbers(<span class="number">5</span>, <span class="number">3</span>)
add_numbers(<span class="number">4</span>, <span class="number">6</span>, operation=<span class="string">"multiply"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 实用装饰器示例</span>
<span class="comment"># 1. 计时装饰器</span>
<span class="keyword">import</span> time
<span class="keyword">import</span> functools

<span class="keyword">def</span> <span class="function">timer</span>(func):
    <span class="string">"""计算函数执行时间的装饰器"""</span>
    <span class="decorator">@functools.wraps</span>(func)
    <span class="keyword">def</span> <span class="function">wrapper</span>(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        <span class="function">print</span>(<span class="string">f"{func.__name__} 执行时间: {execution_time:.4f}秒"</span>)
        <span class="keyword">return</span> result
    <span class="keyword">return</span> wrapper

<span class="comment"># 2. 重试装饰器</span>
<span class="keyword">def</span> <span class="function">retry</span>(max_attempts=<span class="number">3</span>, delay=<span class="number">1</span>):
    <span class="string">"""重试装饰器"""</span>
    <span class="keyword">def</span> <span class="function">decorator</span>(func):
        <span class="decorator">@functools.wraps</span>(func)
        <span class="keyword">def</span> <span class="function">wrapper</span>(*args, **kwargs):
            <span class="keyword">for</span> attempt <span class="keyword">in</span> <span class="function">range</span>(max_attempts):
                <span class="keyword">try</span>:
                    <span class="keyword">return</span> func(*args, **kwargs)
                <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
                    <span class="keyword">if</span> attempt == max_attempts - <span class="number">1</span>:
                        <span class="function">print</span>(<span class="string">f"函数 {func.__name__} 在 {max_attempts} 次尝试后仍然失败"</span>)
                        <span class="keyword">raise</span> e
                    <span class="function">print</span>(<span class="string">f"第 {attempt + 1} 次尝试失败: {e}，{delay}秒后重试..."</span>)
                    time.sleep(delay)
        <span class="keyword">return</span> wrapper
    <span class="keyword">return</span> decorator

<span class="comment"># 3. 缓存装饰器</span>
<span class="keyword">def</span> <span class="function">cache</span>(func):
    <span class="string">"""简单的缓存装饰器"""</span>
    cached_results = {}
    
    <span class="decorator">@functools.wraps</span>(func)
    <span class="keyword">def</span> <span class="function">wrapper</span>(*args, **kwargs):
        <span class="comment"># 创建缓存键</span>
        key = <span class="function">str</span>(args) + <span class="function">str</span>(<span class="function">sorted</span>(kwargs.items()))
        
        <span class="keyword">if</span> key <span class="keyword">in</span> cached_results:
            <span class="function">print</span>(<span class="string">f"缓存命中: {func.__name__}"</span>)
            <span class="keyword">return</span> cached_results[key]
        
        <span class="function">print</span>(<span class="string">f"计算结果: {func.__name__}"</span>)
        result = func(*args, **kwargs)
        cached_results[key] = result
        <span class="keyword">return</span> result
    
    <span class="keyword">return</span> wrapper

<span class="function">print</span>(<span class="string">"=== 实用装饰器演示 ==="</span>)

<span class="comment"># 使用计时装饰器</span>
<span class="decorator">@timer</span>
<span class="keyword">def</span> <span class="function">slow_function</span>():
    time.sleep(<span class="number">0.1</span>)  <span class="comment"># 模拟耗时操作</span>
    <span class="keyword">return</span> <span class="string">"完成"</span>

<span class="function">print</span>(<span class="string">"计时装饰器测试:"</span>)
slow_function()

<span class="comment"># 使用缓存装饰器</span>
<span class="decorator">@cache</span>
<span class="keyword">def</span> <span class="function">fibonacci</span>(n):
    <span class="keyword">if</span> n <= <span class="number">1</span>:
        <span class="keyword">return</span> n
    <span class="keyword">return</span> fibonacci(n-<span class="number">1</span>) + fibonacci(n-<span class="number">2</span>)

<span class="function">print</span>(<span class="string">"\n缓存装饰器测试:"</span>)
<span class="function">print</span>(<span class="string">f"fibonacci(10) = {fibonacci(10)}"</span>)
<span class="function">print</span>(<span class="string">f"fibonacci(10) = {fibonacci(10)}"</span>)  <span class="comment"># 第二次调用会使用缓存</span></div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 带参数的装饰器</span>
<span class="comment"># 装饰器工厂：返回装饰器的函数</span>

<span class="keyword">def</span> <span class="function">log_calls</span>(log_level=<span class="string">"INFO"</span>, include_args=<span class="keyword">True</span>):
    <span class="string">"""可配置的日志装饰器"""</span>
    <span class="keyword">def</span> <span class="function">decorator</span>(func):
        <span class="decorator">@functools.wraps</span>(func)
        <span class="keyword">def</span> <span class="function">wrapper</span>(*args, **kwargs):
            <span class="keyword">if</span> include_args:
                <span class="function">print</span>(<span class="string">f"[{log_level}] 调用 {func.__name__} 参数: {args}, {kwargs}"</span>)
            <span class="keyword">else</span>:
                <span class="function">print</span>(<span class="string">f"[{log_level}] 调用 {func.__name__}"</span>)
            
            result = func(*args, **kwargs)
            <span class="function">print</span>(<span class="string">f"[{log_level}] {func.__name__} 返回: {result}"</span>)
            <span class="keyword">return</span> result
        <span class="keyword">return</span> wrapper
    <span class="keyword">return</span> decorator

<span class="comment"># 权限检查装饰器</span>
<span class="keyword">def</span> <span class="function">require_permission</span>(permission):
    <span class="string">"""权限检查装饰器"""</span>
    <span class="keyword">def</span> <span class="function">decorator</span>(func):
        <span class="decorator">@functools.wraps</span>(func)
        <span class="keyword">def</span> <span class="function">wrapper</span>(*args, **kwargs):
            <span class="comment"># 模拟权限检查</span>
            user_permissions = [<span class="string">"read"</span>, <span class="string">"write"</span>]  <span class="comment"># 模拟用户权限</span>
            
            <span class="keyword">if</span> permission <span class="keyword">not</span> <span class="keyword">in</span> user_permissions:
                <span class="keyword">raise</span> <span class="function">PermissionError</span>(<span class="string">f"需要 {permission} 权限才能执行 {func.__name__}"</span>)
            
            <span class="function">print</span>(<span class="string">f"权限检查通过: {permission}"</span>)
            <span class="keyword">return</span> func(*args, **kwargs)
        <span class="keyword">return</span> wrapper
    <span class="keyword">return</span> decorator

<span class="function">print</span>(<span class="string">"\n=== 带参数的装饰器 ==="</span>)

<span class="comment"># 使用可配置的日志装饰器</span>
<span class="decorator">@log_calls</span>(log_level=<span class="string">"DEBUG"</span>, include_args=<span class="keyword">True</span>)
<span class="keyword">def</span> <span class="function">calculate_area</span>(length, width):
    <span class="keyword">return</span> length * width

<span class="decorator">@log_calls</span>(log_level=<span class="string">"WARNING"</span>, include_args=<span class="keyword">False</span>)
<span class="keyword">def</span> <span class="function">get_user_info</span>(user_id):
    <span class="keyword">return</span> {<span class="string">"id"</span>: user_id, <span class="string">"name"</span>: <span class="string">"张三"</span>, <span class="string">"email"</span>: <span class="string">"<EMAIL>"</span>}

<span class="function">print</span>(<span class="string">"日志装饰器测试:"</span>)
calculate_area(<span class="number">5</span>, <span class="number">3</span>)
get_user_info(<span class="number">123</span>)

<span class="comment"># 使用权限装饰器</span>
<span class="decorator">@require_permission</span>(<span class="string">"read"</span>)
<span class="keyword">def</span> <span class="function">read_file</span>(filename):
    <span class="keyword">return</span> <span class="string">f"读取文件: {filename}"</span>

<span class="decorator">@require_permission</span>(<span class="string">"admin"</span>)
<span class="keyword">def</span> <span class="function">delete_user</span>(user_id):
    <span class="keyword">return</span> <span class="string">f"删除用户: {user_id}"</span>

<span class="function">print</span>(<span class="string">"\n权限装饰器测试:"</span>)
<span class="keyword">try</span>:
    <span class="function">print</span>(read_file(<span class="string">"data.txt"</span>))
<span class="keyword">except</span> <span class="function">PermissionError</span> <span class="keyword">as</span> e:
    <span class="function">print</span>(<span class="string">f"权限错误: {e}"</span>)

<span class="keyword">try</span>:
    <span class="function">print</span>(delete_user(<span class="number">456</span>))
<span class="keyword">except</span> <span class="function">PermissionError</span> <span class="keyword">as</span> e:
    <span class="function">print</span>(<span class="string">f"权限错误: {e}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 类装饰器</span>
<span class="comment"># 使用类作为装饰器</span>

<span class="keyword">class</span> <span class="function">CallCounter</span>:
    <span class="string">"""统计函数调用次数的类装饰器"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>, func):
        <span class="keyword">self</span>.func = func
        <span class="keyword">self</span>.count = <span class="number">0</span>
        functools.update_wrapper(<span class="keyword">self</span>, func)
    
    <span class="keyword">def</span> <span class="function">__call__</span>(<span class="keyword">self</span>, *args, **kwargs):
        <span class="keyword">self</span>.count += <span class="number">1</span>
        <span class="function">print</span>(<span class="string">f"{self.func.__name__} 被调用第 {self.count} 次"</span>)
        <span class="keyword">return</span> <span class="keyword">self</span>.func(*args, **kwargs)
    
    <span class="keyword">def</span> <span class="function">get_count</span>(<span class="keyword">self</span>):
        <span class="keyword">return</span> <span class="keyword">self</span>.count

<span class="comment"># 带参数的类装饰器</span>
<span class="keyword">class</span> <span class="function">RateLimiter</span>:
    <span class="string">"""限制函数调用频率的装饰器"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>, max_calls, time_window):
        <span class="keyword">self</span>.max_calls = max_calls
        <span class="keyword">self</span>.time_window = time_window
        <span class="keyword">self</span>.calls = []
    
    <span class="keyword">def</span> <span class="function">__call__</span>(<span class="keyword">self</span>, func):
        <span class="decorator">@functools.wraps</span>(func)
        <span class="keyword">def</span> <span class="function">wrapper</span>(*args, **kwargs):
            now = time.time()
            
            <span class="comment"># 清理过期的调用记录</span>
            <span class="keyword">self</span>.calls = [call_time <span class="keyword">for</span> call_time <span class="keyword">in</span> <span class="keyword">self</span>.calls 
                          <span class="keyword">if</span> now - call_time < <span class="keyword">self</span>.time_window]
            
            <span class="keyword">if</span> <span class="function">len</span>(<span class="keyword">self</span>.calls) >= <span class="keyword">self</span>.max_calls:
                <span class="keyword">raise</span> <span class="function">Exception</span>(<span class="string">f"调用频率超限: {self.max_calls}次/{self.time_window}秒"</span>)
            
            <span class="keyword">self</span>.calls.append(now)
            <span class="keyword">return</span> func(*args, **kwargs)
        
        <span class="keyword">return</span> wrapper

<span class="function">print</span>(<span class="string">"\n=== 类装饰器演示 ==="</span>)

<span class="comment"># 使用调用计数器</span>
<span class="decorator">@CallCounter</span>
<span class="keyword">def</span> <span class="function">greet</span>(name):
    <span class="keyword">return</span> <span class="string">f"Hello, {name}!"</span>

<span class="function">print</span>(<span class="string">"调用计数器测试:"</span>)
<span class="function">print</span>(greet(<span class="string">"Alice"</span>))
<span class="function">print</span>(greet(<span class="string">"Bob"</span>))
<span class="function">print</span>(greet(<span class="string">"Charlie"</span>))
<span class="function">print</span>(<span class="string">f"总调用次数: {greet.get_count()}"</span>)

<span class="comment"># 使用频率限制器</span>
<span class="decorator">@RateLimiter</span>(max_calls=<span class="number">2</span>, time_window=<span class="number">3</span>)
<span class="keyword">def</span> <span class="function">api_call</span>(data):
    <span class="keyword">return</span> <span class="string">f"处理数据: {data}"</span>

<span class="function">print</span>(<span class="string">"\n频率限制器测试:"</span>)
<span class="keyword">try</span>:
    <span class="function">print</span>(api_call(<span class="string">"数据1"</span>))
    <span class="function">print</span>(api_call(<span class="string">"数据2"</span>))
    <span class="function">print</span>(api_call(<span class="string">"数据3"</span>))  <span class="comment"># 这次调用会被限制</span>
<span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
    <span class="function">print</span>(<span class="string">f"限制错误: {e}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 多个装饰器的组合使用</span>
<span class="comment"># 装饰器的执行顺序：从下到上（从内到外）</span>

<span class="keyword">def</span> <span class="function">bold</span>(func):
    <span class="string">"""加粗装饰器"""</span>
    <span class="decorator">@functools.wraps</span>(func)
    <span class="keyword">def</span> <span class="function">wrapper</span>(*args, **kwargs):
        result = func(*args, **kwargs)
        <span class="keyword">return</span> <span class="string">f"<b>{result}</b>"</span>
    <span class="keyword">return</span> wrapper

<span class="keyword">def</span> <span class="function">italic</span>(func):
    <span class="string">"""斜体装饰器"""</span>
    <span class="decorator">@functools.wraps</span>(func)
    <span class="keyword">def</span> <span class="function">wrapper</span>(*args, **kwargs):
        result = func(*args, **kwargs)
        <span class="keyword">return</span> <span class="string">f"<i>{result}</i>"</span>
    <span class="keyword">return</span> wrapper

<span class="keyword">def</span> <span class="function">underline</span>(func):
    <span class="string">"""下划线装饰器"""</span>
    <span class="decorator">@functools.wraps</span>(func)
    <span class="keyword">def</span> <span class="function">wrapper</span>(*args, **kwargs):
        result = func(*args, **kwargs)
        <span class="keyword">return</span> <span class="string">f"<u>{result}</u>"</span>
    <span class="keyword">return</span> wrapper

<span class="function">print</span>(<span class="string">"=== 多装饰器组合 ==="</span>)

<span class="comment"># 多个装饰器的应用</span>
<span class="decorator">@bold</span>
<span class="decorator">@italic</span>
<span class="decorator">@underline</span>
<span class="keyword">def</span> <span class="function">format_text</span>(text):
    <span class="keyword">return</span> text

<span class="function">print</span>(<span class="string">"多装饰器效果:"</span>)
<span class="function">print</span>(format_text(<span class="string">"Python装饰器"</span>))

<span class="comment"># 实际应用：Web API装饰器组合</span>
<span class="decorator">@log_calls</span>(log_level=<span class="string">"INFO"</span>)
<span class="decorator">@timer</span>
<span class="decorator">@require_permission</span>(<span class="string">"read"</span>)
<span class="keyword">def</span> <span class="function">get_user_data</span>(user_id):
    <span class="comment"># 模拟数据库查询</span>
    time.sleep(<span class="number">0.05</span>)  <span class="comment"># 模拟查询延迟</span>
    <span class="keyword">return</span> {
        <span class="string">"id"</span>: user_id,
        <span class="string">"name"</span>: <span class="string">"用户名"</span>,
        <span class="string">"email"</span>: <span class="string">"<EMAIL>"</span>,
        <span class="string">"created_at"</span>: <span class="string">"2024-01-01"</span>
    }

<span class="function">print</span>(<span class="string">"\nWeb API装饰器组合:"</span>)
<span class="keyword">try</span>:
    user_data = get_user_data(<span class="number">12345</span>)
    <span class="function">print</span>(<span class="string">f"用户数据: {user_data}"</span>)
<span class="keyword">except</span> <span class="function">PermissionError</span> <span class="keyword">as</span> e:
    <span class="function">print</span>(<span class="string">f"权限错误: {e}"</span>)

<span class="comment"># 装饰器的内省和调试</span>
<span class="function">print</span>(<span class="string">"\n=== 装饰器内省 ==="</span>)
<span class="function">print</span>(<span class="string">f"函数名: {get_user_data.__name__}"</span>)
<span class="function">print</span>(<span class="string">f"函数文档: {get_user_data.__doc__}"</span>)
<span class="function">print</span>(<span class="string">f"函数模块: {get_user_data.__module__}"</span>)</div>
                </div>

                <div class="warning-box">
                    <h3><span class="emoji">⚠️</span>装饰器最佳实践</h3>
                    <ul>
                        <li>使用functools.wraps保持原函数的元数据</li>
                        <li>装饰器应该是幂等的，多次应用不会产生副作用</li>
                        <li>考虑装饰器的性能影响，避免在热点代码中使用重型装饰器</li>
                        <li>为装饰器提供清晰的文档和使用示例</li>
                        <li>合理组合多个装饰器，注意执行顺序</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习装饰器：</strong>"请详细解释Python中的装饰器概念，包括基础装饰器、带参数的装饰器、类装饰器，以及装饰器在实际开发中的应用场景。请提供常用的装饰器模式和最佳实践。"</p>
                </div>
            </div>
        </div>

        <!-- 第21页：上下文管理器 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">🔒</span>上下文管理器</h1>
                <h2><span class="emoji">🛡️</span>资源管理的优雅方案</h2>
                <p>上下文管理器是Python中用于管理资源的一种机制，它确保资源在使用后能够正确释放。通过with语句和__enter__、__exit__方法实现。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># 上下文管理器基础概念</span>
<span class="comment"># with语句的基本使用</span>

<span class="comment"># 1. 文件操作 - 最常见的上下文管理器</span>
<span class="function">print</span>(<span class="string">"=== 文件操作上下文管理器 ==="</span>)

<span class="comment"># 传统方式（不推荐）</span>
<span class="keyword">try</span>:
    file = <span class="function">open</span>(<span class="string">"example.txt"</span>, <span class="string">"w"</span>)
    file.write(<span class="string">"Hello, World!"</span>)
finally:
    file.close()  <span class="comment"># 必须手动关闭</span>

<span class="comment"># 使用with语句（推荐）</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"example.txt"</span>, <span class="string">"w"</span>) <span class="keyword">as</span> file:
    file.write(<span class="string">"Hello, World!"</span>)
    <span class="comment"># 文件会自动关闭，即使发生异常</span>

<span class="comment"># 读取文件内容</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"example.txt"</span>, <span class="string">"r"</span>) <span class="keyword">as</span> file:
    content = file.read()
    <span class="function">print</span>(<span class="string">f"文件内容: {content}"</span>)

<span class="comment"># 2. 多个文件同时操作</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"source.txt"</span>, <span class="string">"w"</span>) <span class="keyword">as</span> src, <span class="function">open</span>(<span class="string">"dest.txt"</span>, <span class="string">"w"</span>) <span class="keyword">as</span> dst:
    src.write(<span class="string">"源文件内容"</span>)
    dst.write(<span class="string">"目标文件内容"</span>)
    <span class="function">print</span>(<span class="string">"两个文件都会自动关闭"</span>)

<span class="comment"># 3. 异常处理演示</span>
<span class="function">print</span>(<span class="string">"\n=== 异常处理演示 ==="</span>)
<span class="keyword">try</span>:
    <span class="keyword">with</span> <span class="function">open</span>(<span class="string">"example.txt"</span>, <span class="string">"r"</span>) <span class="keyword">as</span> file:
        content = file.read()
        <span class="function">print</span>(<span class="string">f"成功读取: {content}"</span>)
        <span class="comment"># 即使这里发生异常，文件也会被正确关闭</span>
        <span class="keyword">if</span> <span class="function">len</span>(content) > <span class="number">5</span>:
            <span class="keyword">raise</span> <span class="function">ValueError</span>(<span class="string">"内容太长"</span>)
<span class="keyword">except</span> <span class="function">ValueError</span> <span class="keyword">as</span> e:
    <span class="function">print</span>(<span class="string">f"捕获异常: {e}"</span>)
    <span class="function">print</span>(<span class="string">"文件仍然被正确关闭"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 自定义上下文管理器</span>
<span class="comment"># 方法1：使用类实现</span>

<span class="keyword">class</span> <span class="function">DatabaseConnection</span>:
    <span class="string">"""数据库连接上下文管理器"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>, host, port):
        <span class="keyword">self</span>.host = host
        <span class="keyword">self</span>.port = port
        <span class="keyword">self</span>.connection = <span class="keyword">None</span>
    
    <span class="keyword">def</span> <span class="function">__enter__</span>(<span class="keyword">self</span>):
        <span class="function">print</span>(<span class="string">f"连接到数据库 {self.host}:{self.port}"</span>)
        <span class="keyword">self</span>.connection = <span class="string">f"connection_to_{self.host}"</span>  <span class="comment"># 模拟连接</span>
        <span class="keyword">return</span> <span class="keyword">self</span>.connection
    
    <span class="keyword">def</span> <span class="function">__exit__</span>(<span class="keyword">self</span>, exc_type, exc_val, exc_tb):
        <span class="function">print</span>(<span class="string">f"关闭数据库连接 {self.host}:{self.port}"</span>)
        <span class="keyword">if</span> exc_type <span class="keyword">is</span> <span class="keyword">not</span> <span class="keyword">None</span>:
            <span class="function">print</span>(<span class="string">f"发生异常: {exc_type.__name__}: {exc_val}"</span>)
            <span class="function">print</span>(<span class="string">"执行回滚操作"</span>)
        <span class="keyword">else</span>:
            <span class="function">print</span>(<span class="string">"提交事务"</span>)
        <span class="keyword">self</span>.connection = <span class="keyword">None</span>
        <span class="keyword">return</span> <span class="keyword">False</span>  <span class="comment"># 不抑制异常</span>

<span class="comment"># 使用自定义上下文管理器</span>
<span class="function">print</span>(<span class="string">"=== 自定义上下文管理器 ==="</span>)

<span class="keyword">with</span> DatabaseConnection(<span class="string">"localhost"</span>, <span class="number">5432</span>) <span class="keyword">as</span> conn:
    <span class="function">print</span>(<span class="string">f"使用连接: {conn}"</span>)
    <span class="function">print</span>(<span class="string">"执行数据库操作..."</span>)

<span class="function">print</span>(<span class="string">"\n异常情况下的上下文管理器:"</span>)
<span class="keyword">try</span>:
    <span class="keyword">with</span> DatabaseConnection(<span class="string">"localhost"</span>, <span class="number">3306</span>) <span class="keyword">as</span> conn:
        <span class="function">print</span>(<span class="string">f"使用连接: {conn}"</span>)
        <span class="keyword">raise</span> <span class="function">RuntimeError</span>(<span class="string">"数据库操作失败"</span>)
<span class="keyword">except</span> <span class="function">RuntimeError</span> <span class="keyword">as</span> e:
    <span class="function">print</span>(<span class="string">f"捕获到异常: {e}"</span>)

<span class="comment"># 计时器上下文管理器</span>
<span class="keyword">import</span> time

<span class="keyword">class</span> <span class="function">Timer</span>:
    <span class="string">"""计时器上下文管理器"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>, name=<span class="string">"操作"</span>):
        <span class="keyword">self</span>.name = name
        <span class="keyword">self</span>.start_time = <span class="keyword">None</span>
    
    <span class="keyword">def</span> <span class="function">__enter__</span>(<span class="keyword">self</span>):
        <span class="function">print</span>(<span class="string">f"开始 {self.name}..."</span>)
        <span class="keyword">self</span>.start_time = time.time()
        <span class="keyword">return</span> <span class="keyword">self</span>
    
    <span class="keyword">def</span> <span class="function">__exit__</span>(<span class="keyword">self</span>, exc_type, exc_val, exc_tb):
        end_time = time.time()
        duration = end_time - <span class="keyword">self</span>.start_time
        <span class="function">print</span>(<span class="string">f"{self.name} 完成，耗时: {duration:.4f}秒"</span>)

<span class="function">print</span>(<span class="string">"\n=== 计时器上下文管理器 ==="</span>)
<span class="keyword">with</span> Timer(<span class="string">"数据处理"</span>):
    <span class="comment"># 模拟一些耗时操作</span>
    time.sleep(<span class="number">0.1</span>)
    <span class="function">print</span>(<span class="string">"正在处理数据..."</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 方法2：使用contextlib模块</span>
<span class="keyword">from</span> contextlib <span class="keyword">import</span> contextmanager
<span class="keyword">import</span> os
<span class="keyword">import</span> tempfile

<span class="comment"># 使用@contextmanager装饰器</span>
<span class="decorator">@contextmanager</span>
<span class="keyword">def</span> <span class="function">temporary_directory</span>():
    <span class="string">"""临时目录上下文管理器"""</span>
    temp_dir = tempfile.mkdtemp()
    <span class="function">print</span>(<span class="string">f"创建临时目录: {temp_dir}"</span>)
    <span class="keyword">try</span>:
        <span class="keyword">yield</span> temp_dir  <span class="comment"># 这里是__enter__的返回值</span>
    <span class="keyword">finally</span>:
        <span class="comment"># 清理临时目录</span>
        <span class="keyword">import</span> shutil
        shutil.rmtree(temp_dir)
        <span class="function">print</span>(<span class="string">f"删除临时目录: {temp_dir}"</span>)

<span class="decorator">@contextmanager</span>
<span class="keyword">def</span> <span class="function">change_directory</span>(path):
    <span class="string">"""临时改变工作目录"""</span>
    original_dir = os.getcwd()
    <span class="function">print</span>(<span class="string">f"当前目录: {original_dir}"</span>)
    <span class="function">print</span>(<span class="string">f"切换到: {path}"</span>)
    <span class="keyword">try</span>:
        os.chdir(path)
        <span class="keyword">yield</span> path
    <span class="keyword">finally</span>:
        os.chdir(original_dir)
        <span class="function">print</span>(<span class="string">f"恢复到原目录: {original_dir}"</span>)

<span class="decorator">@contextmanager</span>
<span class="keyword">def</span> <span class="function">suppress_output</span>():
    <span class="string">"""抑制标准输出"""</span>
    <span class="keyword">import</span> sys
    <span class="keyword">from</span> io <span class="keyword">import</span> StringIO
    
    old_stdout = sys.stdout
    sys.stdout = StringIO()
    <span class="keyword">try</span>:
        <span class="keyword">yield</span>
    <span class="keyword">finally</span>:
        sys.stdout = old_stdout

<span class="function">print</span>(<span class="string">"\n=== contextlib装饰器 ==="</span>)

<span class="comment"># 使用临时目录</span>
<span class="keyword">with</span> temporary_directory() <span class="keyword">as</span> temp_dir:
    <span class="function">print</span>(<span class="string">f"在临时目录中工作: {temp_dir}"</span>)
    <span class="comment"># 创建一个临时文件</span>
    temp_file = os.path.join(temp_dir, <span class="string">"temp.txt"</span>)
    <span class="keyword">with</span> <span class="function">open</span>(temp_file, <span class="string">"w"</span>) <span class="keyword">as</span> f:
        f.write(<span class="string">"临时文件内容"</span>)
    <span class="function">print</span>(<span class="string">f"创建文件: {temp_file}"</span>)

<span class="comment"># 使用目录切换</span>
<span class="function">print</span>(<span class="string">"\n目录切换演示:"</span>)
<span class="keyword">with</span> change_directory(<span class="string">"/tmp"</span>):
    <span class="function">print</span>(<span class="string">f"现在在: {os.getcwd()}"</span>)
    <span class="comment"># 在这里可以进行需要特定目录的操作</span>

<span class="comment"># 使用输出抑制</span>
<span class="function">print</span>(<span class="string">"\n输出抑制演示:"</span>)
<span class="function">print</span>(<span class="string">"这行会显示"</span>)
<span class="keyword">with</span> suppress_output():
    <span class="function">print</span>(<span class="string">"这行不会显示"</span>)
    <span class="function">print</span>(<span class="string">"这行也不会显示"</span>)
<span class="function">print</span>(<span class="string">"这行又会显示了"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 实际应用场景</span>
<span class="comment"># 1. 线程锁管理</span>
<span class="keyword">import</span> threading
<span class="keyword">import</span> time

<span class="keyword">class</span> <span class="function">ThreadSafePrinter</span>:
    <span class="string">"""线程安全的打印器"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>):
        <span class="keyword">self</span>.lock = threading.Lock()
    
    <span class="decorator">@contextmanager</span>
    <span class="keyword">def</span> <span class="function">safe_print</span>(<span class="keyword">self</span>):
        <span class="string">"""安全打印上下文管理器"""</span>
        <span class="keyword">self</span>.lock.acquire()
        <span class="function">print</span>(<span class="string">"[获取锁]"</span>)
        <span class="keyword">try</span>:
            <span class="keyword">yield</span>
        <span class="keyword">finally</span>:
            <span class="function">print</span>(<span class="string">"[释放锁]"</span>)
            <span class="keyword">self</span>.lock.release()

<span class="comment"># 2. 配置管理</span>
<span class="decorator">@contextmanager</span>
<span class="keyword">def</span> <span class="function">config_override</span>(config_dict, **overrides):
    <span class="string">"""临时覆盖配置"""</span>
    original_values = {}
    
    <span class="comment"># 保存原始值并设置新值</span>
    <span class="keyword">for</span> key, new_value <span class="keyword">in</span> overrides.items():
        <span class="keyword">if</span> key <span class="keyword">in</span> config_dict:
            original_values[key] = config_dict[key]
        config_dict[key] = new_value
        <span class="function">print</span>(<span class="string">f"设置配置 {key} = {new_value}"</span>)
    
    <span class="keyword">try</span>:
        <span class="keyword">yield</span> config_dict
    <span class="keyword">finally</span>:
        <span class="comment"># 恢复原始值</span>
        <span class="keyword">for</span> key, original_value <span class="keyword">in</span> original_values.items():
            config_dict[key] = original_value
            <span class="function">print</span>(<span class="string">f"恢复配置 {key} = {original_value}"</span>)
        
        <span class="comment"># 删除新添加的键</span>
        <span class="keyword">for</span> key <span class="keyword">in</span> overrides:
            <span class="keyword">if</span> key <span class="keyword">not</span> <span class="keyword">in</span> original_values:
                <span class="keyword">del</span> config_dict[key]
                <span class="function">print</span>(<span class="string">f"删除配置 {key}"</span>)

<span class="comment"># 3. 性能监控</span>
<span class="decorator">@contextmanager</span>
<span class="keyword">def</span> <span class="function">performance_monitor</span>(operation_name):
    <span class="string">"""性能监控上下文管理器"""</span>
    <span class="keyword">import</span> psutil
    <span class="keyword">import</span> tracemalloc
    
    <span class="comment"># 开始监控</span>
    tracemalloc.start()
    process = psutil.Process()
    start_memory = process.memory_info().rss
    start_time = time.time()
    
    <span class="function">print</span>(<span class="string">f"开始监控: {operation_name}"</span>)
    <span class="function">print</span>(<span class="string">f"初始内存: {start_memory / 1024 / 1024:.2f} MB"</span>)
    
    <span class="keyword">try</span>:
        <span class="keyword">yield</span>
    <span class="keyword">finally</span>:
        <span class="comment"># 结束监控</span>
        end_time = time.time()
        end_memory = process.memory_info().rss
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        <span class="function">print</span>(<span class="string">f"结束监控: {operation_name}"</span>)
        <span class="function">print</span>(<span class="string">f"执行时间: {end_time - start_time:.4f}秒"</span>)
        <span class="function">print</span>(<span class="string">f"内存变化: {(end_memory - start_memory) / 1024 / 1024:.2f} MB"</span>)
        <span class="function">print</span>(<span class="string">f"峰值内存: {peak / 1024 / 1024:.2f} MB"</span>)

<span class="function">print</span>(<span class="string">"\n=== 实际应用场景 ==="</span>)

<span class="comment"># 线程安全打印</span>
printer = ThreadSafePrinter()
<span class="keyword">with</span> printer.safe_print():
    <span class="function">print</span>(<span class="string">"这是线程安全的打印"</span>)
    time.sleep(<span class="number">0.01</span>)
    <span class="function">print</span>(<span class="string">"多行内容不会被其他线程打断"</span>)

<span class="comment"># 配置管理</span>
config = {<span class="string">"debug"</span>: <span class="keyword">False</span>, <span class="string">"timeout"</span>: <span class="number">30</span>}
<span class="function">print</span>(<span class="string">f"\n原始配置: {config}"</span>)

<span class="keyword">with</span> config_override(config, debug=<span class="keyword">True</span>, timeout=<span class="number">60</span>, new_option=<span class="string">"test"</span>):
    <span class="function">print</span>(<span class="string">f"临时配置: {config}"</span>)
    <span class="comment"># 在这里使用修改后的配置</span>

<span class="function">print</span>(<span class="string">f"恢复后配置: {config}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 高级用法：嵌套和组合上下文管理器</span>
<span class="keyword">from</span> contextlib <span class="keyword">import</span> ExitStack

<span class="decorator">@contextmanager</span>
<span class="keyword">def</span> <span class="function">log_operation</span>(operation):
    <span class="string">"""记录操作日志"""</span>
    <span class="function">print</span>(<span class="string">f"[LOG] 开始: {operation}"</span>)
    start_time = time.time()
    <span class="keyword">try</span>:
        <span class="keyword">yield</span>
    <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
        <span class="function">print</span>(<span class="string">f"[ERROR] {operation} 失败: {e}"</span>)
        <span class="keyword">raise</span>
    <span class="keyword">finally</span>:
        duration = time.time() - start_time
        <span class="function">print</span>(<span class="string">f"[LOG] 结束: {operation} (耗时: {duration:.4f}秒)"</span>)

<span class="comment"># 使用ExitStack管理多个上下文管理器</span>
<span class="keyword">def</span> <span class="function">process_multiple_files</span>(filenames):
    <span class="string">"""处理多个文件"""</span>
    <span class="keyword">with</span> ExitStack() <span class="keyword">as</span> stack:
        files = []
        <span class="keyword">for</span> filename <span class="keyword">in</span> filenames:
            <span class="comment"># 动态添加上下文管理器</span>
            file_obj = stack.enter_context(<span class="function">open</span>(filename, <span class="string">"w"</span>))
            files.append(file_obj)
        
        <span class="comment"># 同时写入所有文件</span>
        <span class="keyword">for</span> i, file_obj <span class="keyword">in</span> <span class="function">enumerate</span>(files):
            file_obj.write(<span class="string">f"这是文件 {i+1} 的内容\n"</span>)
        
        <span class="function">print</span>(<span class="string">f"成功处理 {len(files)} 个文件"</span>)
        <span class="comment"># 所有文件会自动关闭</span>

<span class="comment"># 嵌套上下文管理器</span>
<span class="keyword">def</span> <span class="function">complex_operation</span>():
    <span class="string">"""复杂操作演示"""</span>
    <span class="keyword">with</span> log_operation(<span class="string">"复杂操作"</span>):
        <span class="keyword">with</span> Timer(<span class="string">"数据准备"</span>):
            <span class="comment"># 模拟数据准备</span>
            time.sleep(<span class="number">0.05</span>)
            data = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>]
        
        <span class="keyword">with</span> temporary_directory() <span class="keyword">as</span> temp_dir:
            <span class="keyword">with</span> Timer(<span class="string">"文件处理"</span>):
                <span class="comment"># 在临时目录中处理文件</span>
                temp_file = os.path.join(temp_dir, <span class="string">"data.txt"</span>)
                <span class="keyword">with</span> <span class="function">open</span>(temp_file, <span class="string">"w"</span>) <span class="keyword">as</span> f:
                    <span class="keyword">for</span> item <span class="keyword">in</span> data:
                        f.write(<span class="string">f"{item}\n"</span>)
                
                <span class="function">print</span>(<span class="string">f"数据已写入: {temp_file}"</span>)

<span class="function">print</span>(<span class="string">"\n=== 高级用法演示 ==="</span>)

<span class="comment"># 处理多个文件</span>
filenames = [<span class="string">"file1.txt"</span>, <span class="string">"file2.txt"</span>, <span class="string">"file3.txt"</span>]
process_multiple_files(filenames)

<span class="comment"># 复杂嵌套操作</span>
complex_operation()

<span class="comment"># 清理测试文件</span>
<span class="keyword">for</span> filename <span class="keyword">in</span> filenames + [<span class="string">"example.txt"</span>, <span class="string">"source.txt"</span>, <span class="string">"dest.txt"</span>]:
    <span class="keyword">try</span>:
        os.remove(filename)
    <span class="keyword">except</span> <span class="function">FileNotFoundError</span>:
        <span class="keyword">pass</span></div>
                </div>

                <div class="warning-box">
                    <h3><span class="emoji">⚠️</span>上下文管理器最佳实践</h3>
                    <ul>
                        <li>总是使用with语句来管理资源，确保正确释放</li>
                        <li>在__exit__方法中处理清理逻辑，即使发生异常也要执行</li>
                        <li>使用contextlib.contextmanager装饰器简化上下文管理器的创建</li>
                        <li>考虑使用ExitStack来管理多个上下文管理器</li>
                        <li>为自定义上下文管理器提供清晰的文档说明</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习上下文管理器：</strong>"请详细解释Python中的上下文管理器概念，包括with语句、__enter__和__exit__方法、contextlib模块的使用，以及在资源管理、异常处理中的应用。请提供实际的使用场景和最佳实践。"</p>
                </div>
            </div>
        </div>

        <!-- 第22页：模块和包 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">📦</span>模块和包</h1>
                <h2><span class="emoji">🔧</span>代码组织与重用</h2>
                <p>模块是Python中组织代码的基本单位，包是模块的集合。通过模块和包，我们可以将代码分解为可重用的组件，提高代码的可维护性和可读性。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># 模块基础概念</span>
<span class="comment"># 1. 创建模块 - math_utils.py</span>

<span class="comment"># math_utils.py 文件内容</span>
<span class="string">"""数学工具模块
这个模块提供了一些常用的数学函数
"""

<span class="keyword">import</span> math

<span class="comment"># 模块级变量</span>
PI = <span class="number">3.14159</span>
E = <span class="number">2.71828</span>

<span class="comment"># 模块级函数</span>
<span class="keyword">def</span> <span class="function">add</span>(a, b):
    <span class="string">"""加法函数"""</span>
    <span class="keyword">return</span> a + b

<span class="keyword">def</span> <span class="function">multiply</span>(a, b):
    <span class="string">"""乘法函数"""</span>
    <span class="keyword">return</span> a * b

<span class="keyword">def</span> <span class="function">factorial</span>(n):
    <span class="string">"""计算阶乘"""</span>
    <span class="keyword">if</span> n <= <span class="number">1</span>:
        <span class="keyword">return</span> <span class="number">1</span>
    <span class="keyword">return</span> n * factorial(n - <span class="number">1</span>)

<span class="keyword">def</span> <span class="function">is_prime</span>(n):
    <span class="string">"""判断是否为质数"""</span>
    <span class="keyword">if</span> n < <span class="number">2</span>:
        <span class="keyword">return</span> <span class="keyword">False</span>
    <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">2</span>, <span class="function">int</span>(math.sqrt(n)) + <span class="number">1</span>):
        <span class="keyword">if</span> n % i == <span class="number">0</span>:
            <span class="keyword">return</span> <span class="keyword">False</span>
    <span class="keyword">return</span> <span class="keyword">True</span>

<span class="keyword">class</span> <span class="function">Calculator</span>:
    <span class="string">"""简单计算器类"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>):
        <span class="keyword">self</span>.history = []
    
    <span class="keyword">def</span> <span class="function">calculate</span>(<span class="keyword">self</span>, operation, a, b):
        <span class="string">"""执行计算"""</span>
        <span class="keyword">if</span> operation == <span class="string">"+"</span>:
            result = a + b
        <span class="keyword">elif</span> operation == <span class="string">"-"</span>:
            result = a - b
        <span class="keyword">elif</span> operation == <span class="string">"*"</span>:
            result = a * b
        <span class="keyword">elif</span> operation == <span class="string">"/"</span>:
            result = a / b <span class="keyword">if</span> b != <span class="number">0</span> <span class="keyword">else</span> <span class="string">"除零错误"</span>
        <span class="keyword">else</span>:
            result = <span class="string">"未知操作"</span>
        
        <span class="keyword">self</span>.history.append(<span class="string">f"{a} {operation} {b} = {result}"</span>)
        <span class="keyword">return</span> result
    
    <span class="keyword">def</span> <span class="function">get_history</span>(<span class="keyword">self</span>):
        <span class="string">"""获取计算历史"""</span>
        <span class="keyword">return</span> <span class="keyword">self</span>.history

<span class="comment"># 模块初始化代码</span>
<span class="keyword">if</span> __name__ == <span class="string">"__main__"</span>:
    <span class="function">print</span>(<span class="string">"math_utils模块被直接运行"</span>)
    <span class="function">print</span>(<span class="string">f"5的阶乘: {factorial(5)}"</span>)
    <span class="function">print</span>(<span class="string">f"17是质数吗: {is_prime(17)}"</span>)
<span class="keyword">else</span>:
    <span class="function">print</span>(<span class="string">"math_utils模块被导入"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 导入模块的不同方式</span>
<span class="comment"># 1. 导入整个模块</span>
<span class="keyword">import</span> math_utils

<span class="function">print</span>(<span class="string">"=== 导入整个模块 ==="</span>)
<span class="function">print</span>(<span class="string">f"PI的值: {math_utils.PI}"</span>)
<span class="function">print</span>(<span class="string">f"5 + 3 = {math_utils.add(5, 3)}"</span>)
<span class="function">print</span>(<span class="string">f"4的阶乘: {math_utils.factorial(4)}"</span>)

<span class="comment"># 使用模块中的类</span>
calc = math_utils.Calculator()
result = calc.calculate(<span class="string">"+"</span>, <span class="number">10</span>, <span class="number">5</span>)
<span class="function">print</span>(<span class="string">f"计算结果: {result}"</span>)

<span class="comment"># 2. 导入特定函数或变量</span>
<span class="keyword">from</span> math_utils <span class="keyword">import</span> add, multiply, PI, Calculator

<span class="function">print</span>(<span class="string">"\n=== 导入特定函数 ==="</span>)
<span class="function">print</span>(<span class="string">f"直接使用PI: {PI}"</span>)
<span class="function">print</span>(<span class="string">f"7 + 2 = {add(7, 2)}"</span>)
<span class="function">print</span>(<span class="string">f"6 * 4 = {multiply(6, 4)}"</span>)

<span class="comment"># 3. 使用别名导入</span>
<span class="keyword">import</span> math_utils <span class="keyword">as</span> mu
<span class="keyword">from</span> math_utils <span class="keyword">import</span> factorial <span class="keyword">as</span> fact

<span class="function">print</span>(<span class="string">"\n=== 使用别名导入 ==="</span>)
<span class="function">print</span>(<span class="string">f"使用别名mu: {mu.E}"</span>)
<span class="function">print</span>(<span class="string">f"使用别名fact: {fact(6)}"</span>)

<span class="comment"># 4. 导入所有内容（不推荐）</span>
<span class="comment"># from math_utils import *</span>
<span class="comment"># print(f"直接使用所有函数: {is_prime(13)}")</span>

<span class="comment"># 5. 查看模块信息</span>
<span class="function">print</span>(<span class="string">"\n=== 模块信息 ==="</span>)
<span class="function">print</span>(<span class="string">f"模块名称: {math_utils.__name__}"</span>)
<span class="function">print</span>(<span class="string">f"模块文档: {math_utils.__doc__}"</span>)
<span class="function">print</span>(<span class="string">f"模块文件: {math_utils.__file__}"</span>)

<span class="comment"># 查看模块中的所有属性</span>
<span class="function">print</span>(<span class="string">"\n模块中的所有属性:"</span>)
<span class="keyword">for</span> attr <span class="keyword">in</span> <span class="function">dir</span>(math_utils):
    <span class="keyword">if</span> <span class="keyword">not</span> attr.startswith(<span class="string">"_"</span>):
        <span class="function">print</span>(<span class="string">f"  {attr}: {type(getattr(math_utils, attr))}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 包的创建和使用</span>
<span class="comment"># 包结构示例：</span>
<span class="comment"># mypackage/</span>
<span class="comment">#     __init__.py</span>
<span class="comment">#     math/</span>
<span class="comment">#         __init__.py</span>
<span class="comment">#         basic.py</span>
<span class="comment">#         advanced.py</span>
<span class="comment">#     string/</span>
<span class="comment">#         __init__.py</span>
<span class="comment">#         utils.py</span>

<span class="comment"># mypackage/__init__.py</span>
<span class="string">"""MyPackage - 一个示例包

这个包包含了数学和字符串处理工具
"""

__version__ = <span class="string">"1.0.0"</span>
__author__ = <span class="string">"Python学习者"</span>

<span class="comment"># 包级别的导入</span>
<span class="keyword">from</span> .math <span class="keyword">import</span> basic
<span class="keyword">from</span> .string <span class="keyword">import</span> utils

<span class="comment"># 定义包的公共接口</span>
__all__ = [<span class="string">"basic"</span>, <span class="string">"utils"</span>, <span class="string">"get_version"</span>]

<span class="keyword">def</span> <span class="function">get_version</span>():
    <span class="string">"""获取包版本"""</span>
    <span class="keyword">return</span> __version__

<span class="function">print</span>(<span class="string">f"MyPackage {__version__} 已加载"</span>)

<span class="comment"># mypackage/math/__init__.py</span>
<span class="string">"""数学工具子包"""</span>

<span class="keyword">from</span> .basic <span class="keyword">import</span> add, subtract, multiply, divide
<span class="keyword">from</span> .advanced <span class="keyword">import</span> power, sqrt, factorial

__all__ = [<span class="string">"add"</span>, <span class="string">"subtract"</span>, <span class="string">"multiply"</span>, <span class="string">"divide"</span>, 
           <span class="string">"power"</span>, <span class="string">"sqrt"</span>, <span class="string">"factorial"</span>]

<span class="comment"># mypackage/math/basic.py</span>
<span class="string">"""基础数学运算"""</span>

<span class="keyword">def</span> <span class="function">add</span>(a, b):
    <span class="string">"""加法"""</span>
    <span class="keyword">return</span> a + b

<span class="keyword">def</span> <span class="function">subtract</span>(a, b):
    <span class="string">"""减法"""</span>
    <span class="keyword">return</span> a - b

<span class="keyword">def</span> <span class="function">multiply</span>(a, b):
    <span class="string">"""乘法"""</span>
    <span class="keyword">return</span> a * b

<span class="keyword">def</span> <span class="function">divide</span>(a, b):
    <span class="string">"""除法"""</span>
    <span class="keyword">if</span> b == <span class="number">0</span>:
        <span class="keyword">raise</span> <span class="function">ValueError</span>(<span class="string">"除数不能为零"</span>)
    <span class="keyword">return</span> a / b

<span class="comment"># mypackage/math/advanced.py</span>
<span class="string">"""高级数学运算"""</span>

<span class="keyword">import</span> math

<span class="keyword">def</span> <span class="function">power</span>(base, exponent):
    <span class="string">"""幂运算"""</span>
    <span class="keyword">return</span> base ** exponent

<span class="keyword">def</span> <span class="function">sqrt</span>(x):
    <span class="string">"""平方根"""</span>
    <span class="keyword">if</span> x < <span class="number">0</span>:
        <span class="keyword">raise</span> <span class="function">ValueError</span>(<span class="string">"不能计算负数的平方根"</span>)
    <span class="keyword">return</span> math.sqrt(x)

<span class="keyword">def</span> <span class="function">factorial</span>(n):
    <span class="string">"""阶乘"""</span>
    <span class="keyword">if</span> n < <span class="number">0</span>:
        <span class="keyword">raise</span> <span class="function">ValueError</span>(<span class="string">"不能计算负数的阶乘"</span>)
    <span class="keyword">if</span> n <= <span class="number">1</span>:
        <span class="keyword">return</span> <span class="number">1</span>
    <span class="keyword">return</span> n * factorial(n - <span class="number">1</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># mypackage/string/__init__.py</span>
<span class="string">"""字符串工具子包"""</span>

<span class="keyword">from</span> .utils <span class="keyword">import</span> reverse_string, count_words, capitalize_words

__all__ = [<span class="string">"reverse_string"</span>, <span class="string">"count_words"</span>, <span class="string">"capitalize_words"</span>]

<span class="comment"># mypackage/string/utils.py</span>
<span class="string">"""字符串处理工具"""</span>

<span class="keyword">def</span> <span class="function">reverse_string</span>(s):
    <span class="string">"""反转字符串"""</span>
    <span class="keyword">return</span> s[::-<span class="number">1</span>]

<span class="keyword">def</span> <span class="function">count_words</span>(s):
    <span class="string">"""统计单词数量"""</span>
    <span class="keyword">return</span> <span class="function">len</span>(s.split())

<span class="keyword">def</span> <span class="function">capitalize_words</span>(s):
    <span class="string">"""首字母大写"""</span>
    <span class="keyword">return</span> <span class="string">" "</span>.join(word.capitalize() <span class="keyword">for</span> word <span class="keyword">in</span> s.split())

<span class="keyword">def</span> <span class="function">remove_duplicates</span>(s):
    <span class="string">"""移除重复字符"""</span>
    seen = <span class="function">set</span>()
    result = []
    <span class="keyword">for</span> char <span class="keyword">in</span> s:
        <span class="keyword">if</span> char <span class="keyword">not</span> <span class="keyword">in</span> seen:
            seen.add(char)
            result.append(char)
    <span class="keyword">return</span> <span class="string">""</span>.join(result)

<span class="comment"># 使用包的示例</span>
<span class="function">print</span>(<span class="string">"\n=== 使用包 ==="</span>)

<span class="comment"># 方式1：导入整个包</span>
<span class="keyword">import</span> mypackage

<span class="function">print</span>(<span class="string">f"包版本: {mypackage.get_version()}"</span>)
<span class="function">print</span>(<span class="string">f"加法: {mypackage.basic.add(10, 5)}"</span>)
<span class="function">print</span>(<span class="string">f"字符串反转: {mypackage.utils.reverse_string('Hello')}"</span>)

<span class="comment"># 方式2：导入子包</span>
<span class="keyword">from</span> mypackage <span class="keyword">import</span> math <span class="keyword">as</span> math_tools
<span class="keyword">from</span> mypackage <span class="keyword">import</span> string <span class="keyword">as</span> string_tools

<span class="function">print</span>(<span class="string">f"\n平方根: {math_tools.sqrt(16)}"</span>)
<span class="function">print</span>(<span class="string">f"单词计数: {string_tools.count_words('Hello World Python')}"</span>)

<span class="comment"># 方式3：导入特定函数</span>
<span class="keyword">from</span> mypackage.math <span class="keyword">import</span> power, factorial
<span class="keyword">from</span> mypackage.string <span class="keyword">import</span> capitalize_words

<span class="function">print</span>(<span class="string">f"\n2的8次方: {power(2, 8)}"</span>)
<span class="function">print</span>(<span class="string">f"5的阶乘: {factorial(5)}"</span>)
<span class="function">print</span>(<span class="string">f"首字母大写: {capitalize_words('hello world python')}"</span>)

<span class="comment"># 方式4：相对导入（在包内部使用）</span>
<span class="comment"># 在mypackage/math/advanced.py中可以这样导入：</span>
<span class="comment"># from .basic import add  # 同级导入</span>
<span class="comment"># from ..string import utils  # 上级包导入</span></div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 模块搜索路径和sys.path</span>
<span class="keyword">import</span> sys
<span class="keyword">import</span> os

<span class="function">print</span>(<span class="string">"=== 模块搜索路径 ==="</span>)
<span class="function">print</span>(<span class="string">"Python模块搜索路径:"</span>)
<span class="keyword">for</span> i, path <span class="keyword">in</span> <span class="function">enumerate</span>(sys.path):
    <span class="function">print</span>(<span class="string">f"{i+1}. {path}"</span>)

<span class="comment"># 动态添加模块路径</span>
custom_path = <span class="string">"/path/to/custom/modules"</span>
<span class="keyword">if</span> custom_path <span class="keyword">not</span> <span class="keyword">in</span> sys.path:
    sys.path.append(custom_path)
    <span class="function">print</span>(<span class="string">f"\n添加自定义路径: {custom_path}"</span>)

<span class="comment"># 查看已导入的模块</span>
<span class="function">print</span>(<span class="string">"\n=== 已导入的模块 ==="</span>)
imported_modules = [name <span class="keyword">for</span> name <span class="keyword">in</span> sys.modules.keys() 
                   <span class="keyword">if</span> <span class="keyword">not</span> name.startswith(<span class="string">"_"</span>)]
<span class="function">print</span>(<span class="string">f"已导入模块数量: {len(imported_modules)}"</span>)
<span class="function">print</span>(<span class="string">"部分已导入模块:"</span>)
<span class="keyword">for</span> module <span class="keyword">in</span> <span class="function">sorted</span>(imported_modules)[:<span class="number">10</span>]:
    <span class="function">print</span>(<span class="string">f"  {module}"</span>)

<span class="comment"># 动态导入模块</span>
<span class="keyword">import</span> importlib

<span class="keyword">def</span> <span class="function">dynamic_import</span>(module_name):
    <span class="string">"""动态导入模块"""</span>
    <span class="keyword">try</span>:
        module = importlib.import_module(module_name)
        <span class="function">print</span>(<span class="string">f"成功导入模块: {module_name}"</span>)
        <span class="keyword">return</span> module
    <span class="keyword">except</span> <span class="function">ImportError</span> <span class="keyword">as</span> e:
        <span class="function">print</span>(<span class="string">f"导入模块失败: {e}"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>

<span class="function">print</span>(<span class="string">"\n=== 动态导入 ==="</span>)
json_module = dynamic_import(<span class="string">"json"</span>)
<span class="keyword">if</span> json_module:
    data = {<span class="string">"name"</span>: <span class="string">"Python"</span>, <span class="string">"version"</span>: <span class="string">"3.9"</span>}
    json_str = json_module.dumps(data)
    <span class="function">print</span>(<span class="string">f"JSON字符串: {json_str}"</span>)

<span class="comment"># 重新加载模块</span>
<span class="keyword">def</span> <span class="function">reload_module</span>(module):
    <span class="string">"""重新加载模块"""</span>
    <span class="keyword">try</span>:
        importlib.reload(module)
        <span class="function">print</span>(<span class="string">f"模块 {module.__name__} 重新加载成功"</span>)
    <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
        <span class="function">print</span>(<span class="string">f"重新加载失败: {e}"</span>)

<span class="comment"># 模块属性检查</span>
<span class="keyword">def</span> <span class="function">inspect_module</span>(module):
    <span class="string">"""检查模块属性"""</span>
    <span class="function">print</span>(<span class="string">f"\n=== 模块 {module.__name__} 信息 ==="</span>)
    <span class="function">print</span>(<span class="string">f"模块文档: {module.__doc__[:100] if module.__doc__ else 'None'}..."</span>)
    
    <span class="comment"># 获取模块中的函数</span>
    functions = [name <span class="keyword">for</span> name <span class="keyword">in</span> <span class="function">dir</span>(module) 
                <span class="keyword">if</span> <span class="function">callable</span>(<span class="function">getattr</span>(module, name)) <span class="keyword">and</span> <span class="keyword">not</span> name.startswith(<span class="string">"_"</span>)]
    <span class="function">print</span>(<span class="string">f"函数数量: {len(functions)}"</span>)
    <span class="function">print</span>(<span class="string">f"函数列表: {functions[:5]}..."</span>)
    
    <span class="comment"># 获取模块中的类</span>
    classes = [name <span class="keyword">for</span> name <span class="keyword">in</span> <span class="function">dir</span>(module) 
              <span class="keyword">if</span> <span class="function">isinstance</span>(<span class="function">getattr</span>(module, name), type) <span class="keyword">and</span> <span class="keyword">not</span> name.startswith(<span class="string">"_"</span>)]
    <span class="function">print</span>(<span class="string">f"类数量: {len(classes)}"</span>)
    <span class="function">print</span>(<span class="string">f"类列表: {classes[:3]}..."</span>)

inspect_module(math_utils)
inspect_module(json_module)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 实际应用：创建一个完整的工具包</span>
<span class="comment"># 文件结构：</span>
<span class="comment"># tools/</span>
<span class="comment">#     __init__.py</span>
<span class="comment">#     file_utils.py</span>
<span class="comment">#     data_utils.py</span>
<span class="comment">#     config.py</span>

<span class="comment"># tools/__init__.py</span>
<span class="string">"""工具包 - 提供文件处理、数据处理等功能"""</span>

__version__ = <span class="string">"2.0.0"</span>
__author__ = <span class="string">"开发团队"</span>

<span class="comment"># 导入主要功能</span>
<span class="keyword">from</span> .file_utils <span class="keyword">import</span> FileManager
<span class="keyword">from</span> .data_utils <span class="keyword">import</span> DataProcessor
<span class="keyword">from</span> .config <span class="keyword">import</span> Config

<span class="comment"># 包级别的便捷函数</span>
<span class="keyword">def</span> <span class="function">get_info</span>():
    <span class="string">"""获取包信息"""</span>
    <span class="keyword">return</span> {
        <span class="string">"name"</span>: <span class="string">"tools"</span>,
        <span class="string">"version"</span>: __version__,
        <span class="string">"author"</span>: __author__
    }

__all__ = [<span class="string">"FileManager"</span>, <span class="string">"DataProcessor"</span>, <span class="string">"Config"</span>, <span class="string">"get_info"</span>]

<span class="comment"># tools/file_utils.py</span>
<span class="string">"""文件处理工具"""</span>

<span class="keyword">import</span> os
<span class="keyword">import</span> json
<span class="keyword">import</span> shutil
<span class="keyword">from</span> pathlib <span class="keyword">import</span> Path

<span class="keyword">class</span> <span class="function">FileManager</span>:
    <span class="string">"""文件管理器"""</span>
    
    <span class="decorator">@staticmethod</span>
    <span class="keyword">def</span> <span class="function">read_file</span>(filepath, encoding=<span class="string">"utf-8"</span>):
        <span class="string">"""读取文件"""</span>
        <span class="keyword">try</span>:
            <span class="keyword">with</span> <span class="function">open</span>(filepath, <span class="string">"r"</span>, encoding=encoding) <span class="keyword">as</span> f:
                <span class="keyword">return</span> f.read()
        <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
            <span class="function">print</span>(<span class="string">f"读取文件失败: {e}"</span>)
            <span class="keyword">return</span> <span class="keyword">None</span>
    
    <span class="decorator">@staticmethod</span>
    <span class="keyword">def</span> <span class="function">write_file</span>(filepath, content, encoding=<span class="string">"utf-8"</span>):
        <span class="string">"""写入文件"""</span>
        <span class="keyword">try</span>:
            os.makedirs(os.path.dirname(filepath), exist_ok=<span class="keyword">True</span>)
            <span class="keyword">with</span> <span class="function">open</span>(filepath, <span class="string">"w"</span>, encoding=encoding) <span class="keyword">as</span> f:
                f.write(content)
            <span class="keyword">return</span> <span class="keyword">True</span>
        <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
            <span class="function">print</span>(<span class="string">f"写入文件失败: {e}"</span>)
            <span class="keyword">return</span> <span class="keyword">False</span>
    
    <span class="decorator">@staticmethod</span>
    <span class="keyword">def</span> <span class="function">copy_file</span>(src, dst):
        <span class="string">"""复制文件"""</span>
        <span class="keyword">try</span>:
            shutil.copy2(src, dst)
            <span class="keyword">return</span> <span class="keyword">True</span>
        <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
            <span class="function">print</span>(<span class="string">f"复制文件失败: {e}"</span>)
            <span class="keyword">return</span> <span class="keyword">False</span>
    
    <span class="decorator">@staticmethod</span>
    <span class="keyword">def</span> <span class="function">list_files</span>(directory, pattern=<span class="string">"*"</span>):
        <span class="string">"""列出目录中的文件"""</span>
        path = Path(directory)
        <span class="keyword">if</span> path.exists() <span class="keyword">and</span> path.is_dir():
            <span class="keyword">return</span> [<span class="function">str</span>(f) <span class="keyword">for</span> f <span class="keyword">in</span> path.glob(pattern) <span class="keyword">if</span> f.is_file()]
        <span class="keyword">return</span> []

<span class="comment"># tools/data_utils.py</span>
<span class="string">"""数据处理工具"""</span>

<span class="keyword">import</span> json
<span class="keyword">import</span> csv
<span class="keyword">from</span> collections <span class="keyword">import</span> Counter

<span class="keyword">class</span> <span class="function">DataProcessor</span>:
    <span class="string">"""数据处理器"""</span>
    
    <span class="decorator">@staticmethod</span>
    <span class="keyword">def</span> <span class="function">load_json</span>(filepath):
        <span class="string">"""加载JSON文件"""</span>
        <span class="keyword">try</span>:
            <span class="keyword">with</span> <span class="function">open</span>(filepath, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:
                <span class="keyword">return</span> json.load(f)
        <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
            <span class="function">print</span>(<span class="string">f"加载JSON失败: {e}"</span>)
            <span class="keyword">return</span> <span class="keyword">None</span>
    
    <span class="decorator">@staticmethod</span>
    <span class="keyword">def</span> <span class="function">save_json</span>(data, filepath):
        <span class="string">"""保存为JSON文件"""</span>
        <span class="keyword">try</span>:
            <span class="keyword">with</span> <span class="function">open</span>(filepath, <span class="string">"w"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:
                json.dump(data, f, ensure_ascii=<span class="keyword">False</span>, indent=<span class="number">2</span>)
            <span class="keyword">return</span> <span class="keyword">True</span>
        <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
            <span class="function">print</span>(<span class="string">f"保存JSON失败: {e}"</span>)
            <span class="keyword">return</span> <span class="keyword">False</span>
    
    <span class="decorator">@staticmethod</span>
    <span class="keyword">def</span> <span class="function">analyze_list</span>(data_list):
        <span class="string">"""分析列表数据"""</span>
        <span class="keyword">if</span> <span class="keyword">not</span> data_list:
            <span class="keyword">return</span> {}
        
        counter = Counter(data_list)
        <span class="keyword">return</span> {
            <span class="string">"total"</span>: <span class="function">len</span>(data_list),
            <span class="string">"unique"</span>: <span class="function">len</span>(counter),
            <span class="string">"most_common"</span>: counter.most_common(<span class="number">5</span>),
            <span class="string">"min"</span>: <span class="function">min</span>(data_list) <span class="keyword">if</span> <span class="function">all</span>(<span class="function">isinstance</span>(x, (<span class="function">int</span>, <span class="function">float</span>)) <span class="keyword">for</span> x <span class="keyword">in</span> data_list) <span class="keyword">else</span> <span class="keyword">None</span>,
            <span class="string">"max"</span>: <span class="function">max</span>(data_list) <span class="keyword">if</span> <span class="function">all</span>(<span class="function">isinstance</span>(x, (<span class="function">int</span>, <span class="function">float</span>)) <span class="keyword">for</span> x <span class="keyword">in</span> data_list) <span class="keyword">else</span> <span class="keyword">None</span>
        }

<span class="comment"># tools/config.py</span>
<span class="string">"""配置管理"""</span>

<span class="keyword">import</span> os
<span class="keyword">import</span> json

<span class="keyword">class</span> <span class="function">Config</span>:
    <span class="string">"""配置管理器"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>, config_file=<span class="string">"config.json"</span>):
        <span class="keyword">self</span>.config_file = config_file
        <span class="keyword">self</span>.config = <span class="keyword">self</span>.load_config()
    
    <span class="keyword">def</span> <span class="function">load_config</span>(<span class="keyword">self</span>):
        <span class="string">"""加载配置"""</span>
        <span class="keyword">if</span> os.path.exists(<span class="keyword">self</span>.config_file):
            <span class="keyword">try</span>:
                <span class="keyword">with</span> <span class="function">open</span>(<span class="keyword">self</span>.config_file, <span class="string">"r"</span>) <span class="keyword">as</span> f:
                    <span class="keyword">return</span> json.load(f)
            <span class="keyword">except</span>:
                <span class="keyword">pass</span>
        <span class="keyword">return</span> {}
    
    <span class="keyword">def</span> <span class="function">get</span>(<span class="keyword">self</span>, key, default=<span class="keyword">None</span>):
        <span class="string">"""获取配置值"""</span>
        <span class="keyword">return</span> <span class="keyword">self</span>.config.get(key, default)
    
    <span class="keyword">def</span> <span class="function">set</span>(<span class="keyword">self</span>, key, value):
        <span class="string">"""设置配置值"""</span>
        <span class="keyword">self</span>.config[key] = value
    
    <span class="keyword">def</span> <span class="function">save</span>(<span class="keyword">self</span>):
        <span class="string">"""保存配置"""</span>
        <span class="keyword">try</span>:
            <span class="keyword">with</span> <span class="function">open</span>(<span class="keyword">self</span>.config_file, <span class="string">"w"</span>) <span class="keyword">as</span> f:
                json.dump(<span class="keyword">self</span>.config, f, indent=<span class="number">2</span>)
            <span class="keyword">return</span> <span class="keyword">True</span>
        <span class="keyword">except</span>:
            <span class="keyword">return</span> <span class="keyword">False</span>

<span class="comment"># 使用工具包</span>
<span class="function">print</span>(<span class="string">"\n=== 使用工具包 ==="</span>)

<span class="comment"># 导入工具包</span>
<span class="keyword">import</span> tools

<span class="function">print</span>(<span class="string">f"工具包信息: {tools.get_info()}"</span>)

<span class="comment"># 使用文件管理器</span>
file_manager = tools.FileManager()
test_content = <span class="string">"这是测试内容\n包含多行文本"</span>
file_manager.write_file(<span class="string">"test.txt"</span>, test_content)
content = file_manager.read_file(<span class="string">"test.txt"</span>)
<span class="function">print</span>(<span class="string">f"读取的内容: {content[:20]}..."</span>)

<span class="comment"># 使用数据处理器</span>
data_processor = tools.DataProcessor()
test_data = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">2</span>, <span class="number">1</span>, <span class="number">4</span>, <span class="number">5</span>, <span class="number">2</span>]
analysis = data_processor.analyze_list(test_data)
<span class="function">print</span>(<span class="string">f"数据分析结果: {analysis}"</span>)

<span class="comment"># 使用配置管理器</span>
config = tools.Config(<span class="string">"app_config.json"</span>)
config.set(<span class="string">"app_name"</span>, <span class="string">"Python学习应用"</span>)
config.set(<span class="string">"version"</span>, <span class="string">"1.0"</span>)
config.save()
<span class="function">print</span>(<span class="string">f"应用名称: {config.get('app_name')}"</span>)</div>
                </div>

                <div class="warning-box">
                    <h3><span class="emoji">⚠️</span>模块和包最佳实践</h3>
                    <ul>
                        <li>使用清晰的模块和包命名，避免与标准库冲突</li>
                        <li>在__init__.py中明确定义包的公共接口</li>
                        <li>使用相对导入来引用包内的其他模块</li>
                        <li>为模块和函数编写详细的文档字符串</li>
                        <li>避免使用from module import *，明确指定导入的内容</li>
                        <li>合理组织包的层次结构，保持逻辑清晰</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习模块和包：</strong>"请详细解释Python中的模块和包概念，包括模块的创建、导入方式、包的结构设计、__init__.py的作用，以及模块搜索路径。请提供实际的项目组织案例和最佳实践。"</p>
                </div>
            </div>
        </div>

        <!-- 第23页：文件操作 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">📁</span>文件操作</h1>
                <h2><span class="emoji">💾</span>读写文件与目录管理</h2>
                <p>文件操作是程序与外部数据交互的重要方式。Python提供了丰富的文件操作功能，包括文件的读写、目录管理、路径处理等。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># 基础文件操作</span>
<span class="comment"># 1. 文件的打开和关闭</span>

<span class="comment"># 基本的文件打开方式</span>
<span class="function">print</span>(<span class="string">"=== 基础文件操作 ==="</span>)

<span class="comment"># 写入文件</span>
file = <span class="function">open</span>(<span class="string">"example.txt"</span>, <span class="string">"w"</span>, encoding=<span class="string">"utf-8"</span>)
file.write(<span class="string">"Hello, Python!\n"</span>)
file.write(<span class="string">"这是第二行\n"</span>)
file.write(<span class="string">"文件操作示例"</span>)
file.close()
<span class="function">print</span>(<span class="string">"文件写入完成"</span>)

<span class="comment"># 读取文件</span>
file = <span class="function">open</span>(<span class="string">"example.txt"</span>, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>)
content = file.read()
<span class="function">print</span>(<span class="string">f"文件内容:\n{content}"</span>)
file.close()

<span class="comment"># 使用with语句（推荐方式）</span>
<span class="function">print</span>(<span class="string">"\n=== 使用with语句 ==="</span>)

<span class="comment"># 写入文件</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"with_example.txt"</span>, <span class="string">"w"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:
    f.write(<span class="string">"使用with语句\n"</span>)
    f.write(<span class="string">"自动管理文件资源\n"</span>)
    f.write(<span class="string">"无需手动关闭文件"</span>)

<span class="comment"># 读取文件</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"with_example.txt"</span>, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:
    content = f.read()
    <span class="function">print</span>(<span class="string">f"读取内容:\n{content}"</span>)

<span class="comment"># 文件打开模式</span>
<span class="function">print</span>(<span class="string">"\n=== 文件打开模式 ==="</span>)
modes = {
    <span class="string">"r"</span>: <span class="string">"只读模式（默认）"</span>,
    <span class="string">"w"</span>: <span class="string">"写入模式（覆盖原文件）"</span>,
    <span class="string">"a"</span>: <span class="string">"追加模式"</span>,
    <span class="string">"x"</span>: <span class="string">"独占创建模式"</span>,
    <span class="string">"b"</span>: <span class="string">"二进制模式"</span>,
    <span class="string">"t"</span>: <span class="string">"文本模式（默认）"</span>,
    <span class="string">"+"</span>: <span class="string">"读写模式"</span>
}

<span class="keyword">for</span> mode, description <span class="keyword">in</span> modes.items():
    <span class="function">print</span>(<span class="string">f"{mode}: {description}"</span>)

<span class="comment"># 追加模式示例</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"with_example.txt"</span>, <span class="string">"a"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:
    f.write(<span class="string">"\n追加的内容"</span>)
    f.write(<span class="string">"\n更多追加内容"</span>)

<span class="comment"># 读写模式示例</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"readwrite.txt"</span>, <span class="string">"w+"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:
    f.write(<span class="string">"初始内容\n"</span>)
    f.write(<span class="string">"第二行内容"</span>)
    
    <span class="comment"># 移动文件指针到开头</span>
    f.seek(<span class="number">0</span>)
    content = f.read()
    <span class="function">print</span>(<span class="string">f"\n读写模式内容:\n{content}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 不同的文件读取方法</span>
<span class="function">print</span>(<span class="string">"\n=== 文件读取方法 ==="</span>)

<span class="comment"># 创建测试文件</span>
test_content = <span class="string">"""第一行内容
第二行内容
第三行内容
第四行内容
第五行内容"""</span>

<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"test_read.txt"</span>, <span class="string">"w"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:
    f.write(test_content)

<span class="comment"># 1. read() - 读取整个文件</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"test_read.txt"</span>, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:
    content = f.read()
    <span class="function">print</span>(<span class="string">f"read()方法:\n{content}"</span>)

<span class="comment"># 2. read(size) - 读取指定字符数</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"test_read.txt"</span>, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:
    partial = f.read(<span class="number">10</span>)
    <span class="function">print</span>(<span class="string">f"\nread(10)方法: '{partial}'"</span>)

<span class="comment"># 3. readline() - 读取一行</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"test_read.txt"</span>, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:
    line1 = f.readline()
    line2 = f.readline()
    <span class="function">print</span>(<span class="string">f"\nreadline()方法:"</span>)
    <span class="function">print</span>(<span class="string">f"第一行: '{line1.strip()}'"</span>)
    <span class="function">print</span>(<span class="string">f"第二行: '{line2.strip()}'"</span>)

<span class="comment"># 4. readlines() - 读取所有行到列表</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"test_read.txt"</span>, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:
    lines = f.readlines()
    <span class="function">print</span>(<span class="string">f"\nreadlines()方法:"</span>)
    <span class="keyword">for</span> i, line <span class="keyword">in</span> <span class="function">enumerate</span>(lines, <span class="number">1</span>):
        <span class="function">print</span>(<span class="string">f"  行{i}: '{line.strip()}'"</span>)

<span class="comment"># 5. 逐行迭代（推荐方式）</span>
<span class="function">print</span>(<span class="string">"\n逐行迭代:"</span>)
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"test_read.txt"</span>, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:
    <span class="keyword">for</span> line_num, line <span class="keyword">in</span> <span class="function">enumerate</span>(f, <span class="number">1</span>):
        <span class="function">print</span>(<span class="string">f"  行{line_num}: '{line.strip()}'"</span>)

<span class="comment"># 文件指针操作</span>
<span class="function">print</span>(<span class="string">"\n=== 文件指针操作 ==="</span>)
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"test_read.txt"</span>, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:
    <span class="function">print</span>(<span class="string">f"初始位置: {f.tell()}"</span>)
    
    first_char = f.read(<span class="number">1</span>)
    <span class="function">print</span>(<span class="string">f"读取一个字符 '{first_char}' 后位置: {f.tell()}"</span>)
    
    <span class="comment"># 移动到文件开头</span>
    f.seek(<span class="number">0</span>)
    <span class="function">print</span>(<span class="string">f"seek(0)后位置: {f.tell()}"</span>)
    
    <span class="comment"># 移动到文件末尾</span>
    f.seek(<span class="number">0</span>, <span class="number">2</span>)
    <span class="function">print</span>(<span class="string">f"移动到末尾后位置: {f.tell()}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 二进制文件操作</span>
<span class="function">print</span>(<span class="string">"\n=== 二进制文件操作 ==="</span>)

<span class="comment"># 写入二进制文件</span>
binary_data = <span class="string">b"\x48\x65\x6c\x6c\x6f\x20\x57\x6f\x72\x6c\x64"</span>  <span class="comment"># "Hello World"的字节表示</span>

<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"binary_file.bin"</span>, <span class="string">"wb"</span>) <span class="keyword">as</span> f:
    f.write(binary_data)
    <span class="function">print</span>(<span class="string">"二进制数据写入完成"</span>)

<span class="comment"># 读取二进制文件</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"binary_file.bin"</span>, <span class="string">"rb"</span>) <span class="keyword">as</span> f:
    data = f.read()
    <span class="function">print</span>(<span class="string">f"读取的二进制数据: {data}"</span>)
    <span class="function">print</span>(<span class="string">f"转换为字符串: {data.decode('utf-8')}"</span>)

<span class="comment"># 处理图片等二进制文件示例</span>
<span class="keyword">def</span> <span class="function">copy_binary_file</span>(src, dst):
    <span class="string">"""复制二进制文件"""</span>
    <span class="keyword">try</span>:
        <span class="keyword">with</span> <span class="function">open</span>(src, <span class="string">"rb"</span>) <span class="keyword">as</span> src_file:
            <span class="keyword">with</span> <span class="function">open</span>(dst, <span class="string">"wb"</span>) <span class="keyword">as</span> dst_file:
                <span class="comment"># 分块读取，适合大文件</span>
                <span class="keyword">while</span> <span class="keyword">True</span>:
                    chunk = src_file.read(<span class="number">1024</span>)  <span class="comment"># 每次读取1KB</span>
                    <span class="keyword">if</span> <span class="keyword">not</span> chunk:
                        <span class="keyword">break</span>
                    dst_file.write(chunk)
        <span class="function">print</span>(<span class="string">f"文件复制成功: {src} -> {dst}"</span>)
        <span class="keyword">return</span> <span class="keyword">True</span>
    <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
        <span class="function">print</span>(<span class="string">f"文件复制失败: {e}"</span>)
        <span class="keyword">return</span> <span class="keyword">False</span>

<span class="comment"># 测试二进制文件复制</span>
copy_binary_file(<span class="string">"binary_file.bin"</span>, <span class="string">"binary_copy.bin"</span>)

<span class="comment"># 文件编码处理</span>
<span class="function">print</span>(<span class="string">"\n=== 文件编码处理 ==="</span>)

<span class="comment"># 创建包含中文的文件</span>
chinese_content = <span class="string">"你好，世界！\nHello, World!\n这是中文测试"</span>

<span class="comment"># 使用不同编码保存</span>
encodings = [<span class="string">"utf-8"</span>, <span class="string">"gbk"</span>, <span class="string">"utf-16"</span>]

<span class="keyword">for</span> encoding <span class="keyword">in</span> encodings:
    filename = <span class="string">f"chinese_{encoding}.txt"</span>
    <span class="keyword">try</span>:
        <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">"w"</span>, encoding=encoding) <span class="keyword">as</span> f:
            f.write(chinese_content)
        <span class="function">print</span>(<span class="string">f"使用{encoding}编码保存成功"</span>)
        
        <span class="comment"># 读取并验证</span>
        <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">"r"</span>, encoding=encoding) <span class="keyword">as</span> f:
            content = f.read()
            <span class="function">print</span>(<span class="string">f"  读取验证: {content[:10]}..."</span>)
    <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
        <span class="function">print</span>(<span class="string">f"编码{encoding}处理失败: {e}"</span>)

<span class="comment"># 自动检测文件编码</span>
<span class="keyword">def</span> <span class="function">detect_encoding</span>(filename):
    <span class="string">"""简单的编码检测"""</span>
    encodings_to_try = [<span class="string">"utf-8"</span>, <span class="string">"gbk"</span>, <span class="string">"utf-16"</span>, <span class="string">"latin-1"</span>]
    
    <span class="keyword">for</span> encoding <span class="keyword">in</span> encodings_to_try:
        <span class="keyword">try</span>:
            <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">"r"</span>, encoding=encoding) <span class="keyword">as</span> f:
                f.read()
            <span class="keyword">return</span> encoding
        <span class="keyword">except</span> <span class="function">UnicodeDecodeError</span>:
            <span class="keyword">continue</span>
    <span class="keyword">return</span> <span class="keyword">None</span>

<span class="function">print</span>(<span class="string">"\n编码检测结果:"</span>)
<span class="keyword">for</span> encoding <span class="keyword">in</span> encodings:
    filename = <span class="string">f"chinese_{encoding}.txt"</span>
    detected = detect_encoding(filename)
    <span class="function">print</span>(<span class="string">f"  {filename}: 检测到编码 {detected}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 目录和路径操作</span>
<span class="keyword">import</span> os
<span class="keyword">import</span> shutil
<span class="keyword">from</span> pathlib <span class="keyword">import</span> Path
<span class="keyword">import</span> glob

<span class="function">print</span>(<span class="string">"\n=== 目录和路径操作 ==="</span>)

<span class="comment"># 获取当前工作目录</span>
current_dir = os.getcwd()
<span class="function">print</span>(<span class="string">f"当前工作目录: {current_dir}"</span>)

<span class="comment"># 创建目录</span>
test_dir = <span class="string">"test_directory"</span>
<span class="keyword">if</span> <span class="keyword">not</span> os.path.exists(test_dir):
    os.mkdir(test_dir)
    <span class="function">print</span>(<span class="string">f"创建目录: {test_dir}"</span>)

<span class="comment"># 创建多级目录</span>
nested_dir = <span class="string">"parent/child/grandchild"</span>
os.makedirs(nested_dir, exist_ok=<span class="keyword">True</span>)
<span class="function">print</span>(<span class="string">f"创建多级目录: {nested_dir}"</span>)

<span class="comment"># 列出目录内容</span>
<span class="function">print</span>(<span class="string">f"\n当前目录内容:"</span>)
<span class="keyword">for</span> item <span class="keyword">in</span> os.listdir(<span class="string">"."</span>):
    item_path = os.path.join(<span class="string">"."</span>, item)
    item_type = <span class="string">"目录"</span> <span class="keyword">if</span> os.path.isdir(item_path) <span class="keyword">else</span> <span class="string">"文件"</span>
    <span class="function">print</span>(<span class="string">f"  {item} ({item_type})"</span>)

<span class="comment"># 路径操作</span>
<span class="function">print</span>(<span class="string">"\n=== 路径操作 ==="</span>)
file_path = <span class="string">"/home/<USER>/documents/file.txt"</span>

<span class="function">print</span>(<span class="string">f"完整路径: {file_path}"</span>)
<span class="function">print</span>(<span class="string">f"目录名: {os.path.dirname(file_path)}"</span>)
<span class="function">print</span>(<span class="string">f"文件名: {os.path.basename(file_path)}"</span>)
<span class="function">print</span>(<span class="string">f"文件名(无扩展名): {os.path.splitext(file_path)[0]}"</span>)
<span class="function">print</span>(<span class="string">f"扩展名: {os.path.splitext(file_path)[1]}"</span>)

<span class="comment"># 路径拼接</span>
joined_path = os.path.join(<span class="string">"folder"</span>, <span class="string">"subfolder"</span>, <span class="string">"file.txt"</span>)
<span class="function">print</span>(<span class="string">f"拼接路径: {joined_path}"</span>)

<span class="comment"># 绝对路径和相对路径</span>
rel_path = <span class="string">"test_directory"</span>
abs_path = os.path.abspath(rel_path)
<span class="function">print</span>(<span class="string">f"相对路径: {rel_path}"</span>)
<span class="function">print</span>(<span class="string">f"绝对路径: {abs_path}"</span>)

<span class="comment"># 使用pathlib（推荐方式）</span>
<span class="function">print</span>(<span class="string">"\n=== 使用pathlib ==="</span>)
path = Path(<span class="string">"test_directory"</span>) / <span class="string">"subdir"</span> / <span class="string">"file.txt"</span>
<span class="function">print</span>(<span class="string">f"Path对象: {path}"</span>)
<span class="function">print</span>(<span class="string">f"父目录: {path.parent}"</span>)
<span class="function">print</span>(<span class="string">f"文件名: {path.name}"</span>)
<span class="function">print</span>(<span class="string">f"文件后缀: {path.suffix}"</span>)
<span class="function">print</span>(<span class="string">f"是否存在: {path.exists()}"</span>)

<span class="comment"># 创建文件和目录</span>
path.parent.mkdir(parents=<span class="keyword">True</span>, exist_ok=<span class="keyword">True</span>)
path.write_text(<span class="string">"使用pathlib创建的文件"</span>, encoding=<span class="string">"utf-8"</span>)
<span class="function">print</span>(<span class="string">f"文件创建成功: {path}"</span>)

<span class="comment"># 读取文件</span>
content = path.read_text(encoding=<span class="string">"utf-8"</span>)
<span class="function">print</span>(<span class="string">f"文件内容: {content}"</span>)

<span class="comment"># 文件和目录的属性</span>
<span class="function">print</span>(<span class="string">"\n=== 文件属性 ==="</span>)
<span class="keyword">if</span> path.exists():
    stat = path.stat()
    <span class="function">print</span>(<span class="string">f"文件大小: {stat.st_size} 字节"</span>)
    <span class="function">print</span>(<span class="string">f"修改时间: {stat.st_mtime}"</span>)
    <span class="function">print</span>(<span class="string">f"是否为文件: {path.is_file()}"</span>)
    <span class="function">print</span>(<span class="string">f"是否为目录: {path.is_dir()}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 文件搜索和模式匹配</span>
<span class="function">print</span>(<span class="string">"\n=== 文件搜索 ==="</span>)

<span class="comment"># 使用glob模块搜索文件</span>
<span class="function">print</span>(<span class="string">"所有.txt文件:"</span>)
txt_files = glob.glob(<span class="string">"*.txt"</span>)
<span class="keyword">for</span> file <span class="keyword">in</span> txt_files:
    <span class="function">print</span>(<span class="string">f"  {file}"</span>)

<span class="comment"># 递归搜索</span>
<span class="function">print</span>(<span class="string">"\n递归搜索所有.txt文件:"</span>)
all_txt_files = glob.glob(<span class="string">"**/*.txt"</span>, recursive=<span class="keyword">True</span>)
<span class="keyword">for</span> file <span class="keyword">in</span> all_txt_files:
    <span class="function">print</span>(<span class="string">f"  {file}"</span>)

<span class="comment"># 使用pathlib搜索</span>
<span class="function">print</span>(<span class="string">"\n使用pathlib搜索:"</span>)
current_path = Path(<span class="string">"."</span>)

<span class="comment"># 搜索所有Python文件</span>
py_files = <span class="function">list</span>(current_path.glob(<span class="string">"*.py"</span>))
<span class="function">print</span>(<span class="string">f"Python文件: {[f.name for f in py_files]}"</span>)

<span class="comment"># 递归搜索</span>
all_files = <span class="function">list</span>(current_path.rglob(<span class="string">"*.*"</span>))
<span class="function">print</span>(<span class="string">f"所有文件数量: {len(all_files)}"</span>)

<span class="comment"># 文件过滤</span>
<span class="keyword">def</span> <span class="function">find_files_by_size</span>(directory, min_size=<span class="number">0</span>, max_size=<span class="keyword">None</span>):
    <span class="string">"""根据文件大小过滤文件"""</span>
    path = Path(directory)
    matching_files = []
    
    <span class="keyword">for</span> file_path <span class="keyword">in</span> path.rglob(<span class="string">"*"</span>):
        <span class="keyword">if</span> file_path.is_file():
            size = file_path.stat().st_size
            <span class="keyword">if</span> size >= min_size:
                <span class="keyword">if</span> max_size <span class="keyword">is</span> <span class="keyword">None</span> <span class="keyword">or</span> size <= max_size:
                    matching_files.append((file_path, size))
    
    <span class="keyword">return</span> matching_files

<span class="comment"># 查找小于1KB的文件</span>
small_files = find_files_by_size(<span class="string">"."</span>, max_size=<span class="number">1024</span>)
<span class="function">print</span>(<span class="string">f"\n小于1KB的文件:"</span>)
<span class="keyword">for</span> file_path, size <span class="keyword">in</span> small_files[:<span class="number">5</span>]:  <span class="comment"># 只显示前5个</span>
    <span class="function">print</span>(<span class="string">f"  {file_path.name}: {size} 字节"</span>)

<span class="comment"># 文件操作：复制、移动、删除</span>
<span class="function">print</span>(<span class="string">"\n=== 文件操作 ==="</span>)

<span class="comment"># 复制文件</span>
src_file = <span class="string">"example.txt"</span>
dst_file = <span class="string">"example_copy.txt"</span>

<span class="keyword">if</span> os.path.exists(src_file):
    shutil.copy2(src_file, dst_file)
    <span class="function">print</span>(<span class="string">f"文件复制: {src_file} -> {dst_file}"</span>)

<span class="comment"># 移动文件</span>
<span class="keyword">if</span> os.path.exists(dst_file):
    moved_file = os.path.join(test_dir, <span class="string">"moved_file.txt"</span>)
    shutil.move(dst_file, moved_file)
    <span class="function">print</span>(<span class="string">f"文件移动: {dst_file} -> {moved_file}"</span>)

<span class="comment"># 复制目录</span>
src_dir = <span class="string">"test_directory"</span>
dst_dir = <span class="string">"backup_directory"</span>

<span class="keyword">if</span> os.path.exists(src_dir) <span class="keyword">and</span> <span class="keyword">not</span> os.path.exists(dst_dir):
    shutil.copytree(src_dir, dst_dir)
    <span class="function">print</span>(<span class="string">f"目录复制: {src_dir} -> {dst_dir}"</span>)

<span class="comment"># 删除文件和目录</span>
<span class="keyword">def</span> <span class="function">safe_remove</span>(path):
    <span class="string">"""安全删除文件或目录"""</span>
    <span class="keyword">try</span>:
        <span class="keyword">if</span> os.path.isfile(path):
            os.remove(path)
            <span class="function">print</span>(<span class="string">f"删除文件: {path}"</span>)
        <span class="keyword">elif</span> os.path.isdir(path):
            shutil.rmtree(path)
            <span class="function">print</span>(<span class="string">f"删除目录: {path}"</span>)
    <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
        <span class="function">print</span>(<span class="string">f"删除失败 {path}: {e}"</span>)

<span class="comment"># 清理测试文件（注释掉以保留文件）</span>
<span class="comment"># safe_remove("backup_directory")</span>
<span class="comment"># safe_remove("parent")</span></div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 实际应用：文件处理工具类</span>
<span class="keyword">import</span> json
<span class="keyword">import</span> csv
<span class="keyword">from</span> datetime <span class="keyword">import</span> datetime

<span class="keyword">class</span> <span class="function">FileProcessor</span>:
    <span class="string">"""文件处理工具类"""</span>
    
    <span class="decorator">@staticmethod</span>
    <span class="keyword">def</span> <span class="function">read_json</span>(filepath):
        <span class="string">"""读取JSON文件"""</span>
        <span class="keyword">try</span>:
            <span class="keyword">with</span> <span class="function">open</span>(filepath, <span class="string">"r"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:
                <span class="keyword">return</span> json.load(f)
        <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
            <span class="function">print</span>(<span class="string">f"读取JSON失败: {e}"</span>)
            <span class="keyword">return</span> <span class="keyword">None</span>
    
    <span class="decorator">@staticmethod</span>
    <span class="keyword">def</span> <span class="function">write_json</span>(data, filepath, indent=<span class="number">2</span>):
        <span class="string">"""写入JSON文件"""</span>
        <span class="keyword">try</span>:
            <span class="keyword">with</span> <span class="function">open</span>(filepath, <span class="string">"w"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> f:
                json.dump(data, f, ensure_ascii=<span class="keyword">False</span>, indent=indent)
            <span class="keyword">return</span> <span class="keyword">True</span>
        <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
            <span class="function">print</span>(<span class="string">f"写入JSON失败: {e}"</span>)
            <span class="keyword">return</span> <span class="keyword">False</span>
    
    <span class="decorator">@staticmethod</span>
    <span class="keyword">def</span> <span class="function">read_csv</span>(filepath, encoding=<span class="string">"utf-8"</span>):
        <span class="string">"""读取CSV文件"""</span>
        <span class="keyword">try</span>:
            <span class="keyword">with</span> <span class="function">open</span>(filepath, <span class="string">"r"</span>, encoding=encoding, newline=<span class="string">""</span>) <span class="keyword">as</span> f:
                reader = csv.DictReader(f)
                <span class="keyword">return</span> <span class="function">list</span>(reader)
        <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
            <span class="function">print</span>(<span class="string">f"读取CSV失败: {e}"</span>)
            <span class="keyword">return</span> []
    
    <span class="decorator">@staticmethod</span>
    <span class="keyword">def</span> <span class="function">write_csv</span>(data, filepath, fieldnames=<span class="keyword">None</span>, encoding=<span class="string">"utf-8"</span>):
        <span class="string">"""写入CSV文件"""</span>
        <span class="keyword">if</span> <span class="keyword">not</span> data:
            <span class="keyword">return</span> <span class="keyword">False</span>
        
        <span class="keyword">if</span> fieldnames <span class="keyword">is</span> <span class="keyword">None</span>:
            fieldnames = data[<span class="number">0</span>].keys() <span class="keyword">if</span> <span class="function">isinstance</span>(data[<span class="number">0</span>], <span class="function">dict</span>) <span class="keyword">else</span> <span class="keyword">None</span>
        
        <span class="keyword">try</span>:
            <span class="keyword">with</span> <span class="function">open</span>(filepath, <span class="string">"w"</span>, encoding=encoding, newline=<span class="string">""</span>) <span class="keyword">as</span> f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data)
            <span class="keyword">return</span> <span class="keyword">True</span>
        <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
            <span class="function">print</span>(<span class="string">f"写入CSV失败: {e}"</span>)
            <span class="keyword">return</span> <span class="keyword">False</span>
    
    <span class="decorator">@staticmethod</span>
    <span class="keyword">def</span> <span class="function">backup_file</span>(filepath, backup_dir=<span class="string">"backups"</span>):
        <span class="string">"""备份文件"""</span>
        <span class="keyword">if</span> <span class="keyword">not</span> os.path.exists(filepath):
            <span class="keyword">return</span> <span class="keyword">False</span>
        
        <span class="comment"># 创建备份目录</span>
        os.makedirs(backup_dir, exist_ok=<span class="keyword">True</span>)
        
        <span class="comment"># 生成备份文件名</span>
        timestamp = datetime.now().strftime(<span class="string">"%Y%m%d_%H%M%S"</span>)
        filename = os.path.basename(filepath)
        name, ext = os.path.splitext(filename)
        backup_filename = <span class="string">f"{name}_{timestamp}{ext}"</span>
        backup_path = os.path.join(backup_dir, backup_filename)
        
        <span class="keyword">try</span>:
            shutil.copy2(filepath, backup_path)
            <span class="function">print</span>(<span class="string">f"文件备份成功: {backup_path}"</span>)
            <span class="keyword">return</span> backup_path
        <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
            <span class="function">print</span>(<span class="string">f"文件备份失败: {e}"</span>)
            <span class="keyword">return</span> <span class="keyword">False</span>
    
    <span class="decorator">@staticmethod</span>
    <span class="keyword">def</span> <span class="function">get_file_info</span>(filepath):
        <span class="string">"""获取文件详细信息"""</span>
        <span class="keyword">if</span> <span class="keyword">not</span> os.path.exists(filepath):
            <span class="keyword">return</span> <span class="keyword">None</span>
        
        stat = os.stat(filepath)
        <span class="keyword">return</span> {
            <span class="string">"name"</span>: os.path.basename(filepath),
            <span class="string">"path"</span>: os.path.abspath(filepath),
            <span class="string">"size"</span>: stat.st_size,
            <span class="string">"created"</span>: datetime.fromtimestamp(stat.st_ctime),
            <span class="string">"modified"</span>: datetime.fromtimestamp(stat.st_mtime),
            <span class="string">"is_file"</span>: os.path.isfile(filepath),
            <span class="string">"is_dir"</span>: os.path.isdir(filepath)
        }

<span class="comment"># 使用文件处理工具类</span>
<span class="function">print</span>(<span class="string">"\n=== 使用文件处理工具类 ==="</span>)

processor = FileProcessor()

<span class="comment"># 创建测试数据</span>
test_data = {
    <span class="string">"name"</span>: <span class="string">"Python学习"</span>,
    <span class="string">"topics"</span>: [<span class="string">"变量"</span>, <span class="string">"函数"</span>, <span class="string">"类"</span>, <span class="string">"文件操作"</span>],
    <span class="string">"difficulty"</span>: <span class="string">"中级"</span>,
    <span class="string">"completed"</span>: <span class="keyword">True</span>
}

<span class="comment"># JSON操作</span>
json_file = <span class="string">"test_data.json"</span>
<span class="keyword">if</span> processor.write_json(test_data, json_file):
    loaded_data = processor.read_json(json_file)
    <span class="function">print</span>(<span class="string">f"JSON数据: {loaded_data}"</span>)

<span class="comment"># CSV操作</span>
csv_data = [
    {<span class="string">"姓名"</span>: <span class="string">"张三"</span>, <span class="string">"年龄"</span>: <span class="number">25</span>, <span class="string">"城市"</span>: <span class="string">"北京"</span>},
    {<span class="string">"姓名"</span>: <span class="string">"李四"</span>, <span class="string">"年龄"</span>: <span class="number">30</span>, <span class="string">"城市"</span>: <span class="string">"上海"</span>},
    {<span class="string">"姓名"</span>: <span class="string">"王五"</span>, <span class="string">"年龄"</span>: <span class="number">28</span>, <span class="string">"城市"</span>: <span class="string">"广州"</span>}
]

csv_file = <span class="string">"test_data.csv"</span>
<span class="keyword">if</span> processor.write_csv(csv_data, csv_file):
    loaded_csv = processor.read_csv(csv_file)
    <span class="function">print</span>(<span class="string">f"\nCSV数据:"</span>)
    <span class="keyword">for</span> row <span class="keyword">in</span> loaded_csv:
        <span class="function">print</span>(<span class="string">f"  {row}"</span>)

<span class="comment"># 文件信息</span>
file_info = processor.get_file_info(json_file)
<span class="keyword">if</span> file_info:
    <span class="function">print</span>(<span class="string">f"\n文件信息:"</span>)
    <span class="keyword">for</span> key, value <span class="keyword">in</span> file_info.items():
        <span class="function">print</span>(<span class="string">f"  {key}: {value}"</span>)

<span class="comment"># 备份文件</span>
backup_path = processor.backup_file(json_file)
<span class="keyword">if</span> backup_path:
    <span class="function">print</span>(<span class="string">f"\n备份文件路径: {backup_path}"</span>)</div>
                </div>

                <div class="warning-box">
                    <h3><span class="emoji">⚠️</span>文件操作最佳实践</h3>
                    <ul>
                        <li>始终使用with语句进行文件操作，确保文件正确关闭</li>
                        <li>明确指定文件编码，避免编码问题</li>
                        <li>处理大文件时使用分块读取，避免内存溢出</li>
                        <li>在文件操作前检查文件是否存在</li>
                        <li>使用异常处理机制处理文件操作错误</li>
                        <li>优先使用pathlib模块进行路径操作</li>
                        <li>定期备份重要文件</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习文件操作：</strong>"请详细解释Python中的文件操作，包括文件的读写方式、不同的打开模式、编码处理、目录操作、路径处理，以及pathlib模块的使用。请提供实际的文件处理案例和最佳实践。"</p>
                </div>
            </div>
        </div>

        <!-- 第24页：面向对象编程基础 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">🏗️</span>面向对象编程基础</h1>
                <h2><span class="emoji">🎯</span>类与对象的概念</h2>
                <p>面向对象编程（OOP）是一种编程范式，它将数据和操作数据的方法组织在一起，形成类和对象。Python是一种面向对象的语言，支持封装、继承和多态等特性。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># 基础类定义</span>
<span class="comment"># 1. 简单的类定义</span>

<span class="keyword">class</span> <span class="function">Person</span>:
    <span class="string">"""人员类 - 演示基础类定义"""</span>
    
    <span class="comment"># 类变量（所有实例共享）</span>
    species = <span class="string">"Homo sapiens"</span>
    population = <span class="number">0</span>
    
    <span class="comment"># 构造方法</span>
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>, name, age, gender=<span class="string">"未知"</span>):
        <span class="string">"""初始化方法"""</span>
        <span class="comment"># 实例变量（每个实例独有）</span>
        <span class="keyword">self</span>.name = name
        <span class="keyword">self</span>.age = age
        <span class="keyword">self</span>.gender = gender
        <span class="keyword">self</span>.skills = []  <span class="comment"># 可变属性</span>
        
        <span class="comment"># 更新类变量</span>
        Person.population += <span class="number">1</span>
        <span class="function">print</span>(<span class="string">f"创建了新的人员: {name}，当前总人数: {Person.population}"</span>)
    
    <span class="comment"># 实例方法</span>
    <span class="keyword">def</span> <span class="function">introduce</span>(<span class="keyword">self</span>):
        <span class="string">"""自我介绍"""</span>
        <span class="keyword">return</span> <span class="string">f"大家好，我是{self.name}，今年{self.age}岁，性别{self.gender}"</span>
    
    <span class="keyword">def</span> <span class="function">add_skill</span>(<span class="keyword">self</span>, skill):
        <span class="string">"""添加技能"""</span>
        <span class="keyword">if</span> skill <span class="keyword">not</span> <span class="keyword">in</span> <span class="keyword">self</span>.skills:
            <span class="keyword">self</span>.skills.append(skill)
            <span class="function">print</span>(<span class="string">f"{self.name} 学会了 {skill}"</span>)
        <span class="keyword">else</span>:
            <span class="function">print</span>(<span class="string">f"{self.name} 已经掌握了 {skill}"</span>)
    
    <span class="keyword">def</span> <span class="function">show_skills</span>(<span class="keyword">self</span>):
        <span class="string">"""显示技能列表"""</span>
        <span class="keyword">if</span> <span class="keyword">self</span>.skills:
            <span class="keyword">return</span> <span class="string">f"{self.name} 的技能: {', '.join(self.skills)}"</span>
        <span class="keyword">else</span>:
            <span class="keyword">return</span> <span class="string">f"{self.name} 还没有学会任何技能"</span>
    
    <span class="keyword">def</span> <span class="function">celebrate_birthday</span>(<span class="keyword">self</span>):
        <span class="string">"""过生日"""</span>
        <span class="keyword">self</span>.age += <span class="number">1</span>
        <span class="function">print</span>(<span class="string">f"🎂 {self.name} 过生日了！现在 {self.age} 岁"</span>)
    
    <span class="comment"># 类方法</span>
    <span class="decorator">@classmethod</span>
    <span class="keyword">def</span> <span class="function">get_population</span>(<span class="function">cls</span>):
        <span class="string">"""获取总人数"""</span>
        <span class="keyword">return</span> <span class="string">f"当前总人数: {cls.population}"</span>
    
    <span class="decorator">@classmethod</span>
    <span class="keyword">def</span> <span class="function">create_baby</span>(<span class="function">cls</span>, name):
        <span class="string">"""创建婴儿"""</span>
        <span class="keyword">return</span> <span class="function">cls</span>(name, <span class="number">0</span>, <span class="string">"婴儿"</span>)
    
    <span class="comment"># 静态方法</span>
    <span class="decorator">@staticmethod</span>
    <span class="keyword">def</span> <span class="function">is_adult</span>(age):
        <span class="string">"""判断是否成年"""</span>
        <span class="keyword">return</span> age >= <span class="number">18</span>
    
    <span class="comment"># 特殊方法（魔术方法）</span>
    <span class="keyword">def</span> <span class="function">__str__</span>(<span class="keyword">self</span>):
        <span class="string">"""字符串表示"""</span>
        <span class="keyword">return</span> <span class="string">f"Person(name='{self.name}', age={self.age}, gender='{self.gender}')"</span>
    
    <span class="keyword">def</span> <span class="function">__repr__</span>(<span class="keyword">self</span>):
        <span class="string">"""开发者表示"""</span>
        <span class="keyword">return</span> <span class="string">f"Person('{self.name}', {self.age}, '{self.gender}')"</span>
    
    <span class="keyword">def</span> <span class="function">__len__</span>(<span class="keyword">self</span>):
        <span class="string">"""返回技能数量"""</span>
        <span class="keyword">return</span> <span class="function">len</span>(<span class="keyword">self</span>.skills)
    
    <span class="keyword">def</span> <span class="function">__eq__</span>(<span class="keyword">self</span>, other):
        <span class="string">"""相等比较"""</span>
        <span class="keyword">if</span> <span class="keyword">isinstance</span>(other, Person):
            <span class="keyword">return</span> <span class="keyword">self</span>.name == other.name <span class="keyword">and</span> <span class="keyword">self</span>.age == other.age
        <span class="keyword">return</span> <span class="keyword">False</span>

<span class="comment"># 使用类和对象</span>
<span class="function">print</span>(<span class="string">"=== 创建和使用对象 ==="</span>)

<span class="comment"># 创建对象实例</span>
person1 = Person(<span class="string">"张三"</span>, <span class="number">25</span>, <span class="string">"男"</span>)
person2 = Person(<span class="string">"李四"</span>, <span class="number">30</span>, <span class="string">"女"</span>)
person3 = Person(<span class="string">"王五"</span>, <span class="number">22</span>)  <span class="comment"># 使用默认性别</span>

<span class="comment"># 调用实例方法</span>
<span class="function">print</span>(<span class="string">"\n自我介绍:"</span>)
<span class="function">print</span>(person1.introduce())
<span class="function">print</span>(person2.introduce())
<span class="function">print</span>(person3.introduce())

<span class="comment"># 添加技能</span>
<span class="function">print</span>(<span class="string">"\n学习技能:"</span>)
person1.add_skill(<span class="string">"Python编程"</span>)
person1.add_skill(<span class="string">"数据分析"</span>)
person1.add_skill(<span class="string">"Python编程"</span>)  <span class="comment"># 重复技能</span>

person2.add_skill(<span class="string">"项目管理"</span>)
person2.add_skill(<span class="string">"团队协作"</span>)

<span class="comment"># 显示技能</span>
<span class="function">print</span>(<span class="string">"\n技能展示:"</span>)
<span class="function">print</span>(person1.show_skills())
<span class="function">print</span>(person2.show_skills())
<span class="function">print</span>(person3.show_skills())

<span class="comment"># 过生日</span>
<span class="function">print</span>(<span class="string">"\n生日庆祝:"</span>)
person1.celebrate_birthday()
person3.celebrate_birthday()</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 类方法和静态方法的使用</span>
<span class="function">print</span>(<span class="string">"\n=== 类方法和静态方法 ==="</span>)

<span class="comment"># 使用类方法</span>
<span class="function">print</span>(Person.get_population())

<span class="comment"># 创建婴儿</span>
baby = Person.create_baby(<span class="string">"小明"</span>)
<span class="function">print</span>(baby.introduce())
<span class="function">print</span>(Person.get_population())

<span class="comment"># 使用静态方法</span>
<span class="function">print</span>(<span class="string">"\n年龄判断:"</span>)
ages = [<span class="number">15</span>, <span class="number">18</span>, <span class="number">25</span>, <span class="number">30</span>]
<span class="keyword">for</span> age <span class="keyword">in</span> ages:
    status = <span class="string">"成年人"</span> <span class="keyword">if</span> Person.is_adult(age) <span class="keyword">else</span> <span class="string">"未成年人"</span>
    <span class="function">print</span>(<span class="string">f"{age}岁: {status}"</span>)

<span class="comment"># 访问类变量</span>
<span class="function">print</span>(<span class="string">f"\n物种: {Person.species}"</span>)
<span class="function">print</span>(<span class="string">f"总人数: {Person.population}"</span>)

<span class="comment"># 特殊方法的使用</span>
<span class="function">print</span>(<span class="string">"\n=== 特殊方法演示 ==="</span>)

<span class="comment"># __str__ 和 __repr__</span>
<span class="function">print</span>(<span class="string">f"str(person1): {str(person1)}"</span>)
<span class="function">print</span>(<span class="string">f"repr(person1): {repr(person1)}"</span>)

<span class="comment"># __len__</span>
<span class="function">print</span>(<span class="string">f"person1的技能数量: {len(person1)}"</span>)
<span class="function">print</span>(<span class="string">f"person3的技能数量: {len(person3)}"</span>)

<span class="comment"># __eq__</span>
person4 = Person(<span class="string">"张三"</span>, <span class="number">26</span>, <span class="string">"男"</span>)  <span class="comment"># 同名但年龄不同</span>
person5 = Person(<span class="string">"张三"</span>, <span class="number">25</span>, <span class="string">"男"</span>)  <span class="comment"># 同名同年龄</span>

<span class="function">print</span>(<span class="string">f"\nperson1 == person4: {person1 == person4}"</span>)
<span class="function">print</span>(<span class="string">f"person1 == person5: {person1 == person5}"</span>)

<span class="comment"># 属性访问</span>
<span class="function">print</span>(<span class="string">"\n=== 属性访问 ==="</span>)
<span class="function">print</span>(<span class="string">f"person1.name: {person1.name}"</span>)
<span class="function">print</span>(<span class="string">f"person1.age: {person1.age}"</span>)
<span class="function">print</span>(<span class="string">f"person1.skills: {person1.skills}"</span>)

<span class="comment"># 动态添加属性</span>
person1.hobby = <span class="string">"阅读"</span>
<span class="function">print</span>(<span class="string">f"person1.hobby: {person1.hobby}"</span>)

<span class="comment"># 检查属性是否存在</span>
<span class="function">print</span>(<span class="string">f"person1 有 'hobby' 属性: {hasattr(person1, 'hobby')}"</span>)
<span class="function">print</span>(<span class="string">f"person2 有 'hobby' 属性: {hasattr(person2, 'hobby')}"</span>)

<span class="comment"># 获取对象的所有属性</span>
<span class="function">print</span>(<span class="string">f"\nperson1的所有属性: {vars(person1)}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 继承示例</span>
<span class="keyword">class</span> <span class="function">Student</span>(Person):
    <span class="string">"""学生类 - 继承自Person"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>, name, age, gender, student_id, major):
        <span class="comment"># 调用父类构造方法</span>
        <span class="function">super</span>().__init__(name, age, gender)
        <span class="keyword">self</span>.student_id = student_id
        <span class="keyword">self</span>.major = major
        <span class="keyword">self</span>.grades = {}  <span class="comment"># 成绩字典</span>
        <span class="keyword">self</span>.gpa = <span class="number">0.0</span>
    
    <span class="comment"># 重写父类方法</span>
    <span class="keyword">def</span> <span class="function">introduce</span>(<span class="keyword">self</span>):
        <span class="comment"># 调用父类方法并扩展</span>
        base_intro = <span class="function">super</span>().introduce()
        <span class="keyword">return</span> <span class="string">f"{base_intro}，我是{self.major}专业的学生，学号是{self.student_id}"</span>
    
    <span class="comment"># 新增方法</span>
    <span class="keyword">def</span> <span class="function">add_grade</span>(<span class="keyword">self</span>, subject, score):
        <span class="string">"""添加成绩"""</span>
        <span class="keyword">if</span> <span class="number">0</span> <= score <= <span class="number">100</span>:
            <span class="keyword">self</span>.grades[subject] = score
            <span class="keyword">self</span>._calculate_gpa()
            <span class="function">print</span>(<span class="string">f"{self.name} 的 {subject} 成绩: {score}分"</span>)
        <span class="keyword">else</span>:
            <span class="function">print</span>(<span class="string">"成绩必须在0-100之间"</span>)
    
    <span class="keyword">def</span> <span class="function">_calculate_gpa</span>(<span class="keyword">self</span>):
        <span class="string">"""计算GPA（私有方法）"""</span>
        <span class="keyword">if</span> <span class="keyword">self</span>.grades:
            total = <span class="function">sum</span>(<span class="keyword">self</span>.grades.values())
            <span class="keyword">self</span>.gpa = total / <span class="function">len</span>(<span class="keyword">self</span>.grades)
    
    <span class="keyword">def</span> <span class="function">get_grade_report</span>(<span class="keyword">self</span>):
        <span class="string">"""获取成绩报告"""</span>
        <span class="keyword">if</span> <span class="keyword">not</span> <span class="keyword">self</span>.grades:
            <span class="keyword">return</span> <span class="string">f"{self.name} 还没有任何成绩记录"</span>
        
        report = <span class="string">f"\n=== {self.name} 的成绩报告 ===\n"</span>
        <span class="keyword">for</span> subject, score <span class="keyword">in</span> <span class="keyword">self</span>.grades.items():
            grade_letter = <span class="keyword">self</span>._score_to_letter(score)
            report += <span class="string">f"{subject}: {score}分 ({grade_letter})\n"</span>
        report += <span class="string">f"平均分: {self.gpa:.2f}\n"</span>
        report += <span class="string">f"等级: {self._score_to_letter(self.gpa)}"</span>
        <span class="keyword">return</span> report
    
    <span class="keyword">def</span> <span class="function">_score_to_letter</span>(<span class="keyword">self</span>, score):
        <span class="string">"""分数转换为等级"""</span>
        <span class="keyword">if</span> score >= <span class="number">90</span>:
            <span class="keyword">return</span> <span class="string">"A"</span>
        <span class="keyword">elif</span> score >= <span class="number">80</span>:
            <span class="keyword">return</span> <span class="string">"B"</span>
        <span class="keyword">elif</span> score >= <span class="number">70</span>:
            <span class="keyword">return</span> <span class="string">"C"</span>
        <span class="keyword">elif</span> score >= <span class="number">60</span>:
            <span class="keyword">return</span> <span class="string">"D"</span>
        <span class="keyword">else</span>:
            <span class="keyword">return</span> <span class="string">"F"</span>
    
    <span class="keyword">def</span> <span class="function">study</span>(<span class="keyword">self</span>, subject):
        <span class="string">"""学习某个科目"""</span>
        <span class="function">print</span>(<span class="string">f"📚 {self.name} 正在学习 {subject}"</span>)
        <span class="comment"># 学习也算是一种技能</span>
        <span class="keyword">self</span>.add_skill(<span class="string">f"{subject}学习"</span>)
    
    <span class="comment"># 重写特殊方法</span>
    <span class="keyword">def</span> <span class="function">__str__</span>(<span class="keyword">self</span>):
        <span class="keyword">return</span> <span class="string">f"Student(name='{self.name}', id='{self.student_id}', major='{self.major}')"</span>

<span class="comment"># 教师类</span>
<span class="keyword">class</span> <span class="function">Teacher</span>(Person):
    <span class="string">"""教师类 - 继承自Person"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>, name, age, gender, employee_id, department):
        <span class="function">super</span>().__init__(name, age, gender)
        <span class="keyword">self</span>.employee_id = employee_id
        <span class="keyword">self</span>.department = department
        <span class="keyword">self</span>.courses = []  <span class="comment"># 教授的课程</span>
        <span class="keyword">self</span>.students = []  <span class="comment"># 学生列表</span>
    
    <span class="keyword">def</span> <span class="function">introduce</span>(<span class="keyword">self</span>):
        base_intro = <span class="function">super</span>().introduce()
        <span class="keyword">return</span> <span class="string">f"{base_intro}，我是{self.department}的老师，工号是{self.employee_id}"</span>
    
    <span class="keyword">def</span> <span class="function">add_course</span>(<span class="keyword">self</span>, course):
        <span class="string">"""添加课程"""</span>
        <span class="keyword">if</span> course <span class="keyword">not</span> <span class="keyword">in</span> <span class="keyword">self</span>.courses:
            <span class="keyword">self</span>.courses.append(course)
            <span class="function">print</span>(<span class="string">f"{self.name} 老师开始教授 {course}"</span>)
    
    <span class="keyword">def</span> <span class="function">add_student</span>(<span class="keyword">self</span>, student):
        <span class="string">"""添加学生"""</span>
        <span class="keyword">if</span> <span class="function">isinstance</span>(student, Student):
            <span class="keyword">self</span>.students.append(student)
            <span class="function">print</span>(<span class="string">f"{student.name} 成为了 {self.name} 老师的学生"</span>)
    
    <span class="keyword">def</span> <span class="function">teach</span>(<span class="keyword">self</span>, course):
        <span class="string">"""教学"""</span>
        <span class="keyword">if</span> course <span class="keyword">in</span> <span class="keyword">self</span>.courses:
            <span class="function">print</span>(<span class="string">f"👨‍🏫 {self.name} 老师正在教授 {course}"</span>)
            <span class="comment"># 让所有学生学习这门课</span>
            <span class="keyword">for</span> student <span class="keyword">in</span> <span class="keyword">self</span>.students:
                student.study(course)
        <span class="keyword">else</span>:
            <span class="function">print</span>(<span class="string">f"{self.name} 老师不教授 {course}"</span>)
    
    <span class="keyword">def</span> <span class="function">__str__</span>(<span class="keyword">self</span>):
        <span class="keyword">return</span> <span class="string">f"Teacher(name='{self.name}', id='{self.employee_id}', dept='{self.department}')"</span>

<span class="comment"># 使用继承</span>
<span class="function">print</span>(<span class="string">"\n=== 继承示例 ==="</span>)

<span class="comment"># 创建学生对象</span>
student1 = Student(<span class="string">"小红"</span>, <span class="number">20</span>, <span class="string">"女"</span>, <span class="string">"2023001"</span>, <span class="string">"计算机科学"</span>)
student2 = Student(<span class="string">"小蓝"</span>, <span class="number">19</span>, <span class="string">"男"</span>, <span class="string">"2023002"</span>, <span class="string">"数据科学"</span>)

<span class="comment"># 创建教师对象</span>
teacher1 = Teacher(<span class="string">"王教授"</span>, <span class="number">45</span>, <span class="string">"男"</span>, <span class="string">"T001"</span>, <span class="string">"计算机学院"</span>)

<span class="comment"># 介绍</span>
<span class="function">print</span>(student1.introduce())
<span class="function">print</span>(student2.introduce())
<span class="function">print</span>(teacher1.introduce())

<span class="comment"># 添加课程和学生</span>
teacher1.add_course(<span class="string">"Python编程"</span>)
teacher1.add_course(<span class="string">"数据结构"</span>)
teacher1.add_student(student1)
teacher1.add_student(student2)

<span class="comment"># 教学</span>
<span class="function">print</span>(<span class="string">"\n开始上课:"</span>)
teacher1.teach(<span class="string">"Python编程"</span>)

<span class="comment"># 添加成绩</span>
<span class="function">print</span>(<span class="string">"\n成绩录入:"</span>)
student1.add_grade(<span class="string">"Python编程"</span>, <span class="number">95</span>)
student1.add_grade(<span class="string">"数据结构"</span>, <span class="number">88</span>)
student1.add_grade(<span class="string">"数学"</span>, <span class="number">92</span>)

student2.add_grade(<span class="string">"Python编程"</span>, <span class="number">87</span>)
student2.add_grade(<span class="string">"统计学"</span>, <span class="number">90</span>)

<span class="comment"># 查看成绩报告</span>
<span class="function">print</span>(student1.get_grade_report())
<span class="function">print</span>(student2.get_grade_report())

<span class="comment"># 检查继承关系</span>
<span class="function">print</span>(<span class="string">"\n=== 继承关系检查 ==="</span>)
<span class="function">print</span>(<span class="string">f"student1 是 Student 的实例: {isinstance(student1, Student)}"</span>)
<span class="function">print</span>(<span class="string">f"student1 是 Person 的实例: {isinstance(student1, Person)}"</span>)
<span class="function">print</span>(<span class="string">f"teacher1 是 Teacher 的实例: {isinstance(teacher1, Teacher)}"</span>)
<span class="function">print</span>(<span class="string">f"teacher1 是 Person 的实例: {isinstance(teacher1, Person)}"</span>)

<span class="comment"># 方法解析顺序</span>
<span class="function">print</span>(<span class="string">f"\nStudent 的方法解析顺序: {Student.__mro__}"</span>)</div>
                </div>

                <div class="warning-box">
                    <h3><span class="emoji">⚠️</span>面向对象编程要点</h3>
                    <ul>
                        <li><strong>封装：</strong>将数据和方法组织在类中，控制访问权限</li>
                        <li><strong>继承：</strong>子类可以继承父类的属性和方法，实现代码复用</li>
                        <li><strong>多态：</strong>不同类的对象可以响应相同的方法调用</li>
                        <li><strong>类变量 vs 实例变量：</strong>理解它们的区别和使用场景</li>
                        <li><strong>方法类型：</strong>实例方法、类方法、静态方法的区别</li>
                        <li><strong>特殊方法：</strong>__init__, __str__, __repr__ 等的作用</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习面向对象编程：</strong>"请详细解释Python中的面向对象编程概念，包括类和对象的定义、实例变量和类变量、方法类型（实例方法、类方法、静态方法）、继承、多态、封装等特性。请提供实际的类设计案例和最佳实践。"</p>
                </div>
            </div>
        </div>

        <!-- 第25页：数据结构与算法基础 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">📊</span>数据结构与算法基础</h1>
                <h2><span class="emoji">🔗</span>常用数据结构实现</h2>
                <p>数据结构是计算机存储、组织数据的方式。Python内置了多种数据结构，同时我们也可以实现自定义的数据结构来解决特定问题。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># 栈（Stack）实现</span>
<span class="keyword">class</span> <span class="function">Stack</span>:
    <span class="string">"""栈数据结构 - 后进先出（LIFO）"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>):
        <span class="keyword">self</span>.items = []
    
    <span class="keyword">def</span> <span class="function">push</span>(<span class="keyword">self</span>, item):
        <span class="string">"""入栈"""</span>
        <span class="keyword">self</span>.items.append(item)
        <span class="function">print</span>(<span class="string">f"入栈: {item}"</span>)
    
    <span class="keyword">def</span> <span class="function">pop</span>(<span class="keyword">self</span>):
        <span class="string">"""出栈"""</span>
        <span class="keyword">if</span> <span class="keyword">self</span>.is_empty():
            <span class="keyword">raise</span> <span class="function">IndexError</span>(<span class="string">"栈为空"</span>)
        item = <span class="keyword">self</span>.items.pop()
        <span class="function">print</span>(<span class="string">f"出栈: {item}"</span>)
        <span class="keyword">return</span> item
    
    <span class="keyword">def</span> <span class="function">peek</span>(<span class="keyword">self</span>):
        <span class="string">"""查看栈顶元素"""</span>
        <span class="keyword">if</span> <span class="keyword">self</span>.is_empty():
            <span class="keyword">return</span> <span class="keyword">None</span>
        <span class="keyword">return</span> <span class="keyword">self</span>.items[-<span class="number">1</span>]
    
    <span class="keyword">def</span> <span class="function">is_empty</span>(<span class="keyword">self</span>):
        <span class="string">"""检查栈是否为空"""</span>
        <span class="keyword">return</span> <span class="function">len</span>(<span class="keyword">self</span>.items) == <span class="number">0</span>
    
    <span class="keyword">def</span> <span class="function">size</span>(<span class="keyword">self</span>):
        <span class="string">"""获取栈的大小"""</span>
        <span class="keyword">return</span> <span class="function">len</span>(<span class="keyword">self</span>.items)
    
    <span class="keyword">def</span> <span class="function">__str__</span>(<span class="keyword">self</span>):
        <span class="keyword">return</span> <span class="string">f"Stack({self.items})"</span>

<span class="comment"># 栈的应用：括号匹配检查</span>
<span class="keyword">def</span> <span class="function">check_parentheses</span>(expression):
    <span class="string">"""检查括号是否匹配"""</span>
    stack = Stack()
    pairs = {<span class="string">')'</span>: <span class="string">'('</span>, <span class="string">']'</span>: <span class="string">'['</span>, <span class="string">'}'</span>: <span class="string">'{'</span>}
    
    <span class="keyword">for</span> char <span class="keyword">in</span> expression:
        <span class="keyword">if</span> char <span class="keyword">in</span> <span class="string">'([{'</span>:
            stack.push(char)
        <span class="keyword">elif</span> char <span class="keyword">in</span> <span class="string">')]}'</span>:
            <span class="keyword">if</span> stack.is_empty() <span class="keyword">or</span> stack.pop() != pairs[char]:
                <span class="keyword">return</span> <span class="keyword">False</span>
    
    <span class="keyword">return</span> stack.is_empty()

<span class="comment"># 队列（Queue）实现</span>
<span class="keyword">class</span> <span class="function">Queue</span>:
    <span class="string">"""队列数据结构 - 先进先出（FIFO）"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>):
        <span class="keyword">self</span>.items = []
    
    <span class="keyword">def</span> <span class="function">enqueue</span>(<span class="keyword">self</span>, item):
        <span class="string">"""入队"""</span>
        <span class="keyword">self</span>.items.insert(<span class="number">0</span>, item)
        <span class="function">print</span>(<span class="string">f"入队: {item}"</span>)
    
    <span class="keyword">def</span> <span class="function">dequeue</span>(<span class="keyword">self</span>):
        <span class="string">"""出队"""</span>
        <span class="keyword">if</span> <span class="keyword">self</span>.is_empty():
            <span class="keyword">raise</span> <span class="function">IndexError</span>(<span class="string">"队列为空"</span>)
        item = <span class="keyword">self</span>.items.pop()
        <span class="function">print</span>(<span class="string">f"出队: {item}"</span>)
        <span class="keyword">return</span> item
    
    <span class="keyword">def</span> <span class="function">front</span>(<span class="keyword">self</span>):
        <span class="string">"""查看队首元素"""</span>
        <span class="keyword">if</span> <span class="keyword">self</span>.is_empty():
            <span class="keyword">return</span> <span class="keyword">None</span>
        <span class="keyword">return</span> <span class="keyword">self</span>.items[-<span class="number">1</span>]
    
    <span class="keyword">def</span> <span class="function">is_empty</span>(<span class="keyword">self</span>):
        <span class="string">"""检查队列是否为空"""</span>
        <span class="keyword">return</span> <span class="function">len</span>(<span class="keyword">self</span>.items) == <span class="number">0</span>
    
    <span class="keyword">def</span> <span class="function">size</span>(<span class="keyword">self</span>):
        <span class="string">"""获取队列大小"""</span>
        <span class="keyword">return</span> <span class="function">len</span>(<span class="keyword">self</span>.items)
    
    <span class="keyword">def</span> <span class="function">__str__</span>(<span class="keyword">self</span>):
        <span class="keyword">return</span> <span class="string">f"Queue({self.items})"</span>

<span class="comment"># 链表节点</span>
<span class="keyword">class</span> <span class="function">ListNode</span>:
    <span class="string">"""链表节点"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>, data):
        <span class="keyword">self</span>.data = data
        <span class="keyword">self</span>.next = <span class="keyword">None</span>
    
    <span class="keyword">def</span> <span class="function">__str__</span>(<span class="keyword">self</span>):
        <span class="keyword">return</span> <span class="string">f"Node({self.data})"</span>

<span class="comment"># 单向链表</span>
<span class="keyword">class</span> <span class="function">LinkedList</span>:
    <span class="string">"""单向链表"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>):
        <span class="keyword">self</span>.head = <span class="keyword">None</span>
        <span class="keyword">self</span>.size = <span class="number">0</span>
    
    <span class="keyword">def</span> <span class="function">append</span>(<span class="keyword">self</span>, data):
        <span class="string">"""在链表末尾添加元素"""</span>
        new_node = ListNode(data)
        <span class="keyword">if</span> <span class="keyword">not</span> <span class="keyword">self</span>.head:
            <span class="keyword">self</span>.head = new_node
        <span class="keyword">else</span>:
            current = <span class="keyword">self</span>.head
            <span class="keyword">while</span> current.next:
                current = current.next
            current.next = new_node
        <span class="keyword">self</span>.size += <span class="number">1</span>
        <span class="function">print</span>(<span class="string">f"添加元素: {data}"</span>)
    
    <span class="keyword">def</span> <span class="function">prepend</span>(<span class="keyword">self</span>, data):
        <span class="string">"""在链表开头添加元素"""</span>
        new_node = ListNode(data)
        new_node.next = <span class="keyword">self</span>.head
        <span class="keyword">self</span>.head = new_node
        <span class="keyword">self</span>.size += <span class="number">1</span>
        <span class="function">print</span>(<span class="string">f"在开头添加元素: {data}"</span>)
    
    <span class="keyword">def</span> <span class="function">delete</span>(<span class="keyword">self</span>, data):
        <span class="string">"""删除指定元素"""</span>
        <span class="keyword">if</span> <span class="keyword">not</span> <span class="keyword">self</span>.head:
            <span class="keyword">return</span> <span class="keyword">False</span>
        
        <span class="keyword">if</span> <span class="keyword">self</span>.head.data == data:
            <span class="keyword">self</span>.head = <span class="keyword">self</span>.head.next
            <span class="keyword">self</span>.size -= <span class="number">1</span>
            <span class="function">print</span>(<span class="string">f"删除元素: {data}"</span>)
            <span class="keyword">return</span> <span class="keyword">True</span>
        
        current = <span class="keyword">self</span>.head
        <span class="keyword">while</span> current.next:
            <span class="keyword">if</span> current.next.data == data:
                current.next = current.next.next
                <span class="keyword">self</span>.size -= <span class="number">1</span>
                <span class="function">print</span>(<span class="string">f"删除元素: {data}"</span>)
                <span class="keyword">return</span> <span class="keyword">True</span>
            current = current.next
        
        <span class="function">print</span>(<span class="string">f"元素 {data} 不存在"</span>)
        <span class="keyword">return</span> <span class="keyword">False</span>
    
    <span class="keyword">def</span> <span class="function">find</span>(<span class="keyword">self</span>, data):
        <span class="string">"""查找元素"""</span>
        current = <span class="keyword">self</span>.head
        position = <span class="number">0</span>
        
        <span class="keyword">while</span> current:
            <span class="keyword">if</span> current.data == data:
                <span class="keyword">return</span> position
            current = current.next
            position += <span class="number">1</span>
        
        <span class="keyword">return</span> -<span class="number">1</span>
    
    <span class="keyword">def</span> <span class="function">display</span>(<span class="keyword">self</span>):
        <span class="string">"""显示链表"""</span>
        elements = []
        current = <span class="keyword">self</span>.head
        
        <span class="keyword">while</span> current:
            elements.append(<span class="function">str</span>(current.data))
            current = current.next
        
        <span class="keyword">return</span> <span class="string">" -> "</span>.join(elements) + <span class="string">" -> None"</span>
    
    <span class="keyword">def</span> <span class="function">__len__</span>(<span class="keyword">self</span>):
        <span class="keyword">return</span> <span class="keyword">self</span>.size
    
    <span class="keyword">def</span> <span class="function">__str__</span>(<span class="keyword">self</span>):
        <span class="keyword">return</span> <span class="keyword">self</span>.display()

<span class="comment"># 使用数据结构</span>
<span class="function">print</span>(<span class="string">"=== 栈的使用 ==="</span>)
stack = Stack()
stack.push(<span class="number">1</span>)
stack.push(<span class="number">2</span>)
stack.push(<span class="number">3</span>)
<span class="function">print</span>(<span class="string">f"栈顶元素: {stack.peek()}"</span>)
<span class="function">print</span>(<span class="string">f"栈大小: {stack.size()}"</span>)
stack.pop()
stack.pop()
<span class="function">print</span>(<span class="string">f"当前栈: {stack}"</span>)

<span class="comment"># 括号匹配测试</span>
<span class="function">print</span>(<span class="string">"\n=== 括号匹配检查 ==="</span>)
test_expressions = [
    <span class="string">"((()))",</span>
    <span class="string">"([{}])",</span>
    <span class="string">"((())",</span>
    <span class="string">"([)]"</span>
]

<span class="keyword">for</span> expr <span class="keyword">in</span> test_expressions:
    result = <span class="string">"匹配"</span> <span class="keyword">if</span> check_parentheses(expr) <span class="keyword">else</span> <span class="string">"不匹配"</span>
    <span class="function">print</span>(<span class="string">f"{expr}: {result}"</span>)

<span class="function">print</span>(<span class="string">"\n=== 队列的使用 ==="</span>)
queue = Queue()
queue.enqueue(<span class="string">"第一个"</span>)
queue.enqueue(<span class="string">"第二个"</span>)
queue.enqueue(<span class="string">"第三个"</span>)
<span class="function">print</span>(<span class="string">f"队首元素: {queue.front()}"</span>)
<span class="function">print</span>(<span class="string">f"队列大小: {queue.size()}"</span>)
queue.dequeue()
<span class="function">print</span>(<span class="string">f"当前队列: {queue}"</span>)

<span class="function">print</span>(<span class="string">"\n=== 链表的使用 ==="</span>)
linked_list = LinkedList()
linked_list.append(<span class="number">1</span>)
linked_list.append(<span class="number">2</span>)
linked_list.append(<span class="number">3</span>)
linked_list.prepend(<span class="number">0</span>)
<span class="function">print</span>(<span class="string">f"链表: {linked_list}"</span>)
<span class="function">print</span>(<span class="string">f"链表长度: {len(linked_list)}"</span>)
<span class="function">print</span>(<span class="string">f"查找元素2的位置: {linked_list.find(2)}"</span>)
linked_list.delete(<span class="number">2</span>)
<span class="function">print</span>(<span class="string">f"删除后的链表: {linked_list}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 二叉树实现</span>
<span class="keyword">class</span> <span class="function">TreeNode</span>:
    <span class="string">"""二叉树节点"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>, data):
        <span class="keyword">self</span>.data = data
        <span class="keyword">self</span>.left = <span class="keyword">None</span>
        <span class="keyword">self</span>.right = <span class="keyword">None</span>
    
    <span class="keyword">def</span> <span class="function">__str__</span>(<span class="keyword">self</span>):
        <span class="keyword">return</span> <span class="string">f"TreeNode({self.data})"</span>

<span class="keyword">class</span> <span class="function">BinarySearchTree</span>:
    <span class="string">"""二叉搜索树"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>):
        <span class="keyword">self</span>.root = <span class="keyword">None</span>
    
    <span class="keyword">def</span> <span class="function">insert</span>(<span class="keyword">self</span>, data):
        <span class="string">"""插入节点"""</span>
        <span class="keyword">if</span> <span class="keyword">not</span> <span class="keyword">self</span>.root:
            <span class="keyword">self</span>.root = TreeNode(data)
            <span class="function">print</span>(<span class="string">f"插入根节点: {data}"</span>)
        <span class="keyword">else</span>:
            <span class="keyword">self</span>._insert_recursive(<span class="keyword">self</span>.root, data)
    
    <span class="keyword">def</span> <span class="function">_insert_recursive</span>(<span class="keyword">self</span>, node, data):
        <span class="string">"""递归插入"""</span>
        <span class="keyword">if</span> data < node.data:
            <span class="keyword">if</span> node.left <span class="keyword">is</span> <span class="keyword">None</span>:
                node.left = TreeNode(data)
                <span class="function">print</span>(<span class="string">f"插入节点: {data} (作为 {node.data} 的左子节点)"</span>)
            <span class="keyword">else</span>:
                <span class="keyword">self</span>._insert_recursive(node.left, data)
        <span class="keyword">elif</span> data > node.data:
            <span class="keyword">if</span> node.right <span class="keyword">is</span> <span class="keyword">None</span>:
                node.right = TreeNode(data)
                <span class="function">print</span>(<span class="string">f"插入节点: {data} (作为 {node.data} 的右子节点)"</span>)
            <span class="keyword">else</span>:
                <span class="keyword">self</span>._insert_recursive(node.right, data)
        <span class="keyword">else</span>:
            <span class="function">print</span>(<span class="string">f"节点 {data} 已存在"</span>)
    
    <span class="keyword">def</span> <span class="function">search</span>(<span class="keyword">self</span>, data):
        <span class="string">"""搜索节点"""</span>
        <span class="keyword">return</span> <span class="keyword">self</span>._search_recursive(<span class="keyword">self</span>.root, data)
    
    <span class="keyword">def</span> <span class="function">_search_recursive</span>(<span class="keyword">self</span>, node, data):
        <span class="string">"""递归搜索"""</span>
        <span class="keyword">if</span> node <span class="keyword">is</span> <span class="keyword">None</span>:
            <span class="keyword">return</span> <span class="keyword">False</span>
        
        <span class="keyword">if</span> data == node.data:
            <span class="keyword">return</span> <span class="keyword">True</span>
        <span class="keyword">elif</span> data < node.data:
            <span class="keyword">return</span> <span class="keyword">self</span>._search_recursive(node.left, data)
        <span class="keyword">else</span>:
            <span class="keyword">return</span> <span class="keyword">self</span>._search_recursive(node.right, data)
    
    <span class="keyword">def</span> <span class="function">inorder_traversal</span>(<span class="keyword">self</span>):
        <span class="string">"""中序遍历（左-根-右）"""</span>
        result = []
        <span class="keyword">self</span>._inorder_recursive(<span class="keyword">self</span>.root, result)
        <span class="keyword">return</span> result
    
    <span class="keyword">def</span> <span class="function">_inorder_recursive</span>(<span class="keyword">self</span>, node, result):
        <span class="string">"""递归中序遍历"""</span>
        <span class="keyword">if</span> node:
            <span class="keyword">self</span>._inorder_recursive(node.left, result)
            result.append(node.data)
            <span class="keyword">self</span>._inorder_recursive(node.right, result)
    
    <span class="keyword">def</span> <span class="function">preorder_traversal</span>(<span class="keyword">self</span>):
        <span class="string">"""前序遍历（根-左-右）"""</span>
        result = []
        <span class="keyword">self</span>._preorder_recursive(<span class="keyword">self</span>.root, result)
        <span class="keyword">return</span> result
    
    <span class="keyword">def</span> <span class="function">_preorder_recursive</span>(<span class="keyword">self</span>, node, result):
        <span class="string">"""递归前序遍历"""</span>
        <span class="keyword">if</span> node:
            result.append(node.data)
            <span class="keyword">self</span>._preorder_recursive(node.left, result)
            <span class="keyword">self</span>._preorder_recursive(node.right, result)
    
    <span class="keyword">def</span> <span class="function">postorder_traversal</span>(<span class="keyword">self</span>):
        <span class="string">"""后序遍历（左-右-根）"""</span>
        result = []
        <span class="keyword">self</span>._postorder_recursive(<span class="keyword">self</span>.root, result)
        <span class="keyword">return</span> result
    
    <span class="keyword">def</span> <span class="function">_postorder_recursive</span>(<span class="keyword">self</span>, node, result):
        <span class="string">"""递归后序遍历"""</span>
        <span class="keyword">if</span> node:
            <span class="keyword">self</span>._postorder_recursive(node.left, result)
            <span class="keyword">self</span>._postorder_recursive(node.right, result)
            result.append(node.data)
    
    <span class="keyword">def</span> <span class="function">find_min</span>(<span class="keyword">self</span>):
        <span class="string">"""查找最小值"""</span>
        <span class="keyword">if</span> <span class="keyword">not</span> <span class="keyword">self</span>.root:
            <span class="keyword">return</span> <span class="keyword">None</span>
        
        current = <span class="keyword">self</span>.root
        <span class="keyword">while</span> current.left:
            current = current.left
        <span class="keyword">return</span> current.data
    
    <span class="keyword">def</span> <span class="function">find_max</span>(<span class="keyword">self</span>):
        <span class="string">"""查找最大值"""</span>
        <span class="keyword">if</span> <span class="keyword">not</span> <span class="keyword">self</span>.root:
            <span class="keyword">return</span> <span class="keyword">None</span>
        
        current = <span class="keyword">self</span>.root
        <span class="keyword">while</span> current.right:
            current = current.right
        <span class="keyword">return</span> current.data

<span class="comment"># 常用排序算法</span>
<span class="keyword">def</span> <span class="function">bubble_sort</span>(arr):
    <span class="string">"""冒泡排序"""</span>
    n = <span class="function">len</span>(arr)
    <span class="function">print</span>(<span class="string">f"开始冒泡排序: {arr}"</span>)
    
    <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(n):
        swapped = <span class="keyword">False</span>
        <span class="keyword">for</span> j <span class="keyword">in</span> <span class="function">range</span>(<span class="number">0</span>, n - i - <span class="number">1</span>):
            <span class="keyword">if</span> arr[j] > arr[j + <span class="number">1</span>]:
                arr[j], arr[j + <span class="number">1</span>] = arr[j + <span class="number">1</span>], arr[j]
                swapped = <span class="keyword">True</span>
        
        <span class="keyword">if</span> <span class="keyword">not</span> swapped:
            <span class="keyword">break</span>
        
        <span class="function">print</span>(<span class="string">f"第 {i+1} 轮后: {arr}"</span>)
    
    <span class="keyword">return</span> arr

<span class="keyword">def</span> <span class="function">quick_sort</span>(arr):
    <span class="string">"""快速排序"""</span>
    <span class="keyword">if</span> <span class="function">len</span>(arr) <= <span class="number">1</span>:
        <span class="keyword">return</span> arr
    
    pivot = arr[<span class="function">len</span>(arr) // <span class="number">2</span>]
    left = [x <span class="keyword">for</span> x <span class="keyword">in</span> arr <span class="keyword">if</span> x < pivot]
    middle = [x <span class="keyword">for</span> x <span class="keyword">in</span> arr <span class="keyword">if</span> x == pivot]
    right = [x <span class="keyword">for</span> x <span class="keyword">in</span> arr <span class="keyword">if</span> x > pivot]
    
    <span class="function">print</span>(<span class="string">f"分割: 左={left}, 中={middle}, 右={right}"</span>)
    
    <span class="keyword">return</span> quick_sort(left) + middle + quick_sort(right)

<span class="keyword">def</span> <span class="function">binary_search</span>(arr, target):
    <span class="string">"""二分查找"""</span>
    left, right = <span class="number">0</span>, <span class="function">len</span>(arr) - <span class="number">1</span>
    
    <span class="keyword">while</span> left <= right:
        mid = (left + right) // <span class="number">2</span>
        <span class="function">print</span>(<span class="string">f"查找范围: [{left}, {right}], 中点: {mid}, 值: {arr[mid]}"</span>)
        
        <span class="keyword">if</span> arr[mid] == target:
            <span class="keyword">return</span> mid
        <span class="keyword">elif</span> arr[mid] < target:
            left = mid + <span class="number">1</span>
        <span class="keyword">else</span>:
            right = mid - <span class="number">1</span>
    
    <span class="keyword">return</span> -<span class="number">1</span>

<span class="comment"># 使用二叉搜索树</span>
<span class="function">print</span>(<span class="string">"\n=== 二叉搜索树 ==="</span>)
bst = BinarySearchTree()
values = [<span class="number">50</span>, <span class="number">30</span>, <span class="number">70</span>, <span class="number">20</span>, <span class="number">40</span>, <span class="number">60</span>, <span class="number">80</span>]

<span class="keyword">for</span> value <span class="keyword">in</span> values:
    bst.insert(value)

<span class="function">print</span>(<span class="string">f"\n中序遍历（有序）: {bst.inorder_traversal()}"</span>)
<span class="function">print</span>(<span class="string">f"前序遍历: {bst.preorder_traversal()}"</span>)
<span class="function">print</span>(<span class="string">f"后序遍历: {bst.postorder_traversal()}"</span>)
<span class="function">print</span>(<span class="string">f"最小值: {bst.find_min()}"</span>)
<span class="function">print</span>(<span class="string">f"最大值: {bst.find_max()}"</span>)

<span class="comment"># 搜索测试</span>
search_values = [<span class="number">40</span>, <span class="number">25</span>, <span class="number">90</span>]
<span class="keyword">for</span> value <span class="keyword">in</span> search_values:
    found = <span class="string">"找到"</span> <span class="keyword">if</span> bst.search(value) <span class="keyword">else</span> <span class="string">"未找到"</span>
    <span class="function">print</span>(<span class="string">f"搜索 {value}: {found}"</span>)

<span class="comment"># 排序算法测试</span>
<span class="function">print</span>(<span class="string">"\n=== 排序算法 ==="</span>)
test_array = [<span class="number">64</span>, <span class="number">34</span>, <span class="number">25</span>, <span class="number">12</span>, <span class="number">22</span>, <span class="number">11</span>, <span class="number">90</span>]

<span class="comment"># 冒泡排序</span>
bubble_result = bubble_sort(test_array.copy())
<span class="function">print</span>(<span class="string">f"冒泡排序结果: {bubble_result}"</span>)

<span class="comment"># 快速排序</span>
<span class="function">print</span>(<span class="string">f"\n开始快速排序: {test_array}"</span>)
quick_result = quick_sort(test_array.copy())
<span class="function">print</span>(<span class="string">f"快速排序结果: {quick_result}"</span>)

<span class="comment"># 二分查找</span>
<span class="function">print</span>(<span class="string">"\n=== 二分查找 ==="</span>)
sorted_array = [<span class="number">11</span>, <span class="number">12</span>, <span class="number">22</span>, <span class="number">25</span>, <span class="number">34</span>, <span class="number">64</span>, <span class="number">90</span>]
target = <span class="number">25</span>
<span class="function">print</span>(<span class="string">f"在数组 {sorted_array} 中查找 {target}"</span>)
result = binary_search(sorted_array, target)
<span class="keyword">if</span> result != -<span class="number">1</span>:
    <span class="function">print</span>(<span class="string">f"找到 {target}，位置: {result}"</span>)
<span class="keyword">else</span>:
    <span class="function">print</span>(<span class="string">f"未找到 {target}"</span>)</div>
                </div>

                <div class="warning-box">
                    <h3><span class="emoji">⚠️</span>数据结构与算法要点</h3>
                    <ul>
                        <li><strong>时间复杂度：</strong>分析算法执行时间与输入规模的关系</li>
                        <li><strong>空间复杂度：</strong>分析算法所需存储空间与输入规模的关系</li>
                        <li><strong>数据结构选择：</strong>根据应用场景选择合适的数据结构</li>
                        <li><strong>算法优化：</strong>考虑最坏、平均和最好情况的性能</li>
                        <li><strong>递归与迭代：</strong>理解两种实现方式的优缺点</li>
                        <li><strong>实际应用：</strong>将理论知识应用到实际问题解决中</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习数据结构与算法：</strong>"请详细解释Python中的常用数据结构（栈、队列、链表、树）和基础算法（排序、查找），包括它们的实现原理、时间空间复杂度分析、适用场景和实际应用案例。请提供完整的代码实现和性能比较。"</p>
                </div>
            </div>
        </div>

        <!-- 第26页：Python标准库精选 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">📚</span>Python标准库精选</h1>
                <h2><span class="emoji">🔧</span>常用标准库模块</h2>
                <p>Python标准库提供了丰富的模块，涵盖了文件处理、网络编程、数据处理、系统操作等各个方面。掌握这些标准库可以大大提高开发效率。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># datetime - 日期时间处理</span>
<span class="keyword">import</span> datetime
<span class="keyword">from</span> datetime <span class="keyword">import</span> date, time, timedelta

<span class="comment"># 获取当前时间</span>
now = datetime.datetime.now()
<span class="function">print</span>(<span class="string">f"当前时间: {now}"</span>)
<span class="function">print</span>(<span class="string">f"格式化时间: {now.strftime('%Y-%m-%d %H:%M:%S')}"</span>)

<span class="comment"># 日期计算</span>
today = date.today()
tomorrow = today + timedelta(days=<span class="number">1</span>)
last_week = today - timedelta(weeks=<span class="number">1</span>)
<span class="function">print</span>(<span class="string">f"今天: {today}"</span>)
<span class="function">print</span>(<span class="string">f"明天: {tomorrow}"</span>)
<span class="function">print</span>(<span class="string">f"上周: {last_week}"</span>)

<span class="comment"># 时间解析</span>
date_string = <span class="string">"2024-12-25 15:30:00"</span>
parsed_date = datetime.datetime.strptime(date_string, <span class="string">"%Y-%m-%d %H:%M:%S"</span>)
<span class="function">print</span>(<span class="string">f"解析的日期: {parsed_date}"</span>)

<span class="comment"># 时间戳转换</span>
timestamp = now.timestamp()
<span class="function">print</span>(<span class="string">f"时间戳: {timestamp}"</span>)
from_timestamp = datetime.datetime.fromtimestamp(timestamp)
<span class="function">print</span>(<span class="string">f"从时间戳转换: {from_timestamp}"</span>)

<span class="comment"># collections - 特殊容器数据类型</span>
<span class="keyword">from</span> collections <span class="keyword">import</span> Counter, defaultdict, deque, namedtuple

<span class="comment"># Counter - 计数器</span>
text = <span class="string">"hello world hello python"</span>
word_count = Counter(text.split())
<span class="function">print</span>(<span class="string">f"单词计数: {word_count}"</span>)
<span class="function">print</span>(<span class="string">f"最常见的2个单词: {word_count.most_common(2)}"</span>)

char_count = Counter(text.replace(<span class="string">" "</span>, <span class="string">""</span>))
<span class="function">print</span>(<span class="string">f"字符计数: {char_count}"</span>)

<span class="comment"># defaultdict - 默认字典</span>
dd = defaultdict(<span class="function">list</span>)
dd[<span class="string">'fruits'</span>].append(<span class="string">'apple'</span>)
dd[<span class="string">'fruits'</span>].append(<span class="string">'banana'</span>)
dd[<span class="string">'vegetables'</span>].append(<span class="string">'carrot'</span>)
<span class="function">print</span>(<span class="string">f"默认字典: {dict(dd)}"</span>)

<span class="comment"># deque - 双端队列</span>
dq = deque([<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>])
dq.appendleft(<span class="number">0</span>)  <span class="comment"># 左侧添加</span>
dq.append(<span class="number">4</span>)     <span class="comment"># 右侧添加</span>
<span class="function">print</span>(<span class="string">f"双端队列: {dq}"</span>)
<span class="function">print</span>(<span class="string">f"左侧弹出: {dq.popleft()}"</span>)
<span class="function">print</span>(<span class="string">f"右侧弹出: {dq.pop()}"</span>)

<span class="comment"># namedtuple - 命名元组</span>
Point = namedtuple(<span class="string">'Point'</span>, [<span class="string">'x'</span>, <span class="string">'y'</span>])
p1 = Point(<span class="number">1</span>, <span class="number">2</span>)
p2 = Point(x=<span class="number">3</span>, y=<span class="number">4</span>)
<span class="function">print</span>(<span class="string">f"点1: {p1}, x={p1.x}, y={p1.y}"</span>)
<span class="function">print</span>(<span class="string">f"点2: {p2}"</span>)

<span class="comment"># itertools - 迭代器工具</span>
<span class="keyword">import</span> itertools

<span class="comment"># 组合和排列</span>
data = [<span class="string">'A'</span>, <span class="string">'B'</span>, <span class="string">'C'</span>]
combinations = <span class="function">list</span>(itertools.combinations(data, <span class="number">2</span>))
permutations = <span class="function">list</span>(itertools.permutations(data, <span class="number">2</span>))
<span class="function">print</span>(<span class="string">f"组合: {combinations}"</span>)
<span class="function">print</span>(<span class="string">f"排列: {permutations}"</span>)

<span class="comment"># 无限迭代器</span>
counter = itertools.count(<span class="number">1</span>, <span class="number">2</span>)  <span class="comment"># 从1开始，步长为2</span>
first_five = [<span class="function">next</span>(counter) <span class="keyword">for</span> _ <span class="keyword">in</span> <span class="function">range</span>(<span class="number">5</span>)]
<span class="function">print</span>(<span class="string">f"前5个数: {first_five}"</span>)

<span class="comment"># 循环迭代器</span>
cycle_iter = itertools.cycle([<span class="string">'red'</span>, <span class="string">'green'</span>, <span class="string">'blue'</span>])
colors = [<span class="function">next</span>(cycle_iter) <span class="keyword">for</span> _ <span class="keyword">in</span> <span class="function">range</span>(<span class="number">7</span>)]
<span class="function">print</span>(<span class="string">f"循环颜色: {colors}"</span>)

<span class="comment"># 重复迭代器</span>
repeated = <span class="function">list</span>(itertools.repeat(<span class="string">'hello'</span>, <span class="number">3</span>))
<span class="function">print</span>(<span class="string">f"重复: {repeated}"</span>)

<span class="comment"># 链式迭代器</span>
list1 = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>]
list2 = [<span class="number">4</span>, <span class="number">5</span>, <span class="number">6</span>]
chained = <span class="function">list</span>(itertools.chain(list1, list2))
<span class="function">print</span>(<span class="string">f"链式连接: {chained}"</span>)

<span class="comment"># functools - 函数工具</span>
<span class="keyword">import</span> functools

<span class="comment"># reduce - 累积计算</span>
numbers = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>]
product = functools.reduce(<span class="keyword">lambda</span> x, y: x * y, numbers)
<span class="function">print</span>(<span class="string">f"数字乘积: {product}"</span>)

<span class="comment"># partial - 偏函数</span>
<span class="keyword">def</span> <span class="function">multiply</span>(x, y):
    <span class="keyword">return</span> x * y

double = functools.partial(multiply, <span class="number">2</span>)
triple = functools.partial(multiply, <span class="number">3</span>)

<span class="function">print</span>(<span class="string">f"2 * 5 = {double(5)}"</span>)
<span class="function">print</span>(<span class="string">f"3 * 5 = {triple(5)}"</span>)

<span class="comment"># lru_cache - 最近最少使用缓存</span>
<span class="function">@functools.lru_cache</span>(maxsize=<span class="number">128</span>)
<span class="keyword">def</span> <span class="function">fibonacci</span>(n):
    <span class="keyword">if</span> n < <span class="number">2</span>:
        <span class="keyword">return</span> n
    <span class="keyword">return</span> fibonacci(n-<span class="number">1</span>) + fibonacci(n-<span class="number">2</span>)

<span class="comment"># 测试缓存效果</span>
<span class="keyword">import</span> time

start_time = time.time()
result = fibonacci(<span class="number">30</span>)
end_time = time.time()

<span class="function">print</span>(<span class="string">f"斐波那契(30) = {result}"</span>)
<span class="function">print</span>(<span class="string">f"计算时间: {end_time - start_time:.6f}秒"</span>)
<span class="function">print</span>(<span class="string">f"缓存信息: {fibonacci.cache_info()}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># random - 随机数生成</span>
<span class="keyword">import</span> random

<span class="comment"># 基本随机数</span>
<span class="function">print</span>(<span class="string">f"随机浮点数 [0,1): {random.random()}"</span>)
<span class="function">print</span>(<span class="string">f"随机整数 [1,10]: {random.randint(1, 10)}"</span>)
<span class="function">print</span>(<span class="string">f"随机浮点数 [1,10]: {random.uniform(1, 10)}"</span>)

<span class="comment"># 序列操作</span>
colors = [<span class="string">'red'</span>, <span class="string">'green'</span>, <span class="string">'blue'</span>, <span class="string">'yellow'</span>, <span class="string">'purple'</span>]
<span class="function">print</span>(<span class="string">f"随机选择: {random.choice(colors)}"</span>)
<span class="function">print</span>(<span class="string">f"随机选择多个: {random.choices(colors, k=3)}"</span>)
<span class="function">print</span>(<span class="string">f"随机抽样: {random.sample(colors, 3)}"</span>)

<span class="comment"># 打乱序列</span>
numbers = <span class="function">list</span>(<span class="function">range</span>(<span class="number">1</span>, <span class="number">11</span>))
<span class="function">print</span>(<span class="string">f"原序列: {numbers}"</span>)
random.shuffle(numbers)
<span class="function">print</span>(<span class="string">f"打乱后: {numbers}"</span>)

<span class="comment"># 设置随机种子</span>
random.seed(<span class="number">42</span>)
<span class="function">print</span>(<span class="string">f"种子42的随机数: {[random.randint(1, 100) for _ in range(5)]}"</span>)

<span class="comment"># re - 正则表达式</span>
<span class="keyword">import</span> re

<span class="comment"># 基本匹配</span>
text = <span class="string">"联系电话：138-1234-5678，邮箱：<EMAIL>"</span>

<span class="comment"># 查找电话号码</span>
phone_pattern = <span class="string">r'\d{3}-\d{4}-\d{4}'</span>
phone_match = re.search(phone_pattern, text)
<span class="keyword">if</span> phone_match:
    <span class="function">print</span>(<span class="string">f"找到电话: {phone_match.group()}"</span>)

<span class="comment"># 查找邮箱</span>
email_pattern = <span class="string">r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'</span>
email_match = re.search(email_pattern, text)
<span class="keyword">if</span> email_match:
    <span class="function">print</span>(<span class="string">f"找到邮箱: {email_match.group()}"</span>)

<span class="comment"># 查找所有匹配</span>
text_with_numbers = <span class="string">"价格：100元，折扣：20%，总计：80元"</span>
number_pattern = <span class="string">r'\d+'</span>
all_numbers = re.findall(number_pattern, text_with_numbers)
<span class="function">print</span>(<span class="string">f"所有数字: {all_numbers}"</span>)

<span class="comment"># 替换操作</span>
original = <span class="string">"今天是2024年12月25日"</span>
date_pattern = <span class="string">r'(\d{4})年(\d{1,2})月(\d{1,2})日'</span>
replaced = re.sub(date_pattern, <span class="string">r'\1-\2-\3'</span>, original)
<span class="function">print</span>(<span class="string">f"日期格式转换: {replaced}"</span>)

<span class="comment"># 分组捕获</span>
log_entry = <span class="string">"2024-12-25 14:30:15 [INFO] 用户登录成功"</span>
log_pattern = <span class="string">r'(\d{4}-\d{2}-\d{2}) (\d{2}:\d{2}:\d{2}) \[(\w+)\] (.+)'</span>
log_match = re.match(log_pattern, log_entry)
<span class="keyword">if</span> log_match:
    date, time, level, message = log_match.groups()
    <span class="function">print</span>(<span class="string">f"日期: {date}, 时间: {time}, 级别: {level}, 消息: {message}"</span>)

<span class="comment"># json - JSON数据处理</span>
<span class="keyword">import</span> json

<span class="comment"># Python对象转JSON</span>
data = {
    <span class="string">"name"</span>: <span class="string">"张三"</span>,
    <span class="string">"age"</span>: <span class="number">25</span>,
    <span class="string">"skills"</span>: [<span class="string">"Python"</span>, <span class="string">"JavaScript"</span>, <span class="string">"SQL"</span>],
    <span class="string">"is_student"</span>: <span class="keyword">False</span>,
    <span class="string">"address"</span>: {
        <span class="string">"city"</span>: <span class="string">"北京"</span>,
        <span class="string">"district"</span>: <span class="string">"朝阳区"</span>
    }
}

json_string = json.dumps(data, ensure_ascii=<span class="keyword">False</span>, indent=<span class="number">2</span>)
<span class="function">print</span>(<span class="string">f"JSON字符串:\n{json_string}"</span>)

<span class="comment"># JSON转Python对象</span>
parsed_data = json.loads(json_string)
<span class="function">print</span>(<span class="string">f"解析后的数据: {parsed_data}"</span>)
<span class="function">print</span>(<span class="string">f"姓名: {parsed_data['name']}"</span>)
<span class="function">print</span>(<span class="string">f"技能: {', '.join(parsed_data['skills'])}"</span>)

<span class="comment"># 文件操作</span>
<span class="keyword">try</span>:
    <span class="comment"># 写入JSON文件</span>
    <span class="keyword">with</span> <span class="function">open</span>(<span class="string">'user_data.json'</span>, <span class="string">'w'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> f:
        json.dump(data, f, ensure_ascii=<span class="keyword">False</span>, indent=<span class="number">2</span>)
    <span class="function">print</span>(<span class="string">"JSON文件写入成功"</span>)
    
    <span class="comment"># 读取JSON文件</span>
    <span class="keyword">with</span> <span class="function">open</span>(<span class="string">'user_data.json'</span>, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> f:
        loaded_data = json.load(f)
    <span class="function">print</span>(<span class="string">f"从文件读取的数据: {loaded_data}"</span>)
    
<span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
    <span class="function">print</span>(<span class="string">f"文件操作错误: {e}"</span>)

<span class="comment"># urllib - URL处理</span>
<span class="keyword">from</span> urllib.parse <span class="keyword">import</span> urlparse, urljoin, quote, unquote

<span class="comment"># URL解析</span>
url = <span class="string">"https://www.example.com:8080/path/to/page?param1=value1&param2=value2#section"</span>
parsed_url = urlparse(url)
<span class="function">print</span>(<span class="string">f"协议: {parsed_url.scheme}"</span>)
<span class="function">print</span>(<span class="string">f"主机: {parsed_url.netloc}"</span>)
<span class="function">print</span>(<span class="string">f"路径: {parsed_url.path}"</span>)
<span class="function">print</span>(<span class="string">f"查询参数: {parsed_url.query}"</span>)
<span class="function">print</span>(<span class="string">f"片段: {parsed_url.fragment}"</span>)

<span class="comment"># URL编码和解码</span>
original_text = <span class="string">"Python 编程"</span>
encoded = quote(original_text)
decoded = unquote(encoded)
<span class="function">print</span>(<span class="string">f"原文: {original_text}"</span>)
<span class="function">print</span>(<span class="string">f"编码: {encoded}"</span>)
<span class="function">print</span>(<span class="string">f"解码: {decoded}"</span>)

<span class="comment"># URL拼接</span>
base_url = <span class="string">"https://api.example.com/"</span>
endpoint = <span class="string">"users/123"</span>
full_url = urljoin(base_url, endpoint)
<span class="function">print</span>(<span class="string">f"完整URL: {full_url}"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 实用工具类</span>
<span class="keyword">class</span> <span class="function">DataProcessor</span>:
    <span class="string">"""数据处理工具类"""</span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>):
        <span class="keyword">self</span>.data_cache = {}
    
    <span class="keyword">def</span> <span class="function">process_text</span>(<span class="keyword">self</span>, text):
        <span class="string">"""文本处理"""</span>
        <span class="keyword">import</span> re
        
        <span class="comment"># 清理文本</span>
        cleaned = re.sub(<span class="string">r'[^\w\s]'</span>, <span class="string">''</span>, text)
        words = cleaned.lower().split()
        
        <span class="comment"># 统计词频</span>
        <span class="keyword">from</span> collections <span class="keyword">import</span> Counter
        word_count = Counter(words)
        
        <span class="keyword">return</span> {
            <span class="string">'original'</span>: text,
            <span class="string">'cleaned'</span>: cleaned,
            <span class="string">'word_count'</span>: <span class="function">dict</span>(word_count),
            <span class="string">'total_words'</span>: <span class="function">len</span>(words),
            <span class="string">'unique_words'</span>: <span class="function">len</span>(word_count)
        }
    
    <span class="keyword">def</span> <span class="function">generate_report</span>(<span class="keyword">self</span>, data_list):
        <span class="string">"""生成数据报告"""</span>
        <span class="keyword">if</span> <span class="keyword">not</span> data_list:
            <span class="keyword">return</span> <span class="string">"无数据"</span>
        
        <span class="keyword">import</span> statistics
        
        report = {
            <span class="string">'count'</span>: <span class="function">len</span>(data_list),
            <span class="string">'sum'</span>: <span class="function">sum</span>(data_list),
            <span class="string">'mean'</span>: statistics.mean(data_list),
            <span class="string">'median'</span>: statistics.median(data_list),
            <span class="string">'min'</span>: <span class="function">min</span>(data_list),
            <span class="string">'max'</span>: <span class="function">max</span>(data_list)
        }
        
        <span class="keyword">if</span> <span class="function">len</span>(data_list) > <span class="number">1</span>:
            report[<span class="string">'stdev'</span>] = statistics.stdev(data_list)
        
        <span class="keyword">return</span> report
    
    <span class="keyword">def</span> <span class="function">cache_data</span>(<span class="keyword">self</span>, key, data):
        <span class="string">"""缓存数据"""</span>
        <span class="keyword">import</span> datetime
        <span class="keyword">self</span>.data_cache[key] = {
            <span class="string">'data'</span>: data,
            <span class="string">'timestamp'</span>: datetime.datetime.now()
        }
        <span class="function">print</span>(<span class="string">f"数据已缓存: {key}"</span>)
    
    <span class="keyword">def</span> <span class="function">get_cached_data</span>(<span class="keyword">self</span>, key, max_age_minutes=<span class="number">60</span>):
        <span class="string">"""获取缓存数据"""</span>
        <span class="keyword">if</span> key <span class="keyword">not</span> <span class="keyword">in</span> <span class="keyword">self</span>.data_cache:
            <span class="keyword">return</span> <span class="keyword">None</span>
        
        <span class="keyword">import</span> datetime
        cached_item = <span class="keyword">self</span>.data_cache[key]
        age = datetime.datetime.now() - cached_item[<span class="string">'timestamp'</span>]
        
        <span class="keyword">if</span> age.total_seconds() > max_age_minutes * <span class="number">60</span>:
            <span class="keyword">del</span> <span class="keyword">self</span>.data_cache[key]
            <span class="keyword">return</span> <span class="keyword">None</span>
        
        <span class="keyword">return</span> cached_item[<span class="string">'data'</span>]
    
    <span class="keyword">def</span> <span class="function">export_to_csv</span>(<span class="keyword">self</span>, data, filename):
        <span class="string">"""导出数据到CSV"""</span>
        <span class="keyword">import</span> csv
        
        <span class="keyword">try</span>:
            <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">'w'</span>, newline=<span class="string">''</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> csvfile:
                <span class="keyword">if</span> <span class="function">isinstance</span>(data[<span class="number">0</span>], <span class="function">dict</span>):
                    fieldnames = data[<span class="number">0</span>].keys()
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(data)
                <span class="keyword">else</span>:
                    writer = csv.writer(csvfile)
                    writer.writerows(data)
            
            <span class="function">print</span>(<span class="string">f"数据已导出到: {filename}"</span>)
            <span class="keyword">return</span> <span class="keyword">True</span>
        
        <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
            <span class="function">print</span>(<span class="string">f"导出失败: {e}"</span>)
            <span class="keyword">return</span> <span class="keyword">False</span>

<span class="comment"># 使用示例</span>
<span class="function">print</span>(<span class="string">"\n=== 数据处理工具类使用示例 ==="</span>)
processor = DataProcessor()

<span class="comment"># 文本处理</span>
sample_text = <span class="string">"Python是一种强大的编程语言！Python易学易用，Python应用广泛。"</span>
text_result = processor.process_text(sample_text)
<span class="function">print</span>(<span class="string">f"文本分析结果: {text_result}"</span>)

<span class="comment"># 数据报告</span>
sample_numbers = [<span class="number">85</span>, <span class="number">92</span>, <span class="number">78</span>, <span class="number">96</span>, <span class="number">88</span>, <span class="number">91</span>, <span class="number">84</span>, <span class="number">89</span>]
report = processor.generate_report(sample_numbers)
<span class="function">print</span>(<span class="string">f"数据报告: {report}"</span>)

<span class="comment"># 缓存操作</span>
processor.cache_data(<span class="string">'test_data'</span>, sample_numbers)
cached = processor.get_cached_data(<span class="string">'test_data'</span>)
<span class="function">print</span>(<span class="string">f"缓存的数据: {cached}"</span>)

<span class="comment"># CSV导出</span>
csv_data = [
    {<span class="string">'姓名'</span>: <span class="string">'张三'</span>, <span class="string">'年龄'</span>: <span class="number">25</span>, <span class="string">'分数'</span>: <span class="number">85</span>},
    {<span class="string">'姓名'</span>: <span class="string">'李四'</span>, <span class="string">'年龄'</span>: <span class="number">23</span>, <span class="string">'分数'</span>: <span class="number">92</span>},
    {<span class="string">'姓名'</span>: <span class="string">'王五'</span>, <span class="string">'年龄'</span>: <span class="number">24</span>, <span class="string">'分数'</span>: <span class="number">78</span>}
]
processor.export_to_csv(csv_data, <span class="string">'student_data.csv'</span>)</div>
                </div>

                <div class="warning-box">
                    <h3><span class="emoji">⚠️</span>标准库使用要点</h3>
                    <ul>
                        <li><strong>模块导入：</strong>根据需要选择合适的导入方式（import、from import）</li>
                        <li><strong>性能考虑：</strong>了解不同模块的性能特点，选择最适合的工具</li>
                        <li><strong>异常处理：</strong>在使用文件操作、网络请求等功能时要处理异常</li>
                        <li><strong>编码问题：</strong>处理中文时注意编码设置（UTF-8）</li>
                        <li><strong>资源管理：</strong>使用with语句管理文件等资源</li>
                        <li><strong>文档查阅：</strong>善用Python官方文档了解模块详细用法</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习Python标准库：</strong>"请详细介绍Python标准库中的常用模块，包括datetime、collections、itertools、functools、random、re、json、urllib等，解释它们的主要功能、常用方法、实际应用场景和最佳实践。请提供具体的代码示例和使用技巧。"</p>
                </div>
            </div>
        </div>

        <!-- 第27页：Python第三方库生态 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">🌟</span>Python第三方库生态</h1>
                <h2><span class="emoji">📦</span>热门第三方库介绍</h2>
                <p>Python拥有丰富的第三方库生态系统，这些库极大地扩展了Python的功能。通过pip包管理器，我们可以轻松安装和使用这些库。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># 包管理 - pip使用</span>
<span class="comment"># 安装包</span>
<span class="comment"># pip install requests</span>
<span class="comment"># pip install pandas numpy matplotlib</span>
<span class="comment"># pip install flask django</span>

<span class="comment"># 查看已安装的包</span>
<span class="comment"># pip list</span>
<span class="comment"># pip show requests</span>

<span class="comment"># 升级包</span>
<span class="comment"># pip install --upgrade requests</span>

<span class="comment"># 卸载包</span>
<span class="comment"># pip uninstall requests</span>

<span class="comment"># 导出依赖列表</span>
<span class="comment"># pip freeze > requirements.txt</span>

<span class="comment"># 从依赖列表安装</span>
<span class="comment"># pip install -r requirements.txt</span>

<span class="comment"># requests - HTTP库</span>
<span class="keyword">import</span> requests
<span class="keyword">import</span> json

<span class="comment"># GET请求</span>
response = requests.get(<span class="string">'https://api.github.com/users/octocat'</span>)
<span class="function">print</span>(<span class="string">f"状态码: {response.status_code}"</span>)
<span class="function">print</span>(<span class="string">f"响应头: {response.headers['content-type']}"</span>)

<span class="keyword">if</span> response.status_code == <span class="number">200</span>:
    user_data = response.json()
    <span class="function">print</span>(<span class="string">f"用户名: {user_data['login']}"</span>)
    <span class="function">print</span>(<span class="string">f"公开仓库数: {user_data['public_repos']}"</span>)

<span class="comment"># POST请求</span>
api_url = <span class="string">'https://httpbin.org/post'</span>
data = {
    <span class="string">'name'</span>: <span class="string">'张三'</span>,
    <span class="string">'age'</span>: <span class="number">25</span>,
    <span class="string">'city'</span>: <span class="string">'北京'</span>
}

response = requests.post(api_url, json=data)
<span class="keyword">if</span> response.status_code == <span class="number">200</span>:
    result = response.json()
    <span class="function">print</span>(<span class="string">f"发送的数据: {result['json']}"</span>)

<span class="comment"># 带参数的请求</span>
params = {
    <span class="string">'q'</span>: <span class="string">'python'</span>,
    <span class="string">'sort'</span>: <span class="string">'stars'</span>,
    <span class="string">'order'</span>: <span class="string">'desc'</span>
}

response = requests.get(<span class="string">'https://api.github.com/search/repositories'</span>, params=params)
<span class="keyword">if</span> response.status_code == <span class="number">200</span>:
    search_result = response.json()
    <span class="function">print</span>(<span class="string">f"找到 {search_result['total_count']} 个仓库"</span>)
    
    <span class="comment"># 显示前3个仓库</span>
    <span class="keyword">for</span> i, repo <span class="keyword">in</span> <span class="function">enumerate</span>(search_result[<span class="string">'items'</span>][:<span class="number">3</span>]):
        <span class="function">print</span>(<span class="string">f"{i+1}. {repo['name']} - ⭐{repo['stargazers_count']}"</span>)

<span class="comment"># 会话和Cookie</span>
session = requests.Session()
session.headers.update({<span class="string">'User-Agent'</span>: <span class="string">'My Python App'</span>})

<span class="comment"># 超时和重试</span>
<span class="keyword">try</span>:
    response = requests.get(<span class="string">'https://httpbin.org/delay/2'</span>, timeout=<span class="number">5</span>)
    <span class="function">print</span>(<span class="string">f"请求成功: {response.status_code}"</span>)
<span class="keyword">except</span> requests.exceptions.Timeout:
    <span class="function">print</span>(<span class="string">"请求超时"</span>)
<span class="keyword">except</span> requests.exceptions.RequestException <span class="keyword">as</span> e:
    <span class="function">print</span>(<span class="string">f"请求错误: {e}"</span>)

<span class="comment"># pandas - 数据分析库</span>
<span class="keyword">import</span> pandas <span class="keyword">as</span> pd
<span class="keyword">import</span> numpy <span class="keyword">as</span> np

<span class="comment"># 创建DataFrame</span>
data = {
    <span class="string">'姓名'</span>: [<span class="string">'张三'</span>, <span class="string">'李四'</span>, <span class="string">'王五'</span>, <span class="string">'赵六'</span>, <span class="string">'钱七'</span>],
    <span class="string">'年龄'</span>: [<span class="number">25</span>, <span class="number">30</span>, <span class="number">35</span>, <span class="number">28</span>, <span class="number">32</span>],
    <span class="string">'城市'</span>: [<span class="string">'北京'</span>, <span class="string">'上海'</span>, <span class="string">'广州'</span>, <span class="string">'深圳'</span>, <span class="string">'杭州'</span>],
    <span class="string">'薪资'</span>: [<span class="number">8000</span>, <span class="number">12000</span>, <span class="number">15000</span>, <span class="number">10000</span>, <span class="number">13000</span>]
}

df = pd.DataFrame(data)
<span class="function">print</span>(<span class="string">"DataFrame:"</span>)
<span class="function">print</span>(df)

<span class="comment"># 基本信息</span>
<span class="function">print</span>(<span class="string">f"\n数据形状: {df.shape}"</span>)
<span class="function">print</span>(<span class="string">f"列名: {list(df.columns)}"</span>)
<span class="function">print</span>(<span class="string">"\n数据类型:"</span>)
<span class="function">print</span>(df.dtypes)

<span class="comment"># 统计信息</span>
<span class="function">print</span>(<span class="string">"\n数值列统计:"</span>)
<span class="function">print</span>(df.describe())

<span class="comment"># 数据筛选</span>
high_salary = df[df[<span class="string">'薪资'</span>] > <span class="number">10000</span>]
<span class="function">print</span>(<span class="string">"\n高薪人员:"</span>)
<span class="function">print</span>(high_salary)

<span class="comment"># 数据排序</span>
sorted_df = df.sort_values(<span class="string">'薪资'</span>, ascending=<span class="keyword">False</span>)
<span class="function">print</span>(<span class="string">"\n按薪资降序:"</span>)
<span class="function">print</span>(sorted_df)

<span class="comment"># 分组统计</span>
city_stats = df.groupby(<span class="string">'城市'</span>)[<span class="string">'薪资'</span>].agg([<span class="string">'mean'</span>, <span class="string">'count'</span>])
<span class="function">print</span>(<span class="string">"\n城市薪资统计:"</span>)
<span class="function">print</span>(city_stats)

<span class="comment"># 添加新列</span>
df[<span class="string">'薪资等级'</span>] = df[<span class="string">'薪资'</span>].apply(
    <span class="keyword">lambda</span> x: <span class="string">'高'</span> <span class="keyword">if</span> x > <span class="number">12000</span> <span class="keyword">else</span> (<span class="string">'中'</span> <span class="keyword">if</span> x > <span class="number">9000</span> <span class="keyword">else</span> <span class="string">'低'</span>)
)
<span class="function">print</span>(<span class="string">"\n添加薪资等级后:"</span>)
<span class="function">print</span>(df)

<span class="comment"># 保存到文件</span>
df.to_csv(<span class="string">'employee_data.csv'</span>, index=<span class="keyword">False</span>, encoding=<span class="string">'utf-8'</span>)
<span class="function">print</span>(<span class="string">"\n数据已保存到 employee_data.csv"</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># matplotlib - 数据可视化</span>
<span class="keyword">import</span> matplotlib.pyplot <span class="keyword">as</span> plt
<span class="keyword">import</span> matplotlib

<span class="comment"># 设置中文字体</span>
matplotlib.rcParams[<span class="string">'font.sans-serif'</span>] = [<span class="string">'SimHei'</span>]
matplotlib.rcParams[<span class="string">'axes.unicode_minus'</span>] = <span class="keyword">False</span>

<span class="comment"># 创建示例数据</span>
months = [<span class="string">'1月'</span>, <span class="string">'2月'</span>, <span class="string">'3月'</span>, <span class="string">'4月'</span>, <span class="string">'5月'</span>, <span class="string">'6月'</span>]
sales = [<span class="number">120</span>, <span class="number">135</span>, <span class="number">148</span>, <span class="number">162</span>, <span class="number">178</span>, <span class="number">195</span>]
profit = [<span class="number">20</span>, <span class="number">25</span>, <span class="number">30</span>, <span class="number">35</span>, <span class="number">42</span>, <span class="number">48</span>]

<span class="comment"># 创建子图</span>
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(<span class="number">2</span>, <span class="number">2</span>, figsize=(<span class="number">12</span>, <span class="number">10</span>))

<span class="comment"># 折线图</span>
ax1.plot(months, sales, marker=<span class="string">'o'</span>, linewidth=<span class="number">2</span>, label=<span class="string">'销售额'</span>)
ax1.plot(months, profit, marker=<span class="string">'s'</span>, linewidth=<span class="number">2</span>, label=<span class="string">'利润'</span>)
ax1.set_title(<span class="string">'月度销售趋势'</span>)
ax1.set_xlabel(<span class="string">'月份'</span>)
ax1.set_ylabel(<span class="string">'金额(万元)'</span>)
ax1.legend()
ax1.grid(<span class="keyword">True</span>, alpha=<span class="number">0.3</span>)

<span class="comment"># 柱状图</span>
colors = [<span class="string">'#FF6B6B'</span>, <span class="string">'#4ECDC4'</span>, <span class="string">'#45B7D1'</span>, <span class="string">'#96CEB4'</span>, <span class="string">'#FFEAA7'</span>, <span class="string">'#DDA0DD'</span>]
ax2.bar(months, sales, color=colors, alpha=<span class="number">0.8</span>)
ax2.set_title(<span class="string">'月度销售额'</span>)
ax2.set_xlabel(<span class="string">'月份'</span>)
ax2.set_ylabel(<span class="string">'销售额(万元)'</span>)

<span class="comment"># 饼图</span>
departments = [<span class="string">'研发部'</span>, <span class="string">'销售部'</span>, <span class="string">'市场部'</span>, <span class="string">'运营部'</span>]
employee_count = [<span class="number">25</span>, <span class="number">18</span>, <span class="number">12</span>, <span class="number">15</span>]
colors_pie = [<span class="string">'#FF9999'</span>, <span class="string">'#66B2FF'</span>, <span class="string">'#99FF99'</span>, <span class="string">'#FFCC99'</span>]

wedges, texts, autotexts = ax3.pie(employee_count, labels=departments, 
                                  colors=colors_pie, autopct=<span class="string">'%1.1f%%'</span>,
                                  startangle=<span class="number">90</span>, explode=(<span class="number">0.05</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>))
ax3.set_title(<span class="string">'部门人员分布'</span>)

<span class="comment"># 散点图</span>
np.random.seed(<span class="number">42</span>)
x = np.random.randn(<span class="number">50</span>)
y = <span class="number">2</span> * x + np.random.randn(<span class="number">50</span>)
colors_scatter = np.random.rand(<span class="number">50</span>)

scatter = ax4.scatter(x, y, c=colors_scatter, alpha=<span class="number">0.6</span>, cmap=<span class="string">'viridis'</span>)
ax4.set_title(<span class="string">'数据相关性分析'</span>)
ax4.set_xlabel(<span class="string">'X值'</span>)
ax4.set_ylabel(<span class="string">'Y值'</span>)
plt.colorbar(scatter, ax=ax4)

plt.tight_layout()
plt.savefig(<span class="string">'data_visualization.png'</span>, dpi=<span class="number">300</span>, bbox_inches=<span class="string">'tight'</span>)
<span class="function">print</span>(<span class="string">"图表已保存为 data_visualization.png"</span>)

<span class="comment"># Flask - Web框架</span>
<span class="keyword">from</span> flask <span class="keyword">import</span> Flask, request, jsonify, render_template_string

app = Flask(__name__)

<span class="comment"># 简单的HTML模板</span>
HTML_TEMPLATE = <span class="string">'''
<!DOCTYPE html>
<html>
<head>
    <title>Python Flask 示例</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin: 15px 0; }
        input, button { padding: 10px; margin: 5px; }
        button { background: #007bff; color: white; border: none; cursor: pointer; }
        .result { background: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐍 Python Flask 示例应用</h1>
        
        <h2>计算器</h2>
        <form action="/calculate" method="post">
            <div class="form-group">
                <input type="number" name="num1" placeholder="第一个数字" required>
                <select name="operation">
                    <option value="add">加法</option>
                    <option value="subtract">减法</option>
                    <option value="multiply">乘法</option>
                    <option value="divide">除法</option>
                </select>
                <input type="number" name="num2" placeholder="第二个数字" required>
            </div>
            <button type="submit">计算</button>
        </form>
        
        {% if result is defined %}
        <div class="result">
            <h3>计算结果: {{ result }}</h3>
        </div>
        {% endif %}
        
        <h2>API 测试</h2>
        <p>访问 <a href="/api/users">/api/users</a> 查看用户API</p>
        <p>访问 <a href="/api/status">/api/status</a> 查看状态API</p>
    </div>
</body>
</html>
'''</span>

<span class="comment"># 路由定义</span>
<span class="function">@app.route</span>(<span class="string">'/'</span>)
<span class="keyword">def</span> <span class="function">home</span>():
    <span class="keyword">return</span> render_template_string(HTML_TEMPLATE)

<span class="function">@app.route</span>(<span class="string">'/calculate'</span>, methods=[<span class="string">'POST'</span>])
<span class="keyword">def</span> <span class="function">calculate</span>():
    <span class="keyword">try</span>:
        num1 = <span class="function">float</span>(request.form[<span class="string">'num1'</span>])
        num2 = <span class="function">float</span>(request.form[<span class="string">'num2'</span>])
        operation = request.form[<span class="string">'operation'</span>]
        
        <span class="keyword">if</span> operation == <span class="string">'add'</span>:
            result = num1 + num2
        <span class="keyword">elif</span> operation == <span class="string">'subtract'</span>:
            result = num1 - num2
        <span class="keyword">elif</span> operation == <span class="string">'multiply'</span>:
            result = num1 * num2
        <span class="keyword">elif</span> operation == <span class="string">'divide'</span>:
            <span class="keyword">if</span> num2 == <span class="number">0</span>:
                result = <span class="string">"错误：除数不能为零"</span>
            <span class="keyword">else</span>:
                result = num1 / num2
        <span class="keyword">else</span>:
            result = <span class="string">"未知操作"</span>
        
        <span class="keyword">return</span> render_template_string(HTML_TEMPLATE, result=result)
    
    <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
        <span class="keyword">return</span> render_template_string(HTML_TEMPLATE, result=<span class="string">f"错误: {str(e)}"</span>)

<span class="function">@app.route</span>(<span class="string">'/api/users'</span>)
<span class="keyword">def</span> <span class="function">get_users</span>():
    users = [
        {<span class="string">'id'</span>: <span class="number">1</span>, <span class="string">'name'</span>: <span class="string">'张三'</span>, <span class="string">'email'</span>: <span class="string">'<EMAIL>'</span>},
        {<span class="string">'id'</span>: <span class="number">2</span>, <span class="string">'name'</span>: <span class="string">'李四'</span>, <span class="string">'email'</span>: <span class="string">'<EMAIL>'</span>},
        {<span class="string">'id'</span>: <span class="number">3</span>, <span class="string">'name'</span>: <span class="string">'王五'</span>, <span class="string">'email'</span>: <span class="string">'<EMAIL>'</span>}
    ]
    <span class="keyword">return</span> jsonify({<span class="string">'users'</span>: users, <span class="string">'total'</span>: <span class="function">len</span>(users)})

<span class="function">@app.route</span>(<span class="string">'/api/status'</span>)
<span class="keyword">def</span> <span class="function">get_status</span>():
    <span class="keyword">import</span> datetime
    <span class="keyword">return</span> jsonify({
        <span class="string">'status'</span>: <span class="string">'running'</span>,
        <span class="string">'timestamp'</span>: datetime.datetime.now().isoformat(),
        <span class="string">'version'</span>: <span class="string">'1.0.0'</span>
    })

<span class="function">@app.route</span>(<span class="string">'/api/user/<int:user_id>'</span>)
<span class="keyword">def</span> <span class="function">get_user</span>(user_id):
    <span class="comment"># 模拟用户数据</span>
    users = {
        <span class="number">1</span>: {<span class="string">'id'</span>: <span class="number">1</span>, <span class="string">'name'</span>: <span class="string">'张三'</span>, <span class="string">'age'</span>: <span class="number">25</span>},
        <span class="number">2</span>: {<span class="string">'id'</span>: <span class="number">2</span>, <span class="string">'name'</span>: <span class="string">'李四'</span>, <span class="string">'age'</span>: <span class="number">30</span>},
        <span class="number">3</span>: {<span class="string">'id'</span>: <span class="number">3</span>, <span class="string">'name'</span>: <span class="string">'王五'</span>, <span class="string">'age'</span>: <span class="number">28</span>}
    }
    
    user = users.get(user_id)
    <span class="keyword">if</span> user:
        <span class="keyword">return</span> jsonify(user)
    <span class="keyword">else</span>:
        <span class="keyword">return</span> jsonify({<span class="string">'error'</span>: <span class="string">'用户不存在'</span>}), <span class="number">404</span>

<span class="comment"># 启动应用</span>
<span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:
    <span class="function">print</span>(<span class="string">"Flask应用启动中..."</span>)
    <span class="function">print</span>(<span class="string">"访问 http://localhost:5000 查看应用"</span>)
    app.run(debug=<span class="keyword">True</span>, host=<span class="string">'0.0.0.0'</span>, port=<span class="number">5000</span>)</div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 其他热门库简介</span>

<span class="comment"># BeautifulSoup - HTML/XML解析</span>
<span class="keyword">from</span> bs4 <span class="keyword">import</span> BeautifulSoup
<span class="keyword">import</span> requests

html_content = <span class="string">'''
<html>
<head><title>示例页面</title></head>
<body>
    <div class="container">
        <h1>新闻标题</h1>
        <p class="content">这是新闻内容...</p>
        <a href="https://example.com">链接</a>
    </div>
</body>
</html>
'''</span>

soup = BeautifulSoup(html_content, <span class="string">'html.parser'</span>)
<span class="function">print</span>(<span class="string">f"标题: {soup.find('h1').text}"</span>)
<span class="function">print</span>(<span class="string">f"内容: {soup.find('p', class_='content').text}"</span>)
<span class="function">print</span>(<span class="string">f"链接: {soup.find('a')['href']}"</span>)

<span class="comment"># Pillow - 图像处理</span>
<span class="keyword">from</span> PIL <span class="keyword">import</span> Image, ImageDraw, ImageFont

<span class="comment"># 创建图像</span>
img = Image.new(<span class="string">'RGB'</span>, (<span class="number">400</span>, <span class="number">200</span>), color=<span class="string">'lightblue'</span>)
draw = ImageDraw.Draw(img)

<span class="comment"># 绘制文字</span>
draw.text((<span class="number">50</span>, <span class="number">80</span>), <span class="string">'Hello Python!'</span>, fill=<span class="string">'black'</span>)

<span class="comment"># 绘制形状</span>
draw.rectangle([<span class="number">20</span>, <span class="number">20</span>, <span class="number">120</span>, <span class="number">60</span>], outline=<span class="string">'red'</span>, width=<span class="number">2</span>)
draw.circle([<span class="number">300</span>, <span class="number">100</span>], <span class="number">30</span>, fill=<span class="string">'yellow'</span>, outline=<span class="string">'orange'</span>)

img.save(<span class="string">'sample_image.png'</span>)
<span class="function">print</span>(<span class="string">"图像已保存为 sample_image.png"</span>)

<span class="comment"># SQLAlchemy - 数据库ORM</span>
<span class="keyword">from</span> sqlalchemy <span class="keyword">import</span> create_engine, Column, Integer, String
<span class="keyword">from</span> sqlalchemy.ext.declarative <span class="keyword">import</span> declarative_base
<span class="keyword">from</span> sqlalchemy.orm <span class="keyword">import</span> sessionmaker

Base = declarative_base()

<span class="keyword">class</span> <span class="function">User</span>(Base):
    __tablename__ = <span class="string">'users'</span>
    
    id = Column(Integer, primary_key=<span class="keyword">True</span>)
    name = Column(String(<span class="number">50</span>))
    email = Column(String(<span class="number">100</span>))
    
    <span class="keyword">def</span> <span class="function">__repr__</span>(<span class="keyword">self</span>):
        <span class="keyword">return</span> <span class="string">f"<User(name='{self.name}', email='{self.email}')>"</span>

<span class="comment"># 创建内存数据库</span>
engine = create_engine(<span class="string">'sqlite:///:memory:'</span>, echo=<span class="keyword">False</span>)
Base.metadata.create_all(engine)

Session = sessionmaker(bind=engine)
session = Session()

<span class="comment"># 添加用户</span>
user1 = User(name=<span class="string">'张三'</span>, email=<span class="string">'<EMAIL>'</span>)
user2 = User(name=<span class="string">'李四'</span>, email=<span class="string">'<EMAIL>'</span>)

session.add_all([user1, user2])
session.commit()

<span class="comment"># 查询用户</span>
users = session.query(User).all()
<span class="function">print</span>(<span class="string">"数据库中的用户:"</span>)
<span class="keyword">for</span> user <span class="keyword">in</span> users:
    <span class="function">print</span>(<span class="string">f"ID: {user.id}, 姓名: {user.name}, 邮箱: {user.email}"</span>)

session.close()

<span class="comment"># 虚拟环境管理</span>
<span class="function">print</span>(<span class="string">"\n=== 虚拟环境管理 ==="</span>)
<span class="function">print</span>(<span class="string">"创建虚拟环境: python -m venv myenv"</span>)
<span class="function">print</span>(<span class="string">"激活虚拟环境 (Windows): myenv\\Scripts\\activate"</span>)
<span class="function">print</span>(<span class="string">"激活虚拟环境 (Mac/Linux): source myenv/bin/activate"</span>)
<span class="function">print</span>(<span class="string">"退出虚拟环境: deactivate"</span>)

<span class="comment"># 项目结构示例</span>
project_structure = <span class="string">'''
项目结构示例:
my_project/
├── requirements.txt      # 依赖列表
├── README.md            # 项目说明
├── .gitignore          # Git忽略文件
├── config.py           # 配置文件
├── main.py             # 主程序
├── tests/              # 测试目录
│   ├── __init__.py
│   └── test_main.py
├── src/                # 源代码目录
│   ├── __init__.py
│   ├── models.py       # 数据模型
│   ├── views.py        # 视图函数
│   └── utils.py        # 工具函数
└── static/             # 静态文件
    ├── css/
    ├── js/
    └── images/
'''</span>

<span class="function">print</span>(<span class="string">"\n=== 推荐的项目结构 ==="</span>)
<span class="function">print</span>(project_structure)

<span class="comment"># 常用第三方库列表</span>
libraries = {
    <span class="string">'Web开发'</span>: [<span class="string">'Flask'</span>, <span class="string">'Django'</span>, <span class="string">'FastAPI'</span>, <span class="string">'Tornado'</span>],
    <span class="string">'数据科学'</span>: [<span class="string">'pandas'</span>, <span class="string">'numpy'</span>, <span class="string">'scipy'</span>, <span class="string">'scikit-learn'</span>],
    <span class="string">'可视化'</span>: [<span class="string">'matplotlib'</span>, <span class="string">'seaborn'</span>, <span class="string">'plotly'</span>, <span class="string">'bokeh'</span>],
    <span class="string">'网络请求'</span>: [<span class="string">'requests'</span>, <span class="string">'httpx'</span>, <span class="string">'aiohttp'</span>],
    <span class="string">'数据库'</span>: [<span class="string">'SQLAlchemy'</span>, <span class="string">'pymongo'</span>, <span class="string">'redis-py'</span>],
    <span class="string">'测试'</span>: [<span class="string">'pytest'</span>, <span class="string">'unittest'</span>, <span class="string">'mock'</span>],
    <span class="string">'图像处理'</span>: [<span class="string">'Pillow'</span>, <span class="string">'opencv-python'</span>, <span class="string">'imageio'</span>],
    <span class="string">'爬虫'</span>: [<span class="string">'BeautifulSoup4'</span>, <span class="string">'Scrapy'</span>, <span class="string">'Selenium'</span>],
    <span class="string">'机器学习'</span>: [<span class="string">'tensorflow'</span>, <span class="string">'pytorch'</span>, <span class="string">'keras'</span>],
    <span class="string">'异步编程'</span>: [<span class="string">'asyncio'</span>, <span class="string">'aiofiles'</span>, <span class="string">'uvloop'</span>]
}

<span class="function">print</span>(<span class="string">"\n=== 热门第三方库分类 ==="</span>)
<span class="keyword">for</span> category, libs <span class="keyword">in</span> libraries.items():
    <span class="function">print</span>(<span class="string">f"{category}: {', '.join(libs)}"</span>)</div>
                </div>

                <div class="warning-box">
                    <h3><span class="emoji">⚠️</span>第三方库使用要点</h3>
                    <ul>
                        <li><strong>虚拟环境：</strong>为每个项目创建独立的虚拟环境，避免依赖冲突</li>
                        <li><strong>版本管理：</strong>在requirements.txt中固定版本号，确保环境一致性</li>
                        <li><strong>安全考虑：</strong>只从可信源安装包，定期更新依赖以修复安全漏洞</li>
                        <li><strong>文档阅读：</strong>仔细阅读库的官方文档和示例代码</li>
                        <li><strong>性能测试：</strong>在生产环境前进行充分的性能和稳定性测试</li>
                        <li><strong>许可证：</strong>了解第三方库的许可证，确保符合项目要求</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习Python第三方库：</strong>"请详细介绍Python生态系统中的热门第三方库，包括requests、pandas、matplotlib、Flask等，解释它们的主要功能、安装方法、基本用法和实际应用场景。请提供完整的代码示例和最佳实践建议。"</p>
                </div>
            </div>
        </div>

        <!-- 第28页：Python最佳实践与编程规范 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">✨</span>Python最佳实践与编程规范</h1>
                <h2><span class="emoji">📋</span>代码质量与规范</h2>
                <p>良好的编程习惯和代码规范是成为优秀Python开发者的关键。遵循最佳实践可以让代码更易读、维护和协作。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># PEP 8 编码规范示例</span>

<span class="comment"># 1. 导入规范</span>
<span class="comment"># 标准库导入</span>
<span class="keyword">import</span> os
<span class="keyword">import</span> sys
<span class="keyword">from</span> datetime <span class="keyword">import</span> datetime, timedelta

<span class="comment"># 第三方库导入</span>
<span class="keyword">import</span> requests
<span class="keyword">import</span> pandas <span class="keyword">as</span> pd

<span class="comment"># 本地应用导入</span>
<span class="keyword">from</span> myapp.models <span class="keyword">import</span> User
<span class="keyword">from</span> myapp.utils <span class="keyword">import</span> helper_function

<span class="comment"># 2. 命名规范</span>
<span class="comment"># 常量：全大写，下划线分隔</span>
MAX_CONNECTIONS = 100
DEFAULT_TIMEOUT = 30

<span class="comment"># 变量和函数：小写，下划线分隔</span>
user_name = "张三"
total_count = 0

<span class="keyword">def</span> <span class="function">calculate_total_price</span>(items, tax_rate=<span class="number">0.1</span>):
    <span class="string">"""计算商品总价格（含税）
    
    Args:
        items (list): 商品列表，每个元素包含price字段
        tax_rate (float): 税率，默认10%
    
    Returns:
        float: 含税总价格
    
    Raises:
        ValueError: 当税率为负数时抛出异常
    """\span>
    <span class="keyword">if</span> tax_rate < <span class="number">0</span>:
        <span class="keyword">raise</span> <span class="function">ValueError</span>(<span class="string">"税率不能为负数"</span>)
    
    subtotal = <span class="function">sum</span>(item.get(<span class="string">"price", <span class="number">0</span>) <span class="keyword">for</span> item <span class="keyword">in</span> items)
    <span class="keyword">return</span> subtotal * (<span class="number">1</span> + tax_rate)

<span class="comment"># 类名：驼峰命名法</span>
<span class="keyword">class</span> <span class="function">ShoppingCart</span>:
    <span class="string">"""购物车类
    
    管理用户的购物车商品，提供添加、删除、计算总价等功能。
    """\span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="keyword">self</span>, user_id):
        <span class="keyword">self</span>.user_id = user_id
        <span class="keyword">self</span>._items = []  <span class="comment"># 私有属性用下划线开头</span>
        <span class="keyword">self</span>.__total_value = <span class="number">0</span>  <span class="comment"># 强私有属性用双下划线</span>
    
    <span class="keyword">def</span> <span class="function">add_item</span>(<span class="keyword">self</span>, item):
        <span class="string">"""添加商品到购物车"""\span>
        <span class="keyword">if</span> <span class="keyword">not</span> <span class="function">isinstance</span>(item, <span class="function">dict</span>) <span class="keyword">or</span> <span class="string">"price"</span> <span class="keyword">not</span> <span class="keyword">in</span> item:
            <span class="keyword">raise</span> <span class="function">ValueError</span>(<span class="string">"商品必须包含price字段"</span>)
        
        <span class="keyword">self</span>._items.append(item)
        <span class="keyword">self</span>._update_total()
    
    <span class="keyword">def</span> <span class="function">_update_total</span>(<span class="keyword">self</span>):
        <span class="string">"""更新购物车总价（私有方法）"""\span>
        <span class="keyword">self</span>.__total_value = <span class="function">sum</span>(
            item.get(<span class="string">"price", <span class="number">0</span>) * item.get(<span class="string">"quantity", <span class="number">1</span>)
            <span class="keyword">for</span> item <span class="keyword">in</span> <span class="keyword">self</span>._items
        )
    
    <span class="function">@property</span>
    <span class="keyword">def</span> <span class="function">total_value</span>(<span class="keyword">self</span>):
        <span class="string">"""获取购物车总价"""\span>
        <span class="keyword">return</span> <span class="keyword">self</span>.__total_value
    
    <span class="function">@property</span>
    <span class="keyword">def</span> <span class="function">item_count</span>(<span class="keyword">self</span>):
        <span class="string">"""获取商品数量"""\span>
        <span class="keyword">return</span> <span class="function">len</span>(<span class="keyword">self</span>._items)
    
    <span class="keyword">def</span> <span class="function">__str__</span>(<span class="keyword">self</span>):
        <span class="keyword">return</span> <span class="string">f"购物车(用户:{self.user_id}, 商品:{self.item_count}, 总价:{self.total_value})"</span>

<span class="comment"># 3. 代码格式化</span>
<span class="comment"># 行长度不超过79字符</span>
<span class="comment"># 使用4个空格缩进</span>
<span class="comment"># 运算符前后加空格</span>
<span class="comment"># 逗号后加空格</span>

<span class="comment"># 好的例子</span>
result = (first_value + second_value) * multiplier
my_list = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>]
my_dict = {<span class="string">"name"</span>: <span class="string">"张三"</span>, <span class="string">"age"</span>: <span class="number">25</span>, <span class="string">"city"</span>: <span class="string">"北京"</span>}

<span class="comment"># 长行的处理</span>
very_long_variable_name = some_function_with_long_name(
    first_parameter,
    second_parameter,
    third_parameter,
    fourth_parameter
)

<span class="comment"># 4. 异常处理最佳实践</span>
<span class="keyword">def</span> <span class="function">safe_divide</span>(a, b):
    <span class="string">"""安全除法函数"""\span>
    <span class="keyword">try</span>:
        result = a / b
    <span class="keyword">except</span> <span class="function">ZeroDivisionError</span>:
        <span class="function">print</span>(<span class="string">"错误：除数不能为零"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
    <span class="keyword">except</span> <span class="function">TypeError</span>:
        <span class="function">print</span>(<span class="string">"错误：参数类型不正确"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
    <span class="keyword">else</span>:
        <span class="keyword">return</span> result
    <span class="keyword">finally</span>:
        <span class="function">print</span>(<span class="string">"除法操作完成"</span>)

<span class="comment"># 5. 使用上下文管理器</span>
<span class="keyword">def</span> <span class="function">process_file</span>(filename):
    <span class="string">"""处理文件的安全方式"""\span>
    <span class="keyword">try</span>:
        <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
            content = file.read()
            <span class="comment"># 处理文件内容</span>
            processed_content = content.upper()
            <span class="keyword">return</span> processed_content
    <span class="keyword">except</span> <span class="function">FileNotFoundError</span>:
        <span class="function">print</span>(<span class="string">f"文件 {filename} 不存在"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
    <span class="keyword">except</span> <span class="function">IOError</span>:
        <span class="function">print</span>(<span class="string">f"读取文件 {filename} 时发生错误"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span></div>
                </div>

                <div class="code-container">
                    <div class="code"><span class="comment"># 6. 性能优化技巧</span>

<span class="comment"># 使用列表推导式而不是循环</span>
<span class="comment"># 慢的方式</span>
squares_slow = []
<span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1000</span>):
    squares_slow.append(i ** <span class="number">2</span>)

<span class="comment"># 快的方式</span>
squares_fast = [i ** <span class="number">2</span> <span class="keyword">for</span> i <span class="keyword">in</span> <span class="function">range</span>(<span class="number">1000</span>)]

<span class="comment"># 使用生成器处理大数据</span>
<span class="keyword">def</span> <span class="function">process_large_dataset</span>(data):
    <span class="string">"""使用生成器处理大数据集"""\span>
    <span class="keyword">for</span> item <span class="keyword">in</span> data:
        <span class="keyword">if</span> item > <span class="number">100</span>:
            <span class="keyword">yield</span> item * <span class="number">2</span>

<span class="comment"># 使用字典而不是多个if-elif</span>
<span class="comment"># 慢的方式</span>
<span class="keyword">def</span> <span class="function">get_grade_slow</span>(score):
    <span class="keyword">if</span> score >= <span class="number">90</span>:
        <span class="keyword">return</span> <span class="string">'A'</span>
    <span class="keyword">elif</span> score >= <span class="number">80</span>:
        <span class="keyword">return</span> <span class="string">'B'</span>
    <span class="keyword">elif</span> score >= <span class="number">70</span>:
        <span class="keyword">return</span> <span class="string">'C'</span>
    <span class="keyword">elif</span> score >= <span class="number">60</span>:
        <span class="keyword">return</span> <span class="string">'D'</span>
    <span class="keyword">else</span>:
        <span class="keyword">return</span> <span class="string">'F'</span>

<span class="comment"># 快的方式（对于复杂映射）</span>
GRADE_MAPPING = {
    <span class="function">range</span>(<span class="number">90</span>, <span class="number">101</span>): <span class="string">'A'</span>,
    <span class="function">range</span>(<span class="number">80</span>, <span class="number">90</span>): <span class="string">'B'</span>,
    <span class="function">range</span>(<span class="number">70</span>, <span class="number">80</span>): <span class="string">'C'</span>,
    <span class="function">range</span>(<span class="number">60</span>, <span class="number">70</span>): <span class="string">'D'</span>
}

<span class="keyword">def</span> <span class="function">get_grade_fast</span>(score):
    <span class="keyword">for</span> score_range, grade <span class="keyword">in</span> GRADE_MAPPING.items():
        <span class="keyword">if</span> score <span class="keyword">in</span> score_range:
            <span class="keyword">return</span> grade
    <span class="keyword">return</span> <span class="string">'F'</span>

<span class="comment"># 7. 代码测试示例</span>
<span class="keyword">import</span> unittest
<span class="keyword">from</span> unittest.mock <span class="keyword">import</span> patch, MagicMock

<span class="keyword">class</span> <span class="function">TestShoppingCart</span>(unittest.TestCase):
    <span class="string">"""购物车测试类"""\span>
    
    <span class="keyword">def</span> <span class="function">setUp</span>(<span class="keyword">self</span>):
        <span class="string">"""测试前的准备工作"""\span>
        <span class="keyword">self</span>.cart = ShoppingCart(<span class="string">"user123"</span>)
        <span class="keyword">self</span>.sample_item = {
            <span class="string">"name"</span>: <span class="string">"苹果"</span>,
            <span class="string">"price"</span>: <span class="number">5.0</span>,
            <span class="string">"quantity"</span>: <span class="number">2</span>
        }
    
    <span class="keyword">def</span> <span class="function">test_add_item</span>(<span class="keyword">self</span>):
        <span class="string">"""测试添加商品功能"""\span>
        <span class="keyword">self</span>.cart.add_item(<span class="keyword">self</span>.sample_item)
        <span class="keyword">self</span>.assertEqual(<span class="keyword">self</span>.cart.item_count, <span class="number">1</span>)
        <span class="keyword">self</span>.assertEqual(<span class="keyword">self</span>.cart.total_value, <span class="number">10.0</span>)
    
    <span class="keyword">def</span> <span class="function">test_add_invalid_item</span>(<span class="keyword">self</span>):
        <span class="string">"""测试添加无效商品"""\span>
        <span class="keyword">with</span> <span class="keyword">self</span>.assertRaises(<span class="function">ValueError</span>):
            <span class="keyword">self</span>.cart.add_item({<span class="string">"name"</span>: <span class="string">"无效商品"</span>})
    
    <span class="keyword">def</span> <span class="function">test_calculate_total_price</span>(<span class="keyword">self</span>):
        <span class="string">"""测试总价计算"""\span>
        items = [
            {<span class="string">"price"</span>: <span class="number">100</span>},
            {<span class="string">"price"</span>: <span class="number">200</span>}
        ]
        total = calculate_total_price(items, tax_rate=<span class="number">0.1</span>)
        <span class="keyword">self</span>.assertEqual(total, <span class="number">330.0</span>)  <span class="comment"># 300 * 1.1</span>
    
    <span class="function">@patch</span>(<span class="string">'builtins.print'</span>)
    <span class="keyword">def</span> <span class="function">test_safe_divide</span>(<span class="keyword">self</span>, mock_print):
        <span class="string">"""测试安全除法"""\span>
        result = safe_divide(<span class="number">10</span>, <span class="number">2</span>)
        <span class="keyword">self</span>.assertEqual(result, <span class="number">5.0</span>)
        
        result = safe_divide(<span class="number">10</span>, <span class="number">0</span>)
        <span class="keyword">self</span>.assertIsNone(result)
        mock_print.assert_called()

<span class="comment"># 运行测试</span>
<span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:
    <span class="comment"># 创建测试套件</span>
    suite = unittest.TestLoader().loadTestsFromTestCase(TestShoppingCart)
    runner = unittest.TextTestRunner(verbosity=<span class="number">2</span>)
    result = runner.run(suite)
    
    <span class="function">print</span>(<span class="string">f"\n测试结果: 运行 {result.testsRun} 个测试"</span>)
    <span class="function">print</span>(<span class="string">f"失败: {len(result.failures)} 个"</span>)
    <span class="function">print</span>(<span class="string">f"错误: {len(result.errors)} 个"</span>)

<span class="comment"># 8. 日志记录最佳实践</span>
<span class="keyword">import</span> logging
<span class="keyword">from</span> datetime <span class="keyword">import</span> datetime

<span class="comment"># 配置日志</span>
logging.basicConfig(
    level=logging.INFO,
    format=<span class="string">'%(asctime)s - %(name)s - %(levelname)s - %(message)s'</span>,
    handlers=[
        logging.FileHandler(<span class="string">'app.log'</span>, encoding=<span class="string">'utf-8'</span>),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

<span class="keyword">def</span> <span class="function">process_user_data</span>(user_data):
    <span class="string">"""处理用户数据的示例函数"""\span>
    logger.info(<span class="string">f"开始处理用户数据: {user_data.get('id', 'unknown')}"</span>)
    
    <span class="keyword">try</span>:
        <span class="comment"># 验证数据</span>
        <span class="keyword">if</span> <span class="keyword">not</span> user_data.get(<span class="string">'email'</span>):
            logger.warning(<span class="string">"用户数据缺少邮箱信息"</span>)
        
        <span class="comment"># 处理数据</span>
        processed_data = {
            <span class="string">'id'</span>: user_data.get(<span class="string">'id'</span>),
            <span class="string">'name'</span>: user_data.get(<span class="string">'name'</span>, <span class="string">'').strip(),
            <span class="string">'email'</span>: user_data.get(<span class="string">'email'</span>, <span class="string">''</span>).lower(),
            <span class="string">'processed_at'</span>: datetime.now().isoformat()
        }
        
        logger.info(<span class="string">f"用户数据处理完成: {processed_data['id']}"</span>)
        <span class="keyword">return</span> processed_data
        
    <span class="keyword">except</span> <span class="function">Exception</span> <span class="keyword">as</span> e:
        logger.error(<span class="string">f"处理用户数据时发生错误: {e}"</span>, exc_info=<span class="keyword">True</span>)
        <span class="keyword">raise</span>

<span class="comment"># 9. 配置管理</span>
<span class="keyword">import</span> os
<span class="keyword">from</span> dataclasses <span class="keyword">import</span> dataclass

<span class="function">@dataclass</span>
<span class="keyword">class</span> <span class="function">AppConfig</span>:
    <span class="string">"""应用配置类"""\span>
    debug: <span class="function">bool</span> = <span class="keyword">False</span>
    database_url: <span class="function">str</span> = <span class="string">"sqlite:///app.db"</span>
    secret_key: <span class="function">str</span> = <span class="string">"your-secret-key"</span>
    max_connections: <span class="function">int</span> = <span class="number">100</span>
    
    <span class="function">@classmethod</span>
    <span class="keyword">def</span> <span class="function">from_env</span>(<span class="function">cls</span>):
        <span class="string">"""从环境变量创建配置"""\span>
        <span class="keyword">return</span> <span class="function">cls</span>(
            debug=os.getenv(<span class="string">'DEBUG', <span class="string">'False'</span>).lower() == <span class="string">'true'</span>,
            database_url=os.getenv(<span class="string">'DATABASE_URL', <span class="string">'sqlite:///app.db'</span>),
            secret_key=os.getenv(<span class="string">'SECRET_KEY', <span class="string">'your-secret-key'</span>),
            max_connections=<span class="function">int</span>(os.getenv(<span class="string">'MAX_CONNECTIONS', <span class="string">'100'</span>))
        )

<span class="comment"># 使用配置</span>
config = AppConfig.from_env()
<span class="function">print</span>(<span class="string">f"应用配置: Debug={config.debug}, 数据库={config.database_url}"</span>)</div>
                </div>

                <div class="warning-box">
                    <h3><span class="emoji">📝</span>编程最佳实践要点</h3>
                    <ul>
                        <li><strong>代码可读性：</strong>代码是写给人看的，机器只是恰好能执行</li>
                        <li><strong>DRY原则：</strong>Don't Repeat Yourself，避免重复代码</li>
                        <li><strong>SOLID原则：</strong>单一职责、开闭原则、里氏替换、接口隔离、依赖倒置</li>
                        <li><strong>测试驱动：</strong>编写测试用例，确保代码质量和功能正确性</li>
                        <li><strong>文档注释：</strong>为函数、类和模块编写清晰的文档字符串</li>
                        <li><strong>版本控制：</strong>使用Git进行代码版本管理和协作开发</li>
                        <li><strong>持续学习：</strong>关注Python社区动态，学习新特性和最佳实践</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>学习Python最佳实践：</strong>"请详细介绍Python编程的最佳实践和代码规范，包括PEP 8编码规范、命名约定、代码结构、异常处理、测试方法、性能优化技巧等。请提供具体的代码示例和实际应用建议。"</p>
                </div>
            </div>
        </div>

        <!-- 第29页：总结与学习路径 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">🎯</span>Python学习总结</h1>
                <h2><span class="emoji">🗺️</span>知识体系与学习路径</h2>
                <p>通过本PPT的学习，我们系统地掌握了Python的核心概念和实用技能。让我们回顾所学内容并规划进一步的学习路径。</p>

                <div class="code-container">
                    <div class="code"><span class="comment"># 本PPT涵盖的知识点总结</span>

<span class="comment"># 1. 基础语法与数据类型</span>
knowledge_map = {
    <span class="string">"基础语法"</span>: [
        <span class="string">"变量与赋值"</span>,
        <span class="string">"数据类型(int, float, str, bool)"</span>,
        <span class="string">"运算符与表达式"</span>,
        <span class="string">"输入输出函数"</span>
    ],
    
    <span class="string">"流程控制"</span>: [
        <span class="string">"条件语句(if-elif-else)"</span>,
        <span class="string">"循环语句(for, while)"</span>,
        <span class="string">"循环控制(break, continue)"</span>,
        <span class="string">"嵌套结构"</span>
    ],
    
    <span class="string">"数据结构"</span>: [
        <span class="string">"列表(list)操作与方法"</span>,
        <span class="string">"元组(tuple)特性"</span>,
        <span class="string">"字典(dict)键值对操作"</span>,
        <span class="string">"集合(set)去重与运算"</span>
    ],
    
    <span class="string">"字符串处理"</span>: [
        <span class="string">"字符串方法与格式化"</span>,
        <span class="string">"正则表达式应用"</span>,
        <span class="string">"文本处理技巧"</span>
    ],
    
    <span class="string">"函数编程"</span>: [
        <span class="string">"函数定义与调用"</span>,
        <span class="string">"参数类型与作用域"</span>,
        <span class="string">"递归函数"</span>,
        <span class="string">"Lambda表达式"</span>,
        <span class="string">"装饰器模式"</span>
    ],
    
    <span class="string">"高级特性"</span>: [
        <span class="string">"列表推导式"</span>,
        <span class="string">"生成器表达式"</span>,
        <span class="string">"上下文管理器"</span>,
        <span class="string">"异常处理机制"</span>
    ],
    
    <span class="string">"面向对象"</span>: [
        <span class="string">"类与对象"</span>,
        <span class="string">"继承与多态"</span>,
        <span class="string">"特殊方法"</span>,
        <span class="string">"属性管理"</span>
    ],
    
    <span class="string">"模块系统"</span>: [
        <span class="string">"模块导入与使用"</span>,
        <span class="string">"包的组织结构"</span>,
        <span class="string">"标准库应用"</span>,
        <span class="string">"第三方库生态"</span>
    ],
    
    <span class="string">"文件操作"</span>: [
        <span class="string">"文件读写操作"</span>,
        <span class="string">"路径处理"</span>,
        <span class="string">"CSV/JSON处理"</span>,
        <span class="string">"文件系统操作"</span>
    ],
    
    <span class="string">"实用技能"</span>: [
        <span class="string">"数据结构与算法"</span>,
        <span class="string">"代码调试技巧"</span>,
        <span class="string">"性能优化"</span>,
        <span class="string">"最佳实践"</span>
    ]
}

<span class="comment"># 打印知识体系</span>
<span class="function">print</span>(<span class="string">"🐍 Python知识体系总览"</span>)
<span class="function">print</span>(<span class="string">"=" * 50</span>)

<span class="keyword">for</span> category, topics <span class="keyword">in</span> knowledge_map.items():
    <span class="function">print</span>(<span class="string">f"\n📚 {category}:"</span>)
    <span class="keyword">for</span> i, topic <span class="keyword">in</span> <span class="function">enumerate</span>(topics, <span class="number">1</span>):
        <span class="function">print</span>(<span class="string">f"   {i}. {topic}"</span>)

<span class="comment"># 学习路径建议</span>
learning_paths = {
    <span class="string">"初学者路径"</span>: {
        <span class="string">"阶段1"</span>: [<span class="string">"基础语法"</span>, <span class="string">"数据类型"</span>, <span class="string">"流程控制"</span>],
        <span class="string">"阶段2"</span>: [<span class="string">"数据结构"</span>, <span class="string">"函数编程"</span>, <span class="string">"文件操作"</span>],
        <span class="string">"阶段3"</span>: [<span class="string">"面向对象"</span>, <span class="string">"异常处理"</span>, <span class="string">"模块系统"</span>],
        <span class="string">"阶段4"</span>: [<span class="string">"高级特性"</span>, <span class="string">"标准库"</span>, <span class="string">"第三方库"</span>]
    },
    
    <span class="string">"Web开发路径"</span>: {
        <span class="string">"基础"</span>: [<span class="string">"Python基础"</span>, <span class="string">"HTTP协议"</span>, <span class="string">"HTML/CSS"</span>],
        <span class="string">"框架"</span>: [<span class="string">"Flask入门"</span>, <span class="string">"Django框架"</span>, <span class="string">"FastAPI"</span>],
        <span class="string">"数据库"</span>: [<span class="string">"SQL基础"</span>, <span class="string">"SQLAlchemy"</span>, <span class="string">"Redis"</span>],
        <span class="string">"部署"</span>: [<span class="string">"Docker"</span>, <span class="string">"云服务"</span>, <span class="string">"CI/CD"</span>]
    },
    
    <span class="string">"数据科学路径"</span>: {
        <span class="string">"数据处理"</span>: [<span class="string">"NumPy"</span>, <span class="string">"Pandas"</span>, <span class="string">"数据清洗"</span>],
        <span class="string">"可视化"</span>: [<span class="string">"Matplotlib"</span>, <span class="string">"Seaborn"</span>, <span class="string">"Plotly"</span>],
        <span class="string">"机器学习"</span>: [<span class="string">"Scikit-learn"</span>, <span class="string">"统计学"</span>, <span class="string">"算法"</span>],
        <span class="string">"深度学习"</span>: [<span class="string">"TensorFlow"</span>, <span class="string">"PyTorch"</span>, <span class="string">"神经网络"</span>]
    },
    
    <span class="string">"自动化路径"</span>: {
        <span class="string">"脚本编程"</span>: [<span class="string">"文件操作"</span>, <span class="string">"系统调用"</span>, <span class="string">"定时任务"</span>],
        <span class="string">"网络爬虫"</span>: [<span class="string">"Requests"</span>, <span class="string">"BeautifulSoup"</span>, <span class="string">"Scrapy"</span>],
        <span class="string">"测试自动化"</span>: [<span class="string">"Selenium"</span>, <span class="string">"Pytest"</span>, <span class="string">"Mock"</span>],
        <span class="string">"运维自动化"</span>: [<span class="string">"Ansible"</span>, <span class="string">"Docker"</span>, <span class="string">"监控"</span>]
    }
}

<span class="function">print</span>(<span class="string">"\n\n🎯 推荐学习路径"</span>)
<span class="function">print</span>(<span class="string">"=" * 50</span>)

<span class="keyword">for</span> path_name, stages <span class="keyword">in</span> learning_paths.items():
    <span class="function">print</span>(<span class="string">f"\n🚀 {path_name}:"</span>)
    <span class="keyword">for</span> stage, topics <span class="keyword">in</span> stages.items():
        <span class="function">print</span>(<span class="string">f"   📖 {stage}: {' → '.join(topics)}"</span>)

<span class="comment"># 实践项目建议</span>
project_suggestions = {
    <span class="string">"初级项目"</span>: [
        <span class="string">"计算器程序"</span>,
        <span class="string">"猜数字游戏"</span>,
        <span class="string">"待办事项管理"</span>,
        <span class="string">"简单文本处理工具"</span>
    ],
    
    <span class="string">"中级项目"</span>: [
        <span class="string">"个人博客系统"</span>,
        <span class="string">"网络爬虫工具"</span>,
        <span class="string">"数据可视化仪表板"</span>,
        <span class="string">"API接口开发"</span>
    ],
    
    <span class="string">"高级项目"</span>: [
        <span class="string">"电商网站"</span>,
        <span class="string">"机器学习模型"</span>,
        <span class="string">"分布式系统"</span>,
        <span class="string">"开源项目贡献"</span>
    ]
}

<span class="function">print</span>(<span class="string">"\n\n💡 实践项目建议"</span>)
<span class="function">print</span>(<span class="string">"=" * 50</span>)

<span class="keyword">for</span> level, projects <span class="keyword">in</span> project_suggestions.items():
    <span class="function">print</span>(<span class="string">f"\n🔨 {level}:"</span>)
    <span class="keyword">for</span> i, project <span class="keyword">in</span> <span class="function">enumerate</span>(projects, <span class="number">1</span>):
        <span class="function">print</span>(<span class="string">f"   {i}. {project}"</span>)

<span class="comment"># 学习资源推荐</span>
resources = {
    <span class="string">"官方文档"</span>: [
        <span class="string">"Python.org 官方文档"</span>,
        <span class="string">"PEP (Python Enhancement Proposals)"</span>,
        <span class="string">"Python标准库文档"</span>
    ],
    
    <span class="string">"在线教程"</span>: [
        <span class="string">"Real Python"</span>,
        <span class="string">"Python.org Tutorial"</span>,
        <span class="string">"Automate the Boring Stuff"</span>
    ],
    
    <span class="string">"实践平台"</span>: [
        <span class="string">"LeetCode (算法练习)"</span>,
        <span class="string">"HackerRank (编程挑战)"</span>,
        <span class="string">"GitHub (开源项目)"</span>
    ],
    
    <span class="string">"社区资源"</span>: [
        <span class="string">"Stack Overflow"</span>,
        <span class="string">"Reddit r/Python"</span>,
        <span class="string">"Python中文社区"</span>
    ]
}

<span class="function">print</span>(<span class="string">"\n\n📚 学习资源推荐"</span>)
<span class="function">print</span>(<span class="string">"=" * 50</span>)

<span class="keyword">for</span> category, items <span class="keyword">in</span> resources.items():
    <span class="function">print</span>(<span class="string">f"\n📖 {category}:"</span>)
    <span class="keyword">for</span> item <span class="keyword">in</span> items:
        <span class="function">print</span>(<span class="string">f"   • {item}"</span>)</div>
                </div>

                <div class="warning-box">
                    <h3><span class="emoji">🎓</span>持续学习建议</h3>
                    <ul>
                        <li><strong>每日编程：</strong>保持每天至少1小时的编程练习</li>
                        <li><strong>项目驱动：</strong>通过实际项目来巩固和应用所学知识</li>
                        <li><strong>代码阅读：</strong>阅读优秀的开源项目代码，学习最佳实践</li>
                        <li><strong>社区参与：</strong>参与Python社区讨论，分享经验和问题</li>
                        <li><strong>技术博客：</strong>记录学习过程，总结经验和心得</li>
                        <li><strong>版本更新：</strong>关注Python新版本特性和改进</li>
                        <li><strong>跨领域学习：</strong>结合具体应用领域深入学习</li>
                    </ul>
                </div>

                <div class="ai-prompt">
                    <h3><span class="emoji">🤖</span>AI提示词模板</h3>
                    <p><strong>制定Python学习计划：</strong>"请根据我的编程基础和学习目标，为我制定一个详细的Python学习计划。包括学习路径、时间安排、实践项目建议和学习资源推荐。我的目标是[具体目标，如Web开发/数据科学/自动化等]。"</p>
                </div>
            </div>
        </div>

        <!-- 第30页：结束页 -->
        <div class="slide">
            <div class="animated">
                <h1><span class="emoji bounce">🎉</span>感谢学习！</h1>
                <div style="text-align: center; margin-top: 100px;">
                    <h2><span class="emoji">🐍</span>Python编程之旅</h2>
                    <p style="font-size: 24px; margin: 40px 0;">从基础语法到高级特性，从理论学习到实践应用</p>
                    
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px; border-radius: 20px; margin: 40px auto; max-width: 600px; color: white;">
                        <h3 style="margin-bottom: 20px;">🌟 学习成果</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; text-align: left;">
                            <div>
                                <p>✅ 掌握Python基础语法</p>
                                <p>✅ 理解面向对象编程</p>
                                <p>✅ 熟悉常用数据结构</p>
                                <p>✅ 学会异常处理机制</p>
                            </div>
                            <div>
                                <p>✅ 掌握函数式编程</p>
                                <p>✅ 了解标准库应用</p>
                                <p>✅ 认识第三方库生态</p>
                                <p>✅ 养成良好编程习惯</p>
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin: 60px 0;">
                        <h3><span class="emoji">🚀</span>继续前进</h3>
                        <p style="font-size: 18px; line-height: 1.6;">编程是一个持续学习的过程<br>
                        保持好奇心，勇于实践，不断挑战自己<br>
                        Python的世界等待你去探索！</p>
                    </div>
                    
                    <div style="background: #f8f9fa; padding: 30px; border-radius: 15px; margin: 40px auto; max-width: 500px;">
                        <h4 style="color: #333; margin-bottom: 15px;">📞 学习支持</h4>
                        <p style="color: #666; margin: 5px 0;">💬 遇到问题？使用AI助手获得帮助</p>
                        <p style="color: #666; margin: 5px 0;">🌐 加入Python社区交流学习</p>
                        <p style="color: #666; margin: 5px 0;">📚 查阅官方文档深入学习</p>
                    </div>
                    
                    <div style="margin-top: 80px;">
                        <p style="font-size: 32px; margin: 20px 0;">🐍 Happy Coding! 🎯</p>
                        <p style="font-size: 16px; color: #666;">Python编程学习PPT - 完</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 导航控制 -->
        <div class="navigation">
            <button class="nav-btn" onclick="previousSlide()">‹ 上一页</button>
            <span class="slide-counter">
                <span id="currentSlide">1</span> / 30
            </span>
            <button class="nav-btn" onclick="nextSlide()">下一页 ›</button>
        </div>

        <!-- 页面指示器 -->
        <div class="slide-indicators">
            <div class="indicators-container">
                <span class="indicator active" onclick="showSlide(0)"></span>
                <span class="indicator" onclick="showSlide(1)"></span>
                <span class="indicator" onclick="showSlide(2)"></span>
                <span class="indicator" onclick="showSlide(3)"></span>
                <span class="indicator" onclick="showSlide(4)"></span>
                <span class="indicator" onclick="showSlide(5)"></span>
                <span class="indicator" onclick="showSlide(6)"></span>
                <span class="indicator" onclick="showSlide(7)"></span>
                <span class="indicator" onclick="showSlide(8)"></span>
                <span class="indicator" onclick="showSlide(9)"></span>
                <span class="indicator" onclick="showSlide(10)"></span>
                <span class="indicator" onclick="showSlide(11)"></span>
                <span class="indicator" onclick="showSlide(12)"></span>
                <span class="indicator" onclick="showSlide(13)"></span>
                <span class="indicator" onclick="showSlide(14)"></span>
                <span class="indicator" onclick="showSlide(15)"></span>
                <span class="indicator" onclick="showSlide(16)"></span>
                <span class="indicator" onclick="showSlide(17)"></span>
                <span class="indicator" onclick="showSlide(18)"></span>
                <span class="indicator" onclick="showSlide(19)"></span>
                <span class="indicator" onclick="showSlide(20)"></span>
                <span class="indicator" onclick="showSlide(21)"></span>
                <span class="indicator" onclick="showSlide(22)"></span>
                <span class="indicator" onclick="showSlide(23)"></span>
                <span class="indicator" onclick="showSlide(24)"></span>
                <span class="indicator" onclick="showSlide(25)"></span>
                <span class="indicator" onclick="showSlide(26)"></span>
                <span class="indicator" onclick="showSlide(27)"></span>
                <span class="indicator" onclick="showSlide(28)"></span>
                <span class="indicator" onclick="showSlide(29)"></span>
            </div>
        </div>
    </div>

    <script>
        let currentSlideIndex = 0;
        const slides = document.querySelectorAll('.slide');
        const indicators = document.querySelectorAll('.indicator');
        const totalSlides = slides.length;

        function showSlide(index) {
            // 隐藏所有幻灯片
            slides.forEach(slide => slide.classList.remove('active'));
            indicators.forEach(indicator => indicator.classList.remove('active'));
            
            // 显示指定幻灯片
            if (index >= 0 && index < totalSlides) {
                currentSlideIndex = index;
                slides[currentSlideIndex].classList.add('active');
                indicators[currentSlideIndex].classList.add('active');
                document.getElementById('currentSlide').textContent = currentSlideIndex + 1;
            }
        }

        function nextSlide() {
            if (currentSlideIndex < totalSlides - 1) {
                showSlide(currentSlideIndex + 1);
            }
        }

        function previousSlide() {
            if (currentSlideIndex > 0) {
                showSlide(currentSlideIndex - 1);
            }
        }

        // 键盘导航
        document.addEventListener('keydown', function(event) {
            if (event.key === 'ArrowRight' || event.key === ' ') {
                nextSlide();
            } else if (event.key === 'ArrowLeft') {
                previousSlide();
            } else if (event.key === 'Home') {
                showSlide(0);
            } else if (event.key === 'End') {
                showSlide(totalSlides - 1);
            }
        });

        // 初始化显示第一张幻灯片
        showSlide(0);
    </script>