<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python文件处理完整教程</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow: hidden;
        }

        .presentation {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            background: white;
            border-radius: 20px;
            margin: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow-y: auto;
            padding: 40px;
            text-align: left;
        }

        .slide.active {
            opacity: 1;
            transform: translateX(0);
        }

        .slide.prev {
            transform: translateX(-100%);
        }

        .slide h1 {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
            animation: slideInDown 1s ease-out;
        }

        .slide h2 {
            font-size: 2em;
            color: #34495e;
            margin: 25px 0 15px 0;
            animation: slideInLeft 1s ease-out 0.3s both;
        }

        .slide h3 {
            font-size: 1.5em;
            color: #7f8c8d;
            margin: 20px 0 10px 0;
            animation: slideInLeft 1s ease-out 0.5s both;
        }

        .slide p, .slide li {
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 15px;
            animation: fadeInUp 1s ease-out 0.7s both;
        }

        .slide ul {
            margin-left: 30px;
            margin-bottom: 20px;
        }

        .emoji {
            font-size: 1.5em;
            margin-right: 10px;
            animation: bounce 2s infinite;
        }

        .code-container {
            background: #1e1e1e;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
            min-height: 200px;
            animation: slideInUp 1s ease-out 0.9s both;
        }

        .code {
            color: #d4d4d4;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            white-space: pre;
            text-align: left;
        }

        .keyword { color: #569cd6; }
        .string { color: #ce9178; }
        .comment { color: #6a9955; }
        .function { color: #dcdcaa; }
        .number { color: #b5cea8; }
        .operator { color: #d4d4d4; }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            padding: 12px 24px;
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .nav-btn:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(255,255,255,0.9);
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            z-index: 1000;
        }

        .highlight-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            animation: pulse 2s infinite;
        }

        .ai-prompt {
            background: #f8f9fa;
            border-left: 5px solid #007bff;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            font-style: italic;
        }

        @keyframes slideInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }
            100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }
        }
    </style>
</head>
<body>
    <div class="presentation">
        <div class="slide-counter">
            <span id="current-slide">1</span> / <span id="total-slides">30</span>
        </div>

        <!-- 第1页：标题页 -->
        <div class="slide active">
            <h1><span class="emoji">📁</span>Python文件处理完整教程</h1>
            <div style="text-align: center; margin-top: 50px;">
                <h2><span class="emoji">🐍</span>掌握CSV和JSON文件操作</h2>
                <h3><span class="emoji">🤖</span>结合AI辅助编程学习</h3>
                <div class="highlight-box" style="margin-top: 40px;">
                    <p><span class="emoji">🎯</span>本教程将带你深入了解Python文件处理的核心技能</p>
                    <p><span class="emoji">💡</span>学会使用AI工具提升编程效率</p>
                </div>
            </div>
        </div>

        <!-- 第2页：课程大纲 -->
        <div class="slide">
            <h1><span class="emoji">📋</span>课程大纲</h1>
            <h2><span class="emoji">🗂️</span>文件处理基础</h2>
            <ul>
                <li><span class="emoji">📄</span>文件的打开、读取、写入和关闭</li>
                <li><span class="emoji">🔧</span>文件模式和编码处理</li>
                <li><span class="emoji">⚠️</span>异常处理和资源管理</li>
            </ul>
            
            <h2><span class="emoji">📊</span>CSV文件处理</h2>
            <ul>
                <li><span class="emoji">📈</span>csv模块的使用</li>
                <li><span class="emoji">🔄</span>数据读取和写入</li>
                <li><span class="emoji">🛠️</span>高级操作技巧</li>
            </ul>
            
            <h2><span class="emoji">🌐</span>JSON文件处理</h2>
            <ul>
                <li><span class="emoji">📝</span>json模块详解</li>
                <li><span class="emoji">🔀</span>序列化和反序列化</li>
                <li><span class="emoji">🎨</span>复杂数据结构处理</li>
            </ul>
            
            <h2><span class="emoji">🤖</span>AI辅助编程</h2>
            <ul>
                <li><span class="emoji">💬</span>提示词模板设计</li>
                <li><span class="emoji">🚀</span>提升学习效率</li>
            </ul>
        </div>

        <!-- 第3页：文件处理基础概念 -->
        <div class="slide">
            <h1><span class="emoji">📚</span>文件处理基础概念</h1>
            <h2><span class="emoji">💭</span>什么是文件处理？</h2>
            <p>文件处理是程序与外部数据交互的重要方式，包括：</p>
            <ul>
                <li><span class="emoji">📖</span>读取文件内容</li>
                <li><span class="emoji">✍️</span>写入数据到文件</li>
                <li><span class="emoji">🔄</span>修改现有文件</li>
                <li><span class="emoji">🗑️</span>删除文件</li>
            </ul>
            
            <h2><span class="emoji">🎯</span>为什么要学习文件处理？</h2>
            <ul>
                <li><span class="emoji">💾</span>数据持久化存储</li>
                <li><span class="emoji">📊</span>数据分析和处理</li>
                <li><span class="emoji">🔗</span>系统间数据交换</li>
                <li><span class="emoji">📈</span>日志记录和监控</li>
                <li><span class="emoji">⚙️</span>配置文件管理</li>
            </ul>
            
            <div class="highlight-box">
                <p><span class="emoji">💡</span>掌握文件处理是成为优秀Python开发者的必备技能！</p>
            </div>
        </div>

        <!-- 第4页：文件打开和关闭 -->
        <div class="slide">
            <h1><span class="emoji">🚪</span>文件的打开和关闭</h1>
            <h2><span class="emoji">🔑</span>基本语法</h2>
            <div class="code-container">
                <div class="code"># 基本文件操作
<span class="comment"># 打开文件</span>
<span class="keyword">file</span> = <span class="function">open</span>(<span class="string">'example.txt'</span>, <span class="string">'r'</span>)

<span class="comment"># 读取内容</span>
content = file.<span class="function">read</span>()
<span class="function">print</span>(content)

<span class="comment"># 关闭文件</span>
file.<span class="function">close</span>()

<span class="comment"># 推荐使用with语句（自动关闭文件）</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'example.txt'</span>, <span class="string">'r'</span>) <span class="keyword">as</span> file:
    content = file.<span class="function">read</span>()
    <span class="function">print</span>(content)
<span class="comment"># 文件自动关闭，无需手动调用close()</span></div>
            </div>
            
            <h2><span class="emoji">✅</span>最佳实践</h2>
            <ul>
                <li><span class="emoji">🛡️</span>始终使用with语句确保文件正确关闭</li>
                <li><span class="emoji">⚡</span>避免资源泄露和文件锁定问题</li>
                <li><span class="emoji">🔒</span>异常发生时也能正确释放资源</li>
            </ul>
        </div>

        <!-- 第5页：文件模式详解 -->
        <div class="slide">
            <h1><span class="emoji">🎛️</span>文件模式详解</h1>
            <h2><span class="emoji">📖</span>读取模式</h2>
            <ul>
                <li><span class="emoji">📄</span><strong>'r'</strong> - 只读模式（默认）</li>
                <li><span class="emoji">🔤</span><strong>'rt'</strong> - 文本模式读取</li>
                <li><span class="emoji">💾</span><strong>'rb'</strong> - 二进制模式读取</li>
            </ul>
            
            <h2><span class="emoji">✍️</span>写入模式</h2>
            <ul>
                <li><span class="emoji">📝</span><strong>'w'</strong> - 写入模式（覆盖原文件）</li>
                <li><span class="emoji">➕</span><strong>'a'</strong> - 追加模式（在文件末尾添加）</li>
                <li><span class="emoji">🆕</span><strong>'x'</strong> - 创建模式（文件不存在时创建）</li>
            </ul>
            
            <h2><span class="emoji">🔄</span>读写模式</h2>
            <ul>
                <li><span class="emoji">🔀</span><strong>'r+'</strong> - 读写模式</li>
                <li><span class="emoji">📊</span><strong>'w+'</strong> - 写读模式（清空文件）</li>
                <li><span class="emoji">📈</span><strong>'a+'</strong> - 追加读写模式</li>
            </ul>
            
            <div class="code-container">
                <div class="code"><span class="comment"># 不同模式示例</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'data.txt'</span>, <span class="string">'w'</span>) <span class="keyword">as</span> f:
    f.<span class="function">write</span>(<span class="string">'Hello World'</span>)  <span class="comment"># 覆盖写入</span>

<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'data.txt'</span>, <span class="string">'a'</span>) <span class="keyword">as</span> f:
    f.<span class="function">write</span>(<span class="string">'\nNew Line'</span>)  <span class="comment"># 追加写入</span>

<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'data.txt'</span>, <span class="string">'r'</span>) <span class="keyword">as</span> f:
    content = f.<span class="function">read</span>()  <span class="comment"># 读取全部内容</span>
    <span class="function">print</span>(content)</div>
            </div>
        </div>

        <!-- 第6页：文件读取方法 -->
        <div class="slide">
            <h1><span class="emoji">📖</span>文件读取方法</h1>
            <h2><span class="emoji">🔍</span>三种主要读取方式</h2>
            <div class="code-container">
                <div class="code"><span class="comment"># 1. read() - 读取整个文件</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'example.txt'</span>, <span class="string">'r'</span>) <span class="keyword">as</span> file:
    content = file.<span class="function">read</span>()  <span class="comment"># 读取全部内容</span>
    <span class="function">print</span>(content)

<span class="comment"># 2. readline() - 逐行读取</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'example.txt'</span>, <span class="string">'r'</span>) <span class="keyword">as</span> file:
    line = file.<span class="function">readline</span>()  <span class="comment"># 读取一行</span>
    <span class="keyword">while</span> line:
        <span class="function">print</span>(line.<span class="function">strip</span>())  <span class="comment"># 去除换行符</span>
        line = file.<span class="function">readline</span>()

<span class="comment"># 3. readlines() - 读取所有行到列表</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'example.txt'</span>, <span class="string">'r'</span>) <span class="keyword">as</span> file:
    lines = file.<span class="function">readlines</span>()  <span class="comment"># 返回行列表</span>
    <span class="keyword">for</span> line <span class="keyword">in</span> lines:
        <span class="function">print</span>(line.<span class="function">strip</span>())

<span class="comment"># 4. 推荐方式：直接迭代文件对象</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'example.txt'</span>, <span class="string">'r'</span>) <span class="keyword">as</span> file:
    <span class="keyword">for</span> line <span class="keyword">in</span> file:
        <span class="function">print</span>(line.<span class="function">strip</span>())  <span class="comment"># 内存效率最高</span></div>
            </div>
            
            <h2><span class="emoji">💡</span>选择建议</h2>
            <ul>
                <li><span class="emoji">📄</span>小文件：使用read()或readlines()</li>
                <li><span class="emoji">📚</span>大文件：使用readline()或文件迭代</li>
                <li><span class="emoji">⚡</span>最佳实践：直接迭代文件对象</li>
            </ul>
        </div>

        <!-- 第7页：文件写入方法 -->
        <div class="slide">
            <h1><span class="emoji">✍️</span>文件写入方法</h1>
            <h2><span class="emoji">📝</span>基本写入操作</h2>
            <div class="code-container">
                <div class="code"><span class="comment"># 1. write() - 写入字符串</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'output.txt'</span>, <span class="string">'w'</span>) <span class="keyword">as</span> file:
    file.<span class="function">write</span>(<span class="string">'Hello, Python!\n'</span>)
    file.<span class="function">write</span>(<span class="string">'文件写入示例\n'</span>)

<span class="comment"># 2. writelines() - 写入字符串列表</span>
lines = [<span class="string">'第一行\n'</span>, <span class="string">'第二行\n'</span>, <span class="string">'第三行\n'</span>]
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'output.txt'</span>, <span class="string">'w'</span>) <span class="keyword">as</span> file:
    file.<span class="function">writelines</span>(lines)

<span class="comment"># 3. 使用print()函数写入文件</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'output.txt'</span>, <span class="string">'w'</span>) <span class="keyword">as</span> file:
    <span class="function">print</span>(<span class="string">'使用print写入'</span>, file=file)
    <span class="function">print</span>(<span class="string">'自动添加换行符'</span>, file=file)

<span class="comment"># 4. 追加模式写入</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'output.txt'</span>, <span class="string">'a'</span>) <span class="keyword">as</span> file:
    file.<span class="function">write</span>(<span class="string">'追加的内容\n'</span>)

<span class="comment"># 5. 格式化写入</span>
name = <span class="string">'张三'</span>
age = <span class="number">25</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'user_info.txt'</span>, <span class="string">'w'</span>) <span class="keyword">as</span> file:
    file.<span class="function">write</span>(<span class="string">f'姓名: {name}\n年龄: {age}\n'</span>)</div>
            </div>
            
            <h2><span class="emoji">⚠️</span>注意事项</h2>
            <ul>
                <li><span class="emoji">🔄</span>write()不会自动添加换行符</li>
                <li><span class="emoji">📋</span>writelines()需要手动添加换行符</li>
                <li><span class="emoji">🖨️</span>print()函数自动添加换行符</li>
            </ul>
        </div>

        <!-- 第8页：异常处理 -->
        <div class="slide">
            <h1><span class="emoji">⚠️</span>文件操作异常处理</h1>
            <h2><span class="emoji">🛡️</span>常见异常类型</h2>
            <ul>
                <li><span class="emoji">❌</span><strong>FileNotFoundError</strong> - 文件不存在</li>
                <li><span class="emoji">🚫</span><strong>PermissionError</strong> - 权限不足</li>
                <li><span class="emoji">💾</span><strong>IOError</strong> - 输入输出错误</li>
                <li><span class="emoji">🔤</span><strong>UnicodeDecodeError</strong> - 编码错误</li>
            </ul>
            
            <div class="code-container">
                <div class="code"><span class="comment"># 基本异常处理</span>
<span class="keyword">try</span>:
    <span class="keyword">with</span> <span class="function">open</span>(<span class="string">'nonexistent.txt'</span>, <span class="string">'r'</span>) <span class="keyword">as</span> file:
        content = file.<span class="function">read</span>()
        <span class="function">print</span>(content)
<span class="keyword">except</span> FileNotFoundError:
    <span class="function">print</span>(<span class="string">'错误：文件不存在！'</span>)
<span class="keyword">except</span> PermissionError:
    <span class="function">print</span>(<span class="string">'错误：没有访问权限！'</span>)
<span class="keyword">except</span> IOError <span class="keyword">as</span> e:
    <span class="function">print</span>(<span class="string">f'IO错误：{e}'</span>)

<span class="comment"># 更完善的异常处理</span>
<span class="keyword">def</span> <span class="function">safe_read_file</span>(filename):
    <span class="keyword">try</span>:
        <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
            <span class="keyword">return</span> file.<span class="function">read</span>()
    <span class="keyword">except</span> FileNotFoundError:
        <span class="function">print</span>(<span class="string">f'文件 {filename} 不存在'</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
    <span class="keyword">except</span> UnicodeDecodeError:
        <span class="function">print</span>(<span class="string">'文件编码错误，尝试其他编码'</span>)
        <span class="keyword">try</span>:
            <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">'r'</span>, encoding=<span class="string">'gbk'</span>) <span class="keyword">as</span> file:
                <span class="keyword">return</span> file.<span class="function">read</span>()
        <span class="keyword">except</span> Exception <span class="keyword">as</span> e:
            <span class="function">print</span>(<span class="string">f'读取失败：{e}'</span>)
            <span class="keyword">return</span> <span class="keyword">None</span>
    <span class="keyword">except</span> Exception <span class="keyword">as</span> e:
        <span class="function">print</span>(<span class="string">f'未知错误：{e}'</span>)
        <span class="keyword">return</span> <span class="keyword">None</span></div>
            </div>
        </div>

        <!-- 第9页：编码处理 -->
        <div class="slide">
            <h1><span class="emoji">🔤</span>文件编码处理</h1>
            <h2><span class="emoji">🌍</span>常见编码格式</h2>
            <ul>
                <li><span class="emoji">🌐</span><strong>UTF-8</strong> - 国际标准，推荐使用</li>
                <li><span class="emoji">🇨🇳</span><strong>GBK/GB2312</strong> - 中文编码</li>
                <li><span class="emoji">🇺🇸</span><strong>ASCII</strong> - 基础英文编码</li>
                <li><span class="emoji">🇪🇺</span><strong>ISO-8859-1</strong> - 西欧编码</li>
            </ul>
            
            <div class="code-container">
                <div class="code"><span class="comment"># 指定编码读取文件</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'chinese.txt'</span>, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
    content = file.<span class="function">read</span>()
    <span class="function">print</span>(content)

<span class="comment"># 处理不同编码的文件</span>
<span class="keyword">def</span> <span class="function">read_with_encoding</span>(filename):
    encodings = [<span class="string">'utf-8'</span>, <span class="string">'gbk'</span>, <span class="string">'gb2312'</span>, <span class="string">'iso-8859-1'</span>]
    
    <span class="keyword">for</span> encoding <span class="keyword">in</span> encodings:
        <span class="keyword">try</span>:
            <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">'r'</span>, encoding=encoding) <span class="keyword">as</span> file:
                content = file.<span class="function">read</span>()
                <span class="function">print</span>(<span class="string">f'成功使用 {encoding} 编码读取文件'</span>)
                <span class="keyword">return</span> content
        <span class="keyword">except</span> UnicodeDecodeError:
            <span class="function">print</span>(<span class="string">f'{encoding} 编码失败，尝试下一个'</span>)
            <span class="keyword">continue</span>
    
    <span class="function">print</span>(<span class="string">'所有编码都失败了'</span>)
    <span class="keyword">return</span> <span class="keyword">None</span>

<span class="comment"># 写入时指定编码</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'output.txt'</span>, <span class="string">'w'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
    file.<span class="function">write</span>(<span class="string">'你好，世界！\n'</span>)
    file.<span class="function">write</span>(<span class="string">'Hello, World!\n'</span>)

<span class="comment"># 检测文件编码（需要安装chardet库）</span>
<span class="keyword">import</span> chardet

<span class="keyword">def</span> <span class="function">detect_encoding</span>(filename):
    <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">'rb'</span>) <span class="keyword">as</span> file:
        raw_data = file.<span class="function">read</span>()
        result = chardet.<span class="function">detect</span>(raw_data)
        <span class="keyword">return</span> result[<span class="string">'encoding'</span>]</div>
            </div>
        </div>

        <!-- 第10页：CSV文件简介 -->
        <div class="slide">
            <h1><span class="emoji">📊</span>CSV文件处理简介</h1>
            <h2><span class="emoji">❓</span>什么是CSV文件？</h2>
            <p>CSV（Comma-Separated Values）是一种简单的文件格式，用于存储表格数据。</p>
            
            <h3><span class="emoji">✨</span>CSV文件特点：</h3>
            <ul>
                <li><span class="emoji">📋</span>纯文本格式，易于阅读和编辑</li>
                <li><span class="emoji">🔗</span>使用逗号分隔字段</li>
                <li><span class="emoji">📈</span>广泛用于数据交换</li>
                <li><span class="emoji">💼</span>Excel等软件都支持</li>
            </ul>
            
            <h3><span class="emoji">📄</span>CSV文件示例：</h3>
            <div class="code-container">
                <div class="code"><span class="comment"># students.csv 文件内容</span>
姓名,年龄,成绩,班级
张三,<span class="number">18</span>,<span class="number">85</span>,高一1班
李四,<span class="number">17</span>,<span class="number">92</span>,高一2班
王五,<span class="number">18</span>,<span class="number">78</span>,高一1班
赵六,<span class="number">17</span>,<span class="number">88</span>,高一3班</div>
            </div>
            
            <h3><span class="emoji">🎯</span>为什么学习CSV处理？</h3>
            <ul>
                <li><span class="emoji">📊</span>数据分析的基础技能</li>
                <li><span class="emoji">💾</span>数据导入导出常用格式</li>
                <li><span class="emoji">🔄</span>系统间数据交换标准</li>
                <li><span class="emoji">📈</span>报表生成和数据可视化</li>
            </ul>
            
            <div class="highlight-box">
                <p><span class="emoji">💡</span>Python的csv模块提供了强大而简单的CSV处理功能！</p>
            </div>
        </div>

        <!-- 第11页：CSV模块基础 -->
        <div class="slide">
            <h1><span class="emoji">🛠️</span>CSV模块基础操作</h1>
            <h2><span class="emoji">📖</span>读取CSV文件</h2>
            <div class="code-container">
                <div class="code"><span class="keyword">import</span> csv

<span class="comment"># 基本读取方式</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'students.csv'</span>, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
    csv_reader = csv.<span class="function">reader</span>(file)
    
    <span class="comment"># 读取表头</span>
    headers = <span class="function">next</span>(csv_reader)
    <span class="function">print</span>(<span class="string">'表头:'</span>, headers)
    
    <span class="comment"># 读取数据行</span>
    <span class="keyword">for</span> row <span class="keyword">in</span> csv_reader:
        <span class="function">print</span>(row)

<span class="comment"># 使用DictReader读取（推荐）</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'students.csv'</span>, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
    csv_reader = csv.<span class="function">DictReader</span>(file)
    
    <span class="keyword">for</span> row <span class="keyword">in</span> csv_reader:
        <span class="function">print</span>(<span class="string">f'姓名: {row["姓名"]}, 年龄: {row["年龄"]}, 成绩: {row["成绩"]}'</span>)

<span class="comment"># 读取所有数据到列表</span>
<span class="keyword">def</span> <span class="function">read_csv_to_list</span>(filename):
    data = []
    <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
        csv_reader = csv.<span class="function">DictReader</span>(file)
        <span class="keyword">for</span> row <span class="keyword">in</span> csv_reader:
            data.<span class="function">append</span>(row)
    <span class="keyword">return</span> data

students = <span class="function">read_csv_to_list</span>(<span class="string">'students.csv'</span>)
<span class="function">print</span>(<span class="string">f'共读取 {len(students)} 条学生记录'</span>)</div>
            </div>
            
            <h2><span class="emoji">🔍</span>reader vs DictReader</h2>
            <ul>
                <li><span class="emoji">📋</span><strong>reader</strong>：返回列表，需要记住列的位置</li>
                <li><span class="emoji">🗂️</span><strong>DictReader</strong>：返回字典，使用列名访问数据</li>
            </ul>
        </div>

        <!-- 第12页：CSV写入操作 -->
        <div class="slide">
            <h1><span class="emoji">✍️</span>CSV文件写入操作</h1>
            <h2><span class="emoji">📝</span>基本写入方法</h2>
            <div class="code-container">
                <div class="code"><span class="keyword">import</span> csv

<span class="comment"># 使用writer写入</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'output.csv'</span>, <span class="string">'w'</span>, newline=<span class="string">''</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
    csv_writer = csv.<span class="function">writer</span>(file)
    
    <span class="comment"># 写入表头</span>
    csv_writer.<span class="function">writerow</span>([<span class="string">'姓名'</span>, <span class="string">'年龄'</span>, <span class="string">'城市'</span>])
    
    <span class="comment"># 写入数据行</span>
    csv_writer.<span class="function">writerow</span>([<span class="string">'张三'</span>, <span class="number">25</span>, <span class="string">'北京'</span>])
    csv_writer.<span class="function">writerow</span>([<span class="string">'李四'</span>, <span class="number">30</span>, <span class="string">'上海'</span>])
    
    <span class="comment"># 批量写入多行</span>
    data = [
        [<span class="string">'王五'</span>, <span class="number">28</span>, <span class="string">'广州'</span>],
        [<span class="string">'赵六'</span>, <span class="number">32</span>, <span class="string">'深圳'</span>]
    ]
    csv_writer.<span class="function">writerows</span>(data)

<span class="comment"># 使用DictWriter写入（推荐）</span>
fieldnames = [<span class="string">'姓名'</span>, <span class="string">'年龄'</span>, <span class="string">'职业'</span>, <span class="string">'薪资'</span>]

<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'employees.csv'</span>, <span class="string">'w'</span>, newline=<span class="string">''</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
    csv_writer = csv.<span class="function">DictWriter</span>(file, fieldnames=fieldnames)
    
    <span class="comment"># 写入表头</span>
    csv_writer.<span class="function">writeheader</span>()
    
    <span class="comment"># 写入字典数据</span>
    csv_writer.<span class="function">writerow</span>({
        <span class="string">'姓名'</span>: <span class="string">'张三'</span>,
        <span class="string">'年龄'</span>: <span class="number">28</span>,
        <span class="string">'职业'</span>: <span class="string">'程序员'</span>,
        <span class="string">'薪资'</span>: <span class="number">15000</span>
    })
    
    <span class="comment"># 批量写入字典列表</span>
    employees = [
        {<span class="string">'姓名'</span>: <span class="string">'李四'</span>, <span class="string">'年龄'</span>: <span class="number">32</span>, <span class="string">'职业'</span>: <span class="string">'设计师'</span>, <span class="string">'薪资'</span>: <span class="number">12000</span>},
        {<span class="string">'姓名'</span>: <span class="string">'王五'</span>, <span class="string">'年龄'</span>: <span class="number">29</span>, <span class="string">'职业'</span>: <span class="string">'产品经理'</span>, <span class="string">'薪资'</span>: <span class="number">18000</span>}
    ]
    csv_writer.<span class="function">writerows</span>(employees)</div>
            </div>
            
            <h2><span class="emoji">⚠️</span>重要提示</h2>
            <ul>
                <li><span class="emoji">📝</span>写入时使用 newline='' 参数</li>
                <li><span class="emoji">🔤</span>指定正确的编码格式</li>
                <li><span class="emoji">🗂️</span>DictWriter更直观易用</li>
            </ul>
        </div>

        <!-- 第13页：CSV高级操作 -->
        <div class="slide">
            <h1><span class="emoji">🚀</span>CSV高级操作技巧</h1>
            <h2><span class="emoji">⚙️</span>自定义分隔符和格式</h2>
            <div class="code-container">
                <div class="code"><span class="keyword">import</span> csv

<span class="comment"># 处理不同分隔符的文件</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'data.tsv'</span>, <span class="string">'r'</span>) <span class="keyword">as</span> file:
    <span class="comment"># 制表符分隔</span>
    csv_reader = csv.<span class="function">reader</span>(file, delimiter=<span class="string">'\t'</span>)
    <span class="keyword">for</span> row <span class="keyword">in</span> csv_reader:
        <span class="function">print</span>(row)

<span class="comment"># 处理包含引号的数据</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'quotes.csv'</span>, <span class="string">'r'</span>) <span class="keyword">as</span> file:
    csv_reader = csv.<span class="function">reader</span>(file, quotechar=<span class="string">'"'</span>)
    <span class="keyword">for</span> row <span class="keyword">in</span> csv_reader:
        <span class="function">print</span>(row)

<span class="comment"># 自定义CSV方言</span>
csv.<span class="function">register_dialect</span>(<span class="string">'custom'</span>, 
                      delimiter=<span class="string">';'</span>,
                      quotechar=<span class="string">'"'</span>,
                      quoting=csv.QUOTE_MINIMAL)

<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'custom.csv'</span>, <span class="string">'w'</span>, newline=<span class="string">''</span>) <span class="keyword">as</span> file:
    csv_writer = csv.<span class="function">writer</span>(file, dialect=<span class="string">'custom'</span>)
    csv_writer.<span class="function">writerow</span>([<span class="string">'名称'</span>, <span class="string">'描述'</span>, <span class="string">'价格'</span>])
    csv_writer.<span class="function">writerow</span>([<span class="string">'产品A'</span>, <span class="string">'这是一个"特殊"产品'</span>, <span class="number">99.99</span>])</div>
            </div>
            
            <h2><span class="emoji">🔍</span>数据处理和分析</h2>
            <div class="code-container">
                <div class="code"><span class="comment"># 数据过滤和统计</span>
<span class="keyword">def</span> <span class="function">analyze_students</span>(filename):
    total_students = <span class="number">0</span>
    total_score = <span class="number">0</span>
    high_achievers = []
    
    <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
        csv_reader = csv.<span class="function">DictReader</span>(file)
        
        <span class="keyword">for</span> row <span class="keyword">in</span> csv_reader:
            total_students += <span class="number">1</span>
            score = <span class="function">int</span>(row[<span class="string">'成绩'</span>])
            total_score += score
            
            <span class="comment"># 筛选高分学生</span>
            <span class="keyword">if</span> score >= <span class="number">90</span>:
                high_achievers.<span class="function">append</span>(row)
    
    average_score = total_score / total_students
    
    <span class="function">print</span>(<span class="string">f'总学生数: {total_students}'</span>)
    <span class="function">print</span>(<span class="string">f'平均分: {average_score:.2f}'</span>)
    <span class="function">print</span>(<span class="string">f'高分学生数: {len(high_achievers)}'</span>)
    
    <span class="keyword">return</span> high_achievers

<span class="comment"># 数据转换和导出</span>
<span class="keyword">def</span> <span class="function">export_high_achievers</span>(input_file, output_file):
    high_achievers = <span class="function">analyze_students</span>(input_file)
    
    <span class="keyword">with</span> <span class="function">open</span>(output_file, <span class="string">'w'</span>, newline=<span class="string">''</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
        fieldnames = [<span class="string">'姓名'</span>, <span class="string">'年龄'</span>, <span class="string">'成绩'</span>, <span class="string">'班级'</span>]
        csv_writer = csv.<span class="function">DictWriter</span>(file, fieldnames=fieldnames)
        
        csv_writer.<span class="function">writeheader</span>()
        csv_writer.<span class="function">writerows</span>(high_achievers)</div>
            </div>
        </div>

        <!-- 第14页：JSON文件简介 -->
        <div class="slide">
            <h1><span class="emoji">🌐</span>JSON文件处理简介</h1>
            <h2><span class="emoji">❓</span>什么是JSON？</h2>
            <p>JSON（JavaScript Object Notation）是一种轻量级的数据交换格式，易于人阅读和编写。</p>
            
            <h3><span class="emoji">✨</span>JSON特点：</h3>
            <ul>
                <li><span class="emoji">📝</span>基于文本，人类可读</li>
                <li><span class="emoji">🌍</span>语言无关的数据格式</li>
                <li><span class="emoji">🔗</span>广泛用于Web API</li>
                <li><span class="emoji">🏗️</span>支持复杂的数据结构</li>
            </ul>
            
            <h3><span class="emoji">📊</span>JSON数据类型：</h3>
            <ul>
                <li><span class="emoji">🔤</span>字符串（String）</li>
                <li><span class="emoji">🔢</span>数字（Number）</li>
                <li><span class="emoji">✅</span>布尔值（Boolean）</li>
                <li><span class="emoji">❌</span>空值（null）</li>
                <li><span class="emoji">📋</span>数组（Array）</li>
                <li><span class="emoji">🗂️</span>对象（Object）</li>
            </ul>
            
            <h3><span class="emoji">📄</span>JSON文件示例：</h3>
            <div class="code-container">
                <div class="code">{
  <span class="string">"name"</span>: <span class="string">"张三"</span>,
  <span class="string">"age"</span>: <span class="number">25</span>,
  <span class="string">"is_student"</span>: <span class="keyword">false</span>,
  <span class="string">"hobbies"</span>: [<span class="string">"读书"</span>, <span class="string">"游泳"</span>, <span class="string">"编程"</span>],
  <span class="string">"address"</span>: {
    <span class="string">"city"</span>: <span class="string">"北京"</span>,
    <span class="string">"district"</span>: <span class="string">"朝阳区"</span>
  },
  <span class="string">"phone"</span>: <span class="keyword">null</span>
}</div>
            </div>
            
            <div class="highlight-box">
                <p><span class="emoji">💡</span>JSON是现代Web开发和数据交换的标准格式！</p>
            </div>
        </div>

        <!-- 第15页：JSON模块基础 -->
        <div class="slide">
            <h1><span class="emoji">🛠️</span>JSON模块基础操作</h1>
            <h2><span class="emoji">📖</span>读取JSON文件</h2>
            <div class="code-container">
                <div class="code"><span class="keyword">import</span> json

<span class="comment"># 从文件读取JSON数据</span>
<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'data.json'</span>, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
    data = json.<span class="function">load</span>(file)
    <span class="function">print</span>(data)
    <span class="function">print</span>(<span class="function">type</span>(data))  <span class="comment"># <class 'dict'></span>

<span class="comment"># 从字符串解析JSON</span>
json_string = <span class="string">'{"name": "李四", "age": 30, "city": "上海"}'</span>
data = json.<span class="function">loads</span>(json_string)
<span class="function">print</span>(data[<span class="string">'name'</span>])  <span class="comment"># 李四</span>

<span class="comment"># 安全读取JSON文件</span>
<span class="keyword">def</span> <span class="function">safe_load_json</span>(filename):
    <span class="keyword">try</span>:
        <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
            <span class="keyword">return</span> json.<span class="function">load</span>(file)
    <span class="keyword">except</span> FileNotFoundError:
        <span class="function">print</span>(<span class="string">f'文件 {filename} 不存在'</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
    <span class="keyword">except</span> json.JSONDecodeError <span class="keyword">as</span> e:
        <span class="function">print</span>(<span class="string">f'JSON格式错误: {e}'</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
    <span class="keyword">except</span> Exception <span class="keyword">as</span> e:
        <span class="function">print</span>(<span class="string">f'读取错误: {e}'</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>

<span class="comment"># 使用示例</span>
user_data = <span class="function">safe_load_json</span>(<span class="string">'user.json'</span>)
<span class="keyword">if</span> user_data:
    <span class="function">print</span>(<span class="string">f'用户姓名: {user_data.get("name", "未知")}'</span>)
    <span class="function">print</span>(<span class="string">f'用户年龄: {user_data.get("age", "未知")}'</span>)</div>
            </div>
            
            <h2><span class="emoji">🔍</span>load vs loads</h2>
            <ul>
                <li><span class="emoji">📁</span><strong>json.load()</strong>：从文件对象读取JSON</li>
                <li><span class="emoji">🔤</span><strong>json.loads()</strong>：从字符串解析JSON</li>
            </ul>
        </div>

        <!-- 第16页：JSON写入操作 -->
        <div class="slide">
            <h1><span class="emoji">✍️</span>JSON文件写入操作</h1>
            <h2><span class="emoji">📝</span>基本写入方法</h2>
            <div class="code-container">
                <div class="code"><span class="keyword">import</span> json

<span class="comment"># 写入JSON到文件</span>
data = {
    <span class="string">"name"</span>: <span class="string">"张三"</span>,
    <span class="string">"age"</span>: <span class="number">25</span>,
    <span class="string">"hobbies"</span>: [<span class="string">"读书"</span>, <span class="string">"游泳"</span>],
    <span class="string">"is_student"</span>: <span class="keyword">False</span>
}

<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"user.json"</span>, <span class="string">"w"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> file:
    json.<span class="function">dump</span>(data, file, ensure_ascii=<span class="keyword">False</span>, indent=<span class="number">4</span>)

<span class="comment"># 转换为JSON字符串</span>
json_string = json.<span class="function">dumps</span>(data, ensure_ascii=<span class="keyword">False</span>, indent=<span class="number">2</span>)
<span class="function">print</span>(json_string)

<span class="comment"># 批量处理用户数据</span>
users = [
    {<span class="string">"id"</span>: <span class="number">1</span>, <span class="string">"name"</span>: <span class="string">"张三"</span>, <span class="string">"email"</span>: <span class="string">"<EMAIL>"</span>},
    {<span class="string">"id"</span>: <span class="number">2</span>, <span class="string">"name"</span>: <span class="string">"李四"</span>, <span class="string">"email"</span>: <span class="string">"<EMAIL>"</span>},
    {<span class="string">"id"</span>: <span class="number">3</span>, <span class="string">"name"</span>: <span class="string">"王五"</span>, <span class="string">"email"</span>: <span class="string">"<EMAIL>"</span>}
]

<span class="keyword">with</span> <span class="function">open</span>(<span class="string">"users.json"</span>, <span class="string">"w"</span>, encoding=<span class="string">"utf-8"</span>) <span class="keyword">as</span> file:
    json.<span class="function">dump</span>(users, file, ensure_ascii=<span class="keyword">False</span>, indent=<span class="number">4</span>)</div>
            </div>
            
            <h2><span class="emoji">⚙️</span>重要参数说明</h2>
            <ul>
                <li><span class="emoji">🌍</span><strong>ensure_ascii=False</strong>：支持中文字符</li>
                <li><span class="emoji">📐</span><strong>indent=4</strong>：美化输出格式</li>
                <li><span class="emoji">🔤</span><strong>encoding='utf-8'</strong>：指定编码格式</li>
            </ul>
        </div>

        <!-- 第17页：JSON高级操作 -->
        <div class="slide">
            <h1><span class="emoji">🚀</span>JSON高级操作技巧</h1>
            <h2><span class="emoji">🔄</span>复杂数据结构处理</h2>
            <div class="code-container">
                <div class="code"><span class="keyword">import</span> json
<span class="keyword">from</span> datetime <span class="keyword">import</span> datetime

<span class="comment"># 处理嵌套的复杂数据</span>
complex_data = {
    <span class="string">"company"</span>: <span class="string">"科技公司"</span>,
    <span class="string">"employees"</span>: [
        {
            <span class="string">"id"</span>: <span class="number">1</span>,
            <span class="string">"name"</span>: <span class="string">"张三"</span>,
            <span class="string">"department"</span>: <span class="string">"开发部"</span>,
            <span class="string">"skills"</span>: [<span class="string">"Python"</span>, <span class="string">"JavaScript"</span>, <span class="string">"SQL"</span>],
            <span class="string">"projects"</span>: {
                <span class="string">"current"</span>: <span class="string">"Web应用开发"</span>,
                <span class="string">"completed"</span>: [<span class="string">"数据分析系统"</span>, <span class="string">"API接口"</span>]
            }
        }
    ]
}

<span class="comment"># 自定义JSON编码器</span>
<span class="keyword">class</span> <span class="function">DateTimeEncoder</span>(json.JSONEncoder):
    <span class="keyword">def</span> <span class="function">default</span>(self, obj):
        <span class="keyword">if</span> <span class="function">isinstance</span>(obj, datetime):
            <span class="keyword">return</span> obj.<span class="function">isoformat</span>()
        <span class="keyword">return</span> <span class="function">super</span>().<span class="function">default</span>(obj)

<span class="comment"># 包含日期时间的数据</span>
data_with_datetime = {
    <span class="string">"event"</span>: <span class="string">"会议"</span>,
    <span class="string">"timestamp"</span>: <span class="function">datetime</span>.<span class="function">now</span>()
}

json_str = json.<span class="function">dumps</span>(data_with_datetime, cls=DateTimeEncoder, 
                     ensure_ascii=<span class="keyword">False</span>, indent=<span class="number">2</span>)
<span class="function">print</span>(json_str)</div>
            </div>
            
            <h2><span class="emoji">🔍</span>数据验证和处理</h2>
            <div class="code-container">
                <div class="code"><span class="comment"># JSON数据验证</span>
<span class="keyword">def</span> <span class="function">validate_user_data</span>(json_data):
    required_fields = [<span class="string">"name"</span>, <span class="string">"age"</span>, <span class="string">"email"</span>]
    
    <span class="keyword">for</span> field <span class="keyword">in</span> required_fields:
        <span class="keyword">if</span> field <span class="keyword">not</span> <span class="keyword">in</span> json_data:
            <span class="keyword">return</span> <span class="keyword">False</span>, <span class="string">f"缺少必需字段: {field}"</span>
    
    <span class="keyword">if</span> <span class="keyword">not</span> <span class="function">isinstance</span>(json_data[<span class="string">"age"</span>], <span class="function">int</span>) <span class="keyword">or</span> json_data[<span class="string">"age"</span>] < <span class="number">0</span>:
        <span class="keyword">return</span> <span class="keyword">False</span>, <span class="string">"年龄必须是非负整数"</span>
    
    <span class="keyword">return</span> <span class="keyword">True</span>, <span class="string">"数据有效"</span></div>
            </div>
        </div>

        <!-- 第18页：文件格式转换 -->
        <div class="slide">
            <h1><span class="emoji">🔄</span>CSV与JSON格式转换</h1>
            <h2><span class="emoji">📊➡️🌐</span>CSV转JSON</h2>
            <div class="code-container">
                <div class="code"><span class="keyword">import</span> csv
<span class="keyword">import</span> json

<span class="comment"># CSV转JSON</span>
<span class="keyword">def</span> <span class="function">csv_to_json</span>(csv_file, json_file):
    data = []
    
    <span class="keyword">with</span> <span class="function">open</span>(csv_file, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
        csv_reader = csv.<span class="function">DictReader</span>(file)
        <span class="keyword">for</span> row <span class="keyword">in</span> csv_reader:
            data.<span class="function">append</span>(row)
    
    <span class="keyword">with</span> <span class="function">open</span>(json_file, <span class="string">'w'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
        json.<span class="function">dump</span>(data, file, ensure_ascii=<span class="keyword">False</span>, indent=<span class="number">4</span>)
    
    <span class="function">print</span>(<span class="string">f'成功将 {csv_file} 转换为 {json_file}'</span>)

<span class="comment"># 使用示例</span>
<span class="function">csv_to_json</span>(<span class="string">'students.csv'</span>, <span class="string">'students.json'</span>)</div>
            </div>
            
            <h2><span class="emoji">🌐➡️📊</span>JSON转CSV</h2>
            <div class="code-container">
                <div class="code"><span class="comment"># JSON转CSV</span>
<span class="keyword">def</span> <span class="function">json_to_csv</span>(json_file, csv_file):
    <span class="keyword">with</span> <span class="function">open</span>(json_file, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
        data = json.<span class="function">load</span>(file)
    
    <span class="keyword">if</span> <span class="keyword">not</span> data:
        <span class="function">print</span>(<span class="string">'JSON文件为空'</span>)
        <span class="keyword">return</span>
    
    <span class="comment"># 获取字段名</span>
    fieldnames = data[<span class="number">0</span>].<span class="function">keys</span>()
    
    <span class="keyword">with</span> <span class="function">open</span>(csv_file, <span class="string">'w'</span>, newline=<span class="string">''</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
        csv_writer = csv.<span class="function">DictWriter</span>(file, fieldnames=fieldnames)
        csv_writer.<span class="function">writeheader</span>()
        csv_writer.<span class="function">writerows</span>(data)
    
    <span class="function">print</span>(<span class="string">f'成功将 {json_file} 转换为 {csv_file}'</span>)

<span class="comment"># 使用示例</span>
<span class="function">json_to_csv</span>(<span class="string">'students.json'</span>, <span class="string">'students_from_json.csv'</span>)</div>
            </div>
        </div>

        <!-- 第19页：实际应用案例1 -->
        <div class="slide">
            <h1><span class="emoji">💼</span>实际应用案例：学生成绩管理系统</h1>
            <h2><span class="emoji">🎯</span>项目需求</h2>
            <ul>
                <li><span class="emoji">📊</span>读取学生成绩CSV文件</li>
                <li><span class="emoji">📈</span>计算平均分、最高分、最低分</li>
                <li><span class="emoji">🏆</span>生成成绩报告JSON文件</li>
                <li><span class="emoji">📋</span>导出优秀学生名单</li>
            </ul>
            
            <div class="code-container">
                <div class="code"><span class="keyword">import</span> csv
<span class="keyword">import</span> json
<span class="keyword">from</span> datetime <span class="keyword">import</span> datetime

<span class="keyword">class</span> <span class="function">StudentGradeManager</span>:
    <span class="keyword">def</span> <span class="function">__init__</span>(self, csv_file):
        self.csv_file = csv_file
        self.students = []
        self.<span class="function">load_data</span>()
    
    <span class="keyword">def</span> <span class="function">load_data</span>(self):
        <span class="keyword">try</span>:
            <span class="keyword">with</span> <span class="function">open</span>(self.csv_file, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
                csv_reader = csv.<span class="function">DictReader</span>(file)
                <span class="keyword">for</span> row <span class="keyword">in</span> csv_reader:
                    row[<span class="string">'成绩'</span>] = <span class="function">float</span>(row[<span class="string">'成绩'</span>])
                    self.students.<span class="function">append</span>(row)
            <span class="function">print</span>(<span class="string">f'成功加载 {len(self.students)} 条学生记录'</span>)
        <span class="keyword">except</span> Exception <span class="keyword">as</span> e:
            <span class="function">print</span>(<span class="string">f'加载数据失败: {e}'</span>)
    
    <span class="keyword">def</span> <span class="function">calculate_statistics</span>(self):
        <span class="keyword">if</span> <span class="keyword">not</span> self.students:
            <span class="keyword">return</span> <span class="keyword">None</span>
        
        scores = [student[<span class="string">'成绩'</span>] <span class="keyword">for</span> student <span class="keyword">in</span> self.students]
        
        stats = {
            <span class="string">'总人数'</span>: <span class="function">len</span>(scores),
            <span class="string">'平均分'</span>: <span class="function">round</span>(<span class="function">sum</span>(scores) / <span class="function">len</span>(scores), <span class="number">2</span>),
            <span class="string">'最高分'</span>: <span class="function">max</span>(scores),
            <span class="string">'最低分'</span>: <span class="function">min</span>(scores),
            <span class="string">'及格人数'</span>: <span class="function">len</span>([s <span class="keyword">for</span> s <span class="keyword">in</span> scores <span class="keyword">if</span> s >= <span class="number">60</span>]),
            <span class="string">'优秀人数'</span>: <span class="function">len</span>([s <span class="keyword">for</span> s <span class="keyword">in</span> scores <span class="keyword">if</span> s >= <span class="number">90</span>])
        }
        
        <span class="keyword">return</span> stats</div>
            </div>
        </div>

        <!-- 第20页：实际应用案例2 -->
        <div class="slide">
            <h1><span class="emoji">📊</span>学生成绩管理系统（续）</h1>
            <div class="code-container">
                <div class="code">    <span class="keyword">def</span> <span class="function">get_excellent_students</span>(self, threshold=<span class="number">90</span>):
        excellent = [student <span class="keyword">for</span> student <span class="keyword">in</span> self.students 
                    <span class="keyword">if</span> student[<span class="string">'成绩'</span>] >= threshold]
        <span class="keyword">return</span> <span class="function">sorted</span>(excellent, key=<span class="keyword">lambda</span> x: x[<span class="string">'成绩'</span>], reverse=<span class="keyword">True</span>)
    
    <span class="keyword">def</span> <span class="function">generate_report</span>(self, output_file):
        stats = self.<span class="function">calculate_statistics</span>()
        excellent_students = self.<span class="function">get_excellent_students</span>()
        
        report = {
            <span class="string">'生成时间'</span>: <span class="function">datetime</span>.<span class="function">now</span>().<span class="function">strftime</span>(<span class="string">'%Y-%m-%d %H:%M:%S'</span>),
            <span class="string">'统计信息'</span>: stats,
            <span class="string">'优秀学生'</span>: excellent_students
        }
        
        <span class="keyword">with</span> <span class="function">open</span>(output_file, <span class="string">'w'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
            json.<span class="function">dump</span>(report, file, ensure_ascii=<span class="keyword">False</span>, indent=<span class="number">4</span>)
        
        <span class="function">print</span>(<span class="string">f'成绩报告已生成: {output_file}'</span>)
    
    <span class="keyword">def</span> <span class="function">export_excellent_csv</span>(self, output_file):
        excellent_students = self.<span class="function">get_excellent_students</span>()
        
        <span class="keyword">if</span> <span class="keyword">not</span> excellent_students:
            <span class="function">print</span>(<span class="string">'没有优秀学生'</span>)
            <span class="keyword">return</span>
        
        fieldnames = excellent_students[<span class="number">0</span>].<span class="function">keys</span>()
        
        <span class="keyword">with</span> <span class="function">open</span>(output_file, <span class="string">'w'</span>, newline=<span class="string">''</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
            csv_writer = csv.<span class="function">DictWriter</span>(file, fieldnames=fieldnames)
            csv_writer.<span class="function">writeheader</span>()
            csv_writer.<span class="function">writerows</span>(excellent_students)
        
        <span class="function">print</span>(<span class="string">f'优秀学生名单已导出: {output_file}'</span>)

<span class="comment"># 使用示例</span>
manager = <span class="function">StudentGradeManager</span>(<span class="string">'students.csv'</span>)
manager.<span class="function">generate_report</span>(<span class="string">'grade_report.json'</span>)
manager.<span class="function">export_excellent_csv</span>(<span class="string">'excellent_students.csv'</span>)</div>
            </div>
            
            <h2><span class="emoji">✨</span>项目亮点</h2>
            <ul>
                <li><span class="emoji">🔧</span>面向对象设计，代码结构清晰</li>
                <li><span class="emoji">⚠️</span>完善的异常处理机制</li>
                <li><span class="emoji">📊</span>自动统计分析功能</li>
                <li><span class="emoji">📁</span>支持多种格式输出</li>
            </ul>
        </div>

        <!-- 第21页：性能优化技巧 -->
        <div class="slide">
            <h1><span class="emoji">⚡</span>文件处理性能优化</h1>
            <h2><span class="emoji">🚀</span>大文件处理优化</h2>
            <div class="code-container">
                <div class="code"><span class="keyword">import</span> csv
<span class="keyword">import</span> json
<span class="keyword">from</span> itertools <span class="keyword">import</span> islice

<span class="comment"># 分批处理大文件</span>
<span class="keyword">def</span> <span class="function">process_large_csv</span>(filename, batch_size=<span class="number">1000</span>):
    <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
        csv_reader = csv.<span class="function">DictReader</span>(file)
        
        <span class="keyword">while</span> <span class="keyword">True</span>:
            batch = <span class="function">list</span>(<span class="function">islice</span>(csv_reader, batch_size))
            <span class="keyword">if</span> <span class="keyword">not</span> batch:
                <span class="keyword">break</span>
            
            <span class="comment"># 处理当前批次</span>
            <span class="function">process_batch</span>(batch)
            <span class="function">print</span>(<span class="string">f'已处理 {len(batch)} 条记录'</span>)

<span class="keyword">def</span> <span class="function">process_batch</span>(batch):
    <span class="comment"># 对批次数据进行处理</span>
    <span class="keyword">for</span> row <span class="keyword">in</span> batch:
        <span class="comment"># 数据处理逻辑</span>
        <span class="keyword">pass</span>

<span class="comment"># 内存友好的JSON流式处理</span>
<span class="keyword">def</span> <span class="function">stream_json_array</span>(filename):
    <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
        data = json.<span class="function">load</span>(file)
        
        <span class="comment"># 逐个处理数组元素</span>
        <span class="keyword">for</span> item <span class="keyword">in</span> data:
            <span class="keyword">yield</span> item  <span class="comment"># 使用生成器节省内存</span>

<span class="comment"># 使用生成器处理大数据</span>
<span class="keyword">for</span> item <span class="keyword">in</span> <span class="function">stream_json_array</span>(<span class="string">'large_data.json'</span>):
    <span class="comment"># 处理单个项目</span>
    <span class="function">print</span>(item)</div>
            </div>
            
            <h2><span class="emoji">💡</span>性能优化建议</h2>
            <ul>
                <li><span class="emoji">📦</span>分批处理大文件，避免内存溢出</li>
                <li><span class="emoji">🔄</span>使用生成器进行流式处理</li>
                <li><span class="emoji">⚡</span>选择合适的数据结构</li>
                <li><span class="emoji">🗜️</span>考虑数据压缩和缓存</li>
            </ul>
        </div>

        <!-- 第22页：错误处理最佳实践 -->
        <div class="slide">
            <h1><span class="emoji">🛡️</span>错误处理最佳实践</h1>
            <h2><span class="emoji">🔧</span>健壮的文件处理函数</h2>
            <div class="code-container">
                <div class="code"><span class="keyword">import</span> csv
<span class="keyword">import</span> json
<span class="keyword">import</span> logging
<span class="keyword">from</span> pathlib <span class="keyword">import</span> Path

<span class="comment"># 配置日志</span>
logging.<span class="function">basicConfig</span>(level=logging.INFO, 
                    format=<span class="string">'%(asctime)s - %(levelname)s - %(message)s'</span>)

<span class="keyword">def</span> <span class="function">safe_file_operation</span>(operation, filename, *args, **kwargs):
    <span class="string">"""安全的文件操作包装器"""</span>
    <span class="keyword">try</span>:
        <span class="comment"># 检查文件是否存在</span>
        file_path = <span class="function">Path</span>(filename)
        <span class="keyword">if</span> operation <span class="keyword">in</span> [<span class="string">'read'</span>, <span class="string">'r'</span>] <span class="keyword">and</span> <span class="keyword">not</span> file_path.<span class="function">exists</span>():
            logging.<span class="function">error</span>(<span class="string">f'文件不存在: {filename}'</span>)
            <span class="keyword">return</span> <span class="keyword">None</span>
        
        <span class="comment"># 检查文件权限</span>
        <span class="keyword">if</span> operation <span class="keyword">in</span> [<span class="string">'read'</span>, <span class="string">'r'</span>] <span class="keyword">and</span> <span class="keyword">not</span> file_path.<span class="function">is_file</span>():
            logging.<span class="function">error</span>(<span class="string">f'不是有效文件: {filename}'</span>)
            <span class="keyword">return</span> <span class="keyword">None</span>
        
        <span class="comment"># 执行文件操作</span>
        <span class="keyword">if</span> operation == <span class="string">'read_csv'</span>:
            <span class="keyword">return</span> <span class="function">_read_csv_safe</span>(filename, *args, **kwargs)
        <span class="keyword">elif</span> operation == <span class="string">'read_json'</span>:
            <span class="keyword">return</span> <span class="function">_read_json_safe</span>(filename, *args, **kwargs)
        <span class="keyword">elif</span> operation == <span class="string">'write_csv'</span>:
            <span class="keyword">return</span> <span class="function">_write_csv_safe</span>(filename, *args, **kwargs)
        <span class="keyword">elif</span> operation == <span class="string">'write_json'</span>:
            <span class="keyword">return</span> <span class="function">_write_json_safe</span>(filename, *args, **kwargs)
        
    <span class="keyword">except</span> Exception <span class="keyword">as</span> e:
        logging.<span class="function">error</span>(<span class="string">f'文件操作失败 {filename}: {e}'</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>

<span class="keyword">def</span> <span class="function">_read_csv_safe</span>(filename, encoding=<span class="string">'utf-8'</span>):
    <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">'r'</span>, encoding=encoding) <span class="keyword">as</span> file:
        <span class="keyword">return</span> <span class="function">list</span>(csv.<span class="function">DictReader</span>(file))

<span class="keyword">def</span> <span class="function">_read_json_safe</span>(filename, encoding=<span class="string">'utf-8'</span>):
    <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">'r'</span>, encoding=encoding) <span class="keyword">as</span> file:
        <span class="keyword">return</span> json.<span class="function">load</span>(file)</div>
            </div>
        </div>

        <!-- 第23页：AI辅助编程工具介绍 -->
        <div class="slide">
            <h1><span class="emoji">🤖</span>AI辅助编程工具</h1>
            <h2><span class="emoji">🛠️</span>主流AI编程助手</h2>
            <ul>
                <li><span class="emoji">🔥</span><strong>GitHub Copilot</strong> - 代码自动补全和生成</li>
                <li><span class="emoji">💬</span><strong>ChatGPT</strong> - 代码解释和问题解答</li>
                <li><span class="emoji">🧠</span><strong>Claude</strong> - 代码分析和优化建议</li>
                <li><span class="emoji">⚡</span><strong>Cursor</strong> - AI驱动的代码编辑器</li>
                <li><span class="emoji">🎯</span><strong>Tabnine</strong> - 智能代码补全</li>
            </ul>
            
            <h2><span class="emoji">✨</span>AI工具的优势</h2>
            <ul>
                <li><span class="emoji">🚀</span>提高编程效率，减少重复工作</li>
                <li><span class="emoji">📚</span>学习最佳实践和代码模式</li>
                <li><span class="emoji">🐛</span>快速发现和修复代码错误</li>
                <li><span class="emoji">💡</span>获得代码优化建议</li>
                <li><span class="emoji">📖</span>理解复杂代码逻辑</li>
            </ul>
            
            <h2><span class="emoji">⚠️</span>使用注意事项</h2>
            <ul>
                <li><span class="emoji">🔍</span>始终验证AI生成的代码</li>
                <li><span class="emoji">🧪</span>进行充分的测试</li>
                <li><span class="emoji">📝</span>理解代码逻辑，不要盲目复制</li>
                <li><span class="emoji">🔒</span>注意数据安全和隐私保护</li>
            </ul>
        </div>

        <!-- 第24页：AI提示词模板 -->
        <div class="slide">
            <h1><span class="emoji">📝</span>AI编程提示词模板</h1>
            <h2><span class="emoji">🎯</span>文件处理相关提示词</h2>
            
            <div class="code-container">
                <div class="code"><span class="comment"># 基础代码生成模板</span>
<span class="string">"请帮我写一个Python函数，用于[具体功能描述]。
要求：
1. 使用[指定库/模块]
2. 包含错误处理
3. 添加详细注释
4. 提供使用示例"</span>

<span class="comment"># CSV处理专用模板</span>
<span class="string">"我需要处理一个CSV文件，包含以下列：[列名列表]。
请帮我写代码实现：
1. 读取CSV文件
2. [具体处理需求]
3. 输出结果到[格式]
请包含异常处理和数据验证。"</span>

<span class="comment"># JSON处理专用模板</span>
<span class="string">"请帮我创建一个JSON数据处理脚本：
- 输入：[数据结构描述]
- 处理：[具体操作]
- 输出：[期望格式]
要求支持嵌套结构和中文编码。"</span>

<span class="comment"># 代码优化模板</span>
<span class="string">"请分析以下代码并提供优化建议：
[粘贴代码]

请从以下角度分析：
1. 性能优化
2. 代码可读性
3. 错误处理
4. 最佳实践"</span>

<span class="comment"># 错误调试模板</span>
<span class="string">"我的代码遇到了错误：
错误信息：[错误信息]
代码：[相关代码]

请帮我：
1. 分析错误原因
2. 提供解决方案
3. 给出修改后的代码"</span></div>
            </div>
        </div>

        <!-- 第25页：学习路径规划 -->
        <div class="slide">
            <h1><span class="emoji">🗺️</span>Python文件处理学习路径</h1>
            
            <h2><span class="emoji">📚</span>初级阶段（1-2周）</h2>
            <ul>
                <li><span class="emoji">📖</span>掌握基本文件操作（open, read, write）</li>
                <li><span class="emoji">📄</span>学习文本文件处理</li>
                <li><span class="emoji">🔧</span>理解文件路径和编码</li>
                <li><span class="emoji">⚠️</span>学习基础异常处理</li>
            </ul>
            
            <h2><span class="emoji">📊</span>中级阶段（2-3周）</h2>
            <ul>
                <li><span class="emoji">📋</span>掌握CSV文件读写操作</li>
                <li><span class="emoji">🔄</span>学习JSON数据处理</li>
                <li><span class="emoji">📈</span>数据格式转换技巧</li>
                <li><span class="emoji">🛡️</span>完善错误处理机制</li>
            </ul>
            
            <h2><span class="emoji">🚀</span>高级阶段（3-4周）</h2>
            <ul>
                <li><span class="emoji">⚡</span>大文件处理和性能优化</li>
                <li><span class="emoji">🏗️</span>设计文件处理框架</li>
                <li><span class="emoji">🔍</span>数据验证和清洗</li>
                <li><span class="emoji">📦</span>集成第三方库（pandas等）</li>
            </ul>
            
            <h2><span class="emoji">💼</span>实战项目</h2>
            <ul>
                <li><span class="emoji">📊</span>数据分析报告生成器</li>
                <li><span class="emoji">🔄</span>文件格式转换工具</li>
                <li><span class="emoji">📈</span>日志分析系统</li>
                <li><span class="emoji">🗃️</span>数据备份和同步工具</li>
            </ul>
        </div>

        <!-- 第26页：实用工具库推荐 -->
        <div class="slide">
            <h1><span class="emoji">🛠️</span>实用工具库推荐</h1>
            
            <h2><span class="emoji">📊</span>数据处理库</h2>
            <div class="code-container">
                <div class="code"><span class="comment"># pandas - 强大的数据分析库</span>
<span class="keyword">import</span> pandas <span class="keyword">as</span> pd

<span class="comment"># 读取CSV文件</span>
df = pd.<span class="function">read_csv</span>(<span class="string">'data.csv'</span>)

<span class="comment"># 数据处理</span>
result = df.<span class="function">groupby</span>(<span class="string">'category'</span>).<span class="function">mean</span>()

<span class="comment"># 导出JSON</span>
result.<span class="function">to_json</span>(<span class="string">'result.json'</span>, orient=<span class="string">'records'</span>)

<span class="comment"># openpyxl - Excel文件处理</span>
<span class="keyword">from</span> openpyxl <span class="keyword">import</span> Workbook, load_workbook

wb = <span class="function">Workbook</span>()
ws = wb.<span class="function">active</span>
ws[<span class="string">'A1'</span>] = <span class="string">'Hello World'</span>
wb.<span class="function">save</span>(<span class="string">'example.xlsx'</span>)

<span class="comment"># pathlib - 现代路径处理</span>
<span class="keyword">from</span> pathlib <span class="keyword">import</span> Path

file_path = <span class="function">Path</span>(<span class="string">'data/file.txt'</span>)
<span class="keyword">if</span> file_path.<span class="function">exists</span>():
    content = file_path.<span class="function">read_text</span>(encoding=<span class="string">'utf-8'</span>)</div>
            </div>
            
            <h2><span class="emoji">🔧</span>配置文件处理</h2>
            <div class="code-container">
                <div class="code"><span class="comment"># configparser - INI配置文件</span>
<span class="keyword">import</span> configparser

config = configparser.<span class="function">ConfigParser</span>()
config.<span class="function">read</span>(<span class="string">'config.ini'</span>)
database_url = config[<span class="string">'database'</span>][<span class="string">'url'</span>]

<span class="comment"># PyYAML - YAML文件处理</span>
<span class="keyword">import</span> yaml

<span class="keyword">with</span> <span class="function">open</span>(<span class="string">'config.yaml'</span>, <span class="string">'r'</span>) <span class="keyword">as</span> file:
    config = yaml.<span class="function">safe_load</span>(file)</div>
            </div>
        </div>

        <!-- 第27页：调试技巧 -->
        <div class="slide">
            <h1><span class="emoji">🐛</span>文件处理调试技巧</h1>
            
            <h2><span class="emoji">🔍</span>常见问题诊断</h2>
            <div class="code-container">
                <div class="code"><span class="keyword">import</span> os
<span class="keyword">import</span> sys
<span class="keyword">from</span> pathlib <span class="keyword">import</span> Path

<span class="keyword">def</span> <span class="function">debug_file_issues</span>(filename):
    <span class="string">"""文件问题诊断工具"""</span>
    <span class="function">print</span>(<span class="string">f'调试文件: {filename}'</span>)
    <span class="function">print</span>(<span class="string">'-' * 40</span>)
    
    <span class="comment"># 检查文件是否存在</span>
    <span class="keyword">if</span> <span class="keyword">not</span> os.path.<span class="function">exists</span>(filename):
        <span class="function">print</span>(<span class="string">'❌ 文件不存在'</span>)
        <span class="function">print</span>(<span class="string">f'当前工作目录: {os.getcwd()}'</span>)
        <span class="keyword">return</span>
    
    <span class="comment"># 检查文件权限</span>
    <span class="keyword">if</span> <span class="keyword">not</span> os.access(filename, os.R_OK):
        <span class="function">print</span>(<span class="string">'❌ 文件不可读'</span>)
    <span class="keyword">else</span>:
        <span class="function">print</span>(<span class="string">'✅ 文件可读'</span>)
    
    <span class="comment"># 检查文件大小</span>
    file_size = os.path.<span class="function">getsize</span>(filename)
    <span class="function">print</span>(<span class="string">f'📏 文件大小: {file_size} 字节'</span>)
    
    <span class="comment"># 检测文件编码</span>
    <span class="keyword">try</span>:
        <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> f:
            f.<span class="function">read</span>(<span class="number">100</span>)
        <span class="function">print</span>(<span class="string">'✅ UTF-8编码正常'</span>)
    <span class="keyword">except</span> UnicodeDecodeError:
        <span class="function">print</span>(<span class="string">'⚠️ 可能不是UTF-8编码'</span>)
        <span class="comment"># 尝试其他编码</span>
        <span class="keyword">for</span> encoding <span class="keyword">in</span> [<span class="string">'gbk'</span>, <span class="string">'gb2312'</span>, <span class="string">'latin-1'</span>]:
            <span class="keyword">try</span>:
                <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">'r'</span>, encoding=encoding) <span class="keyword">as</span> f:
                    f.<span class="function">read</span>(<span class="number">100</span>)
                <span class="function">print</span>(<span class="string">f'✅ {encoding}编码可用'</span>)
                <span class="keyword">break</span>
            <span class="keyword">except</span>:
                <span class="keyword">continue</span>

<span class="comment"># 使用示例</span>
<span class="function">debug_file_issues</span>(<span class="string">'problematic_file.csv'</span>)</div>
            </div>
            
            <h2><span class="emoji">💡</span>调试建议</h2>
            <ul>
                <li><span class="emoji">📍</span>使用绝对路径避免路径问题</li>
                <li><span class="emoji">🔤</span>明确指定文件编码</li>
                <li><span class="emoji">📊</span>先读取小部分数据进行测试</li>
                <li><span class="emoji">📝</span>添加详细的日志记录</li>
            </ul>
        </div>

        <!-- 第28页：性能测试 -->
        <div class="slide">
            <h1><span class="emoji">⏱️</span>文件处理性能测试</h1>
            
            <div class="code-container">
                <div class="code"><span class="keyword">import</span> time
<span class="keyword">import</span> csv
<span class="keyword">import</span> json
<span class="keyword">import</span> pandas <span class="keyword">as</span> pd
<span class="keyword">from</span> functools <span class="keyword">import</span> wraps

<span class="keyword">def</span> <span class="function">timing_decorator</span>(func):
    <span class="string">"""性能测试装饰器"""</span>
    <span class="keyword">@wraps</span>(func)
    <span class="keyword">def</span> <span class="function">wrapper</span>(*args, **kwargs):
        start_time = time.<span class="function">time</span>()
        result = <span class="function">func</span>(*args, **kwargs)
        end_time = time.<span class="function">time</span>()
        <span class="function">print</span>(<span class="string">f'{func.__name__} 执行时间: {end_time - start_time:.4f}秒'</span>)
        <span class="keyword">return</span> result
    <span class="keyword">return</span> wrapper

<span class="keyword">@timing_decorator</span>
<span class="keyword">def</span> <span class="function">read_csv_standard</span>(filename):
    <span class="string">"""标准CSV读取"""</span>
    data = []
    <span class="keyword">with</span> <span class="function">open</span>(filename, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> file:
        csv_reader = csv.<span class="function">DictReader</span>(file)
        <span class="keyword">for</span> row <span class="keyword">in</span> csv_reader:
            data.<span class="function">append</span>(row)
    <span class="keyword">return</span> data

<span class="keyword">@timing_decorator</span>
<span class="keyword">def</span> <span class="function">read_csv_pandas</span>(filename):
    <span class="string">"""Pandas CSV读取"""</span>
    <span class="keyword">return</span> pd.<span class="function">read_csv</span>(filename).<span class="function">to_dict</span>(<span class="string">'records'</span>)

<span class="keyword">def</span> <span class="function">performance_comparison</span>(filename):
    <span class="string">"""性能对比测试"""</span>
    <span class="function">print</span>(<span class="string">'=== 性能对比测试 ==='</span>)
    
    <span class="comment"># 测试标准库</span>
    data1 = <span class="function">read_csv_standard</span>(filename)
    
    <span class="comment"># 测试pandas</span>
    data2 = <span class="function">read_csv_pandas</span>(filename)
    
    <span class="function">print</span>(<span class="string">f'数据量: {len(data1)} 条记录'</span>)

<span class="comment"># 内存使用监控</span>
<span class="keyword">import</span> psutil
<span class="keyword">import</span> os

<span class="keyword">def</span> <span class="function">monitor_memory</span>(func):
    <span class="string">"""内存使用监控装饰器"""</span>
    <span class="keyword">@wraps</span>(func)
    <span class="keyword">def</span> <span class="function">wrapper</span>(*args, **kwargs):
        process = psutil.<span class="function">Process</span>(os.<span class="function">getpid</span>())
        mem_before = process.<span class="function">memory_info</span>().rss / <span class="number">1024</span> / <span class="number">1024</span>  <span class="comment"># MB</span>
        
        result = <span class="function">func</span>(*args, **kwargs)
        
        mem_after = process.<span class="function">memory_info</span>().rss / <span class="number">1024</span> / <span class="number">1024</span>  <span class="comment"># MB</span>
        <span class="function">print</span>(<span class="string">f'{func.__name__} 内存使用: {mem_after - mem_before:.2f}MB'</span>)
        
        <span class="keyword">return</span> result
    <span class="keyword">return</span> wrapper</div>
            </div>
        </div>

        <!-- 第29页：项目实战总结 -->
        <div class="slide">
            <h1><span class="emoji">🎯</span>项目实战总结</h1>
            
            <h2><span class="emoji">📋</span>完整项目开发流程</h2>
            <div class="code-container">
                <div class="code"><span class="comment"># 项目结构示例</span>
project/
├── data/
│   ├── input/          <span class="comment"># 输入数据文件</span>
│   ├── output/         <span class="comment"># 输出结果文件</span>
│   └── temp/           <span class="comment"># 临时文件</span>
├── src/
│   ├── __init__.py
│   ├── file_handler.py <span class="comment"># 文件处理核心模块</span>
│   ├── data_processor.py <span class="comment"># 数据处理模块</span>
│   ├── utils.py        <span class="comment"># 工具函数</span>
│   └── config.py       <span class="comment"># 配置文件</span>
├── tests/
│   ├── test_file_handler.py
│   └── test_data_processor.py
├── requirements.txt    <span class="comment"># 依赖包列表</span>
├── README.md          <span class="comment"># 项目说明</span>
└── main.py            <span class="comment"># 主程序入口</span>

<span class="comment"># 主程序示例</span>
<span class="keyword">from</span> src.file_handler <span class="keyword">import</span> FileHandler
<span class="keyword">from</span> src.data_processor <span class="keyword">import</span> DataProcessor
<span class="keyword">from</span> src.config <span class="keyword">import</span> Config

<span class="keyword">def</span> <span class="function">main</span>():
    <span class="comment"># 初始化配置</span>
    config = <span class="function">Config</span>()
    
    <span class="comment"># 创建处理器</span>
    file_handler = <span class="function">FileHandler</span>(config)
    data_processor = <span class="function">DataProcessor</span>(config)
    
    <span class="comment"># 处理流程</span>
    <span class="keyword">try</span>:
        <span class="comment"># 1. 读取数据</span>
        data = file_handler.<span class="function">read_csv</span>(<span class="string">'input/data.csv'</span>)
        
        <span class="comment"># 2. 数据处理</span>
        processed_data = data_processor.<span class="function">process</span>(data)
        
        <span class="comment"># 3. 保存结果</span>
        file_handler.<span class="function">write_json</span>(<span class="string">'output/result.json'</span>, processed_data)
        
        <span class="function">print</span>(<span class="string">'✅ 处理完成'</span>)
        
    <span class="keyword">except</span> Exception <span class="keyword">as</span> e:
        <span class="function">print</span>(<span class="string">f'❌ 处理失败: {e}'</span>)

<span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:
    <span class="function">main</span>()</div>
            </div>
            
            <h2><span class="emoji">🔑</span>关键要点回顾</h2>
            <ul>
                <li><span class="emoji">📁</span>合理的项目结构组织</li>
                <li><span class="emoji">🛡️</span>完善的异常处理机制</li>
                <li><span class="emoji">🧪</span>充分的单元测试覆盖</li>
                <li><span class="emoji">📝</span>清晰的代码文档</li>
                <li><span class="emoji">⚡</span>性能优化考虑</li>
            </ul>
        </div>

        <!-- 第30页：总结与展望 -->
        <div class="slide">
            <h1><span class="emoji">🎉</span>课程总结与展望</h1>
            
            <h2><span class="emoji">📚</span>我们学到了什么</h2>
            <ul>
                <li><span class="emoji">📄</span>Python基础文件操作技能</li>
                <li><span class="emoji">📊</span>CSV和JSON文件处理专业技巧</li>
                <li><span class="emoji">🔧</span>错误处理和调试方法</li>
                <li><span class="emoji">⚡</span>性能优化策略</li>
                <li><span class="emoji">🤖</span>AI辅助编程工具使用</li>
                <li><span class="emoji">💼</span>实际项目开发经验</li>
            </ul>
            
            <h2><span class="emoji">🚀</span>下一步学习建议</h2>
            <ul>
                <li><span class="emoji">🐼</span>深入学习pandas数据分析</li>
                <li><span class="emoji">🗄️</span>掌握数据库操作技能</li>
                <li><span class="emoji">🌐</span>学习Web API数据处理</li>
                <li><span class="emoji">☁️</span>了解云端数据处理服务</li>
                <li><span class="emoji">🔄</span>学习数据流处理框架</li>
            </ul>
            
            <h2><span class="emoji">💡</span>持续学习资源</h2>
            <ul>
                <li><span class="emoji">📖</span>官方文档：Python.org</li>
                <li><span class="emoji">💻</span>在线练习：LeetCode、HackerRank</li>
                <li><span class="emoji">🎓</span>在线课程：Coursera、edX</li>
                <li><span class="emoji">👥</span>社区交流：Stack Overflow、GitHub</li>
                <li><span class="emoji">🤖</span>AI助手：ChatGPT、Claude、Copilot</li>
            </ul>
            
            <div style="text-align: center; margin-top: 40px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 15px; color: white;">
                <h2><span class="emoji">🎯</span>记住：实践是最好的老师！</h2>
                <p style="font-size: 18px; margin: 10px 0;">多写代码，多做项目，多用AI工具</p>
                <p style="font-size: 16px;"><span class="emoji">💪</span>持续学习，不断进步！</p>
            </div>
        </div>

    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">上一页</button>
        <button class="nav-btn" onclick="nextSlide()">下一页</button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        
        document.getElementById('total-slides').textContent = totalSlides;
        
        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            slides[currentSlide].classList.add('prev');
            
            currentSlide = (n + totalSlides) % totalSlides;
            
            slides[currentSlide].classList.remove('prev');
            slides[currentSlide].classList.add('active');
            
            document.getElementById('current-slide').textContent = currentSlide + 1;
        }
        
        function nextSlide() {
            showSlide(currentSlide + 1);
        }
        
        function previousSlide() {
            showSlide(currentSlide - 1);
        }
        
        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                previousSlide();
            }
        });
        
        // 触摸导航
        let startX = 0;
        let endX = 0;
        
        document.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
        });
        
        document.addEventListener('touchend', function(e) {
            endX = e.changedTouches[0].clientX;
            handleSwipe();
        });
        
        function handleSwipe() {
            const threshold = 50;
            const diff = startX - endX;
            
            if (Math.abs(diff) > threshold) {
                if (diff > 0) {
                    nextSlide();
                } else {
                    previousSlide();
                }
            }
        }
    </script>
</body>
</html>