<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python异常处理与断言完整教程</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow-x: hidden;
        }

        .presentation {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100vh;
            padding: 60px;
            display: none;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            text-align: left;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            margin: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
            scroll-behavior: smooth;
        }

        .slide.active {
            display: flex;
            animation: slideIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide h1 {
            font-size: 3.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: left;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .slide h2 {
            font-size: 2.5rem;
            font-weight: 600;
            color: #34495e;
            margin-bottom: 25px;
            text-align: left;
            animation: fadeInUp 1s ease-out 0.5s both;
        }

        .slide h3 {
            font-size: 1.8rem;
            font-weight: 500;
            color: #7f8c8d;
            margin-bottom: 20px;
            text-align: left;
            animation: fadeInUp 1s ease-out 0.7s both;
        }

        .slide p, .slide li {
            font-size: 1.3rem;
            line-height: 1.8;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: left;
            animation: fadeInUp 1s ease-out 0.9s both;
        }

        .slide ul {
            margin-left: 30px;
            margin-bottom: 25px;
        }

        .emoji {
            font-size: 2.5rem;
            margin-right: 15px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .code-container {
            background: #1e1e1e;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            width: 100%;
            min-height: 200px;
            overflow-x: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: slideInLeft 1s ease-out 1.1s both;
        }

        .code {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 1.1rem;
            line-height: 1.6;
            color: #f8f8f2;
            text-align: left;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Python语法高亮 */
        .keyword { color: #66d9ef; font-weight: bold; }
        .string { color: #e6db74; }
        .comment { color: #75715e; font-style: italic; }
        .number { color: #ae81ff; }
        .function { color: #a6e22e; }
        .builtin { color: #f92672; }
        .exception { color: #f92672; font-weight: bold; }

        .navigation {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50px;
            padding: 15px 25px;
            margin: 0 10px;
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            color: #2c3e50;
            backdrop-filter: blur(10px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .highlight-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
            }
        }

        .ai-prompt {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            font-style: italic;
            animation: slideInRight 1s ease-out 1.3s both;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
            z-index: 1001;
        }
    </style>
</head>
<body>
    <div class="progress-bar" id="progressBar"></div>
    <div class="slide-counter" id="slideCounter">1 / 20</div>
    
    <div class="presentation">
        <!-- 第1页：标题页 -->
        <div class="slide active">
            <h1><span class="emoji">🐍</span>Python异常处理与断言</h1>
            <h2>完整教程与AI辅助学习指南</h2>
            <div class="highlight-box">
                <h3>🎯 学习目标</h3>
                <ul>
                    <li>掌握Python异常处理机制</li>
                    <li>理解断言的使用场景</li>
                    <li>学会使用AI工具辅助编程</li>
                    <li>提升代码质量和调试能力</li>
                </ul>
            </div>
        </div>

        <!-- 第2页：什么是异常 -->
        <div class="slide">
            <h1><span class="emoji">⚠️</span>什么是异常？</h1>
            <p>异常是程序执行过程中发生的错误事件，它会中断程序的正常执行流程。</p>
            
            <h3>🔍 常见异常类型：</h3>
            <ul>
                <li><strong>SyntaxError</strong>：语法错误</li>
                <li><strong>NameError</strong>：名称错误</li>
                <li><strong>TypeError</strong>：类型错误</li>
                <li><strong>ValueError</strong>：值错误</li>
                <li><strong>IndexError</strong>：索引错误</li>
                <li><strong>KeyError</strong>：键错误</li>
                <li><strong>FileNotFoundError</strong>：文件未找到错误</li>
            </ul>

            <div class="code-container">
                <div class="code">
<span class="comment"># 常见异常示例</span>

<span class="comment"># NameError - 使用未定义的变量</span>
<span class="keyword">print</span>(undefined_variable)  <span class="comment"># NameError</span>

<span class="comment"># TypeError - 类型不匹配</span>
result = <span class="string">"hello"</span> + <span class="number">5</span>  <span class="comment"># TypeError</span>

<span class="comment"># IndexError - 索引超出范围</span>
my_list = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>]
<span class="keyword">print</span>(my_list[<span class="number">10</span>])  <span class="comment"># IndexError</span>

<span class="comment"># ValueError - 值错误</span>
number = <span class="builtin">int</span>(<span class="string">"abc"</span>)  <span class="comment"># ValueError</span>
                </div>
            </div>
        </div>

        <!-- 第3页：try-except基础语法 -->
        <div class="slide">
            <h1><span class="emoji">🛡️</span>try-except基础语法</h1>
            <p>使用try-except语句可以捕获和处理异常，防止程序崩溃。</p>

            <h3>📝 基本语法结构：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">try</span>:
    <span class="comment"># 可能出现异常的代码</span>
    risky_code()
<span class="keyword">except</span> <span class="exception">ExceptionType</span>:
    <span class="comment"># 处理异常的代码</span>
    handle_exception()
                </div>
            </div>

            <h3>🌟 实际应用示例：</h3>
            <div class="code-container">
                <div class="code">
<span class="comment"># 处理除零错误</span>
<span class="keyword">try</span>:
    a = <span class="number">10</span>
    b = <span class="number">0</span>
    result = a / b
    <span class="keyword">print</span>(<span class="string">f"结果: {result}"</span>)
<span class="keyword">except</span> <span class="exception">ZeroDivisionError</span>:
    <span class="keyword">print</span>(<span class="string">"错误：不能除以零！"</span>)

<span class="comment"># 处理类型转换错误</span>
<span class="keyword">try</span>:
    user_input = <span class="builtin">input</span>(<span class="string">"请输入一个数字: "</span>)
    number = <span class="builtin">int</span>(user_input)
    <span class="keyword">print</span>(<span class="string">f"您输入的数字是: {number}"</span>)
<span class="keyword">except</span> <span class="exception">ValueError</span>:
    <span class="keyword">print</span>(<span class="string">"错误：请输入有效的数字！"</span>)
                </div>
            </div>
        </div>

        <!-- 第4页：多重异常处理 -->
        <div class="slide">
            <h1><span class="emoji">🎯</span>多重异常处理</h1>
            <p>一个try块可以捕获多种不同类型的异常，提供更精确的错误处理。</p>

            <h3>🔧 多个except子句：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">def</span> <span class="function">safe_divide</span>(a, b):
    <span class="keyword">try</span>:
        result = a / b
        <span class="keyword">return</span> result
    <span class="keyword">except</span> <span class="exception">ZeroDivisionError</span>:
        <span class="keyword">print</span>(<span class="string">"错误：除数不能为零"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
    <span class="keyword">except</span> <span class="exception">TypeError</span>:
        <span class="keyword">print</span>(<span class="string">"错误：参数类型不正确"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
    <span class="keyword">except</span> <span class="exception">ValueError</span>:
        <span class="keyword">print</span>(<span class="string">"错误：参数值不正确"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>

<span class="comment"># 测试函数</span>
<span class="keyword">print</span>(safe_divide(<span class="number">10</span>, <span class="number">2</span>))    <span class="comment"># 正常情况</span>
<span class="keyword">print</span>(safe_divide(<span class="number">10</span>, <span class="number">0</span>))    <span class="comment"># ZeroDivisionError</span>
<span class="keyword">print</span>(safe_divide(<span class="string">"10"</span>, <span class="number">2</span>))  <span class="comment"># TypeError</span>
                </div>
            </div>

            <h3>🎪 一次捕获多种异常：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">try</span>:
    <span class="comment"># 可能出现多种异常的代码</span>
    data = [<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>]
    index = <span class="builtin">int</span>(<span class="builtin">input</span>(<span class="string">"输入索引: "</span>))
    <span class="keyword">print</span>(data[index])
<span class="keyword">except</span> (<span class="exception">ValueError</span>, <span class="exception">IndexError</span>) <span class="keyword">as</span> e:
    <span class="keyword">print</span>(<span class="string">f"输入错误: {e}"</span>)
                </div>
            </div>
        </div>

        <!-- 第5页：else和finally子句 -->
        <div class="slide">
            <h1><span class="emoji">🔄</span>else和finally子句</h1>
            <p>else和finally子句为异常处理提供了更多的控制选项。</p>

            <h3>✅ else子句 - 无异常时执行：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">try</span>:
    number = <span class="builtin">int</span>(<span class="builtin">input</span>(<span class="string">"请输入一个数字: "</span>))
<span class="keyword">except</span> <span class="exception">ValueError</span>:
    <span class="keyword">print</span>(<span class="string">"输入的不是有效数字"</span>)
<span class="keyword">else</span>:
    <span class="keyword">print</span>(<span class="string">f"您输入的数字是: {number}"</span>)
    <span class="keyword">print</span>(<span class="string">f"数字的平方是: {number ** 2}"</span>)
                </div>
            </div>

            <h3>🔒 finally子句 - 总是执行：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">def</span> <span class="function">read_file</span>(filename):
    file = <span class="keyword">None</span>
    <span class="keyword">try</span>:
        file = <span class="builtin">open</span>(filename, <span class="string">'r'</span>)
        content = file.read()
        <span class="keyword">return</span> content
    <span class="keyword">except</span> <span class="exception">FileNotFoundError</span>:
        <span class="keyword">print</span>(<span class="string">f"文件 {filename} 不存在"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
    <span class="keyword">except</span> <span class="exception">IOError</span>:
        <span class="keyword">print</span>(<span class="string">f"读取文件 {filename} 时发生错误"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>
    <span class="keyword">else</span>:
        <span class="keyword">print</span>(<span class="string">"文件读取成功"</span>)
    <span class="keyword">finally</span>:
        <span class="keyword">if</span> file:
            file.close()
            <span class="keyword">print</span>(<span class="string">"文件已关闭"</span>)

<span class="comment"># 使用示例</span>
content = read_file(<span class="string">"example.txt"</span>)
                </div>
            </div>
        </div>

        <!-- 第6页：抛出异常 -->
        <div class="slide">
            <h1><span class="emoji">🚀</span>主动抛出异常</h1>
            <p>使用raise语句可以主动抛出异常，用于参数验证和错误控制。</p>

            <h3>⚡ raise语句基本用法：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">def</span> <span class="function">validate_age</span>(age):
    <span class="keyword">if</span> <span class="keyword">not</span> <span class="builtin">isinstance</span>(age, <span class="builtin">int</span>):
        <span class="keyword">raise</span> <span class="exception">TypeError</span>(<span class="string">"年龄必须是整数"</span>)
    
    <span class="keyword">if</span> age < <span class="number">0</span>:
        <span class="keyword">raise</span> <span class="exception">ValueError</span>(<span class="string">"年龄不能为负数"</span>)
    
    <span class="keyword">if</span> age > <span class="number">150</span>:
        <span class="keyword">raise</span> <span class="exception">ValueError</span>(<span class="string">"年龄不能超过150岁"</span>)
    
    <span class="keyword">return</span> <span class="keyword">True</span>

<span class="comment"># 测试函数</span>
<span class="keyword">try</span>:
    validate_age(<span class="number">25</span>)     <span class="comment"># 正常</span>
    validate_age(-<span class="number">5</span>)     <span class="comment"># ValueError</span>
    validate_age(<span class="string">"25"</span>)    <span class="comment"># TypeError</span>
<span class="keyword">except</span> (<span class="exception">ValueError</span>, <span class="exception">TypeError</span>) <span class="keyword">as</span> e:
    <span class="keyword">print</span>(<span class="string">f"验证失败: {e}"</span>)
                </div>
            </div>

            <h3>🔄 重新抛出异常：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">def</span> <span class="function">process_data</span>(data):
    <span class="keyword">try</span>:
        <span class="comment"># 处理数据的代码</span>
        result = data / <span class="number">0</span>  <span class="comment"># 故意制造异常</span>
        <span class="keyword">return</span> result
    <span class="keyword">except</span> <span class="exception">ZeroDivisionError</span>:
        <span class="keyword">print</span>(<span class="string">"记录错误日志..."</span>)
        <span class="keyword">raise</span>  <span class="comment"># 重新抛出异常</span>

<span class="keyword">try</span>:
    process_data(<span class="number">10</span>)
<span class="keyword">except</span> <span class="exception">ZeroDivisionError</span>:
    <span class="keyword">print</span>(<span class="string">"在上层处理异常"</span>)
                </div>
            </div>
        </div>

        <!-- 第7页：自定义异常 -->
        <div class="slide">
            <h1><span class="emoji">🎨</span>自定义异常类</h1>
            <p>创建自定义异常类可以更精确地表达特定的错误情况。</p>

            <h3>🏗️ 创建自定义异常：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">class</span> <span class="function">CustomError</span>(<span class="exception">Exception</span>):
    <span class="string">"""自定义异常基类"""\span>
    <span class="keyword">pass</span>

<span class="keyword">class</span> <span class="function">ValidationError</span>(<span class="function">CustomError</span>):
    <span class="string">"""数据验证异常"""\span>
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="builtin">self</span>, message, error_code=<span class="keyword">None</span>):
        <span class="builtin">super</span>().__init__(message)
        <span class="builtin">self</span>.error_code = error_code
        <span class="builtin">self</span>.message = message

<span class="keyword">class</span> <span class="function">BusinessLogicError</span>(<span class="function">CustomError</span>):
    <span class="string">"""业务逻辑异常"""\span>
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="builtin">self</span>, message, details=<span class="keyword">None</span>):
        <span class="builtin">super</span>().__init__(message)
        <span class="builtin">self</span>.details = details

<span class="comment"># 使用自定义异常</span>
<span class="keyword">def</span> <span class="function">validate_email</span>(email):
    <span class="keyword">if</span> <span class="keyword">not</span> email:
        <span class="keyword">raise</span> <span class="function">ValidationError</span>(<span class="string">"邮箱不能为空"</span>, error_code=<span class="string">"EMPTY_EMAIL"</span>)
    
    <span class="keyword">if</span> <span class="string">"@"</span> <span class="keyword">not</span> <span class="keyword">in</span> email:
        <span class="keyword">raise</span> <span class="function">ValidationError</span>(<span class="string">"邮箱格式不正确"</span>, error_code=<span class="string">"INVALID_FORMAT"</span>)
    
    <span class="keyword">return</span> <span class="keyword">True</span>
                </div>
            </div>

            <h3>🎯 使用自定义异常：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">try</span>:
    validate_email(<span class="string">""</span>)
<span class="keyword">except</span> <span class="function">ValidationError</span> <span class="keyword">as</span> e:
    <span class="keyword">print</span>(<span class="string">f"验证错误: {e.message}"</span>)
    <span class="keyword">print</span>(<span class="string">f"错误代码: {e.error_code}"</span>)

<span class="keyword">try</span>:
    validate_email(<span class="string">"invalid-email"</span>)
<span class="keyword">except</span> <span class="function">ValidationError</span> <span class="keyword">as</span> e:
    <span class="keyword">print</span>(<span class="string">f"验证错误: {e.message}"</span>)
    <span class="keyword">print</span>(<span class="string">f"错误代码: {e.error_code}"</span>)
                </div>
            </div>
        </div>

        <!-- 第8页：异常链和上下文 -->
        <div class="slide">
            <h1><span class="emoji">🔗</span>异常链和上下文</h1>
            <p>Python 3提供了异常链机制，可以保留原始异常信息。</p>

            <h3>⛓️ 使用from关键字：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">def</span> <span class="function">parse_config</span>(config_str):
    <span class="keyword">try</span>:
        <span class="keyword">import</span> json
        config = json.loads(config_str)
        <span class="keyword">return</span> config
    <span class="keyword">except</span> json.<span class="exception">JSONDecodeError</span> <span class="keyword">as</span> e:
        <span class="comment"># 将JSON解析错误转换为自定义异常</span>
        <span class="keyword">raise</span> <span class="function">ValidationError</span>(<span class="string">"配置文件格式错误"</span>) <span class="keyword">from</span> e

<span class="keyword">def</span> <span class="function">load_settings</span>():
    config_str = <span class="string">'{"name": "test", "invalid": }'</span>  <span class="comment"># 无效JSON</span>
    <span class="keyword">try</span>:
        <span class="keyword">return</span> parse_config(config_str)
    <span class="keyword">except</span> <span class="function">ValidationError</span> <span class="keyword">as</span> e:
        <span class="keyword">print</span>(<span class="string">f"设置加载失败: {e}"</span>)
        <span class="keyword">print</span>(<span class="string">f"原始错误: {e.__cause__}"</span>)
        <span class="keyword">raise</span>

<span class="comment"># 测试异常链</span>
<span class="keyword">try</span>:
    load_settings()
<span class="keyword">except</span> <span class="function">ValidationError</span>:
    <span class="keyword">import</span> traceback
    traceback.print_exc()
                </div>
            </div>

            <h3>🔍 异常上下文信息：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">def</span> <span class="function">detailed_error_info</span>(func):
    <span class="keyword">try</span>:
        <span class="keyword">return</span> func()
    <span class="keyword">except</span> <span class="exception">Exception</span> <span class="keyword">as</span> e:
        <span class="keyword">import</span> sys
        exc_type, exc_value, exc_traceback = sys.exc_info()
        
        <span class="keyword">print</span>(<span class="string">f"异常类型: {exc_type.__name__}"</span>)
        <span class="keyword">print</span>(<span class="string">f"异常值: {exc_value}"</span>)
        <span class="keyword">print</span>(<span class="string">f"异常发生位置: {exc_traceback.tb_frame.f_code.co_filename}"</span>)
        <span class="keyword">print</span>(<span class="string">f"异常行号: {exc_traceback.tb_lineno}"</span>)
        
        <span class="keyword">raise</span>  <span class="comment"># 重新抛出异常</span>
                </div>
            </div>
        </div>

        <!-- 第9页：断言基础 -->
        <div class="slide">
            <h1><span class="emoji">✅</span>断言(Assert)基础</h1>
            <p>断言是一种调试工具，用于检查程序中的假设条件是否为真。</p>

            <h3>🔧 assert语句语法：</h3>
            <div class="code-container">
                <div class="code">
<span class="comment"># 基本语法</span>
<span class="keyword">assert</span> condition, <span class="string">"错误消息"</span>

<span class="comment"># 等价于</span>
<span class="keyword">if</span> <span class="keyword">not</span> condition:
    <span class="keyword">raise</span> <span class="exception">AssertionError</span>(<span class="string">"错误消息"</span>)
                </div>
            </div>

            <h3>🎯 断言的基本使用：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">def</span> <span class="function">calculate_average</span>(numbers):
    <span class="comment"># 断言输入不为空</span>
    <span class="keyword">assert</span> numbers, <span class="string">"数字列表不能为空"</span>
    
    <span class="comment"># 断言输入是列表</span>
    <span class="keyword">assert</span> <span class="builtin">isinstance</span>(numbers, <span class="builtin">list</span>), <span class="string">"输入必须是列表"</span>
    
    <span class="comment"># 断言列表中都是数字</span>
    <span class="keyword">for</span> num <span class="keyword">in</span> numbers:
        <span class="keyword">assert</span> <span class="builtin">isinstance</span>(num, (<span class="builtin">int</span>, <span class="builtin">float</span>)), <span class="string">f"{num} 不是数字"</span>
    
    <span class="keyword">return</span> <span class="builtin">sum</span>(numbers) / <span class="builtin">len</span>(numbers)

<span class="comment"># 测试函数</span>
<span class="keyword">print</span>(calculate_average([<span class="number">1</span>, <span class="number">2</span>, <span class="number">3</span>, <span class="number">4</span>, <span class="number">5</span>]))  <span class="comment"># 正常</span>

<span class="keyword">try</span>:
    calculate_average([])  <span class="comment"># 触发断言</span>
<span class="keyword">except</span> <span class="exception">AssertionError</span> <span class="keyword">as</span> e:
    <span class="keyword">print</span>(<span class="string">f"断言错误: {e}"</span>)

<span class="keyword">try</span>:
    calculate_average([<span class="number">1</span>, <span class="number">2</span>, <span class="string">"3"</span>])  <span class="comment"># 触发断言</span>
<span class="keyword">except</span> <span class="exception">AssertionError</span> <span class="keyword">as</span> e:
    <span class="keyword">print</span>(<span class="string">f"断言错误: {e}"</span>)
                </div>
            </div>
        </div>

        <!-- 第10页：断言的使用场景 -->
        <div class="slide">
            <h1><span class="emoji">🎪</span>断言的使用场景</h1>
            <p>断言主要用于开发和调试阶段，帮助发现程序逻辑错误。</p>

            <h3>🔍 前置条件检查：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">def</span> <span class="function">binary_search</span>(arr, target):
    <span class="comment"># 前置条件：数组必须已排序</span>
    <span class="keyword">assert</span> arr == <span class="builtin">sorted</span>(arr), <span class="string">"数组必须是已排序的"</span>
    <span class="keyword">assert</span> <span class="builtin">len</span>(arr) > <span class="number">0</span>, <span class="string">"数组不能为空"</span>
    
    left, right = <span class="number">0</span>, <span class="builtin">len</span>(arr) - <span class="number">1</span>
    
    <span class="keyword">while</span> left <= right:
        mid = (left + right) // <span class="number">2</span>
        <span class="keyword">if</span> arr[mid] == target:
            <span class="keyword">return</span> mid
        <span class="keyword">elif</span> arr[mid] < target:
            left = mid + <span class="number">1</span>
        <span class="keyword">else</span>:
            right = mid - <span class="number">1</span>
    
    <span class="keyword">return</span> -<span class="number">1</span>

<span class="comment"># 测试</span>
sorted_arr = [<span class="number">1</span>, <span class="number">3</span>, <span class="number">5</span>, <span class="number">7</span>, <span class="number">9</span>]
<span class="keyword">print</span>(binary_search(sorted_arr, <span class="number">5</span>))  <span class="comment"># 正常</span>

<span class="comment"># 这会触发断言错误</span>
unsorted_arr = [<span class="number">3</span>, <span class="number">1</span>, <span class="number">5</span>, <span class="number">7</span>, <span class="number">9</span>]
<span class="comment"># binary_search(unsorted_arr, 5)  # AssertionError</span>
                </div>
            </div>

            <h3>🎯 后置条件检查：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">def</span> <span class="function">factorial</span>(n):
    <span class="comment"># 前置条件</span>
    <span class="keyword">assert</span> <span class="builtin">isinstance</span>(n, <span class="builtin">int</span>), <span class="string">"输入必须是整数"</span>
    <span class="keyword">assert</span> n >= <span class="number">0</span>, <span class="string">"输入必须是非负整数"</span>
    
    <span class="keyword">if</span> n == <span class="number">0</span> <span class="keyword">or</span> n == <span class="number">1</span>:
        result = <span class="number">1</span>
    <span class="keyword">else</span>:
        result = n * factorial(n - <span class="number">1</span>)
    
    <span class="comment"># 后置条件：结果必须是正整数</span>
    <span class="keyword">assert</span> <span class="builtin">isinstance</span>(result, <span class="builtin">int</span>), <span class="string">"结果必须是整数"</span>
    <span class="keyword">assert</span> result > <span class="number">0</span>, <span class="string">"阶乘结果必须是正数"</span>
    
    <span class="keyword">return</span> result

<span class="comment"># 测试</span>
<span class="keyword">for</span> i <span class="keyword">in</span> <span class="builtin">range</span>(<span class="number">6</span>):
    <span class="keyword">print</span>(<span class="string">f"{i}! = {factorial(i)}"</span>)
                </div>
            </div>
        </div>

        <!-- 第11页：断言vs异常处理 -->
        <div class="slide">
            <h1><span class="emoji">⚖️</span>断言 vs 异常处理</h1>
            <p>理解断言和异常处理的区别，选择合适的错误处理方式。</p>

            <div class="highlight-box">
                <h3>🎯 何时使用断言：</h3>
                <ul>
                    <li>检查程序内部逻辑错误</li>
                    <li>验证函数的前置和后置条件</li>
                    <li>调试和开发阶段的检查</li>
                    <li>检查"不应该发生"的情况</li>
                </ul>
            </div>

            <div class="highlight-box">
                <h3>🛡️ 何时使用异常处理：</h3>
                <ul>
                    <li>处理外部输入错误</li>
                    <li>处理系统资源问题</li>
                    <li>处理网络和I/O错误</li>
                    <li>处理用户可能犯的错误</li>
                </ul>
            </div>

            <h3>📝 对比示例：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">def</span> <span class="function">process_user_input</span>(user_age):
    <span class="comment"># 使用异常处理 - 处理用户输入错误</span>
    <span class="keyword">try</span>:
        age = <span class="builtin">int</span>(user_age)
        <span class="keyword">if</span> age < <span class="number">0</span> <span class="keyword">or</span> age > <span class="number">150</span>:
            <span class="keyword">raise</span> <span class="exception">ValueError</span>(<span class="string">"年龄必须在0-150之间"</span>)
        <span class="keyword">return</span> age
    <span class="keyword">except</span> <span class="exception">ValueError</span> <span class="keyword">as</span> e:
        <span class="keyword">print</span>(<span class="string">f"输入错误: {e}"</span>)
        <span class="keyword">return</span> <span class="keyword">None</span>

<span class="keyword">def</span> <span class="function">calculate_discount</span>(price, discount_rate):
    <span class="comment"># 使用断言 - 检查程序内部逻辑</span>
    <span class="keyword">assert</span> <span class="builtin">isinstance</span>(price, (<span class="builtin">int</span>, <span class="builtin">float</span>)), <span class="string">"价格必须是数字"</span>
    <span class="keyword">assert</span> price >= <span class="number">0</span>, <span class="string">"价格不能为负"</span>
    <span class="keyword">assert</span> <span class="number">0</span> <= discount_rate <= <span class="number">1</span>, <span class="string">"折扣率必须在0-1之间"</span>
    
    discounted_price = price * (<span class="number">1</span> - discount_rate)
    
    <span class="comment"># 后置条件检查</span>
    <span class="keyword">assert</span> discounted_price <= price, <span class="string">"折扣后价格不能高于原价"</span>
    
    <span class="keyword">return</span> discounted_price

<span class="comment"># 测试</span>
user_input = <span class="string">"25"</span>
age = process_user_input(user_input)
<span class="keyword">if</span> age:
    <span class="keyword">print</span>(<span class="string">f"用户年龄: {age}"</span>)

price = calculate_discount(<span class="number">100</span>, <span class="number">0.2</span>)
<span class="keyword">print</span>(<span class="string">f"折扣后价格: {price}"</span>)
                </div>
            </div>
        </div>

        <!-- 第12页：调试技巧 -->
        <div class="slide">
            <h1><span class="emoji">🐛</span>异常调试技巧</h1>
            <p>掌握有效的调试技巧，快速定位和解决问题。</p>

            <h3>🔍 使用traceback模块：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">import</span> traceback
<span class="keyword">import</span> sys

<span class="keyword">def</span> <span class="function">debug_function</span>():
    <span class="keyword">try</span>:
        <span class="comment"># 模拟一个复杂的调用栈</span>
        level1()
    <span class="keyword">except</span> <span class="exception">Exception</span> <span class="keyword">as</span> e:
        <span class="keyword">print</span>(<span class="string">"=== 异常信息 ==="</span>)
        <span class="keyword">print</span>(<span class="string">f"异常类型: {type(e).__name__}"</span>)
        <span class="keyword">print</span>(<span class="string">f"异常消息: {e}"</span>)
        
        <span class="keyword">print</span>(<span class="string">"\n=== 完整堆栈跟踪 ==="</span>)
        traceback.print_exc()
        
        <span class="keyword">print</span>(<span class="string">"\n=== 格式化堆栈信息 ==="</span>)
        tb_lines = traceback.format_exception(*sys.exc_info())
        <span class="keyword">for</span> line <span class="keyword">in</span> tb_lines:
            <span class="keyword">print</span>(line.strip())

<span class="keyword">def</span> <span class="function">level1</span>():
    level2()

<span class="keyword">def</span> <span class="function">level2</span>():
    level3()

<span class="keyword">def</span> <span class="function">level3</span>():
    <span class="comment"># 故意制造异常</span>
    result = <span class="number">10</span> / <span class="number">0</span>
    <span class="keyword">return</span> result

<span class="comment"># 运行调试</span>
debug_function()
                </div>
            </div>

            <h3>📊 自定义异常记录器：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">import</span> logging
<span class="keyword">import</span> functools

<span class="comment"># 配置日志</span>
logging.basicConfig(
    level=logging.INFO,
    format=<span class="string">'%(asctime)s - %(levelname)s - %(message)s'</span>
)

<span class="keyword">def</span> <span class="function">exception_logger</span>(func):
    <span class="string">"""装饰器：自动记录异常信息"""\span>
    <span class="builtin">@functools.wraps</span>(func)
    <span class="keyword">def</span> <span class="function">wrapper</span>(*args, **kwargs):
        <span class="keyword">try</span>:
            <span class="keyword">return</span> func(*args, **kwargs)
        <span class="keyword">except</span> <span class="exception">Exception</span> <span class="keyword">as</span> e:
            logging.error(
                <span class="string">f"函数 {func.__name__} 发生异常: {type(e).__name__}: {e}"</span>,
                exc_info=<span class="keyword">True</span>
            )
            <span class="keyword">raise</span>
    <span class="keyword">return</span> wrapper

<span class="builtin">@exception_logger</span>
<span class="keyword">def</span> <span class="function">risky_operation</span>(x, y):
    <span class="keyword">return</span> x / y

<span class="comment"># 测试</span>
<span class="keyword">try</span>:
    result = risky_operation(<span class="number">10</span>, <span class="number">0</span>)
<span class="keyword">except</span> <span class="exception">ZeroDivisionError</span>:
    <span class="keyword">print</span>(<span class="string">"异常已被记录"</span>)
                </div>
            </div>
        </div>

        <!-- 第13页：最佳实践 -->
        <div class="slide">
            <h1><span class="emoji">🏆</span>异常处理最佳实践</h1>
            <p>遵循最佳实践，编写健壮和可维护的代码。</p>

            <div class="highlight-box">
                <h3>✅ 推荐做法：</h3>
                <ul>
                    <li>具体化异常类型，避免使用裸露的except</li>
                    <li>提供有意义的错误消息</li>
                    <li>使用finally确保资源清理</li>
                    <li>记录异常信息用于调试</li>
                    <li>在适当的层级处理异常</li>
                </ul>
            </div>

            <h3>🎯 良好的异常处理示例：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">import</span> json
<span class="keyword">import</span> logging

<span class="keyword">class</span> <span class="function">ConfigManager</span>:
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="builtin">self</span>, config_file):
        <span class="builtin">self</span>.config_file = config_file
        <span class="builtin">self</span>.config = {}
        <span class="builtin">self</span>.logger = logging.getLogger(__name__)
    
    <span class="keyword">def</span> <span class="function">load_config</span>(<span class="builtin">self</span>):
        <span class="string">"""加载配置文件"""\span>
        <span class="keyword">try</span>:
            <span class="keyword">with</span> <span class="builtin">open</span>(<span class="builtin">self</span>.config_file, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> f:
                <span class="builtin">self</span>.config = json.load(f)
                <span class="builtin">self</span>.logger.info(<span class="string">f"配置文件 {<span class="builtin">self</span>.config_file} 加载成功"</span>)
                
        <span class="keyword">except</span> <span class="exception">FileNotFoundError</span>:
            <span class="builtin">self</span>.logger.error(<span class="string">f"配置文件 {<span class="builtin">self</span>.config_file} 不存在"</span>)
            <span class="builtin">self</span>._create_default_config()
            
        <span class="keyword">except</span> json.<span class="exception">JSONDecodeError</span> <span class="keyword">as</span> e:
            <span class="builtin">self</span>.logger.error(<span class="string">f"配置文件格式错误: {e}"</span>)
            <span class="keyword">raise</span> <span class="function">ValidationError</span>(<span class="string">f"配置文件格式无效: {e}"</span>) <span class="keyword">from</span> e
            
        <span class="keyword">except</span> <span class="exception">PermissionError</span>:
            <span class="builtin">self</span>.logger.error(<span class="string">f"没有权限读取配置文件 {<span class="builtin">self</span>.config_file}"</span>)
            <span class="keyword">raise</span>
            
        <span class="keyword">except</span> <span class="exception">Exception</span> <span class="keyword">as</span> e:
            <span class="builtin">self</span>.logger.error(<span class="string">f"加载配置文件时发生未知错误: {e}"</span>)
            <span class="keyword">raise</span>
    
    <span class="keyword">def</span> <span class="function">_create_default_config</span>(<span class="builtin">self</span>):
        <span class="string">"""创建默认配置"""\span>
        <span class="builtin">self</span>.config = {
            <span class="string">"app_name"</span>: <span class="string">"MyApp"</span>,
            <span class="string">"version"</span>: <span class="string">"1.0.0"</span>,
            <span class="string">"debug"</span>: <span class="keyword">False</span>
        }
        <span class="builtin">self</span>.logger.info(<span class="string">"使用默认配置"</span>)

<span class="comment"># 使用示例</span>
config_manager = <span class="function">ConfigManager</span>(<span class="string">"app_config.json"</span>)
config_manager.load_config()
                </div>
            </div>
        </div>

        <!-- 第14页：性能考虑 -->
        <div class="slide">
            <h1><span class="emoji">⚡</span>异常处理的性能考虑</h1>
            <p>了解异常处理的性能影响，编写高效的代码。</p>

            <h3>📊 性能测试示例：</h3>
            <div class="code-container">
                <div class="code">
<span class="keyword">import</span> time

<span class="keyword">def</span> <span class="function">performance_test</span>():
    <span class="comment"># 方法1：使用异常处理</span>
    start_time = time.time()
    <span class="keyword">for</span> i <span class="keyword">in</span> <span class="builtin">range</span>(<span class="number">100000</span>):
        <span class="keyword">try</span>:
            result = <span class="number">10</span> / i
        <span class="keyword">except</span> <span class="exception">ZeroDivisionError</span>:
            result = <span class="number">0</span>
    exception_time = time.time() - start_time
    
    <span class="comment"># 方法2：使用条件检查</span>
    start_time = time.time()
    <span class="keyword">for</span> i <span class="keyword">in</span> <span class="builtin">range</span>(<span class="number">100000</span>):
        <span class="keyword">if</span> i == <span class="number">0</span>:
            result = <span class="number">0</span>
        <span class="keyword">else</span>:
            result = <span class="number">10</span> / i
    condition_time = time.time() - start_time
    
    <span class="keyword">print</span>(<span class="string">f"异常处理方法耗时: {exception_time:.4f}秒"</span>)
    <span class="keyword">print</span>(<span class="string">f"条件检查方法耗时: {condition_time:.4f}秒"</span>)
    <span class="keyword">print</span>(<span class="string">f"性能差异: {exception_time/condition_time:.2f}倍"</span>)

<span class="comment"># 运行性能测试</span>
performance_test()
                </div>
            </div>

            <div class="highlight-box">
                <h3>💡 性能优化建议：</h3>
                <ul>
                    <li>异常处理比条件检查慢，避免在循环中频繁使用</li>
                    <li>使用EAFP（Easier to Ask for Forgiveness than Permission）原则</li>
                    <li>在性能关键代码中优先使用条件检查</li>
                    <li>异常应该用于处理真正的异常情况</li>
                </ul>
            </div>
        </div>

        <!-- 第15页：AI辅助编程工具介绍 -->
        <div class="slide">
            <h1><span class="emoji">🤖</span>AI辅助编程工具</h1>
            <p>利用AI工具提升Python异常处理和调试效率。</p>

            <h3>🛠️ 主流AI编程工具：</h3>
            <ul>
                <li><strong>GitHub Copilot</strong>：代码自动补全和生成</li>
                <li><strong>ChatGPT/Claude</strong>：代码解释和问题解答</li>
                <li><strong>Cursor</strong>：AI驱动的代码编辑器</li>
                <li><strong>Tabnine</strong>：智能代码补全</li>
                <li><strong>CodeWhisperer</strong>：Amazon的AI编程助手</li>
            </ul>

            <div class="ai-prompt">
                <h3>🎯 AI提示词模板 - 异常处理：</h3>
                <p><strong>"请帮我为以下Python函数添加完整的异常处理机制，包括：</strong></p>
                <ul>
                    <li>参数验证和类型检查</li>
                    <li>可能出现的异常类型分析</li>
                    <li>具体的异常处理代码</li>
                    <li>日志记录和错误消息</li>
                    <li>函数使用示例"</li>
                </ul>
            </div>

            <div class="code-container">
                <div class="code">
<span class="comment"># 示例：向AI请求帮助的函数</span>
<span class="keyword">def</span> <span class="function">process_data_file</span>(filename, encoding=<span class="string">'utf-8'</span>):
    <span class="comment"># 需要AI帮助添加异常处理的原始函数</span>
    <span class="keyword">with</span> <span class="builtin">open</span>(filename, <span class="string">'r'</span>, encoding=encoding) <span class="keyword">as</span> f:
        data = json.load(f)
    
    processed = []
    <span class="keyword">for</span> item <span class="keyword">in</span> data:
        result = item[<span class="string">'value'</span>] * <span class="number">2</span>
        processed.append(result)
    
    <span class="keyword">return</span> processed
                </div>
            </div>
        </div>

        <!-- 第16页：AI提示词模板 -->
        <div class="slide">
            <h1><span class="emoji">📝</span>AI提示词模板集合</h1>
            <p>精心设计的提示词模板，让AI更好地帮助你学习和编程。</p>

            <div class="ai-prompt">
                <h3>🔍 代码调试模板：</h3>
                <p><strong>"我的Python代码出现了以下错误：[错误信息]。请帮我：</strong></p>
                <ul>
                    <li>分析错误原因和可能的触发条件</li>
                    <li>提供详细的修复方案</li>
                    <li>解释为什么会出现这个错误</li>
                    <li>给出预防类似错误的建议</li>
                    <li>提供测试用例验证修复效果"</li>
                </ul>
            </div>

            <div class="ai-prompt">
                <h3>🎓 学习理解模板：</h3>
                <p><strong>"请用通俗易懂的方式解释Python中的[概念名称]：</strong></p>
                <ul>
                    <li>基本概念和工作原理</li>
                    <li>实际应用场景和示例</li>
                    <li>常见误区和注意事项</li>
                    <li>与相关概念的区别和联系</li>
                    <li>提供练习题帮助巩固理解"</li>
                </ul>
            </div>

            <div class="ai-prompt">
                <h3>🏗️ 代码重构模板：</h3>
                <p><strong>"请帮我重构以下代码，要求：</strong></p>
                <ul>
                    <li>添加完整的异常处理机制</li>
                    <li>提高代码的可读性和可维护性</li>
                    <li>遵循Python最佳实践</li>
                    <li>添加适当的文档字符串和注释</li>
                    <li>提供单元测试用例"</li>
                </ul>
            </div>
        </div>

        <!-- 第17页：实战项目示例 -->
        <div class="slide">
            <h1><span class="emoji">🚀</span>实战项目：文件处理器</h1>
            <p>综合运用异常处理和断言，构建一个健壮的文件处理系统。</p>

            <div class="code-container">
                <div class="code">
<span class="keyword">import</span> os
<span class="keyword">import</span> json
<span class="keyword">import</span> logging
<span class="keyword">from</span> pathlib <span class="keyword">import</span> Path
<span class="keyword">from</span> typing <span class="keyword">import</span> List, Dict, Any

<span class="keyword">class</span> <span class="function">FileProcessor</span>:
    <span class="string">"""文件处理器 - 展示异常处理最佳实践"""\span>
    
    <span class="keyword">def</span> <span class="function">__init__</span>(<span class="builtin">self</span>, base_dir: <span class="builtin">str</span>):
        <span class="comment"># 使用断言验证初始化参数</span>
        <span class="keyword">assert</span> <span class="builtin">isinstance</span>(base_dir, <span class="builtin">str</span>), <span class="string">"base_dir必须是字符串"</span>
        <span class="keyword">assert</span> base_dir.strip(), <span class="string">"base_dir不能为空"</span>
        
        <span class="builtin">self</span>.base_dir = Path(base_dir)
        <span class="builtin">self</span>.logger = logging.getLogger(__name__)
        
        <span class="comment"># 确保目录存在</span>
        <span class="keyword">try</span>:
            <span class="builtin">self</span>.base_dir.mkdir(parents=<span class="keyword">True</span>, exist_ok=<span class="keyword">True</span>)
        <span class="keyword">except</span> <span class="exception">PermissionError</span>:
            <span class="keyword">raise</span> <span class="function">RuntimeError</span>(<span class="string">f"没有权限创建目录: {base_dir}"</span>)
    
    <span class="keyword">def</span> <span class="function">read_json_file</span>(<span class="builtin">self</span>, filename: <span class="builtin">str</span>) -> Dict[<span class="builtin">str</span>, Any]:
        <span class="string">"""安全地读取JSON文件"""\span>
        <span class="keyword">assert</span> <span class="builtin">isinstance</span>(filename, <span class="builtin">str</span>), <span class="string">"文件名必须是字符串"</span>
        
        file_path = <span class="builtin">self</span>.base_dir / filename
        
        <span class="keyword">try</span>:
            <span class="keyword">with</span> <span class="builtin">open</span>(file_path, <span class="string">'r'</span>, encoding=<span class="string">'utf-8'</span>) <span class="keyword">as</span> f:
                data = json.load(f)
                
            <span class="comment"># 后置条件检查</span>
            <span class="keyword">assert</span> <span class="builtin">isinstance</span>(data, <span class="builtin">dict</span>), <span class="string">"JSON数据必须是字典格式"</span>
            
            <span class="builtin">self</span>.logger.info(<span class="string">f"成功读取文件: {filename}"</span>)
            <span class="keyword">return</span> data
            
        <span class="keyword">except</span> <span class="exception">FileNotFoundError</span>:
            <span class="builtin">self</span>.logger.error(<span class="string">f"文件不存在: {filename}"</span>)
            <span class="keyword">raise</span>
        <span class="keyword">except</span> json.<span class="exception">JSONDecodeError</span> <span class="keyword">as</span> e:
            <span class="builtin">self</span>.logger.error(<span class="string">f"JSON格式错误: {e}"</span>)
            <span class="keyword">raise</span> <span class="function">ValueError</span>(<span class="string">f"文件 {filename} 不是有效的JSON格式"</span>) <span class="keyword">from</span> e
        <span class="keyword">except</span> <span class="exception">UnicodeDecodeError</span> <span class="keyword">as</span> e:
            <span class="builtin">self</span>.logger.error(<span class="string">f"文件编码错误: {e}"</span>)
             <span class="keyword">raise</span> <span class="function">ValueError</span>(<span class="string">f"文件 {filename} 编码格式不正确"</span>) <span class="keyword">from</span> e
                </div>
            </div>
        </div>

        <!-- 第18页：单元测试与异常 -->
        <div class="slide">
            <h1><span class="emoji">🧪</span>单元测试与异常处理</h1>
            <p>编写测试用例验证异常处理逻辑的正确性。</p>

            <div class="code-container">
                <div class="code">
<span class="keyword">import</span> unittest
<span class="keyword">from</span> unittest.mock <span class="keyword">import</span> patch, mock_open

<span class="keyword">class</span> <span class="function">TestFileProcessor</span>(unittest.<span class="function">TestCase</span>):
    <span class="string">"""测试文件处理器的异常处理"""\span>
    
    <span class="keyword">def</span> <span class="function">setUp</span>(<span class="builtin">self</span>):
        <span class="builtin">self</span>.processor = <span class="function">FileProcessor</span>(<span class="string">"/tmp/test"</span>)
    
    <span class="keyword">def</span> <span class="function">test_read_json_file_success</span>(<span class="builtin">self</span>):
        <span class="string">"""测试成功读取JSON文件"""\span>
        mock_data = <span class="string">'{"key": "value"}'</span>
        <span class="keyword">with</span> patch(<span class="string">"builtins.open"</span>, mock_open(read_data=mock_data)):
            result = <span class="builtin">self</span>.processor.read_json_file(<span class="string">"test.json"</span>)
            <span class="builtin">self</span>.assertEqual(result, {<span class="string">"key"</span>: <span class="string">"value"</span>})
    
    <span class="keyword">def</span> <span class="function">test_read_json_file_not_found</span>(<span class="builtin">self</span>):
        <span class="string">"""测试文件不存在异常"""\span>
        <span class="keyword">with</span> patch(<span class="string">"builtins.open"</span>, side_effect=<span class="exception">FileNotFoundError</span>):
            <span class="keyword">with</span> <span class="builtin">self</span>.assertRaises(<span class="exception">FileNotFoundError</span>):
                <span class="builtin">self</span>.processor.read_json_file(<span class="string">"nonexistent.json"</span>)
    
    <span class="keyword">def</span> <span class="function">test_read_json_file_invalid_json</span>(<span class="builtin">self</span>):
        <span class="string">"""测试无效JSON格式异常"""\span>
        invalid_json = <span class="string">'{"key": invalid}'</span>
        <span class="keyword">with</span> patch(<span class="string">"builtins.open"</span>, mock_open(read_data=invalid_json)):
            <span class="keyword">with</span> <span class="builtin">self</span>.assertRaises(<span class="exception">ValueError</span>) <span class="keyword">as</span> cm:
                <span class="builtin">self</span>.processor.read_json_file(<span class="string">"invalid.json"</span>)
            <span class="builtin">self</span>.assertIn(<span class="string">"不是有效的JSON格式"</span>, <span class="builtin">str</span>(cm.exception))
    
    <span class="keyword">def</span> <span class="function">test_init_with_invalid_params</span>(<span class="builtin">self</span>):
        <span class="string">"""测试初始化参数验证"""\span>
        <span class="keyword">with</span> <span class="builtin">self</span>.assertRaises(<span class="exception">AssertionError</span>):
            <span class="function">FileProcessor</span>(<span class="number">123</span>)  <span class="comment"># 非字符串参数</span>
        
        <span class="keyword">with</span> <span class="builtin">self</span>.assertRaises(<span class="exception">AssertionError</span>):
            <span class="function">FileProcessor</span>(<span class="string">""</span>)    <span class="comment"># 空字符串</span>

<span class="comment"># 运行测试</span>
<span class="keyword">if</span> __name__ == <span class="string">'__main__'</span>:
    unittest.main()
                </div>
            </div>

            <div class="ai-prompt">
                <h3>🎯 AI提示词 - 测试用例生成：</h3>
                <p><strong>"请为以下Python函数生成完整的单元测试，包括：</strong></p>
                <ul>
                    <li>正常情况的测试用例</li>
                    <li>各种异常情况的测试</li>
                    <li>边界条件测试</li>
                    <li>Mock对象的使用</li>
                    <li>断言验证和错误消息检查"</li>
                </ul>
            </div>
        </div>

        <!-- 第19页：总结与进阶 -->
        <div class="slide">
            <h1><span class="emoji">🎓</span>总结与进阶学习</h1>
            <p>回顾核心概念，规划进阶学习路径。</p>

            <div class="highlight-box">
                <h3>📚 核心知识点回顾：</h3>
                <ul>
                    <li><strong>异常处理</strong>：try-except-else-finally结构</li>
                    <li><strong>异常类型</strong>：内置异常和自定义异常</li>
                    <li><strong>断言机制</strong>：assert语句的使用场景</li>
                    <li><strong>最佳实践</strong>：性能考虑和代码质量</li>
                    <li><strong>调试技巧</strong>：traceback和日志记录</li>
                </ul>
            </div>

            <h3>🚀 进阶学习建议：</h3>
            <ul>
                <li><strong>异步编程异常处理</strong>：asyncio中的异常管理</li>
                <li><strong>多线程异常处理</strong>：线程安全的错误处理</li>
                <li><strong>Web框架异常</strong>：Flask/Django的异常处理机制</li>
                <li><strong>数据科学异常</strong>：pandas/numpy中的错误处理</li>
                <li><strong>性能监控</strong>：Sentry等异常监控工具</li>
            </ul>

            <div class="ai-prompt">
                <h3>🤖 AI学习伙伴提示词：</h3>
                <p><strong>"我想深入学习Python异常处理的高级主题，请为我制定一个学习计划：</strong></p>
                <ul>
                    <li>从基础到高级的学习路径</li>
                    <li>每个阶段的重点知识点</li>
                    <li>实践项目建议</li>
                    <li>推荐的学习资源</li>
                    <li>技能检验的方法"</li>
                </ul>
            </div>

            <div class="code-container">
                <div class="code">
<span class="comment"># 进阶示例：上下文管理器与异常处理</span>
<span class="keyword">class</span> <span class="function">DatabaseConnection</span>:
    <span class="keyword">def</span> <span class="function">__enter__</span>(<span class="builtin">self</span>):
        <span class="keyword">try</span>:
            <span class="builtin">self</span>.connection = connect_to_database()
            <span class="keyword">return</span> <span class="builtin">self</span>.connection
        <span class="keyword">except</span> <span class="exception">ConnectionError</span> <span class="keyword">as</span> e:
            <span class="keyword">raise</span> <span class="function">RuntimeError</span>(<span class="string">f"数据库连接失败: {e}"</span>) <span class="keyword">from</span> e
    
    <span class="keyword">def</span> <span class="function">__exit__</span>(<span class="builtin">self</span>, exc_type, exc_val, exc_tb):
        <span class="keyword">if</span> <span class="builtin">hasattr</span>(<span class="builtin">self</span>, <span class="string">'connection'</span>):
            <span class="builtin">self</span>.connection.close()
        
        <span class="keyword">if</span> exc_type <span class="keyword">is</span> <span class="keyword">not</span> <span class="keyword">None</span>:
            logging.error(<span class="string">f"数据库操作异常: {exc_val}"</span>)
        
        <span class="keyword">return</span> <span class="keyword">False</span>  <span class="comment"># 不抑制异常</span>

<span class="comment"># 使用上下文管理器</span>
<span class="keyword">try</span>:
    <span class="keyword">with</span> <span class="function">DatabaseConnection</span>() <span class="keyword">as</span> db:
        <span class="comment"># 数据库操作</span>
        result = db.execute(<span class="string">"SELECT * FROM users"</span>)
<span class="keyword">except</span> <span class="function">RuntimeError</span> <span class="keyword">as</span> e:
    <span class="keyword">print</span>(<span class="string">f"操作失败: {e}"</span>)
                </div>
            </div>
        </div>

        <!-- 第20页：结束页 -->
        <div class="slide">
            <h1><span class="emoji">🎉</span>恭喜完成学习！</h1>
            <h2>Python异常处理与断言 - 掌握完成</h2>
            
            <div class="highlight-box">
                <h3>🏆 你已经掌握了：</h3>
                <ul>
                    <li>✅ 异常处理的完整语法和最佳实践</li>
                    <li>✅ 断言的正确使用方法和场景</li>
                    <li>✅ 自定义异常类的设计和实现</li>
                    <li>✅ 调试技巧和性能优化策略</li>
                    <li>✅ AI辅助编程的高效方法</li>
                </ul>
            </div>

            <h3>🚀 下一步行动建议：</h3>
            <ul>
                <li><strong>实践项目</strong>：将所学知识应用到实际项目中</li>
                <li><strong>代码审查</strong>：检查现有代码的异常处理质量</li>
                <li><strong>持续学习</strong>：关注Python异常处理的新特性</li>
                <li><strong>分享交流</strong>：与其他开发者分享经验</li>
                <li><strong>AI协作</strong>：熟练使用AI工具提升开发效率</li>
            </ul>

            <div class="ai-prompt">
                <h3>🎯 最终AI提示词模板：</h3>
                <p><strong>"请帮我review以下Python代码的异常处理部分：</strong></p>
                <ul>
                    <li>检查异常处理的完整性和正确性</li>
                    <li>评估代码的健壮性和可维护性</li>
                    <li>提出改进建议和最佳实践</li>
                    <li>指出潜在的安全风险</li>
                    <li>推荐相关的测试用例"</li>
                </ul>
            </div>

            <div class="code-container">
                <div class="code">
<span class="comment"># 感谢使用本教程！</span>
<span class="comment"># 继续你的Python学习之旅 🐍</span>

<span class="keyword">def</span> <span class="function">thank_you</span>():
    <span class="keyword">try</span>:
        <span class="keyword">print</span>(<span class="string">"🎓 恭喜你完成了Python异常处理学习！"</span>)
        <span class="keyword">print</span>(<span class="string">"🚀 现在去创造令人惊叹的代码吧！"</span>)
        <span class="keyword">return</span> <span class="string">"学习成功！"</span>
    <span class="keyword">except</span> <span class="exception">Exception</span> <span class="keyword">as</span> e:
        <span class="comment"># 即使在感谢函数中，我们也要处理异常 😄</span>
        <span class="keyword">print</span>(<span class="string">f"感谢时出现了意外: {e}"</span>)
        <span class="keyword">return</span> <span class="string">"感谢依然有效！"</span>
    <span class="keyword">finally</span>:
        <span class="keyword">print</span>(<span class="string">"💪 继续编程，永不停歇！"</span>)

<span class="comment"># 调用感谢函数</span>
thank_you()
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">← 上一页</button>
        <button class="nav-btn" onclick="nextSlide()">下一页 →</button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        const progressBar = document.getElementById('progressBar');
        const slideCounter = document.getElementById('slideCounter');

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            
            // 更新进度条
            const progress = ((currentSlide + 1) / totalSlides) * 100;
            progressBar.style.width = progress + '%';
            
            // 更新页码
            slideCounter.textContent = `${currentSlide + 1} / ${totalSlides}`;
        }

        function nextSlide() {
            showSlide(currentSlide + 1);
        }

        function previousSlide() {
            showSlide(currentSlide - 1);
        }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                previousSlide();
            }
        });

        // 触摸导航（移动设备）
        let startX = 0;
        let endX = 0;

        document.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
        });

        document.addEventListener('touchend', function(e) {
            endX = e.changedTouches[0].clientX;
            handleSwipe();
        });

        function handleSwipe() {
            const threshold = 50;
            const diff = startX - endX;
            
            if (Math.abs(diff) > threshold) {
                if (diff > 0) {
                    nextSlide(); // 向左滑动，下一页
                } else {
                    previousSlide(); // 向右滑动，上一页
                }
            }
        }

        // 初始化
        showSlide(0);
    </script>
</body>
</html>